/**
 * Asset Browser Component
 * Browse, search, and manage uploaded 360° images
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search, 
  Filter, 
  Grid3X3, 
  List, 
  Trash2, 
  Download, 
  Eye,
  Calendar,
  FileImage,
  AlertTriangle,
  CheckCircle,
  MoreVertical
} from 'lucide-react';
import { toast } from 'sonner';

import { assetManager, type AssetMetadata } from '@/lib/assets/assetManager';

export interface AssetBrowserProps {
  onAssetSelect?: (asset: AssetMetadata) => void;
  onAssetDelete?: (assetId: string) => void;
  selectionMode?: 'single' | 'multiple' | 'none';
  selectedAssets?: string[];
  showUploadButton?: boolean;
  className?: string;
}

type ViewMode = 'grid' | 'list';
type SortBy = 'date' | 'name' | 'size';
type FilterBy = 'all' | 'panorama' | 'regular' | 'errors';

const AssetBrowser: React.FC<AssetBrowserProps> = ({
  onAssetSelect,
  onAssetDelete,
  selectionMode = 'none',
  selectedAssets = [],
  showUploadButton = true,
  className = ''
}) => {
  const [assets, setAssets] = useState<AssetMetadata[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [sortBy, setSortBy] = useState<SortBy>('date');
  const [filterBy, setFilterBy] = useState<FilterBy>('all');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [total, setTotal] = useState(0);

  // Load assets
  const loadAssets = useCallback(async (reset: boolean = false) => {
    try {
      setLoading(true);
      
      const filters: any = {};
      if (filterBy === 'panorama') filters.isPanorama = true;
      if (filterBy === 'regular') filters.isPanorama = false;
      
      const currentPage = reset ? 1 : page;
      const result = await assetManager.listAssets(currentPage, 20, filters);
      
      if (reset) {
        setAssets(result.assets);
        setPage(1);
      } else {
        setAssets(prev => [...prev, ...result.assets]);
      }
      
      setHasMore(result.hasMore);
      setTotal(result.total);
      
    } catch (error) {
      toast.error('Failed to load assets');
      console.error('Load assets error:', error);
    } finally {
      setLoading(false);
    }
  }, [page, filterBy]);

  // Initial load
  useEffect(() => {
    loadAssets(true);
  }, [filterBy]);

  // Filter and sort assets
  const filteredAssets = assets
    .filter(asset => {
      if (!searchTerm) return true;
      return asset.originalName.toLowerCase().includes(searchTerm.toLowerCase()) ||
             asset.filename.toLowerCase().includes(searchTerm.toLowerCase());
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.originalName.localeCompare(b.originalName);
        case 'size':
          return b.size - a.size;
        case 'date':
        default:
          return new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime();
      }
    });

  // Handle asset selection
  const handleAssetClick = useCallback((asset: AssetMetadata) => {
    if (selectionMode !== 'none') {
      onAssetSelect?.(asset);
    }
  }, [selectionMode, onAssetSelect]);

  // Handle asset deletion
  const handleDelete = useCallback(async (assetId: string) => {
    if (!confirm('Are you sure you want to delete this asset?')) return;
    
    try {
      await assetManager.deleteAsset(assetId);
      setAssets(prev => prev.filter(asset => asset.id !== assetId));
      onAssetDelete?.(assetId);
      toast.success('Asset deleted successfully');
    } catch (error) {
      toast.error('Failed to delete asset');
      console.error('Delete error:', error);
    }
  }, [onAssetDelete]);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className={`asset-browser space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">Asset Library</h2>
          <p className="text-sm text-muted-foreground">
            {total} assets • {filteredAssets.length} shown
          </p>
        </div>
        
        {showUploadButton && (
          <Button>
            <FileImage className="w-4 h-4 mr-2" />
            Upload Assets
          </Button>
        )}
      </div>

      {/* Controls */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Search assets..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        {/* Filters */}
        <div className="flex gap-2">
          <Select value={filterBy} onValueChange={(value: FilterBy) => setFilterBy(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Assets</SelectItem>
              <SelectItem value="panorama">Panoramas</SelectItem>
              <SelectItem value="regular">Regular Images</SelectItem>
              <SelectItem value="errors">With Errors</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={sortBy} onValueChange={(value: SortBy) => setSortBy(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="date">Date</SelectItem>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="size">Size</SelectItem>
            </SelectContent>
          </Select>
          
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="rounded-r-none"
            >
              <Grid3X3 className="w-4 h-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-l-none"
            >
              <List className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Asset Grid/List */}
      {loading && assets.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <FileImage className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-muted-foreground">Loading assets...</p>
          </div>
        </div>
      ) : filteredAssets.length === 0 ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <FileImage className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">No Assets Found</h3>
            <p className="text-muted-foreground">
              {searchTerm ? 'No assets match your search criteria' : 'Upload some 360° images to get started'}
            </p>
          </div>
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4'
            : 'space-y-2'
        }>
          {filteredAssets.map((asset) => (
            <Card
              key={asset.id}
              className={`cursor-pointer transition-colors hover:bg-muted/50 ${
                selectedAssets.includes(asset.id) ? 'border-primary bg-primary/5' : ''
              }`}
              onClick={() => handleAssetClick(asset)}
            >
              {viewMode === 'grid' ? (
                <div>
                  {/* Thumbnail */}
                  <div className="aspect-video bg-muted rounded-t-lg overflow-hidden">
                    {asset.optimizedVersions?.thumbnail ? (
                      <img
                        src={asset.optimizedVersions.thumbnail}
                        alt={asset.originalName}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <FileImage className="w-8 h-8 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                  
                  <CardContent className="p-3">
                    <div className="space-y-2">
                      <h3 className="font-medium text-sm truncate" title={asset.originalName}>
                        {asset.originalName}
                      </h3>
                      
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span>{asset.width}×{asset.height}</span>
                        <span>{formatFileSize(asset.size)}</span>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        {asset.validation.isPanorama && (
                          <Badge variant="default" className="text-xs">360°</Badge>
                        )}
                        {asset.validation.errors.length > 0 && (
                          <Badge variant="destructive" className="text-xs">
                            <AlertTriangle className="w-3 h-3 mr-1" />
                            Error
                          </Badge>
                        )}
                        {asset.validation.warnings.length > 0 && (
                          <Badge variant="secondary" className="text-xs">
                            <AlertTriangle className="w-3 h-3 mr-1" />
                            Warning
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">
                          {formatDate(asset.uploadedAt)}
                        </span>
                        
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              window.open(asset.optimizedVersions?.original, '_blank');
                            }}
                          >
                            <Eye className="w-3 h-3" />
                          </Button>
                          
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(asset.id);
                            }}
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </div>
              ) : (
                <CardContent className="p-4">
                  <div className="flex items-center gap-4">
                    {/* Thumbnail */}
                    <div className="w-16 h-10 bg-muted rounded overflow-hidden flex-shrink-0">
                      {asset.optimizedVersions?.thumbnail ? (
                        <img
                          src={asset.optimizedVersions.thumbnail}
                          alt={asset.originalName}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <FileImage className="w-4 h-4 text-muted-foreground" />
                        </div>
                      )}
                    </div>
                    
                    {/* Info */}
                    <div className="flex-1 min-w-0">
                      <h3 className="font-medium truncate" title={asset.originalName}>
                        {asset.originalName}
                      </h3>
                      <div className="flex items-center gap-4 text-sm text-muted-foreground">
                        <span>{asset.width}×{asset.height}</span>
                        <span>{formatFileSize(asset.size)}</span>
                        <span>{formatDate(asset.uploadedAt)}</span>
                      </div>
                    </div>
                    
                    {/* Badges */}
                    <div className="flex items-center gap-2">
                      {asset.validation.isPanorama && (
                        <Badge variant="default">360°</Badge>
                      )}
                      {asset.validation.errors.length > 0 && (
                        <Badge variant="destructive">
                          <AlertTriangle className="w-3 h-3 mr-1" />
                          Error
                        </Badge>
                      )}
                      {asset.validation.warnings.length > 0 && (
                        <Badge variant="secondary">
                          <AlertTriangle className="w-3 h-3 mr-1" />
                          Warning
                        </Badge>
                      )}
                    </div>
                    
                    {/* Actions */}
                    <div className="flex items-center gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          window.open(asset.optimizedVersions?.original, '_blank');
                        }}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(asset.id);
                        }}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}

      {/* Load More */}
      {hasMore && (
        <div className="text-center">
          <Button
            variant="outline"
            onClick={() => {
              setPage(prev => prev + 1);
              loadAssets();
            }}
            disabled={loading}
          >
            Load More Assets
          </Button>
        </div>
      )}
    </div>
  );
};

export default AssetBrowser;
