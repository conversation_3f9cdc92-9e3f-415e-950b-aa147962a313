/**
 * ProductHotspot Component
 * Interactive product hotspot for 360° tours
 * Integrates e-commerce with VRT tour viewing experience
 */

import { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Html } from '@react-three/drei';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ShoppingCart, 
  Eye, 
  X, 
  Package,
  MapPin,
  Star,
  MessageCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import type { Product } from './ProductCard';

interface ProductHotspotProps {
  position: [number, number, number]; // 3D position in tour
  product: Product;
  tourContext?: {
    tourId: string;
    tourTitle: string;
    sceneId: string;
  };
  onAddToCart?: (productId: string, tourContext?: any) => void;
  onViewProduct?: (productId: string) => void;
  onContactVendor?: (vendorId: string, productId: string) => void;
  className?: string;
}

const ProductHotspot = ({
  position,
  product,
  tourContext,
  onAddToCart,
  onViewProduct,
  onContactVendor,
  className
}: ProductHotspotProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isPulsing, setIsPulsing] = useState(true);
  const hotspotRef = useRef<HTMLDivElement>(null);

  const isOnSale = product.compare_at_price && product.compare_at_price > product.price;
  const discountPercentage = isOnSale 
    ? Math.round(((product.compare_at_price! - product.price) / product.compare_at_price!) * 100)
    : 0;

  const isOutOfStock = product.status === 'out_of_stock' || product.inventory_quantity === 0;
  const isUnavailable = product.status !== 'active' || isOutOfStock;

  const handleAddToCart = () => {
    if (!isUnavailable && onAddToCart) {
      onAddToCart(product.id, tourContext);
      setIsExpanded(false);
    }
  };

  const handleViewProduct = () => {
    if (onViewProduct) {
      onViewProduct(product.id);
    }
  };

  const handleContactVendor = () => {
    if (onContactVendor && product.vendor.whatsapp_number) {
      onContactVendor(product.vendor.id, product.id);
    }
  };

  // Hotspot marker component
  const HotspotMarker = () => (
    <motion.div
      ref={hotspotRef}
      className={cn(
        "relative cursor-pointer select-none",
        className
      )}
      onMouseEnter={() => {
        setIsHovered(true);
        setIsPulsing(false);
      }}
      onMouseLeave={() => {
        setIsHovered(false);
        if (!isExpanded) setIsPulsing(true);
      }}
      onClick={() => setIsExpanded(!isExpanded)}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
    >
      {/* Pulsing ring animation */}
      <AnimatePresence>
        {isPulsing && (
          <motion.div
            className="absolute inset-0 rounded-full border-2 border-blue-500"
            initial={{ scale: 1, opacity: 0.8 }}
            animate={{ scale: 2, opacity: 0 }}
            exit={{ opacity: 0 }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeOut"
            }}
          />
        )}
      </AnimatePresence>

      {/* Main hotspot circle */}
      <div className={cn(
        "w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300",
        "bg-blue-600 text-white shadow-lg",
        isHovered && "bg-blue-700 shadow-xl",
        isExpanded && "bg-blue-800"
      )}>
        <ShoppingCart className="w-4 h-4" />
      </div>

      {/* Sale badge */}
      {isOnSale && (
        <div className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
          %
        </div>
      )}

      {/* Quick info tooltip */}
      <AnimatePresence>
        {isHovered && !isExpanded && (
          <motion.div
            initial={{ opacity: 0, y: 10, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 10, scale: 0.9 }}
            className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 z-50"
          >
            <div className="bg-black/80 text-white px-3 py-2 rounded-lg text-sm whitespace-nowrap">
              <div className="font-medium">{product.title}</div>
              <div className="text-xs opacity-90">₦{product.price.toLocaleString()}</div>
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/80" />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );

  // Expanded product card
  const ExpandedCard = () => (
    <AnimatePresence>
      {isExpanded && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 20 }}
          className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-4 z-50"
          style={{ width: '280px' }}
        >
          <Card className="shadow-xl border-2 border-blue-200">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <div className="flex-1 min-w-0">
                  <CardTitle className="text-sm line-clamp-2 mb-1">
                    {product.title}
                  </CardTitle>
                  <CardDescription className="flex items-center gap-1 text-xs">
                    <MapPin className="w-3 h-3" />
                    {product.vendor.name}
                  </CardDescription>
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  className="w-6 h-6 p-0 ml-2"
                  onClick={() => setIsExpanded(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              {/* Product Image */}
              <div className="aspect-video bg-gray-100 rounded-md overflow-hidden mb-3 relative">
                {product.images[0] ? (
                  <img
                    src={product.images[0]}
                    alt={product.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <Package className="w-8 h-8 text-gray-400" />
                  </div>
                )}

                {/* Badges */}
                <div className="absolute top-2 left-2 flex flex-col gap-1">
                  {isOnSale && (
                    <Badge variant="destructive" className="text-xs">
                      -{discountPercentage}%
                    </Badge>
                  )}
                  {isOutOfStock && (
                    <Badge variant="secondary" className="text-xs">
                      Out of Stock
                    </Badge>
                  )}
                </div>
              </div>

              {/* Price */}
              <div className="flex items-center gap-2 mb-3">
                <span className="font-bold text-lg text-primary">
                  ₦{product.price.toLocaleString()}
                </span>
                {isOnSale && (
                  <span className="text-sm text-muted-foreground line-through">
                    ₦{product.compare_at_price!.toLocaleString()}
                  </span>
                )}
              </div>

              {/* Description */}
              {product.description && (
                <p className="text-xs text-muted-foreground line-clamp-2 mb-3">
                  {product.description}
                </p>
              )}

              {/* Stock Status */}
              <div className="flex items-center gap-1 text-xs mb-4">
                <Package className="w-3 h-3" />
                <span className={cn(
                  product.inventory_quantity > 10 
                    ? "text-green-600" 
                    : product.inventory_quantity > 0 
                      ? "text-yellow-600" 
                      : "text-red-600"
                )}>
                  {product.inventory_quantity > 10 
                    ? "In Stock" 
                    : product.inventory_quantity > 0 
                      ? `Only ${product.inventory_quantity} left` 
                      : "Out of Stock"
                  }
                </span>
              </div>

              {/* Tour Context */}
              {tourContext && (
                <div className="bg-blue-50 p-2 rounded-md mb-3">
                  <p className="text-xs text-blue-700">
                    <strong>Found in:</strong> {tourContext.tourTitle}
                  </p>
                </div>
              )}

              {/* Action Buttons */}
              <div className="space-y-2">
                <Button
                  className="w-full"
                  onClick={handleAddToCart}
                  disabled={isUnavailable}
                  variant={isUnavailable ? "secondary" : "default"}
                  size="sm"
                >
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  {isUnavailable ? "Unavailable" : "Add to Cart"}
                </Button>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1"
                    onClick={handleViewProduct}
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    View
                  </Button>

                  {product.vendor.whatsapp_number && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={handleContactVendor}
                    >
                      <MessageCircle className="w-4 h-4 mr-1" />
                      Contact
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );

  return (
    <Html position={position} center>
      <div className="relative">
        <HotspotMarker />
        <ExpandedCard />
      </div>
    </Html>
  );
};

export default ProductHotspot;
