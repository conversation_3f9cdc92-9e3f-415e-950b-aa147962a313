/**
 * Enterprise Features Hook
 * React hook for managing PSV enterprise features and settings
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { toast } from 'sonner';
import type { 
  EnterpriseFeatures, 
  EnterpriseSettings, 
  TourAnalytics, 
  ExportOptions 
} from '@/lib/photosphere/enterpriseFeatures';
import type { PSVInstance } from '@/lib/photosphere/types';

export interface UseEnterpriseFeaturesOptions {
  initialSettings?: Partial<EnterpriseSettings>;
  enableAnalytics?: boolean;
  onSettingsChange?: (settings: EnterpriseSettings) => void;
  onAnalyticsUpdate?: (analytics: TourAnalytics) => void;
}

export interface UseEnterpriseFeaturesReturn {
  // Settings management
  settings: EnterpriseSettings | null;
  updateSettings: (newSettings: Partial<EnterpriseSettings>) => void;
  resetSettings: () => void;
  
  // Analytics
  analytics: TourAnalytics | null;
  refreshAnalytics: () => void;
  
  // Export functionality
  exportTour: (options: ExportOptions) => Promise<void>;
  
  // PSV integration
  attachToPSV: (psvInstance: PSVInstance) => void;
  detachFromPSV: () => void;
  
  // State
  isAttached: boolean;
  isLoading: boolean;
  error: string | null;
  
  // Feature toggles
  toggleAutoRotate: () => void;
  toggleFullscreen: () => void;
  toggleGallery: () => void;
  
  // Advanced controls
  setQuality: (quality: 'low' | 'medium' | 'high' | 'ultra') => void;
  setNavigationVisibility: (visible: boolean) => void;
  enableAccessibilityMode: (enabled: boolean) => void;
}

export function useEnterpriseFeatures(
  options: UseEnterpriseFeaturesOptions = {}
): UseEnterpriseFeaturesReturn {
  const {
    initialSettings,
    enableAnalytics = true,
    onSettingsChange,
    onAnalyticsUpdate
  } = options;

  // State
  const [settings, setSettings] = useState<EnterpriseSettings | null>(null);
  const [analytics, setAnalytics] = useState<TourAnalytics | null>(null);
  const [isAttached, setIsAttached] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Refs
  const enterpriseFeaturesRef = useRef<EnterpriseFeatures | null>(null);
  const psvInstanceRef = useRef<PSVInstance | null>(null);
  const analyticsIntervalRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Update settings
   */
  const updateSettings = useCallback((newSettings: Partial<EnterpriseSettings>) => {
    if (!enterpriseFeaturesRef.current) {
      setError('Enterprise features not initialized');
      return;
    }

    try {
      enterpriseFeaturesRef.current.updateSettings(newSettings);
      const updatedSettings = enterpriseFeaturesRef.current.getSettings();
      setSettings(updatedSettings);
      onSettingsChange?.(updatedSettings);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update settings';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, [onSettingsChange]);

  /**
   * Reset settings to defaults
   */
  const resetSettings = useCallback(() => {
    if (!enterpriseFeaturesRef.current) return;

    try {
      // Reset to initial settings or defaults
      const defaultSettings = initialSettings || {};
      enterpriseFeaturesRef.current.updateSettings(defaultSettings);
      const updatedSettings = enterpriseFeaturesRef.current.getSettings();
      setSettings(updatedSettings);
      onSettingsChange?.(updatedSettings);
      toast.success('Settings reset to defaults');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reset settings';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, [initialSettings, onSettingsChange]);

  /**
   * Refresh analytics data
   */
  const refreshAnalytics = useCallback(() => {
    if (!enterpriseFeaturesRef.current || !enableAnalytics) return;

    try {
      const currentAnalytics = enterpriseFeaturesRef.current.getAnalytics();
      setAnalytics(currentAnalytics);
      onAnalyticsUpdate?.(currentAnalytics);
    } catch (err) {
      console.warn('Failed to refresh analytics:', err);
    }
  }, [enableAnalytics, onAnalyticsUpdate]);

  /**
   * Export tour data
   */
  const exportTour = useCallback(async (exportOptions: ExportOptions) => {
    if (!enterpriseFeaturesRef.current) {
      throw new Error('Enterprise features not initialized');
    }

    try {
      setIsLoading(true);
      const blob = await enterpriseFeaturesRef.current.exportTour(exportOptions);
      
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `tour-export.${exportOptions.format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      toast.success(`Tour exported as ${exportOptions.format.toUpperCase()}`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to export tour';
      setError(errorMessage);
      toast.error(errorMessage);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Attach to PSV instance
   */
  const attachToPSV = useCallback((psvInstance: PSVInstance) => {
    if (!psvInstance.enterpriseFeatures) {
      setError('PSV instance does not have enterprise features enabled');
      return;
    }

    try {
      psvInstanceRef.current = psvInstance;
      enterpriseFeaturesRef.current = psvInstance.enterpriseFeatures;
      
      // Get initial settings and analytics
      const currentSettings = enterpriseFeaturesRef.current.getSettings();
      setSettings(currentSettings);
      
      if (enableAnalytics) {
        refreshAnalytics();
        
        // Set up periodic analytics refresh
        analyticsIntervalRef.current = setInterval(refreshAnalytics, 5000);
      }
      
      setIsAttached(true);
      setError(null);
      
      toast.success('Enterprise features activated');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to attach enterprise features';
      setError(errorMessage);
      toast.error(errorMessage);
    }
  }, [enableAnalytics, refreshAnalytics]);

  /**
   * Detach from PSV instance
   */
  const detachFromPSV = useCallback(() => {
    // Clear analytics interval
    if (analyticsIntervalRef.current) {
      clearInterval(analyticsIntervalRef.current);
      analyticsIntervalRef.current = null;
    }
    
    enterpriseFeaturesRef.current = null;
    psvInstanceRef.current = null;
    setIsAttached(false);
    setSettings(null);
    setAnalytics(null);
    setError(null);
  }, []);

  /**
   * Toggle auto-rotate
   */
  const toggleAutoRotate = useCallback(() => {
    if (!settings) return;
    
    updateSettings({
      autoRotate: {
        ...settings.autoRotate,
        enabled: !settings.autoRotate.enabled
      }
    });
  }, [settings, updateSettings]);

  /**
   * Toggle fullscreen
   */
  const toggleFullscreen = useCallback(() => {
    if (!psvInstanceRef.current?.viewer) return;

    try {
      if (psvInstanceRef.current.viewer.isFullscreenEnabled()) {
        psvInstanceRef.current.viewer.exitFullscreen();
      } else {
        psvInstanceRef.current.viewer.enterFullscreen();
      }
    } catch (err) {
      toast.error('Failed to toggle fullscreen');
    }
  }, []);

  /**
   * Toggle gallery
   */
  const toggleGallery = useCallback(() => {
    if (!psvInstanceRef.current?.plugins.gallery) return;

    try {
      const gallery = psvInstanceRef.current.plugins.gallery;
      if (gallery.isVisible()) {
        gallery.hide();
      } else {
        gallery.show();
      }
    } catch (err) {
      toast.error('Failed to toggle gallery');
    }
  }, []);

  /**
   * Set quality level
   */
  const setQuality = useCallback((quality: 'low' | 'medium' | 'high' | 'ultra') => {
    updateSettings({
      quality: {
        ...settings?.quality,
        resolution: quality
      }
    });
  }, [settings, updateSettings]);

  /**
   * Set navigation visibility
   */
  const setNavigationVisibility = useCallback((visible: boolean) => {
    if (!settings) return;
    
    updateSettings({
      navigation: {
        ...settings.navigation,
        showCompass: visible,
        showZoom: visible,
        showFullscreen: visible,
        showMove: visible
      }
    });
  }, [settings, updateSettings]);

  /**
   * Enable accessibility mode
   */
  const enableAccessibilityMode = useCallback((enabled: boolean) => {
    updateSettings({
      accessibility: {
        ...settings?.accessibility,
        keyboardNavigation: enabled,
        screenReaderSupport: enabled,
        highContrast: enabled
      }
    });
  }, [settings, updateSettings]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      detachFromPSV();
    };
  }, [detachFromPSV]);

  return {
    // Settings management
    settings,
    updateSettings,
    resetSettings,
    
    // Analytics
    analytics,
    refreshAnalytics,
    
    // Export functionality
    exportTour,
    
    // PSV integration
    attachToPSV,
    detachFromPSV,
    
    // State
    isAttached,
    isLoading,
    error,
    
    // Feature toggles
    toggleAutoRotate,
    toggleFullscreen,
    toggleGallery,
    
    // Advanced controls
    setQuality,
    setNavigationVisibility,
    enableAccessibilityMode
  };
}
