/**
 * Redundant Components Cleanup Utility
 * Identifies and manages removal of redundant admin components after streamlining
 */

export interface RedundantComponent {
  path: string;
  name: string;
  replacedBy: string;
  reason: string;
  canRemove: boolean;
  dependencies?: string[];
}

export interface CleanupResult {
  identified: RedundantComponent[];
  removed: string[];
  errors: string[];
  warnings: string[];
}

/**
 * List of redundant components that can be safely removed
 * after the streamlined admin system is implemented
 */
export const REDUNDANT_COMPONENTS: RedundantComponent[] = [
  // Legacy Admin Pages - Now replaced by unified pages
  {
    path: 'src/pages/admin/AdminTours.tsx',
    name: 'AdminTours',
    replacedBy: 'AdminToursUnified',
    reason: 'Replaced by unified tours management with consolidated functionality',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/AdminTourManagement.tsx',
    name: 'AdminTourManagement',
    replacedBy: 'AdminToursUnified',
    reason: 'Tour management functionality consolidated into AdminToursUnified',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/AdminVendors.tsx',
    name: 'AdminVendors',
    replacedBy: 'AdminVendorsUnified',
    reason: 'Replaced by unified vendor management with enhanced features',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/AdminCommerce.tsx',
    name: 'AdminCommerce',
    replacedBy: 'AdminCommerceUnified',
    reason: 'Commerce functionality consolidated with unified service integration',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/AdminContent.tsx',
    name: 'AdminContent',
    replacedBy: 'AdminPages',
    reason: 'Content management consolidated into AdminPages with better organization',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/AdminFeatured.tsx',
    name: 'AdminFeatured',
    replacedBy: 'AdminToursUnified (Featured tab)',
    reason: 'Featured tours management integrated into unified tours system',
    canRemove: true,
    dependencies: ['App.tsx route', 'FeaturedTourManager component']
  },
  {
    path: 'src/pages/admin/AdminDemo.tsx',
    name: 'AdminDemo',
    replacedBy: 'AdminPages (Demo section)',
    reason: 'Demo tours management integrated into pages management',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/AdminTourImport.tsx',
    name: 'AdminTourImport',
    replacedBy: 'AdminToursUnified (Import tab)',
    reason: 'Tour import functionality integrated into unified tours system',
    canRemove: true,
    dependencies: ['App.tsx route', 'ImportToursDialog component']
  },
  {
    path: 'src/pages/admin/AdminMedia.tsx',
    name: 'AdminMedia',
    replacedBy: 'AdminPages (Media section)',
    reason: 'Media management integrated into pages management system',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/AdminAnalytics.tsx',
    name: 'AdminAnalytics',
    replacedBy: 'AdminToursUnified (Analytics tab)',
    reason: 'Analytics integrated into unified tours and commerce systems',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/AdminSettings.tsx',
    name: 'AdminSettings',
    replacedBy: 'AdminIntegrations + AdminTools',
    reason: 'Settings distributed across integrations and tools for better organization',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/AdminDiagnostics.tsx',
    name: 'AdminDiagnostics',
    replacedBy: 'AdminTools (Diagnostics tab)',
    reason: 'Diagnostics functionality integrated into tools system',
    canRemove: true,
    dependencies: ['App.tsx route']
  },

  // Redundant Commerce Components
  {
    path: 'src/pages/admin/WooApiManagement.tsx',
    name: 'WooApiManagement',
    replacedBy: 'AdminIntegrations (WooCommerce tab)',
    reason: 'WooCommerce API management integrated into integrations system',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/WhatsAppCheckoutSettings.tsx',
    name: 'WhatsAppCheckoutSettings',
    replacedBy: 'AdminCommerceUnified (WhatsApp tab)',
    reason: 'WhatsApp settings integrated into unified commerce system',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/CartManagement.tsx',
    name: 'CartManagement',
    replacedBy: 'AdminCommerceUnified (Orders tab)',
    reason: 'Cart management integrated into unified commerce system',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/OverlayPreview.tsx',
    name: 'OverlayPreview',
    replacedBy: 'AdminOverlays (Templates tab)',
    reason: 'Overlay preview integrated into overlay studio system',
    canRemove: true,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/ErrorLogs.tsx',
    name: 'ErrorLogs',
    replacedBy: 'AdminTools (Logs tab)',
    reason: 'Error logs integrated into tools and diagnostics system',
    canRemove: true,
    dependencies: ['App.tsx route']
  },

  // Components that should be kept but may need updates
  {
    path: 'src/pages/admin/AdminProfile.tsx',
    name: 'AdminProfile',
    replacedBy: 'Keep (User profile management)',
    reason: 'Profile management is still needed as standalone functionality',
    canRemove: false,
    dependencies: ['App.tsx route']
  },
  {
    path: 'src/pages/admin/AdminTourEditor.tsx',
    name: 'AdminTourEditor',
    replacedBy: 'Keep (Tour editing interface)',
    reason: 'Tour editor is still needed for individual tour editing',
    canRemove: false,
    dependencies: ['App.tsx route', 'Tour editing components']
  },
  {
    path: 'src/pages/admin/AdminUsers.tsx',
    name: 'AdminUsers',
    replacedBy: 'Keep (Referenced in unified system)',
    reason: 'User management is referenced in AdminVendorsUnified but kept separate',
    canRemove: false,
    dependencies: ['App.tsx route', 'AdminVendorsUnified reference']
  }
];

/**
 * Components that have dependencies and need careful handling
 */
export const COMPONENTS_WITH_DEPENDENCIES = [
  'src/components/admin/FeaturedTourManager.tsx', // Used in AdminPages
  'src/components/admin/ImportToursDialog.tsx', // Used in AdminToursUnified
  'src/components/admin/AdminDemoTour.tsx', // May be used in AdminPages
];

/**
 * Get list of components that can be safely removed
 */
export function getRemovableComponents(): RedundantComponent[] {
  return REDUNDANT_COMPONENTS.filter(component => component.canRemove);
}

/**
 * Get list of components that should be kept
 */
export function getComponentsToKeep(): RedundantComponent[] {
  return REDUNDANT_COMPONENTS.filter(component => !component.canRemove);
}

/**
 * Generate cleanup plan
 */
export function generateCleanupPlan(): {
  toRemove: RedundantComponent[];
  toKeep: RedundantComponent[];
  routesToUpdate: string[];
  dependenciesToCheck: string[];
} {
  const toRemove = getRemovableComponents();
  const toKeep = getComponentsToKeep();
  
  const routesToUpdate = [
    'src/App.tsx', // Remove legacy routes
  ];
  
  const dependenciesToCheck = [
    ...COMPONENTS_WITH_DEPENDENCIES,
    'src/components/admin/AdminSidebar.tsx', // Navigation updates
    'src/components/Navigation.tsx', // Main navigation
  ];

  return {
    toRemove,
    toKeep,
    routesToUpdate,
    dependenciesToCheck
  };
}

/**
 * Validate that unified components have all necessary functionality
 */
export function validateUnifiedComponents(): {
  isValid: boolean;
  missingFeatures: string[];
  recommendations: string[];
} {
  const missingFeatures: string[] = [];
  const recommendations: string[] = [];

  // Check if unified components exist
  const unifiedComponents = [
    'src/pages/admin/AdminPages.tsx',
    'src/pages/admin/AdminToursUnified.tsx',
    'src/pages/admin/AdminOverlays.tsx',
    'src/pages/admin/AdminVendorsUnified.tsx',
    'src/pages/admin/AdminCommerceUnified.tsx',
    'src/pages/admin/AdminIntegrations.tsx',
    'src/pages/admin/AdminTools.tsx'
  ];

  // Add validation logic here
  // For now, assume they exist and are functional

  recommendations.push(
    'Test all unified components thoroughly before removing legacy components',
    'Ensure all routes are properly updated in App.tsx',
    'Verify that all functionality from legacy components is available in unified components',
    'Update any external links or bookmarks that reference legacy routes'
  );

  return {
    isValid: missingFeatures.length === 0,
    missingFeatures,
    recommendations
  };
}

/**
 * Generate removal script commands
 */
export function generateRemovalCommands(): string[] {
  const removableComponents = getRemovableComponents();
  
  return removableComponents.map(component => 
    `// Remove ${component.name}: ${component.reason}\n// rm ${component.path}`
  );
}

export default {
  REDUNDANT_COMPONENTS,
  getRemovableComponents,
  getComponentsToKeep,
  generateCleanupPlan,
  validateUnifiedComponents,
  generateRemovalCommands
};
