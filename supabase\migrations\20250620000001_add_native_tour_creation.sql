-- Add native tour creation support to tours table
-- Migration: 20250620000001_add_native_tour_creation.sql

-- Add new columns for native tour creation
ALTER TABLE public.tours 
ADD COLUMN IF NOT EXISTS creation_method TEXT CHECK (creation_method IN ('upload', 'embed', 'native', 'images')) DEFAULT 'upload',
ADD COLUMN IF NOT EXISTS tour_platform TEXT CHECK (tour_platform IN ('commonninja', 'custom')),
ADD COLUMN IF NOT EXISTS platform_tour_id TEXT,
ADD COLUMN IF NOT EXISTS platform_specific JSONB,
ADD COLUMN IF NOT EXISTS tour_config JSONB;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tours_creation_method ON public.tours(creation_method);
CREATE INDEX IF NOT EXISTS idx_tours_platform ON public.tours(tour_platform);
CREATE INDEX IF NOT EXISTS idx_tours_platform_tour_id ON public.tours(platform_tour_id);

-- Add platform_scene_id to scenes table for native tour integration
ALTER TABLE public.scenes 
ADD COLUMN IF NOT EXISTS platform_scene_id TEXT;

CREATE INDEX IF NOT EXISTS idx_scenes_platform_scene_id ON public.scenes(platform_scene_id);

-- Add platform_hotspot_id to hotspots table for native tour integration
ALTER TABLE public.hotspots 
ADD COLUMN IF NOT EXISTS platform_hotspot_id TEXT;

CREATE INDEX IF NOT EXISTS idx_hotspots_platform_hotspot_id ON public.hotspots(platform_hotspot_id);

-- Update RLS policies to include new fields
-- Tours policy already exists, just need to ensure it covers new fields

-- Add comment for documentation
COMMENT ON COLUMN public.tours.creation_method IS 'Method used to create the tour: upload (file upload), embed (external platform), native (platform API)';
COMMENT ON COLUMN public.tours.tour_platform IS 'Platform used for native tour creation: commonninja, custom';
COMMENT ON COLUMN public.tours.platform_tour_id IS 'ID of the tour on the external platform';
COMMENT ON COLUMN public.tours.platform_specific IS 'Platform-specific metadata and configuration';
COMMENT ON COLUMN public.tours.tour_config IS 'Tour configuration including hotspot settings, e-commerce options, etc.';
COMMENT ON COLUMN public.scenes.platform_scene_id IS 'ID of the scene on the external platform';
COMMENT ON COLUMN public.hotspots.platform_hotspot_id IS 'ID of the hotspot on the external platform';
