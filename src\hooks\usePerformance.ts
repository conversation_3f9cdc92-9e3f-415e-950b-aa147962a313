/**
 * Advanced Performance Hooks
 * Lightning-fast loading utilities for React components
 */

import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { PERFORMANCE_CONFIG, getDevicePerformanceProfile } from '@/config/performanceConfig';

/**
 * Optimized Intersection Observer Hook
 */
export const useIntersectionObserver = (
  options?: IntersectionObserverInit
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      {
        threshold: PERFORMANCE_CONFIG.INTERSECTION_THRESHOLD,
        rootMargin: PERFORMANCE_CONFIG.INTERSECTION_ROOT_MARGIN,
        ...options
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [hasIntersected, options]);

  return { elementRef, isIntersecting, hasIntersected };
};

/**
 * Optimized Debounce Hook
 */
export const useDebounce = <T>(value: T, delay: number = PERFORMANCE_CONFIG.HOVER_DEBOUNCE): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

/**
 * Optimized Throttle Hook
 */
export const useThrottle = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number = PERFORMANCE_CONFIG.SCROLL_THROTTLE
): T => {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args: Parameters<T>) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
};

/**
 * Lazy Loading Hook for Components
 */
export const useLazyLoad = (shouldLoad: boolean = false) => {
  const [isLoaded, setIsLoaded] = useState(shouldLoad);
  const [isLoading, setIsLoading] = useState(false);

  const load = useCallback(() => {
    if (!isLoaded && !isLoading) {
      setIsLoading(true);
      // Simulate async loading
      requestAnimationFrame(() => {
        setIsLoaded(true);
        setIsLoading(false);
      });
    }
  }, [isLoaded, isLoading]);

  return { isLoaded, isLoading, load };
};

/**
 * Performance Monitoring Hook with Edge Cache Integration
 */
export const usePerformanceMonitor = (name: string) => {
  const startTime = useRef<number>();
  const [metrics, setMetrics] = useState<{
    duration: number;
    cacheHit: boolean;
    networkTime?: number;
    cacheTime?: number;
  } | null>(null);

  const start = useCallback(() => {
    startTime.current = performance.now();
    performance.mark(`${name}-start`);
  }, [name]);

  const end = useCallback((cacheHit = false, networkTime?: number) => {
    if (startTime.current) {
      const duration = performance.now() - startTime.current;
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);

      const newMetrics = {
        duration,
        cacheHit,
        networkTime,
        cacheTime: cacheHit ? duration : undefined
      };

      setMetrics(newMetrics);

      // Log performance warnings
      if (duration > 100) {
        console.warn(`Slow operation: ${name} took ${duration.toFixed(2)}ms`, {
          cacheHit,
          networkTime
        });
      }

      // Log cache performance
      if (cacheHit) {
        console.log(`Cache hit for ${name}: ${duration.toFixed(2)}ms`);
      } else if (networkTime) {
        console.log(`Network request for ${name}: ${networkTime.toFixed(2)}ms`);
      }

      return duration;
    }
    return 0;
  }, [name]);

  return { start, end, metrics };
};

/**
 * Enhanced Cache Performance Monitor
 */
export const useCachePerformance = () => {
  const [cacheStats, setCacheStats] = useState({
    hits: 0,
    misses: 0,
    totalRequests: 0,
    averageHitTime: 0,
    averageMissTime: 0
  });

  const recordCacheHit = useCallback((duration: number) => {
    setCacheStats(prev => ({
      ...prev,
      hits: prev.hits + 1,
      totalRequests: prev.totalRequests + 1,
      averageHitTime: (prev.averageHitTime * prev.hits + duration) / (prev.hits + 1)
    }));
  }, []);

  const recordCacheMiss = useCallback((duration: number) => {
    setCacheStats(prev => ({
      ...prev,
      misses: prev.misses + 1,
      totalRequests: prev.totalRequests + 1,
      averageMissTime: (prev.averageMissTime * prev.misses + duration) / (prev.misses + 1)
    }));
  }, []);

  const getCacheHitRatio = useCallback(() => {
    return cacheStats.totalRequests > 0 ? cacheStats.hits / cacheStats.totalRequests : 0;
  }, [cacheStats]);

  return {
    cacheStats,
    recordCacheHit,
    recordCacheMiss,
    getCacheHitRatio
  };
};

/**
 * Device Performance Hook
 */
export const useDevicePerformance = () => {
  const [profile, setProfile] = useState(() => getDevicePerformanceProfile());

  useEffect(() => {
    // Re-evaluate performance profile on memory pressure
    const handleMemoryPressure = () => {
      setProfile(getDevicePerformanceProfile());
    };

    // Listen for memory pressure events (if supported)
    if ('memory' in performance) {
      const checkMemory = () => {
        const memory = (performance as any).memory;
        if (memory.usedJSHeapSize / memory.totalJSHeapSize > 0.9) {
          handleMemoryPressure();
        }
      };

      const interval = setInterval(checkMemory, 30000); // Check every 30s
      return () => clearInterval(interval);
    }
  }, []);

  return profile;
};

/**
 * Optimized Image Loading Hook
 */
export const useOptimizedImage = (src: string, options?: {
  quality?: number;
  width?: number;
  format?: string;
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const optimizedSrc = useMemo(() => {
    if (!src) return '';

    try {
      const url = new URL(src);
      
      // Add optimization parameters
      if (options?.quality) {
        url.searchParams.set('q', options.quality.toString());
      }
      if (options?.width) {
        url.searchParams.set('w', options.width.toString());
      }
      if (options?.format) {
        url.searchParams.set('fm', options.format);
      }
      
      // Add auto format and optimization
      url.searchParams.set('auto', 'format,compress');
      
      return url.toString();
    } catch {
      return src;
    }
  }, [src, options]);

  useEffect(() => {
    if (!optimizedSrc) return;

    setIsLoading(true);
    setError(null);

    const img = new Image();
    
    img.onload = () => {
      setIsLoaded(true);
      setIsLoading(false);
    };
    
    img.onerror = () => {
      setError('Failed to load image');
      setIsLoading(false);
    };

    img.src = optimizedSrc;

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [optimizedSrc]);

  return { src: optimizedSrc, isLoaded, isLoading, error };
};

/**
 * Progressive Loading Hook
 */
export const useProgressiveLoading = <T>(
  items: T[],
  chunkSize: number = PERFORMANCE_CONFIG.PROGRESSIVE_LOADING.chunkSize
) => {
  const [loadedCount, setLoadedCount] = useState(chunkSize);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const visibleItems = useMemo(() => {
    return items.slice(0, loadedCount);
  }, [items, loadedCount]);

  const hasMore = loadedCount < items.length;

  const loadMore = useCallback(() => {
    if (hasMore && !isLoadingMore) {
      setIsLoadingMore(true);
      
      // Simulate async loading
      setTimeout(() => {
        setLoadedCount(prev => Math.min(prev + chunkSize, items.length));
        setIsLoadingMore(false);
      }, 100);
    }
  }, [hasMore, isLoadingMore, chunkSize, items.length]);

  // Auto-load more when near the end
  const shouldLoadMore = useCallback(() => {
    const remaining = items.length - loadedCount;
    return remaining > 0 && remaining <= PERFORMANCE_CONFIG.PROGRESSIVE_LOADING.loadMoreThreshold;
  }, [items.length, loadedCount]);

  return {
    visibleItems,
    hasMore,
    isLoadingMore,
    loadMore,
    shouldLoadMore: shouldLoadMore()
  };
};

/**
 * Memory Cleanup Hook
 */
export const useMemoryCleanup = () => {
  useEffect(() => {
    const cleanup = () => {
      // Force garbage collection if available
      if ('gc' in window && typeof (window as any).gc === 'function') {
        (window as any).gc();
      }
      
      // Clear unused caches
      if ('caches' in window) {
        caches.keys().then(names => {
          names.forEach(name => {
            if (name.includes('old') || name.includes('temp')) {
              caches.delete(name);
            }
          });
        });
      }
    };

    const interval = setInterval(cleanup, PERFORMANCE_CONFIG.CLEANUP_INTERVAL);
    
    // Cleanup on page unload
    window.addEventListener('beforeunload', cleanup);

    return () => {
      clearInterval(interval);
      window.removeEventListener('beforeunload', cleanup);
    };
  }, []);
};
