/**
 * OrderTracking Component
 * Real-time order status tracking with timeline visualization
 * Mobile-first responsive design with WhatsApp integration
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Package, 
  Clock, 
  CheckCircle, 
  Truck, 
  MapPin, 
  Phone, 
  MessageCircle, 
  Calendar,
  User,
  Search,
  RefreshCw,
  ExternalLink,
  Copy,
  Share
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import type { Order } from './OrderSummary';
import { commerceService } from '@/services/commerce/CommerceService';

interface OrderTrackingProps {
  orderId?: string;
  orderNumber?: string;
  className?: string;
}

interface TrackingEvent {
  id: string;
  status: string;
  title: string;
  description: string;
  timestamp: string;
  location?: string;
  vendor?: {
    id: string;
    name: string;
    whatsapp_number?: string;
  };
}

const OrderTracking = ({
  orderId,
  orderNumber: initialOrderNumber,
  className
}: OrderTrackingProps) => {
  const [order, setOrder] = useState<Order | null>(null);
  const [orderNumber, setOrderNumber] = useState(initialOrderNumber || '');
  const [isLoading, setIsLoading] = useState(false);
  const [trackingEvents, setTrackingEvents] = useState<TrackingEvent[]>([]);

  useEffect(() => {
    if (orderId) {
      loadOrderById(orderId);
    } else if (initialOrderNumber) {
      searchOrder(initialOrderNumber);
    }
  }, [orderId, initialOrderNumber]);

  const loadOrderById = async (id: string) => {
    try {
      setIsLoading(true);
      const orderData = await commerceService.getOrder(id);
      if (orderData) {
        setOrder(orderData);
        setOrderNumber(orderData.order_number);
        generateTrackingEvents(orderData);
      }
    } catch (error) {
      console.error('Error loading order:', error);
      toast.error('Failed to load order details');
    } finally {
      setIsLoading(false);
    }
  };

  const searchOrder = async (number: string) => {
    if (!number.trim()) {
      toast.error('Please enter an order number');
      return;
    }

    try {
      setIsLoading(true);
      // In a real implementation, you'd have a search endpoint
      const orders = await commerceService.getOrders({ limit: 100 });
      const foundOrder = orders.find(o => o.order_number === number);
      
      if (foundOrder) {
        setOrder(foundOrder);
        generateTrackingEvents(foundOrder);
        toast.success('Order found!');
      } else {
        toast.error('Order not found. Please check your order number.');
        setOrder(null);
      }
    } catch (error) {
      console.error('Error searching order:', error);
      toast.error('Failed to search for order');
    } finally {
      setIsLoading(false);
    }
  };

  const generateTrackingEvents = (orderData: Order) => {
    const events: TrackingEvent[] = [];
    const createdAt = new Date(orderData.created_at);

    // Order placed
    events.push({
      id: '1',
      status: 'placed',
      title: 'Order Placed',
      description: 'Your order has been successfully placed and is being processed.',
      timestamp: orderData.created_at,
      location: 'VirtualRealTour Platform'
    });

    // Add status-based events
    if (['confirmed', 'processing', 'shipped', 'delivered'].includes(orderData.status)) {
      events.push({
        id: '2',
        status: 'confirmed',
        title: 'Order Confirmed',
        description: 'Vendors have confirmed your order and are preparing items.',
        timestamp: new Date(createdAt.getTime() + 30 * 60 * 1000).toISOString(), // +30 minutes
        location: 'Vendor Locations'
      });
    }

    if (['processing', 'shipped', 'delivered'].includes(orderData.status)) {
      events.push({
        id: '3',
        status: 'processing',
        title: 'Order Processing',
        description: 'Your items are being prepared and packaged for shipment.',
        timestamp: new Date(createdAt.getTime() + 2 * 60 * 60 * 1000).toISOString(), // +2 hours
        location: 'Vendor Warehouses'
      });
    }

    if (['shipped', 'delivered'].includes(orderData.status)) {
      events.push({
        id: '4',
        status: 'shipped',
        title: 'Order Shipped',
        description: 'Your order is on its way! You should receive it soon.',
        timestamp: new Date(createdAt.getTime() + 24 * 60 * 60 * 1000).toISOString(), // +1 day
        location: 'In Transit'
      });
    }

    if (orderData.status === 'delivered') {
      events.push({
        id: '5',
        status: 'delivered',
        title: 'Order Delivered',
        description: 'Your order has been successfully delivered. Enjoy your purchase!',
        timestamp: new Date(createdAt.getTime() + 3 * 24 * 60 * 60 * 1000).toISOString(), // +3 days
        location: orderData.customer_address || 'Delivery Address'
      });
    }

    setTrackingEvents(events);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'processing': return 'bg-purple-100 text-purple-800';
      case 'shipped': return 'bg-indigo-100 text-indigo-800';
      case 'delivered': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string, isCompleted: boolean) => {
    if (isCompleted) {
      return <CheckCircle className="w-5 h-5 text-green-600" />;
    }

    switch (status) {
      case 'placed': return <Package className="w-5 h-5 text-blue-600" />;
      case 'confirmed': return <CheckCircle className="w-5 h-5 text-blue-600" />;
      case 'processing': return <Package className="w-5 h-5 text-purple-600" />;
      case 'shipped': return <Truck className="w-5 h-5 text-indigo-600" />;
      case 'delivered': return <CheckCircle className="w-5 h-5 text-green-600" />;
      default: return <Clock className="w-5 h-5 text-gray-600" />;
    }
  };

  const copyOrderNumber = () => {
    if (order) {
      navigator.clipboard.writeText(order.order_number);
      toast.success('Order number copied to clipboard');
    }
  };

  const shareOrder = () => {
    if (order) {
      const shareText = `Track my VirtualRealTour order: ${order.order_number}`;
      if (navigator.share) {
        navigator.share({
          title: 'Order Tracking',
          text: shareText,
          url: window.location.href
        });
      } else {
        navigator.clipboard.writeText(`${shareText}\n${window.location.href}`);
        toast.success('Order details copied to clipboard');
      }
    }
  };

  const contactVendor = (vendor: any) => {
    if (vendor.whatsapp_number) {
      const message = `Hi ${vendor.name}! I'm inquiring about my order ${order?.order_number}. Can you provide an update?`;
      const whatsappUrl = `https://wa.me/${vendor.whatsapp_number.replace(/[^\d]/g, '')}?text=${encodeURIComponent(message)}`;
      window.open(whatsappUrl, '_blank');
    }
  };

  const getCurrentStatusIndex = () => {
    if (!order) return -1;
    const statusOrder = ['placed', 'confirmed', 'processing', 'shipped', 'delivered'];
    const currentStatusMap: Record<string, string> = {
      'pending': 'placed',
      'confirmed': 'confirmed',
      'processing': 'processing',
      'shipped': 'shipped',
      'delivered': 'delivered'
    };
    return statusOrder.indexOf(currentStatusMap[order.status] || 'placed');
  };

  return (
    <div className={cn("max-w-4xl mx-auto space-y-6", className)}>
      {/* Search Section */}
      {!order && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="w-5 h-5" />
              Track Your Order
            </CardTitle>
            <CardDescription>
              Enter your order number to track your delivery status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-3">
              <div className="flex-1">
                <Label htmlFor="order-number" className="sr-only">Order Number</Label>
                <Input
                  id="order-number"
                  placeholder="Enter order number (e.g., VRT-20240101-1234)"
                  value={orderNumber}
                  onChange={(e) => setOrderNumber(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && searchOrder(orderNumber)}
                />
              </div>
              <Button 
                onClick={() => searchOrder(orderNumber)}
                disabled={isLoading || !orderNumber.trim()}
              >
                {isLoading ? <RefreshCw className="w-4 h-4 animate-spin" /> : 'Track'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Order Details */}
      {order && (
        <>
          {/* Order Header */}
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                  <CardTitle className="flex items-center gap-3">
                    Order {order.order_number}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={copyOrderNumber}
                      className="p-1 h-auto"
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </CardTitle>
                  <CardDescription>
                    Placed on {new Date(order.created_at).toLocaleDateString()} • 
                    {order.items.length} item{order.items.length !== 1 ? 's' : ''}
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getStatusColor(order.status)} variant="outline">
                    {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                  </Badge>
                  <Button variant="outline" size="sm" onClick={shareOrder}>
                    <Share className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Tracking Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Order Timeline</CardTitle>
              <CardDescription>
                Track your order progress from placement to delivery
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {trackingEvents.map((event, index) => {
                  const currentIndex = getCurrentStatusIndex();
                  const isCompleted = index <= currentIndex;
                  const isCurrent = index === currentIndex;
                  
                  return (
                    <motion.div
                      key={event.id}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex gap-4"
                    >
                      {/* Timeline Icon */}
                      <div className="flex flex-col items-center">
                        <div className={cn(
                          "w-10 h-10 rounded-full flex items-center justify-center border-2",
                          isCompleted 
                            ? "bg-green-100 border-green-500" 
                            : isCurrent
                              ? "bg-blue-100 border-blue-500"
                              : "bg-gray-100 border-gray-300"
                        )}>
                          {getStatusIcon(event.status, isCompleted)}
                        </div>
                        {index < trackingEvents.length - 1 && (
                          <div className={cn(
                            "w-0.5 h-12 mt-2",
                            isCompleted ? "bg-green-500" : "bg-gray-300"
                          )} />
                        )}
                      </div>

                      {/* Event Details */}
                      <div className="flex-1 pb-6">
                        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                          <h3 className={cn(
                            "font-semibold",
                            isCompleted ? "text-green-800" : isCurrent ? "text-blue-800" : "text-gray-600"
                          )}>
                            {event.title}
                          </h3>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Calendar className="w-4 h-4" />
                            {new Date(event.timestamp).toLocaleString()}
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {event.description}
                        </p>
                        {event.location && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground mt-2">
                            <MapPin className="w-3 h-3" />
                            {event.location}
                          </div>
                        )}
                      </div>
                    </motion.div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Order Items */}
          <Card>
            <CardHeader>
              <CardTitle>Order Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.items.map((item) => (
                  <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                    <div className="w-16 h-16 bg-gray-100 rounded-md overflow-hidden">
                      {item.product.images[0] ? (
                        <img
                          src={item.product.images[0]}
                          alt={item.product.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Package className="w-6 h-6 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-medium">{item.product.title}</h4>
                      <p className="text-sm text-muted-foreground">
                        Quantity: {item.quantity} • ₦{item.price.toLocaleString()} each
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-sm text-muted-foreground">Sold by:</span>
                        <span className="text-sm font-medium">{item.vendor.name}</span>
                        {item.vendor.whatsapp_number && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => contactVendor(item.vendor)}
                          >
                            <MessageCircle className="w-3 h-3 mr-1" />
                            Contact
                          </Button>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">
                        ₦{(item.price * item.quantity).toLocaleString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle>Delivery Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium mb-2">Customer Details</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-muted-foreground" />
                      {order.customer_name}
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-muted-foreground" />
                      {order.customer_phone}
                    </div>
                    {order.customer_email && (
                      <div className="flex items-center gap-2">
                        <MessageCircle className="w-4 h-4 text-muted-foreground" />
                        {order.customer_email}
                      </div>
                    )}
                  </div>
                </div>
                {order.customer_address && (
                  <div>
                    <h4 className="font-medium mb-2">Delivery Address</h4>
                    <div className="flex items-start gap-2 text-sm">
                      <MapPin className="w-4 h-4 text-muted-foreground mt-0.5" />
                      <span>{order.customer_address}</span>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-wrap gap-3">
                <Button variant="outline">
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Contact Support
                </Button>
                <Button variant="outline">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  View Receipt
                </Button>
                <Button variant="outline" onClick={() => searchOrder('')}>
                  <Search className="w-4 h-4 mr-2" />
                  Track Another Order
                </Button>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default OrderTracking;
