/**
 * Photo Sphere Viewer Utilities
 * Production-ready utilities for PSV v5 integration
 */

import { Viewer } from '@photo-sphere-viewer/core';
import { VirtualTourPlugin } from '@photo-sphere-viewer/virtual-tour-plugin';
import { MarkersPlugin } from '@photo-sphere-viewer/markers-plugin';
import { AutorotatePlugin } from '@photo-sphere-viewer/autorotate-plugin';
import { GalleryPlugin } from '@photo-sphere-viewer/gallery-plugin';
import { SettingsPlugin } from '@photo-sphere-viewer/settings-plugin';

import type {
  PSVConfig,
  PSVInstance,
  PSVError,
  TourScene,
  VirtualTourConfig,
  AssetValidation,
  PSVPerformanceMetrics
} from './types';

/**
 * PSV Error Boundary with retry mechanism
 */
export class PSVErrorBoundary {
  private retryCount = 0;
  private maxRetries = 3;
  private retryDelay = 1000;

  async initializeWithRetry(config: PSVConfig): Promise<Viewer> {
    while (this.retryCount < this.maxRetries) {
      try {
        const viewer = new Viewer(config);
        
        // Wait for ready event
        await new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Viewer initialization timeout'));
          }, 10000);

          viewer.addEventListener('ready', () => {
            clearTimeout(timeout);
            this.retryCount = 0; // Reset on success
            resolve();
          }, { once: true });

          viewer.addEventListener('panorama-error', (error) => {
            clearTimeout(timeout);
            reject(error);
          }, { once: true });
        });

        return viewer;
      } catch (error) {
        this.retryCount++;
        console.warn(`PSV initialization attempt ${this.retryCount} failed:`, error);
        
        if (this.retryCount >= this.maxRetries) {
          throw new PSVError(
            'INIT_FAILED',
            `Failed to initialize Photo Sphere Viewer after ${this.maxRetries} attempts`,
            error,
            { retryCount: this.retryCount, config }
          );
        }
        
        // Exponential backoff
        await new Promise(resolve => 
          setTimeout(resolve, this.retryDelay * Math.pow(2, this.retryCount - 1))
        );
      }
    }
    
    throw new Error('Unexpected error in retry loop');
  }
}

/**
 * Validate container element and ensure proper dimensions
 */
export function validateContainer(container: HTMLElement | string): HTMLElement {
  const element = typeof container === 'string' 
    ? document.querySelector(container) as HTMLElement
    : container;

  if (!element) {
    throw new PSVError(
      'INVALID_CONTAINER',
      'Container element not found',
      null,
      { container }
    );
  }

  // Ensure container has dimensions - Critical for PSV initialization
  const computedStyle = window.getComputedStyle(element);
  const hasWidth = element.offsetWidth > 0 || computedStyle.width !== 'auto';
  const hasHeight = element.offsetHeight > 0 || computedStyle.height !== 'auto';

  if (!hasWidth || !hasHeight) {
    // Set minimum dimensions if not specified
    if (!hasWidth && !element.style.width) {
      element.style.width = '100%';
    }
    if (!hasHeight && !element.style.height) {
      element.style.height = '400px';
    }

    // Force layout recalculation
    element.offsetHeight;

    // Verify dimensions after setting
    if (element.offsetWidth === 0 || element.offsetHeight === 0) {
      throw new PSVError(
        'INVALID_CONTAINER',
        'Container must have non-zero dimensions for PSV initialization',
        null,
        {
          container,
          offsetWidth: element.offsetWidth,
          offsetHeight: element.offsetHeight,
          computedWidth: computedStyle.width,
          computedHeight: computedStyle.height
        }
      );
    }
  }

  return element;
}

/**
 * Validate panorama asset
 */
export async function validatePanorama(imageUrl: string): Promise<AssetValidation> {
  return new Promise((resolve) => {
    const img = new Image();
    const errors: string[] = [];
    const warnings: string[] = [];

    img.onload = () => {
      const aspectRatio = img.width / img.height;
      
      // Check if it's a valid panorama (2:1 aspect ratio for equirectangular)
      if (aspectRatio < 1.5) {
        errors.push('Invalid panorama aspect ratio. Expected 2:1 for equirectangular images.');
      }
      
      // Check minimum resolution
      if (img.width < 2048) {
        warnings.push('Low resolution panorama. Recommended minimum width: 2048px');
      }
      
      // Check maximum resolution for performance
      if (img.width > 8192) {
        warnings.push('Very high resolution panorama may impact performance');
      }

      resolve({
        isValid: errors.length === 0,
        errors,
        warnings,
        metadata: {
          width: img.width,
          height: img.height,
          aspectRatio,
          fileSize: 0, // Would need server-side info
          format: imageUrl.split('.').pop()?.toLowerCase() || 'unknown'
        }
      });
    };

    img.onerror = () => {
      resolve({
        isValid: false,
        errors: ['Failed to load panorama image'],
        warnings: []
      });
    };

    // Set CORS if needed
    img.crossOrigin = 'anonymous';
    img.src = imageUrl;
  });
}

/**
 * Create optimized PSV configuration
 */
export function createPSVConfig(
  container: HTMLElement | string,
  panorama: string,
  options: Partial<PSVConfig> = {}
): PSVConfig {
  const validatedContainer = validateContainer(container);

  return {
    container: validatedContainer,
    panorama,
    
    // Performance optimizations
    useXmpData: false,
    moveInertia: true,
    mousemove: true,
    mousewheelCtrlKey: false,
    touchmoveTwoFingers: true,
    
    // Default view settings
    defaultZoomLvl: 50,
    minFov: 30,
    maxFov: 90,
    
    // Loading configuration
    loadingImg: '/images/loading-sphere.svg',
    loadingTxt: 'Loading virtual tour...',
    
    // Navigation bar
    navbar: [
      'autorotate',
      'zoom',
      'move',
      'download',
      'fullscreen'
    ],
    
    // Transition settings
    transition: {
      duration: 1500,
      loader: true,
      blur: false
    },
    
    // Override with custom options
    ...options
  };
}

/**
 * Memory management utilities
 */
export class PSVMemoryManager {
  private static instances = new Set<PSVInstance>();
  private static cleanupCallbacks = new Map<string, () => void>();
  private static maxInstances = 3;
  private static memoryThreshold = 100 * 1024 * 1024; // 100MB
  private static monitoringInterval: NodeJS.Timeout | null = null;
  private static isMonitoring = false;

  static register(instance: PSVInstance): void {
    // Clean up oldest instances if we exceed the limit
    if (this.instances.size >= this.maxInstances) {
      const oldest = this.instances.values().next().value;
      if (oldest) {
        this.cleanup(oldest);
      }
    }

    this.instances.add(instance);
    this.startMonitoring();
  }

  static unregister(instance: PSVInstance): void {
    this.instances.delete(instance);
    if (this.instances.size === 0) {
      this.stopMonitoring();
    }
  }

  static cleanup(instance: PSVInstance): void {
    try {
      // Cleanup marker service
      if (instance.markerService) {
        instance.markerService.destroy();
      }

      // Cleanup enterprise features
      if (instance.enterpriseFeatures) {
        instance.enterpriseFeatures.destroy();
      }

      // Remove event listeners
      if (instance.viewer) {
        instance.viewer.removeAllListeners();

        // Stop autorotate if active
        if (instance.plugins.autorotate) {
          instance.plugins.autorotate.stop();
        }

        // Clear cache if available
        if ('cache' in instance.viewer && instance.viewer.cache) {
          instance.viewer.cache.clear();
        }

        // Destroy viewer
        instance.viewer.destroy();
      }

      // Cleanup plugins
      Object.values(instance.plugins).forEach(plugin => {
        if (plugin && typeof plugin.destroy === 'function') {
          plugin.destroy();
        }
      });

      console.log('PSV instance cleaned up successfully');
    } catch (error) {
      console.warn('Error during PSV cleanup:', error);
    } finally {
      this.unregister(instance);
    }
  }

  static cleanupAll(): void {
    console.log(`Cleaning up ${this.instances.size} PSV instances`);

    for (const instance of this.instances) {
      this.cleanup(instance);
    }

    this.cleanupCallbacks.clear();
    this.stopMonitoring();

    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }
  }

  static addCleanupCallback(id: string, callback: () => void): void {
    this.cleanupCallbacks.set(id, callback);
  }

  static removeCleanupCallback(id: string): void {
    this.cleanupCallbacks.delete(id);
  }

  /**
   * Start memory monitoring
   */
  private static startMonitoring(): void {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(() => {
      this.checkMemoryUsage();
    }, 10000); // Check every 10 seconds
  }

  /**
   * Stop memory monitoring
   */
  private static stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
  }

  /**
   * Check memory usage and cleanup if necessary
   */
  private static checkMemoryUsage(): void {
    if (!('memory' in performance)) return;

    const memory = (performance as any).memory;
    const usedMemory = memory.usedJSHeapSize;
    const memoryLimit = memory.jsHeapSizeLimit;

    // If memory usage is above 80% of limit, trigger cleanup
    if (usedMemory > memoryLimit * 0.8) {
      console.warn('High memory usage detected, triggering cleanup');
      this.performEmergencyCleanup();
    }

    // If memory usage exceeds our threshold, warn
    if (usedMemory > this.memoryThreshold) {
      console.warn(`Memory usage (${Math.round(usedMemory / 1024 / 1024)}MB) exceeds threshold`);
    }
  }

  /**
   * Perform emergency cleanup
   */
  private static performEmergencyCleanup(): void {
    // Run all cleanup callbacks
    this.cleanupCallbacks.forEach(callback => {
      try {
        callback();
      } catch (error) {
        console.warn('Cleanup callback error:', error);
      }
    });

    // Clear image caches
    this.clearImageCaches();

    // Force garbage collection if available
    if (window.gc) {
      window.gc();
    }
  }

  /**
   * Clear image caches
   */
  private static clearImageCaches(): void {
    // Clear any cached images in PSV instances
    this.instances.forEach(instance => {
      try {
        if (instance.viewer && typeof instance.viewer.clearCache === 'function') {
          instance.viewer.clearCache();
        }
      } catch (error) {
        console.warn('Error clearing PSV cache:', error);
      }
    });
  }

  /**
   * Get memory statistics
   */
  static getMemoryStats(): {
    instances: number;
    memoryUsage?: number;
    memoryLimit?: number;
    memoryPercentage?: number;
  } {
    const stats = {
      instances: this.instances.size
    };

    if ('memory' in performance) {
      const memory = (performance as any).memory;
      return {
        ...stats,
        memoryUsage: memory.usedJSHeapSize,
        memoryLimit: memory.jsHeapSizeLimit,
        memoryPercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
      };
    }

    return stats;
  }

  /**
   * Set memory threshold
   */
  static setMemoryThreshold(threshold: number): void {
    this.memoryThreshold = threshold;
  }
}

/**
 * WebGL context loss handling
 */
export function setupWebGLErrorHandling(viewer: Viewer): void {
  const canvas = viewer.container.querySelector('canvas');
  if (!canvas) return;

  canvas.addEventListener('webglcontextlost', (event) => {
    event.preventDefault();
    console.warn('WebGL context lost, attempting recovery...');
  });

  canvas.addEventListener('webglcontextrestored', () => {
    console.log('WebGL context restored, refreshing viewer...');
    try {
      viewer.refresh();
    } catch (error) {
      console.error('Failed to refresh viewer after WebGL context restore:', error);
    }
  });
}

/**
 * Performance monitoring
 */
export class PSVPerformanceMonitor {
  private startTime: number = 0;
  private metrics: Partial<PSVPerformanceMetrics> = {};

  startInit(): void {
    this.startTime = performance.now();
  }

  recordInit(): void {
    this.metrics.initTime = performance.now() - this.startTime;
  }

  recordLoad(): void {
    this.metrics.loadTime = performance.now() - this.startTime;
  }

  recordError(): void {
    this.metrics.errorCount = (this.metrics.errorCount || 0) + 1;
  }

  getMetrics(): Partial<PSVPerformanceMetrics> {
    return { ...this.metrics };
  }

  logMetrics(): void {
    console.log('PSV Performance Metrics:', this.metrics);
  }
}

/**
 * Progressive loading strategy
 */
export function createProgressiveLoader(scenes: TourScene[]) {
  const preloadQueue: string[] = [];
  const loadedImages = new Set<string>();

  const preloadImage = (url: string): Promise<void> => {
    if (loadedImages.has(url)) {
      return Promise.resolve();
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        loadedImages.add(url);
        resolve();
      };
      img.onerror = reject;
      img.crossOrigin = 'anonymous';
      img.src = url;
    });
  };

  return {
    preloadScene: async (sceneId: string) => {
      const scene = scenes.find(s => s.id === sceneId);
      if (scene && !loadedImages.has(scene.panorama)) {
        await preloadImage(scene.panorama);
      }
    },
    
    preloadConnectedScenes: async (currentSceneId: string) => {
      const currentScene = scenes.find(s => s.id === currentSceneId);
      if (!currentScene?.links) return;

      const preloadPromises = currentScene.links.map(link => {
        const targetScene = scenes.find(s => s.id === link.nodeId);
        return targetScene ? preloadImage(targetScene.panorama) : Promise.resolve();
      });

      await Promise.allSettled(preloadPromises);
    },
    
    getLoadedCount: () => loadedImages.size,
    getTotalCount: () => scenes.length
  };
}

/**
 * Default error handler
 */
export function createErrorHandler(onError?: (error: PSVError) => void) {
  return (error: any, context?: Record<string, any>) => {
    const psvError = error instanceof PSVError 
      ? error 
      : new PSVError('UNKNOWN_ERROR', error.message || 'Unknown error occurred', error, context);
    
    console.error('PSV Error:', psvError);
    
    if (onError) {
      onError(psvError);
    }
  };
}
