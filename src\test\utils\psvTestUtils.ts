/**
 * PSV Testing Utilities
 * Helper functions and mocks for testing Photo Sphere Viewer components
 */

import { vi } from 'vitest';
import type { TourScene, TourMarker, PSVInstance } from '@/lib/photosphere/types';

// Mock PSV classes
export const mockViewer = {
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  removeAllListeners: vi.fn(),
  destroy: vi.fn(),
  refresh: vi.fn(),
  resize: vi.fn(),
  animate: vi.fn(),
  rotate: vi.fn(),
  zoom: vi.fn(),
  getZoomLevel: vi.fn(() => 50),
  isFullscreenEnabled: vi.fn(() => false),
  enterFullscreen: vi.fn(),
  exitFullscreen: vi.fn(),
  container: document.createElement('div'),
  getPlugin: vi.fn(),
  clearCache: vi.fn()
};

export const mockAutorotatePlugin = {
  start: vi.fn(),
  stop: vi.fn(),
  isEnabled: vi.fn(() => false),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  setOptions: vi.fn()
};

export const mockMarkersPlugin = {
  setMarkers: vi.fn(),
  addMarker: vi.fn(),
  removeMarker: vi.fn(),
  updateMarker: vi.fn(),
  clearMarkers: vi.fn(),
  getMarker: vi.fn(),
  getMarkers: vi.fn(() => []),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
};

export const mockVirtualTourPlugin = {
  setCurrentNode: vi.fn(),
  getCurrentNode: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  refresh: vi.fn()
};

export const mockGalleryPlugin = {
  show: vi.fn(),
  hide: vi.fn(),
  isVisible: vi.fn(() => false),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
};

export const mockSettingsPlugin = {
  show: vi.fn(),
  hide: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
};

// Mock PSV instance
export const createMockPSVInstance = (): PSVInstance => ({
  viewer: mockViewer as any,
  plugins: {
    autorotate: mockAutorotatePlugin as any,
    markers: mockMarkersPlugin as any,
    virtualTour: mockVirtualTourPlugin as any,
    gallery: mockGalleryPlugin as any,
    settings: mockSettingsPlugin as any
  },
  config: {} as any,
  markerService: {
    initialize: vi.fn(),
    addMarkers: vi.fn(),
    addMarker: vi.fn(),
    removeMarker: vi.fn(),
    updateMarker: vi.fn(),
    clearMarkers: vi.fn(),
    getMarker: vi.fn(),
    getAllMarkers: vi.fn(() => []),
    destroy: vi.fn()
  },
  enterpriseFeatures: {
    initialize: vi.fn(),
    updateSettings: vi.fn(),
    getSettings: vi.fn(() => ({})),
    getAnalytics: vi.fn(() => ({})),
    exportTour: vi.fn(),
    destroy: vi.fn()
  },
  destroy: vi.fn(),
  refresh: vi.fn(),
  resize: vi.fn()
});

// Test data factories
export const createTestScene = (overrides: Partial<TourScene> = {}): TourScene => ({
  id: 'test-scene-1',
  name: 'Test Scene',
  panorama: '/test-images/panorama.jpg',
  thumbnail: '/test-images/thumbnail.jpg',
  description: 'A test scene for unit testing',
  links: [],
  markers: [],
  data: {
    orderIndex: 0
  },
  ...overrides
});

export const createTestMarker = (overrides: Partial<TourMarker> = {}): TourMarker => ({
  id: 'test-marker-1',
  type: 'info',
  position: {
    yaw: '45deg',
    pitch: '0deg'
  },
  title: 'Test Marker',
  content: 'This is a test marker',
  ...overrides
});

export const createTestScenes = (count: number = 3): TourScene[] => {
  return Array.from({ length: count }, (_, index) => 
    createTestScene({
      id: `scene-${index + 1}`,
      name: `Scene ${index + 1}`,
      panorama: `/test-images/scene-${index + 1}.jpg`,
      data: { orderIndex: index }
    })
  );
};

// Mock WebGL context
export const mockWebGLContext = {
  getParameter: vi.fn((param) => {
    switch (param) {
      case 'VENDOR': return 'Test Vendor';
      case 'RENDERER': return 'Test Renderer';
      case 'VERSION': return 'WebGL 2.0';
      case 'MAX_TEXTURE_SIZE': return 4096;
      default: return null;
    }
  }),
  createShader: vi.fn(),
  shaderSource: vi.fn(),
  compileShader: vi.fn(),
  createProgram: vi.fn(),
  attachShader: vi.fn(),
  linkProgram: vi.fn(),
  useProgram: vi.fn(),
  createBuffer: vi.fn(),
  bindBuffer: vi.fn(),
  bufferData: vi.fn(),
  createTexture: vi.fn(),
  bindTexture: vi.fn(),
  texImage2D: vi.fn(),
  texParameteri: vi.fn(),
  drawArrays: vi.fn(),
  clear: vi.fn(),
  clearColor: vi.fn(),
  enable: vi.fn(),
  disable: vi.fn(),
  viewport: vi.fn()
};

// Mock canvas and WebGL
export const setupWebGLMocks = () => {
  const mockCanvas = document.createElement('canvas');
  mockCanvas.getContext = vi.fn((type) => {
    if (type === 'webgl' || type === 'experimental-webgl') {
      return mockWebGLContext;
    }
    return null;
  });

  // Mock HTMLCanvasElement.prototype.getContext
  vi.spyOn(HTMLCanvasElement.prototype, 'getContext').mockImplementation((type) => {
    if (type === 'webgl' || type === 'experimental-webgl') {
      return mockWebGLContext;
    }
    if (type === '2d') {
      return {
        drawImage: vi.fn(),
        getImageData: vi.fn(),
        putImageData: vi.fn(),
        createImageData: vi.fn(),
        clearRect: vi.fn(),
        fillRect: vi.fn(),
        strokeRect: vi.fn(),
        beginPath: vi.fn(),
        closePath: vi.fn(),
        moveTo: vi.fn(),
        lineTo: vi.fn(),
        arc: vi.fn(),
        fill: vi.fn(),
        stroke: vi.fn()
      } as any;
    }
    return null;
  });

  return mockCanvas;
};

// Mock Image loading
export const mockImageLoading = () => {
  const originalImage = window.Image;
  
  window.Image = class MockImage {
    onload: (() => void) | null = null;
    onerror: (() => void) | null = null;
    src: string = '';
    width: number = 1024;
    height: number = 512;
    crossOrigin: string | null = null;

    constructor() {
      // Simulate successful image loading
      setTimeout(() => {
        if (this.onload) {
          this.onload();
        }
      }, 10);
    }
  } as any;

  return () => {
    window.Image = originalImage;
  };
};

// Mock File and FileReader
export const createMockFile = (
  name: string = 'test-image.jpg',
  type: string = 'image/jpeg',
  size: number = 1024 * 1024
): File => {
  const file = new File([''], name, { type });
  Object.defineProperty(file, 'size', { value: size });
  return file;
};

export const mockFileReader = () => {
  const originalFileReader = window.FileReader;
  
  window.FileReader = class MockFileReader {
    onload: ((event: any) => void) | null = null;
    onerror: (() => void) | null = null;
    result: string | ArrayBuffer | null = null;

    readAsDataURL(file: File) {
      setTimeout(() => {
        this.result = `data:${file.type};base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`;
        if (this.onload) {
          this.onload({ target: { result: this.result } });
        }
      }, 10);
    }

    readAsArrayBuffer(file: File) {
      setTimeout(() => {
        this.result = new ArrayBuffer(8);
        if (this.onload) {
          this.onload({ target: { result: this.result } });
        }
      }, 10);
    }
  } as any;

  return () => {
    window.FileReader = originalFileReader;
  };
};

// Mock performance.memory
export const mockPerformanceMemory = () => {
  const originalPerformance = window.performance;
  
  Object.defineProperty(window.performance, 'memory', {
    value: {
      usedJSHeapSize: 50 * 1024 * 1024, // 50MB
      totalJSHeapSize: 100 * 1024 * 1024, // 100MB
      jsHeapSizeLimit: 2 * 1024 * 1024 * 1024 // 2GB
    },
    configurable: true
  });

  return () => {
    Object.defineProperty(window.performance, 'memory', {
      value: originalPerformance.memory,
      configurable: true
    });
  };
};

// Mock URL.createObjectURL and revokeObjectURL
export const mockURL = () => {
  const originalCreateObjectURL = URL.createObjectURL;
  const originalRevokeObjectURL = URL.revokeObjectURL;
  
  URL.createObjectURL = vi.fn(() => 'blob:mock-url');
  URL.revokeObjectURL = vi.fn();

  return () => {
    URL.createObjectURL = originalCreateObjectURL;
    URL.revokeObjectURL = originalRevokeObjectURL;
  };
};

// Test environment setup
export const setupTestEnvironment = () => {
  const cleanupFunctions: (() => void)[] = [];

  // Setup all mocks
  setupWebGLMocks();
  cleanupFunctions.push(mockImageLoading());
  cleanupFunctions.push(mockFileReader());
  cleanupFunctions.push(mockPerformanceMemory());
  cleanupFunctions.push(mockURL());

  // Mock window.gc if not available
  if (!window.gc) {
    window.gc = vi.fn();
  }

  // Mock navigator.userAgent
  Object.defineProperty(navigator, 'userAgent', {
    value: 'Mozilla/5.0 (Test Environment)',
    configurable: true
  });

  // Return cleanup function
  return () => {
    cleanupFunctions.forEach(cleanup => cleanup());
  };
};

// Assertion helpers
export const expectPSVToBeInitialized = (instance: PSVInstance) => {
  expect(instance).toBeDefined();
  expect(instance.viewer).toBeDefined();
  expect(instance.plugins).toBeDefined();
  expect(instance.destroy).toBeTypeOf('function');
};

export const expectMarkerToBeValid = (marker: TourMarker) => {
  expect(marker.id).toBeDefined();
  expect(marker.type).toBeDefined();
  expect(marker.position).toBeDefined();
  expect(marker.position.yaw).toBeDefined();
  expect(marker.position.pitch).toBeDefined();
};

export const expectSceneToBeValid = (scene: TourScene) => {
  expect(scene.id).toBeDefined();
  expect(scene.name).toBeDefined();
  expect(scene.panorama).toBeDefined();
  expect(scene.links).toBeDefined();
  expect(scene.markers).toBeDefined();
};

// Wait for async operations
export const waitForPSVReady = async (timeout: number = 5000): Promise<void> => {
  return new Promise((resolve, reject) => {
    const timer = setTimeout(() => {
      reject(new Error('PSV ready timeout'));
    }, timeout);

    // Simulate PSV ready event
    setTimeout(() => {
      clearTimeout(timer);
      resolve();
    }, 100);
  });
};

// Mock Supabase for testing
export const mockSupabase = {
  from: vi.fn(() => ({
    select: vi.fn(() => ({
      eq: vi.fn(() => ({
        single: vi.fn(() => Promise.resolve({ data: null, error: null }))
      })),
      order: vi.fn(() => ({
        range: vi.fn(() => Promise.resolve({ data: [], error: null, count: 0 }))
      }))
    })),
    insert: vi.fn(() => Promise.resolve({ data: null, error: null })),
    update: vi.fn(() => Promise.resolve({ data: null, error: null })),
    delete: vi.fn(() => Promise.resolve({ data: null, error: null }))
  })),
  storage: {
    from: vi.fn(() => ({
      upload: vi.fn(() => Promise.resolve({ data: { path: 'test-path' }, error: null })),
      remove: vi.fn(() => Promise.resolve({ data: null, error: null })),
      getPublicUrl: vi.fn(() => ({ data: { publicUrl: 'https://test-url.com/image.jpg' } }))
    }))
  }
};
