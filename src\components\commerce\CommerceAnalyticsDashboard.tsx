/**
 * CommerceAnalyticsDashboard Component
 * Comprehensive analytics dashboard for e-commerce performance
 * Mobile-first responsive design with real-time metrics
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Package, 
  Users, 
  ShoppingCart,
  MessageCircle,
  Star,
  Calendar,
  Download,
  RefreshCw,
  Eye,
  Target,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AnalyticsData {
  overview: {
    total_revenue: number;
    total_orders: number;
    total_customers: number;
    total_vendors: number;
    revenue_growth: number;
    order_growth: number;
    customer_growth: number;
    vendor_growth: number;
  };
  sales: {
    daily_revenue: Array<{ date: string; revenue: number; orders: number }>;
    top_products: Array<{ id: string; name: string; revenue: number; units_sold: number }>;
    vendor_performance: Array<{ id: string; name: string; revenue: number; orders: number; rating: number }>;
  };
  customers: {
    new_customers: number;
    returning_customers: number;
    customer_lifetime_value: number;
    acquisition_channels: Array<{ channel: string; customers: number; percentage: number }>;
  };
  whatsapp: {
    messages_sent: number;
    delivery_rate: number;
    response_rate: number;
    automation_performance: Array<{ rule: string; triggered: number; success_rate: number }>;
  };
}

interface CommerceAnalyticsDashboardProps {
  className?: string;
}

const CommerceAnalyticsDashboard = ({ className }: CommerceAnalyticsDashboardProps) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [dateRange, setDateRange] = useState('30d');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadAnalyticsData();
  }, [dateRange]);

  const loadAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call - replace with actual service
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setAnalyticsData({
        overview: {
          total_revenue: 2450000,
          total_orders: 1234,
          total_customers: 856,
          total_vendors: 45,
          revenue_growth: 15.2,
          order_growth: 8.7,
          customer_growth: 12.3,
          vendor_growth: 22.1
        },
        sales: {
          daily_revenue: [
            { date: '2024-01-01', revenue: 85000, orders: 42 },
            { date: '2024-01-02', revenue: 92000, orders: 38 },
            { date: '2024-01-03', revenue: 78000, orders: 45 },
            { date: '2024-01-04', revenue: 105000, orders: 52 },
            { date: '2024-01-05', revenue: 88000, orders: 41 }
          ],
          top_products: [
            { id: '1', name: 'Premium Headphones', revenue: 450000, units_sold: 89 },
            { id: '2', name: 'Smart Watch', revenue: 380000, units_sold: 76 },
            { id: '3', name: 'Wireless Speaker', revenue: 320000, units_sold: 64 }
          ],
          vendor_performance: [
            { id: '1', name: 'TechHub Nigeria', revenue: 680000, orders: 234, rating: 4.8 },
            { id: '2', name: 'Lagos Electronics', revenue: 520000, orders: 189, rating: 4.6 },
            { id: '3', name: 'Abuja Gadgets', revenue: 410000, orders: 156, rating: 4.7 }
          ]
        },
        customers: {
          new_customers: 234,
          returning_customers: 622,
          customer_lifetime_value: 125000,
          acquisition_channels: [
            { channel: 'Virtual Tours', customers: 345, percentage: 40.3 },
            { channel: 'WhatsApp', customers: 267, percentage: 31.2 },
            { channel: 'Direct', customers: 156, percentage: 18.2 },
            { channel: 'Social Media', customers: 88, percentage: 10.3 }
          ]
        },
        whatsapp: {
          messages_sent: 3456,
          delivery_rate: 98.5,
          response_rate: 67.2,
          automation_performance: [
            { rule: 'Order Confirmation', triggered: 1234, success_rate: 99.2 },
            { rule: 'Abandoned Cart', triggered: 456, success_rate: 78.5 },
            { rule: 'Status Updates', triggered: 892, success_rate: 96.8 }
          ]
        }
      });
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const MetricCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    color = 'blue',
    format = 'number'
  }: { 
    title: string; 
    value: number; 
    change: number;
    icon: any; 
    color?: string;
    format?: 'number' | 'currency' | 'percentage';
  }) => {
    const formatValue = (val: number) => {
      switch (format) {
        case 'currency':
          return `₦${val.toLocaleString()}`;
        case 'percentage':
          return `${val}%`;
        default:
          return val.toLocaleString();
      }
    };

    const isPositive = change > 0;

    return (
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">{title}</p>
              <p className="text-2xl font-bold">{formatValue(value)}</p>
              <div className="flex items-center gap-1 mt-1">
                {isPositive ? (
                  <TrendingUp className="w-3 h-3 text-green-600" />
                ) : (
                  <TrendingDown className="w-3 h-3 text-red-600" />
                )}
                <span className={cn(
                  "text-xs font-medium",
                  isPositive ? "text-green-600" : "text-red-600"
                )}>
                  {isPositive ? '+' : ''}{change}%
                </span>
              </div>
            </div>
            <div className={cn(
              "w-12 h-12 rounded-lg flex items-center justify-center",
              color === 'blue' && 'bg-blue-100 text-blue-600',
              color === 'green' && 'bg-green-100 text-green-600',
              color === 'purple' && 'bg-purple-100 text-purple-600',
              color === 'orange' && 'bg-orange-100 text-orange-600'
            )}>
              <Icon className="w-6 h-6" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  const TopProductCard = ({ product }: { product: any }) => (
    <div className="flex items-center justify-between p-3 border rounded-lg">
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-sm line-clamp-1">{product.name}</h4>
        <p className="text-xs text-muted-foreground">{product.units_sold} units sold</p>
      </div>
      <div className="text-right">
        <p className="font-semibold text-sm">₦{product.revenue.toLocaleString()}</p>
      </div>
    </div>
  );

  const VendorPerformanceCard = ({ vendor }: { vendor: any }) => (
    <div className="flex items-center justify-between p-3 border rounded-lg">
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-sm line-clamp-1">{vendor.name}</h4>
        <div className="flex items-center gap-2 mt-1">
          <div className="flex items-center gap-1">
            <Star className="w-3 h-3 text-yellow-500 fill-current" />
            <span className="text-xs">{vendor.rating}</span>
          </div>
          <span className="text-xs text-muted-foreground">•</span>
          <span className="text-xs text-muted-foreground">{vendor.orders} orders</span>
        </div>
      </div>
      <div className="text-right">
        <p className="font-semibold text-sm">₦{vendor.revenue.toLocaleString()}</p>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!analyticsData) return null;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">Commerce Analytics</h1>
          <p className="text-muted-foreground">Monitor your e-commerce performance and growth</p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={loadAnalyticsData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Revenue"
          value={analyticsData.overview.total_revenue}
          change={analyticsData.overview.revenue_growth}
          icon={DollarSign}
          color="green"
          format="currency"
        />
        <MetricCard
          title="Total Orders"
          value={analyticsData.overview.total_orders}
          change={analyticsData.overview.order_growth}
          icon={ShoppingCart}
          color="blue"
        />
        <MetricCard
          title="Total Customers"
          value={analyticsData.overview.total_customers}
          change={analyticsData.overview.customer_growth}
          icon={Users}
          color="purple"
        />
        <MetricCard
          title="Active Vendors"
          value={analyticsData.overview.total_vendors}
          change={analyticsData.overview.vendor_growth}
          icon={Package}
          color="orange"
        />
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="sales" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="sales">Sales</TabsTrigger>
          <TabsTrigger value="customers">Customers</TabsTrigger>
          <TabsTrigger value="vendors">Vendors</TabsTrigger>
          <TabsTrigger value="whatsapp">WhatsApp</TabsTrigger>
        </TabsList>

        {/* Sales Analytics */}
        <TabsContent value="sales" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Top Products</CardTitle>
                <CardDescription>Best performing products by revenue</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {analyticsData.sales.top_products.map((product) => (
                  <TopProductCard key={product.id} product={product} />
                ))}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Vendor Performance</CardTitle>
                <CardDescription>Top vendors by revenue and rating</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {analyticsData.sales.vendor_performance.map((vendor) => (
                  <VendorPerformanceCard key={vendor.id} vendor={vendor} />
                ))}
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Revenue Trend</CardTitle>
              <CardDescription>Daily revenue and order volume</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <BarChart3 className="w-12 h-12 mx-auto mb-2" />
                  <p>Revenue chart would be rendered here</p>
                  <p className="text-sm">Integration with charting library needed</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Customer Analytics */}
        <TabsContent value="customers" className="space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {analyticsData.customers.new_customers}
                </div>
                <div className="text-sm text-muted-foreground">New Customers</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">
                  {analyticsData.customers.returning_customers}
                </div>
                <div className="text-sm text-muted-foreground">Returning Customers</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">
                  ₦{analyticsData.customers.customer_lifetime_value.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Avg. Lifetime Value</div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Customer Acquisition Channels</CardTitle>
              <CardDescription>How customers discover your platform</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analyticsData.customers.acquisition_channels.map((channel) => (
                  <div key={channel.channel} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                      <span className="font-medium">{channel.channel}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{channel.customers}</div>
                      <div className="text-xs text-muted-foreground">{channel.percentage}%</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Vendor Analytics */}
        <TabsContent value="vendors" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Vendor Growth</CardTitle>
                <CardDescription>New vendor registrations over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-48 flex items-center justify-center text-muted-foreground">
                  <div className="text-center">
                    <TrendingUp className="w-8 h-8 mx-auto mb-2" />
                    <p>Vendor growth chart</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Commission Overview</CardTitle>
                <CardDescription>Platform commission breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Total Commission Earned</span>
                    <span className="font-semibold">₦245,000</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Average Commission Rate</span>
                    <span className="font-semibold">10.5%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Commission This Month</span>
                    <span className="font-semibold">₦89,500</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* WhatsApp Analytics */}
        <TabsContent value="whatsapp" className="space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-green-600">
                  {analyticsData.whatsapp.messages_sent.toLocaleString()}
                </div>
                <div className="text-sm text-muted-foreground">Messages Sent</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {analyticsData.whatsapp.delivery_rate}%
                </div>
                <div className="text-sm text-muted-foreground">Delivery Rate</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {analyticsData.whatsapp.response_rate}%
                </div>
                <div className="text-sm text-muted-foreground">Response Rate</div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Automation Performance</CardTitle>
              <CardDescription>How your automated messages are performing</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {analyticsData.whatsapp.automation_performance.map((automation) => (
                  <div key={automation.rule} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium">{automation.rule}</div>
                      <div className="text-sm text-muted-foreground">
                        {automation.triggered} messages triggered
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">{automation.success_rate}%</div>
                      <div className="text-xs text-muted-foreground">Success Rate</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CommerceAnalyticsDashboard;
