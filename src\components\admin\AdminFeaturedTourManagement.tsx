
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import FeaturedTourManager from './FeaturedTourManager';
import { Setting<PERSON>, Star } from 'lucide-react';

const AdminFeaturedTourManagement = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Settings className="w-6 h-6" />
        <h2 className="text-2xl font-bold">Featured Tour Management</h2>
      </div>

      <Tabs defaultValue="featured" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="featured" className="flex items-center gap-2">
            <Star className="w-4 h-4" />
            Featured Tours
          </TabsTrigger>
          <TabsTrigger value="settings">
            Tour Settings
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="featured" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Manage Featured Tours</CardTitle>
              <p className="text-sm text-gray-600">
                Control which tours appear in different sections of your website
              </p>
            </CardHeader>
            <CardContent>
              <FeaturedTourManager />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Tour Settings</CardTitle>
              <p className="text-sm text-gray-600">
                Configure global tour settings and display options
              </p>
            </CardHeader>
            <CardContent>
              <p className="text-gray-500">Tour settings coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminFeaturedTourManagement;
