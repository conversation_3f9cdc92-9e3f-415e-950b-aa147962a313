-- Add slug column to tours table
-- This migration adds slug support for clean, SEO-friendly URLs

-- Add slug column to tours table
ALTER TABLE public.tours ADD COLUMN IF NOT EXISTS slug TEXT;

-- Create unique index on slug for fast lookups and uniqueness
CREATE UNIQUE INDEX IF NOT EXISTS tours_slug_unique_idx ON public.tours(slug) WHERE slug IS NOT NULL;

-- Create index for slug lookups
CREATE INDEX IF NOT EXISTS tours_slug_idx ON public.tours(slug);

-- Add constraint to ensure slug format (lowercase, alphanumeric with hyphens)
ALTER TABLE public.tours ADD CONSTRAINT tours_slug_format_check 
CHECK (slug IS NULL OR slug ~ '^[a-z0-9]+(?:-[a-z0-9]+)*$');

-- Function to generate slug from title
CREATE OR REPLACE FUNCTION generate_tour_slug(title_text TEXT)
RETURNS TEXT AS $$
BEGIN
  RETURN lower(
    regexp_replace(
      regexp_replace(
        regexp_replace(title_text, '[^\w\s-]', '', 'g'),
        '[\s_-]+', '-', 'g'
      ),
      '^-+|-+$', '', 'g'
    )
  );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to ensure unique slug
CREATE OR REPLACE FUNCTION ensure_unique_slug(base_slug TEXT, tour_id UUID DEFAULT NULL)
RETURNS TEXT AS $$
DECLARE
  unique_slug TEXT := base_slug;
  counter INTEGER := 1;
  slug_exists BOOLEAN;
BEGIN
  LOOP
    -- Check if slug exists (excluding current tour if updating)
    SELECT EXISTS(
      SELECT 1 FROM public.tours 
      WHERE slug = unique_slug 
      AND (tour_id IS NULL OR id != tour_id)
    ) INTO slug_exists;
    
    -- If slug doesn't exist, we can use it
    IF NOT slug_exists THEN
      EXIT;
    END IF;
    
    -- Generate new slug with counter
    unique_slug := base_slug || '-' || counter;
    counter := counter + 1;
  END LOOP;
  
  RETURN unique_slug;
END;
$$ LANGUAGE plpgsql;

-- Function to auto-generate slug on insert/update
CREATE OR REPLACE FUNCTION auto_generate_tour_slug()
RETURNS TRIGGER AS $$
BEGIN
  -- Only generate slug if it's not provided or if title changed
  IF NEW.slug IS NULL OR (TG_OP = 'UPDATE' AND OLD.title != NEW.title AND NEW.slug = OLD.slug) THEN
    NEW.slug := ensure_unique_slug(generate_tour_slug(NEW.title), NEW.id);
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-generate slugs
DROP TRIGGER IF EXISTS tours_auto_slug_trigger ON public.tours;
CREATE TRIGGER tours_auto_slug_trigger
  BEFORE INSERT OR UPDATE ON public.tours
  FOR EACH ROW
  EXECUTE FUNCTION auto_generate_tour_slug();

-- Update existing tours to have slugs (if any exist)
DO $$
DECLARE
  tour_record RECORD;
  new_slug TEXT;
BEGIN
  FOR tour_record IN SELECT id, title FROM public.tours WHERE slug IS NULL LOOP
    new_slug := ensure_unique_slug(generate_tour_slug(tour_record.title), tour_record.id);
    UPDATE public.tours SET slug = new_slug WHERE id = tour_record.id;
  END LOOP;
END $$;

-- Add comment to document the slug column
COMMENT ON COLUMN public.tours.slug IS 'URL-friendly slug for SEO and clean URLs. Auto-generated from title if not provided.';

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION generate_tour_slug(TEXT) TO authenticated, anon;
GRANT EXECUTE ON FUNCTION ensure_unique_slug(TEXT, UUID) TO authenticated, anon;
