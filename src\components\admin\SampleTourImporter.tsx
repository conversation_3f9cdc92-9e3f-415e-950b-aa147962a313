import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Download, 
  ExternalLink, 
  Eye, 
  MapPin, 
  Building, 
  GraduationCap,
  Utensils,
  Camera,
  Landmark,
  Home,
  Store,
  Trophy
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { sampleTours, SampleTour } from '@/data/sampleTours';
import { createTourSlug } from '@/lib/slugUtils';

interface SampleTourImporterProps {
  onSuccess?: () => void;
}

const SampleTourImporter = ({ onSuccess }: SampleTourImporterProps) => {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [importingTours, setImportingTours] = useState<Set<string>>(new Set());

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'education': return GraduationCap;
      case 'hospitality': return Utensils;
      case 'tourism': return Camera;
      case 'culture': return Landmark;
      case 'property': return Home;
      case 'commercial': return Store;
      default: return Building;
    }
  };

  // VRT branding for all tours (hide source platforms)
  const getVRTBranding = () => {
    return 'bg-blue-600 text-white';
  };

  const importTourMutation = useMutation({
    mutationFn: async (tour: SampleTour) => {
      if (!user) throw new Error('User not authenticated');

      // Generate unique slug for the tour
      const baseSlug = createTourSlug(tour.title);

      // Check if slug already exists and make it unique
      const { data: existingTours } = await supabase
        .from('tours')
        .select('slug')
        .like('slug', `${baseSlug}%`);

      const existingSlugs = existingTours?.map(t => t.slug).filter(Boolean) || [];
      let uniqueSlug = baseSlug;
      let counter = 1;

      while (existingSlugs.includes(uniqueSlug)) {
        uniqueSlug = `${baseSlug}-${counter}`;
        counter++;
      }

      const tourData = {
        title: tour.title,
        description: tour.description,
        category: tour.category,
        location: tour.location,
        business_name: tour.business_type || null,
        business_type: tour.business_type || null,
        embed_url: tour.embed_url,
        embed_type: tour.embed_type,
        slug: uniqueSlug, // Add the unique slug
        user_id: user.id,
        status: 'draft', // Import as draft for admin review
        scenes_count: 0,
        featured: false,
        views: 0
      };

      const { data, error } = await supabase
        .from('tours')
        .insert(tourData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data, tour) => {
      queryClient.invalidateQueries({ queryKey: ['admin-all-tours'] });
      toast.success(`${tour.title} imported successfully as draft`);
      setImportingTours(prev => {
        const newSet = new Set(prev);
        newSet.delete(tour.id);
        return newSet;
      });
      onSuccess?.();
    },
    onError: (error, tour) => {
      toast.error(`Failed to import ${tour.title}: ${error.message}`);
      setImportingTours(prev => {
        const newSet = new Set(prev);
        newSet.delete(tour.id);
        return newSet;
      });
    },
  });

  const handleImportTour = (tour: SampleTour) => {
    setImportingTours(prev => new Set(prev).add(tour.id));
    importTourMutation.mutate(tour);
  };

  const importAllTours = () => {
    sampleTours.forEach(tour => {
      if (!importingTours.has(tour.id)) {
        handleImportTour(tour);
      }
    });
  };



  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="w-5 h-5" />
            Professional Tour Examples
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Import professional tour examples from Panoee and MassInteract to test and demonstrate your platform capabilities.
            All tours will be imported as drafts for your review.
          </p>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Badge variant="outline">{sampleTours.length} Professional Tours</Badge>
              <Badge className="bg-blue-600 text-white">VRT Platform</Badge>
            </div>
            <div className="flex items-center gap-2">

              <Button onClick={importAllTours} disabled={importingTours.size > 0}>
                <Download className="w-4 h-4 mr-2" />
                Import All Tours
              </Button>
            </div>
          </div>
          
          <Separator className="mb-4" />
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {sampleTours.map((tour) => {
              const CategoryIcon = getCategoryIcon(tour.category);
              const isImporting = importingTours.has(tour.id);
              
              return (
                <Card key={tour.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-2">
                          <CategoryIcon className="w-4 h-4 text-muted-foreground" />
                          <Badge variant="outline" className="text-xs">
                            {tour.category}
                          </Badge>
                        </div>
                        <Badge className={`text-xs ${getVRTBranding()}`}>
                          VRT
                        </Badge>
                      </div>
                      
                      <div>
                        <h4 className="font-medium text-sm leading-tight mb-1">
                          {tour.title}
                        </h4>
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {tour.description}
                        </p>
                      </div>
                      
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <MapPin className="w-3 h-3" />
                        <span className="truncate">{tour.location}</span>
                      </div>
                      
                      {tour.business_type && (
                        <Badge variant="secondary" className="text-xs">
                          {tour.business_type}
                        </Badge>
                      )}
                      
                      <div className="flex items-center gap-2 pt-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => toast.info('Import this tour to preview it on our platform')}
                          className="flex-1 text-xs"
                        >
                          <Eye className="w-3 h-3 mr-1" />
                          Preview
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleImportTour(tour)}
                          disabled={isImporting}
                          className="flex-1 text-xs"
                        >
                          <Download className="w-3 h-3 mr-1" />
                          {isImporting ? 'Importing...' : 'Import'}
                        </Button>
                      </div>
                      
                      <div className="text-xs text-muted-foreground">
                        <span className="font-medium">{tour.features.length} features:</span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {tour.features.slice(0, 2).map((feature, index) => (
                            <Badge key={index} variant="outline" className="text-xs px-1 py-0">
                              {feature}
                            </Badge>
                          ))}
                          {tour.features.length > 2 && (
                            <Badge variant="outline" className="text-xs px-1 py-0">
                              +{tour.features.length - 2} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SampleTourImporter;
