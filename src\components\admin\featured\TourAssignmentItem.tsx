
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { X, GripVertical } from 'lucide-react';

interface FeaturedTourAssignment {
  id: string;
  tour_id: string;
  section_type: string;
  display_order: number;
  is_active: boolean;
  tours: {
    title: string;
    thumbnail_url: string;
    category: string;
  };
}

interface TourAssignmentItemProps {
  assignment: FeaturedTourAssignment;
}

const TourAssignmentItem = ({ assignment }: TourAssignmentItemProps) => {
  const queryClient = useQueryClient();

  const removeAssignmentMutation = useMutation({
    mutationFn: async (assignmentId: string) => {
      const { error } = await supabase
        .from('featured_tour_assignments')
        .delete()
        .eq('id', assignmentId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['featured-assignments'] });
      toast.success('Tour removed from section');
    },
    onError: (error) => {
      toast.error(`Failed to remove tour: ${error.message}`);
    },
  });

  return (
    <div className="flex items-center gap-4 p-3 border rounded-lg">
      <GripVertical className="w-4 h-4 text-gray-400" />
      <img
        src={assignment.tours.thumbnail_url || '/api/placeholder/60/40'}
        alt={assignment.tours.title}
        className="w-15 h-10 object-cover rounded"
      />
      <div className="flex-1">
        <h4 className="font-medium">{assignment.tours.title}</h4>
        <Badge variant="outline" className="text-xs">
          {assignment.tours.category}
        </Badge>
      </div>
      <div className="text-sm text-gray-500">
        Order: {assignment.display_order}
      </div>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => removeAssignmentMutation.mutate(assignment.id)}
        disabled={removeAssignmentMutation.isPending}
      >
        <X className="w-4 h-4" />
      </Button>
    </div>
  );
};

export default TourAssignmentItem;
