/**
 * Vendor Dashboard Layout
 * Consistent layout structure matching admin panel for vendors
 */

import { ReactNode } from 'react';
import { useLocation } from 'react-router-dom';
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { ProfileDropdown } from '@/components/admin/ProfileDropdown';
import VendorSidebar from './VendorSidebar';

interface VendorLayoutProps {
  children: ReactNode;
}

const VendorLayout = ({ children }: VendorLayoutProps) => {
  const location = useLocation();

  const getBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs = [];

    if (pathSegments[0] === 'vendor-dashboard') {
      breadcrumbs.push({ label: 'Vendor Dashboard', href: '/vendor-dashboard' });

      if (pathSegments[1]) {
        const pageNames: Record<string, string> = {
          'tours': 'My Tours',
          'create': 'Create Tour',
          'products': 'Products',
          'orders': 'Orders',
          'inventory': 'Inventory',
          'analytics': 'Analytics',
          'revenue': 'Revenue',
          'customers': 'Customers',
          'reviews': 'Reviews',
          'profile': 'Profile',
          'settings': 'Settings',
          'support': 'Support'
        };

        breadcrumbs.push({
          label: pageNames[pathSegments[1]] || pathSegments[1],
          href: `/vendor-dashboard/${pathSegments[1]}`
        });
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = getBreadcrumbs();

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <VendorSidebar />
        <div className="flex-1 flex flex-col min-w-0">
          {/* Mobile-First Topbar/Header */}
          <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex h-14 sm:h-16 items-center justify-between px-3 sm:px-4 md:px-6 lg:px-8">
              <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
                <SidebarTrigger className="touch-target" />

                {/* Mobile: Show current page title */}
                <div className="block md:hidden min-w-0 flex-1">
                  <h1 className="text-sm font-semibold truncate">
                    {breadcrumbs.length > 0 ? breadcrumbs[breadcrumbs.length - 1].label : 'Vendor Dashboard'}
                  </h1>
                </div>

                {/* Desktop: Show breadcrumbs */}
                <div className="hidden md:block min-w-0 flex-1">
                  {breadcrumbs.length > 0 && (
                    <Breadcrumb>
                      <BreadcrumbList>
                        {breadcrumbs.map((crumb, index) => (
                          <div key={crumb.href} className="flex items-center">
                            {index > 0 && <BreadcrumbSeparator />}
                            <BreadcrumbItem>
                              {index === breadcrumbs.length - 1 ? (
                                <BreadcrumbPage className="font-medium">
                                  {crumb.label}
                                </BreadcrumbPage>
                              ) : (
                                <BreadcrumbLink 
                                  href={crumb.href}
                                  className="text-muted-foreground hover:text-foreground transition-colors"
                                >
                                  {crumb.label}
                                </BreadcrumbLink>
                              )}
                            </BreadcrumbItem>
                          </div>
                        ))}
                      </BreadcrumbList>
                    </Breadcrumb>
                  )}
                </div>
              </div>

              {/* Mobile-optimized profile section */}
              <div className="flex items-center gap-1 sm:gap-2">
                <ProfileDropdown />
              </div>
            </div>
          </header>

          {/* Mobile-First Main Content Area */}
          <main className="flex-1 w-full overflow-x-hidden">
            <div className="p-3 sm:p-4 md:p-6 lg:p-8 max-w-full">
              {children}
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default VendorLayout;
