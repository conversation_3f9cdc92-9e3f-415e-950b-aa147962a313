/**
 * Application Diagnostics Tool
 * Identifies potential issues that could cause blank screens or application crashes
 */

export interface DiagnosticIssue {
  severity: 'critical' | 'warning' | 'info';
  category: string;
  description: string;
  location: string;
  suggestion: string;
}

export class ApplicationDiagnostics {
  private issues: DiagnosticIssue[] = [];

  /**
   * Analyze the Placements tab issue specifically
   */
  analyzePlacementsIssue(): DiagnosticIssue[] {
    const placementsIssues: DiagnosticIssue[] = [
      {
        severity: 'critical',
        category: 'Data Fetching',
        description: 'Tours query could fail and cause undefined data access',
        location: 'AdminContent.tsx - useQuery for tours',
        suggestion: 'Add proper error boundaries and null checks before accessing tour data'
      },
      {
        severity: 'critical',
        category: 'Component Rendering',
        description: 'Complex mapping with filter operations on potentially undefined data',
        location: 'AdminContent.tsx - tours.filter(t => t.status === "published")',
        suggestion: 'Ensure tours array exists before filtering: tours?.filter() || []'
      },
      {
        severity: 'warning',
        category: 'State Management',
        description: 'Dynamic object key access without validation',
        location: 'AdminContent.tsx - placements[placement.key]',
        suggestion: 'Add validation for placement keys and default values'
      },
      {
        severity: 'warning',
        category: 'Component Props',
        description: 'Select component receiving potentially undefined values',
        location: 'AdminContent.tsx - ShadSelect value prop',
        suggestion: 'Ensure all props have fallback values'
      }
    ];

    return placementsIssues;
  }

  /**
   * Identify common patterns that could cause similar issues
   */
  identifyRiskyPatterns(): DiagnosticIssue[] {
    const riskyPatterns: DiagnosticIssue[] = [
      {
        severity: 'critical',
        category: 'Data Access',
        description: 'Direct array access without null checks (array.map, array.filter)',
        location: 'Multiple components',
        suggestion: 'Use optional chaining: array?.map() or provide fallbacks: array || []'
      },
      {
        severity: 'critical',
        category: 'Query Dependencies',
        description: 'useQuery without proper error handling or loading states',
        location: 'Components using React Query',
        suggestion: 'Always handle loading, error, and empty states explicitly'
      },
      {
        severity: 'warning',
        category: 'Component State',
        description: 'Complex state updates in event handlers without error boundaries',
        location: 'Event handlers in admin components',
        suggestion: 'Wrap state updates in try-catch blocks'
      },
      {
        severity: 'warning',
        category: 'Dynamic Rendering',
        description: 'Conditional rendering based on data that might be undefined',
        location: 'JSX with complex conditionals',
        suggestion: 'Use explicit null checks and provide fallback UI'
      }
    ];

    return riskyPatterns;
  }

  /**
   * Check for specific anti-patterns in the codebase
   */
  checkForAntiPatterns(): DiagnosticIssue[] {
    const antiPatterns: DiagnosticIssue[] = [
      {
        severity: 'critical',
        category: 'Error Handling',
        description: 'Missing error boundaries around complex components',
        location: 'Admin pages with data fetching',
        suggestion: 'Wrap complex components in React Error Boundaries'
      },
      {
        severity: 'warning',
        category: 'Data Validation',
        description: 'Assuming API responses have expected structure',
        location: 'Supabase query results',
        suggestion: 'Validate data structure before using in components'
      },
      {
        severity: 'warning',
        category: 'Component Dependencies',
        description: 'Components depending on external data without fallbacks',
        location: 'Select components, mapping operations',
        suggestion: 'Provide default values and loading states'
      }
    ];

    return antiPatterns;
  }

  /**
   * Generate recommendations for fixing the Placements tab
   */
  getPlacementsFixRecommendations(): string[] {
    return [
      '1. Add null checks: const safeTours = tours || [];',
      '2. Use optional chaining: tours?.filter(t => t.status === "published") || []',
      '3. Add error boundary around the entire placements content',
      '4. Validate placement keys before accessing state',
      '5. Provide fallback values for all Select component props',
      '6. Add loading skeleton instead of conditional rendering',
      '7. Separate data fetching logic from rendering logic',
      '8. Add proper TypeScript types for all data structures'
    ];
  }

  /**
   * Generate application-wide recommendations
   */
  getApplicationWideRecommendations(): string[] {
    return [
      '1. Implement React Error Boundaries for all admin pages',
      '2. Create reusable data fetching hooks with built-in error handling',
      '3. Add runtime data validation for all API responses',
      '4. Use TypeScript strict mode to catch potential null/undefined issues',
      '5. Implement consistent loading and error states across all components',
      '6. Add automated testing for critical user flows',
      '7. Create a centralized error logging system',
      '8. Implement graceful degradation for all features'
    ];
  }

  /**
   * Run complete diagnostic analysis
   */
  runFullDiagnostic(): {
    placementsIssues: DiagnosticIssue[];
    riskyPatterns: DiagnosticIssue[];
    antiPatterns: DiagnosticIssue[];
    placementsRecommendations: string[];
    applicationRecommendations: string[];
    summary: {
      critical: number;
      warnings: number;
      total: number;
    };
  } {
    const placementsIssues = this.analyzePlacementsIssue();
    const riskyPatterns = this.identifyRiskyPatterns();
    const antiPatterns = this.checkForAntiPatterns();
    
    const allIssues = [...placementsIssues, ...riskyPatterns, ...antiPatterns];
    const critical = allIssues.filter(i => i.severity === 'critical').length;
    const warnings = allIssues.filter(i => i.severity === 'warning').length;

    return {
      placementsIssues,
      riskyPatterns,
      antiPatterns,
      placementsRecommendations: this.getPlacementsFixRecommendations(),
      applicationRecommendations: this.getApplicationWideRecommendations(),
      summary: {
        critical,
        warnings,
        total: allIssues.length
      }
    };
  }
}

/**
 * Quick diagnostic function for immediate use
 */
export const runQuickDiagnostic = () => {
  const diagnostics = new ApplicationDiagnostics();
  return diagnostics.runFullDiagnostic();
};

/**
 * Specific patterns to look for in code that could cause blank screens
 */
export const DANGEROUS_PATTERNS = {
  // Direct array access without null checks
  UNSAFE_ARRAY_ACCESS: /\.map\(|\.filter\(|\.find\(/g,
  
  // Object property access without optional chaining
  UNSAFE_OBJECT_ACCESS: /\w+\.\w+\.\w+/g,
  
  // useQuery without proper error handling
  UNSAFE_QUERY: /useQuery\s*\(/g,
  
  // Complex conditional rendering
  COMPLEX_CONDITIONALS: /\?\s*\(/g,
  
  // State updates without error handling
  UNSAFE_STATE_UPDATES: /setState\(|set\w+\(/g
};

export default ApplicationDiagnostics;
