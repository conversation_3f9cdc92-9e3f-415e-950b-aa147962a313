/**
 * Mobile-Responsive Card Component
 * Optimized for touch interactions and mobile viewports
 */

import { forwardRef } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface MobileResponsiveCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  touchOptimized?: boolean;
  stackOnMobile?: boolean;
  fullWidthOnMobile?: boolean;
  children: React.ReactNode;
}

const MobileResponsiveCard = forwardRef<HTMLDivElement, MobileResponsiveCardProps>(
  ({ 
    className, 
    variant = 'default',
    size = 'md',
    touchOptimized = true,
    stackOnMobile = true,
    fullWidthOnMobile = true,
    children,
    ...props 
  }, ref) => {
    const variants = {
      default: "bg-card text-card-foreground border shadow-sm",
      elevated: "bg-card text-card-foreground border shadow-lg",
      outlined: "bg-transparent border-2 border-border",
      ghost: "bg-transparent border-0 shadow-none"
    };

    const sizes = {
      sm: "p-3 rounded-lg",
      md: "p-4 rounded-lg",
      lg: "p-6 rounded-xl"
    };

    const mobileOptimizations = {
      // Touch-friendly minimum sizes
      touchTarget: touchOptimized ? "min-h-[44px]" : "",
      // Mobile-specific spacing and sizing
      mobileSpacing: stackOnMobile ? "w-full sm:w-auto" : "",
      fullWidth: fullWidthOnMobile ? "w-full sm:max-w-none" : "",
      // Enhanced touch feedback
      touchFeedback: touchOptimized ? "active:scale-[0.98] transition-transform duration-150" : ""
    };

    return (
      <motion.div
        ref={ref}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className={cn(
          // Base styles
          variants[variant],
          sizes[size],
          // Mobile optimizations
          mobileOptimizations.touchTarget,
          mobileOptimizations.mobileSpacing,
          mobileOptimizations.fullWidth,
          mobileOptimizations.touchFeedback,
          // Responsive utilities
          "transition-all duration-200",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          // Mobile-first responsive classes
          "text-sm sm:text-base", // Responsive text sizing
          "gap-2 sm:gap-3", // Responsive gap
          className
        )}
        {...props}
      >
        {children}
      </motion.div>
    );
  }
);

MobileResponsiveCard.displayName = "MobileResponsiveCard";

// Card Header Component
interface MobileCardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  stackOnMobile?: boolean;
}

const MobileCardHeader = forwardRef<HTMLDivElement, MobileCardHeaderProps>(
  ({ className, children, stackOnMobile = true, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "flex items-center justify-between",
        // Mobile stacking
        stackOnMobile && "flex-col items-start gap-2 sm:flex-row sm:items-center sm:gap-0",
        // Mobile-optimized spacing
        "pb-2 sm:pb-3",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
);

MobileCardHeader.displayName = "MobileCardHeader";

// Card Content Component
interface MobileCardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  scrollable?: boolean;
  maxHeight?: string;
}

const MobileCardContent = forwardRef<HTMLDivElement, MobileCardContentProps>(
  ({ className, children, scrollable = false, maxHeight, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        // Base content styles
        "space-y-3 sm:space-y-4",
        // Scrollable content
        scrollable && "overflow-y-auto",
        // Mobile-optimized text
        "text-sm sm:text-base leading-relaxed",
        className
      )}
      style={{ maxHeight }}
      {...props}
    >
      {children}
    </div>
  )
);

MobileCardContent.displayName = "MobileCardContent";

// Card Footer Component
interface MobileCardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  stackOnMobile?: boolean;
  reverseOnMobile?: boolean;
}

const MobileCardFooter = forwardRef<HTMLDivElement, MobileCardFooterProps>(
  ({ className, children, stackOnMobile = true, reverseOnMobile = false, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "flex items-center gap-2 sm:gap-3",
        // Mobile stacking and ordering
        stackOnMobile && [
          "flex-col sm:flex-row",
          reverseOnMobile && "flex-col-reverse sm:flex-row"
        ],
        // Mobile-optimized spacing
        "pt-3 sm:pt-4",
        // Full-width buttons on mobile
        stackOnMobile && "[&>button]:w-full sm:[&>button]:w-auto",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
);

MobileCardFooter.displayName = "MobileCardFooter";

// Card Grid Component for responsive layouts
interface MobileCardGridProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
}

const MobileCardGrid = forwardRef<HTMLDivElement, MobileCardGridProps>(
  ({ 
    className, 
    children, 
    columns = { mobile: 1, tablet: 2, desktop: 3 },
    gap = 'md',
    ...props 
  }, ref) => {
    const gapClasses = {
      sm: "gap-3",
      md: "gap-4 sm:gap-6",
      lg: "gap-6 sm:gap-8"
    };

    const gridColumns = {
      mobile: columns.mobile || 1,
      tablet: columns.tablet || 2,
      desktop: columns.desktop || 3
    };

    return (
      <div
        ref={ref}
        className={cn(
          "grid",
          `grid-cols-${gridColumns.mobile}`,
          `sm:grid-cols-${gridColumns.tablet}`,
          `lg:grid-cols-${gridColumns.desktop}`,
          gapClasses[gap],
          // Auto-fit for very large screens
          "xl:grid-cols-4 2xl:grid-cols-5",
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

MobileCardGrid.displayName = "MobileCardGrid";

// Touch-optimized action card for interactive elements
interface TouchActionCardProps extends MobileResponsiveCardProps {
  onTap?: () => void;
  disabled?: boolean;
  loading?: boolean;
}

const TouchActionCard = forwardRef<HTMLDivElement, TouchActionCardProps>(
  ({ onTap, disabled = false, loading = false, children, className, ...props }, ref) => (
    <MobileResponsiveCard
      ref={ref}
      className={cn(
        "cursor-pointer select-none",
        disabled && "opacity-50 cursor-not-allowed",
        loading && "animate-pulse",
        // Enhanced touch feedback
        !disabled && "hover:shadow-md active:shadow-sm",
        // Focus styles for accessibility
        "focus-visible:ring-2 focus-visible:ring-primary",
        className
      )}
      onClick={!disabled && !loading ? onTap : undefined}
      role="button"
      tabIndex={disabled ? -1 : 0}
      aria-disabled={disabled}
      {...props}
    >
      {children}
    </MobileResponsiveCard>
  )
);

TouchActionCard.displayName = "TouchActionCard";

export {
  MobileResponsiveCard,
  MobileCardHeader,
  MobileCardContent,
  MobileCardFooter,
  MobileCardGrid,
  TouchActionCard
};
