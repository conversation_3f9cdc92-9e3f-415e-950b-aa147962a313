/**
 * Tour Card Display Configuration
 * Centralized settings for tour card behavior across the application
 */

export interface TourCardConfig {
  showEmbedded: boolean;
  autoLoad: boolean;
  showActions: boolean;
  height?: string;
  performanceMode: 'fast' | 'balanced' | 'quality';
  preloadCount?: number;
  lazyLoadThreshold?: number;
  enableIntersectionObserver?: boolean;
  debounceHover?: number;
  enablePreloading?: boolean;
}

/**
 * Optimized tour card settings for different contexts
 */
export const tourCardSettings = {
  // Public showcase - Lightning-fast auto-loading
  showcase: {
    showEmbedded: true,
    autoLoad: true,
    showActions: false,
    height: '280px',
    performanceMode: 'fast' as const,
    preloadCount: 6,
    lazyLoadThreshold: 0.1,
    enableIntersectionObserver: true,
    debounceHover: 50,
    enablePreloading: true
  },

  // User dashboard - Optimized for interaction
  dashboard: {
    showEmbedded: true,
    autoLoad: false,
    showActions: true,
    height: '260px',
    performanceMode: 'fast' as const,
    preloadCount: 4,
    lazyLoadThreshold: 0.15,
    enableIntersectionObserver: true,
    debounceHover: 50,
    enablePreloading: true
  },

  // Admin preview - Instant loading for editing
  adminPreview: {
    showEmbedded: true,
    autoLoad: true,
    showActions: true,
    height: '450px',
    performanceMode: 'quality' as const,
    preloadCount: 1,
    lazyLoadThreshold: 0,
    enableIntersectionObserver: false,
    debounceHover: 0,
    enablePreloading: false
  },

  // Admin pending - Fast approval workflow
  adminPending: {
    showEmbedded: true,
    autoLoad: false, // Changed to lazy for better performance
    showActions: false,
    height: '280px',
    performanceMode: 'fast' as const,
    preloadCount: 3,
    lazyLoadThreshold: 0.1,
    enableIntersectionObserver: true,
    debounceHover: 50,
    enablePreloading: true
  },

  // Admin published - Optimized management
  adminPublished: {
    showEmbedded: true,
    autoLoad: false,
    showActions: true,
    height: '260px',
    performanceMode: 'fast' as const,
    preloadCount: 5,
    lazyLoadThreshold: 0.1,
    enableIntersectionObserver: true,
    debounceHover: 50,
    enablePreloading: true
  },

  // Homepage featured tours - Auto-load for engagement (only place with auto-load)
  homepage: {
    showEmbedded: true,
    autoLoad: true,
    showActions: false,
    height: '320px',
    performanceMode: 'quality' as const
  },

  // Featured tours - Lightning-fast auto-loading
  featured: {
    showEmbedded: true,
    autoLoad: true,
    showActions: false,
    height: '320px',
    performanceMode: 'fast' as const,
    preloadCount: 6,
    lazyLoadThreshold: 0.1,
    enableIntersectionObserver: true,
    debounceHover: 50,
    enablePreloading: true
  },

  // Mobile optimized - Lightning-fast loading
  mobile: {
    showEmbedded: true,
    autoLoad: true,
    showActions: false,
    height: '240px',
    performanceMode: 'fast' as const,
    preloadCount: 3,
    lazyLoadThreshold: 0.2,
    enableIntersectionObserver: true,
    debounceHover: 100,
    enablePreloading: true
  }
} as const;

/**
 * Get optimized settings based on context and device
 */
export const getTourCardSettings = (
  context: keyof typeof tourCardSettings,
  isMobile: boolean = false
): TourCardConfig => {
  if (isMobile && context !== 'adminPreview') {
    return tourCardSettings.mobile;
  }
  
  return tourCardSettings[context];
};

/**
 * Performance optimization settings
 */
export const performanceSettings = {
  // Maximum auto-loading tours per page
  maxAutoLoadTours: {
    desktop: 6,
    mobile: 3
  },
  
  // Lazy loading thresholds
  lazyLoadMargin: '100px',
  
  // Intersection observer options
  observerOptions: {
    rootMargin: '50px',
    threshold: 0.1
  }
} as const;
