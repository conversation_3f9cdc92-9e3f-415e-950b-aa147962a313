
-- Enable necessary extensions
create extension if not exists "uuid-ossp";

-- Create enum types
create type tour_status as enum ('draft', 'processing', 'published', 'archived');
create type tour_category as enum ('property', 'education', 'hospitality', 'tourism', 'culture', 'commercial', 'healthcare', 'government');
create type hotspot_type as enum ('navigation', 'info', 'whatsapp', 'link', 'audio', 'video');
create type user_role as enum ('user', 'admin');

-- Create profiles table (extends auth.users)
create table public.profiles (
  id uuid references auth.users(id) on delete cascade primary key,
  email text not null,
  full_name text,
  avatar_url text,
  role user_role default 'user',
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create tours table
create table public.tours (
  id uuid default uuid_generate_v4() primary key,
  title text not null,
  description text,
  category tour_category not null,
  location text,
  thumbnail_url text,
  status tour_status default 'draft',
  views integer default 0,
  scenes_count integer default 0,
  user_id uuid references public.profiles(id) on delete cascade not null,
  featured boolean default false,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null,
  updated_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create scenes table
create table public.scenes (
  id uuid default uuid_generate_v4() primary key,
  tour_id uuid references public.tours(id) on delete cascade not null,
  name text not null,
  description text,
  image_url text not null,
  order_index integer not null default 0,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create hotspots table
create table public.hotspots (
  id uuid default uuid_generate_v4() primary key,
  scene_id uuid references public.scenes(id) on delete cascade not null,
  type hotspot_type not null,
  label text,
  content text,
  position_x real not null,
  position_y real not null,
  position_z real not null,
  target_scene_id uuid references public.scenes(id),
  link_url text,
  whatsapp_phone text,
  whatsapp_message text,
  created_at timestamp with time zone default timezone('utc'::text, now()) not null
);

-- Create analytics table
create table public.tour_analytics (
  id uuid default uuid_generate_v4() primary key,
  tour_id uuid references public.tours(id) on delete cascade not null,
  viewer_ip text,
  viewer_location text,
  viewed_at timestamp with time zone default timezone('utc'::text, now()) not null,
  time_spent_seconds integer default 0
);

-- Row Level Security (RLS)
alter table public.profiles enable row level security;
alter table public.tours enable row level security;
alter table public.scenes enable row level security;
alter table public.hotspots enable row level security;
alter table public.tour_analytics enable row level security;

-- RLS Policies
-- Profiles
create policy "Public profiles are viewable by everyone" on profiles for select using (true);
create policy "Users can insert their own profile" on profiles for insert with check (auth.uid() = id);
create policy "Users can update own profile" on profiles for update using (auth.uid() = id);

-- Tours
create policy "Published tours are viewable by everyone" on tours for select using (status = 'published' or auth.uid() = user_id);
create policy "Users can insert own tours" on tours for insert with check (auth.uid() = user_id);
create policy "Users can update own tours" on tours for update using (auth.uid() = user_id);
create policy "Users can delete own tours" on tours for delete using (auth.uid() = user_id);

-- Scenes
create policy "Scenes viewable if tour is accessible" on scenes for select using (
  exists (
    select 1 from tours 
    where tours.id = scenes.tour_id 
    and (tours.status = 'published' or tours.user_id = auth.uid())
  )
);
create policy "Users can manage scenes of own tours" on scenes for all using (
  exists (
    select 1 from tours 
    where tours.id = scenes.tour_id 
    and tours.user_id = auth.uid()
  )
);

-- Hotspots
create policy "Hotspots viewable if scene is accessible" on hotspots for select using (
  exists (
    select 1 from scenes 
    join tours on tours.id = scenes.tour_id
    where scenes.id = hotspots.scene_id 
    and (tours.status = 'published' or tours.user_id = auth.uid())
  )
);
create policy "Users can manage hotspots of own tours" on hotspots for all using (
  exists (
    select 1 from scenes 
    join tours on tours.id = scenes.tour_id
    where scenes.id = hotspots.scene_id 
    and tours.user_id = auth.uid()
  )
);

-- Analytics
create policy "Analytics viewable by tour owners" on tour_analytics for select using (
  exists (
    select 1 from tours 
    where tours.id = tour_analytics.tour_id 
    and tours.user_id = auth.uid()
  )
);
create policy "Anyone can insert analytics" on tour_analytics for insert with check (true);

-- Functions
create or replace function public.handle_new_user()
returns trigger as $$
begin
  insert into public.profiles (id, email, full_name)
  values (new.id, new.email, new.raw_user_meta_data->>'full_name');
  return new;
end;
$$ language plpgsql security definer;

-- Trigger for new user creation
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();

-- Function to increment tour views
create or replace function public.increment_tour_views(tour_uuid uuid)
returns void as $$
begin
  update public.tours 
  set views = views + 1 
  where id = tour_uuid;
end;
$$ language plpgsql security definer;
