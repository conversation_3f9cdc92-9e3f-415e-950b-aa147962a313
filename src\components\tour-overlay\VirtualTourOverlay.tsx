/**
 * Virtual Tour Overlay System
 * Universal overlay that works with CloudPano, WPVR, PhotoSphereViewer
 * Handles hotspot-triggered product overlays with WooCommerce integration
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ShoppingCart, 
  MessageCircle, 
  X, 
  Info, 
  MapPin,
  Loader2,
  ExternalLink,
  Star
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

// Import our components
import ProductOverlay from '@/components/commerce/ProductOverlay';
import { useShoppingCart } from '@/hooks/useShoppingCart';
import { wooCommerceService, type WooCommerceProduct } from '@/services/commerce/WooCommerceService';

interface HotspotData {
  id: string;
  productId?: string | number;
  type: 'product' | 'info' | 'navigation';
  title?: string;
  description?: string;
  position: {
    x: number; // Percentage from left
    y: number; // Percentage from top
  };
  metadata?: Record<string, any>;
}

interface VirtualTourOverlayProps {
  tourEngine: 'cloudpano' | 'wpvr' | 'photosphere' | 'custom';
  hotspots?: HotspotData[];
  onHotspotClick?: (hotspot: HotspotData) => void;
  className?: string;
  showCartIndicator?: boolean;
  enableWhatsAppCheckout?: boolean;
}

const VirtualTourOverlay: React.FC<VirtualTourOverlayProps> = ({
  tourEngine,
  hotspots = [],
  onHotspotClick,
  className = '',
  showCartIndicator = true,
  enableWhatsAppCheckout = true
}) => {
  const [selectedProduct, setSelectedProduct] = useState<WooCommerceProduct | null>(null);
  const [isProductLoading, setIsProductLoading] = useState(false);
  const [activeHotspot, setActiveHotspot] = useState<HotspotData | null>(null);
  const [hoveredHotspot, setHoveredHotspot] = useState<string | null>(null);

  const { items, totalItems, addItem } = useShoppingCart();

  // Handle hotspot click
  const handleHotspotClick = useCallback(async (hotspot: HotspotData) => {
    setActiveHotspot(hotspot);
    onHotspotClick?.(hotspot);

    if (hotspot.type === 'product' && hotspot.productId) {
      setIsProductLoading(true);
      
      try {
        const product = await wooCommerceService.getProduct(hotspot.productId);
        
        // Extract WhatsApp number from meta data
        const whatsappMeta = product.meta_data?.find(meta => 
          meta.key === 'vendor_whatsapp' || 
          meta.key === '_vendor_whatsapp' ||
          meta.key === 'whatsapp_number'
        );
        
        if (whatsappMeta) {
          product.vendor_whatsapp = whatsappMeta.value;
        }

        setSelectedProduct(product);
        toast.success('Product loaded successfully');
      } catch (error) {
        console.error('Error fetching product:', error);
        toast.error('Failed to load product information');
      } finally {
        setIsProductLoading(false);
      }
    }
  }, [onHotspotClick]);

  // Handle add to cart
  const handleAddToCart = useCallback((product: WooCommerceProduct, quantity: number = 1) => {
    addItem({
      id: product.id.toString(),
      name: product.name,
      price: parseFloat(product.price),
      image: product.images[0]?.src || '',
      quantity,
      vendor_whatsapp: product.vendor_whatsapp
    });
    
    toast.success(`Added ${quantity} ${product.name} to cart`);
    setSelectedProduct(null);
  }, [addItem]);

  // Hotspot component
  const HotspotMarker: React.FC<{ hotspot: HotspotData }> = ({ hotspot }) => {
    const isHovered = hoveredHotspot === hotspot.id;
    const isActive = activeHotspot?.id === hotspot.id;

    return (
      <motion.div
        className={cn(
          'absolute z-20 cursor-pointer transform -translate-x-1/2 -translate-y-1/2',
          'transition-all duration-300'
        )}
        style={{
          left: `${hotspot.position.x}%`,
          top: `${hotspot.position.y}%`,
        }}
        onClick={() => handleHotspotClick(hotspot)}
        onMouseEnter={() => setHoveredHotspot(hotspot.id)}
        onMouseLeave={() => setHoveredHotspot(null)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
      >
        {/* Hotspot pulse animation */}
        <div className="relative">
          {/* Pulse rings */}
          <div className={cn(
            'absolute inset-0 rounded-full animate-ping',
            hotspot.type === 'product' ? 'bg-blue-400' : 'bg-green-400',
            'opacity-75'
          )} />
          <div className={cn(
            'absolute inset-0 rounded-full animate-pulse',
            hotspot.type === 'product' ? 'bg-blue-400' : 'bg-green-400',
            'opacity-50'
          )} />
          
          {/* Main hotspot */}
          <div className={cn(
            'relative w-8 h-8 rounded-full flex items-center justify-center',
            'bg-white/90 backdrop-blur-sm border-2 shadow-lg',
            hotspot.type === 'product' ? 'border-blue-500' : 'border-green-500',
            isHovered && 'bg-white scale-110',
            isActive && 'ring-4 ring-white/50'
          )}>
            {hotspot.type === 'product' ? (
              <ShoppingCart className="w-4 h-4 text-blue-600" />
            ) : hotspot.type === 'info' ? (
              <Info className="w-4 h-4 text-green-600" />
            ) : (
              <MapPin className="w-4 h-4 text-purple-600" />
            )}
          </div>
        </div>

        {/* Hotspot tooltip */}
        <AnimatePresence>
          {isHovered && hotspot.title && (
            <motion.div
              initial={{ opacity: 0, y: 10, scale: 0.9 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: 10, scale: 0.9 }}
              className="absolute top-full mt-2 left-1/2 transform -translate-x-1/2 z-30"
            >
              <Card className="bg-black/80 backdrop-blur-sm border-white/20 text-white">
                <CardContent className="p-3 text-center">
                  <p className="text-sm font-medium whitespace-nowrap">
                    {hotspot.title}
                  </p>
                  {hotspot.description && (
                    <p className="text-xs text-gray-300 mt-1">
                      {hotspot.description}
                    </p>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    );
  };

  return (
    <div className={cn('absolute inset-0 pointer-events-none', className)}>
      {/* Hotspot markers */}
      <div className="absolute inset-0 pointer-events-auto">
        {hotspots.map((hotspot) => (
          <HotspotMarker key={hotspot.id} hotspot={hotspot} />
        ))}
      </div>

      {/* Shopping cart indicator */}
      {showCartIndicator && totalItems > 0 && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="absolute top-4 right-4 z-30 pointer-events-auto"
        >
          <Button
            variant="secondary"
            size="sm"
            className="bg-white/90 backdrop-blur-sm border-white/20 shadow-lg"
          >
            <ShoppingCart className="w-4 h-4 mr-2" />
            Cart ({totalItems})
          </Button>
        </motion.div>
      )}

      {/* Loading overlay for product fetch */}
      <AnimatePresence>
        {isProductLoading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 z-40 flex items-center justify-center bg-black/20 backdrop-blur-sm pointer-events-auto"
          >
            <Card className="bg-white/90 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <Loader2 className="w-6 h-6 animate-spin mx-auto mb-2 text-blue-600" />
                <p className="text-sm text-gray-600">Loading product...</p>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Product overlay */}
      <ProductOverlay
        product={selectedProduct}
        isOpen={!!selectedProduct}
        isLoading={isProductLoading}
        onClose={() => {
          setSelectedProduct(null);
          setActiveHotspot(null);
        }}
        onAddToCart={handleAddToCart}
      />

      {/* Info overlay for non-product hotspots */}
      <AnimatePresence>
        {activeHotspot && activeHotspot.type !== 'product' && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 z-40 flex items-center justify-center bg-black/40 backdrop-blur-sm pointer-events-auto"
            onClick={() => setActiveHotspot(null)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="relative max-w-md mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <Card className="bg-white/95 backdrop-blur-xl border-white/20 shadow-2xl">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {activeHotspot.title}
                      </h3>
                      <Badge variant="secondary" className="mt-1">
                        {activeHotspot.type}
                      </Badge>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setActiveHotspot(null)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                  
                  {activeHotspot.description && (
                    <p className="text-gray-600 leading-relaxed">
                      {activeHotspot.description}
                    </p>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default VirtualTourOverlay;
