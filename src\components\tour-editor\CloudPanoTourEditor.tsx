/**
 * CloudPano Tour Editor - Widget & API Integration
 * Primary CloudPano integration with widget fallback
 */

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ExternalLink,
  Copy,
  Check,
  AlertCircle,
  Play,
  Settings,
  Save,
  Sparkles,
  Code,
  Globe,
  RefreshCw,
  Upload,
  Eye,
  Share2,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';
import { cloudPanoService } from '@/services/tour-creation/cloudpano.service';
import { useCloudPanoSettings } from '@/hooks/useChatSettings';

interface CloudPanoTourEditorProps {
  tourId: string;
  tourData: {
    title: string;
    description: string;
    category: string;
    location: string;
    business_name?: string;
    business_phone?: string;
    business_email?: string;
    business_whatsapp?: string;
    business_address?: string;
    business_website?: string;
  };
  onSave?: (tourData: any) => void;
  onPublish?: (tourData: any) => void;
  onClose?: () => void;
  isAdmin?: boolean; // Add admin flag to control API mode visibility
}

const CloudPanoTourEditor = ({
  tourId,
  tourData,
  onSave,
  onPublish,
  onClose,
  isAdmin = false
}: CloudPanoTourEditorProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [mode, setMode] = useState<'widget' | 'api'>('widget');
  const [widgetEmbedCode, setWidgetEmbedCode] = useState('');
  const [cloudPanoTourId, setCloudPanoTourId] = useState('');
  const [cloudPanoEditUrl, setCloudPanoEditUrl] = useState('');
  const [isCopied, setIsCopied] = useState(false);
  const [isCreatingTour, setIsCreatingTour] = useState(false);
  const [approvalStatus, setApprovalStatus] = useState<'draft' | 'pending_approval' | 'approved' | 'rejected'>('draft');
  const widgetContainerRef = useRef<HTMLDivElement>(null);

  // Get CloudPano settings
  const { cloudPanoSettings } = useCloudPanoSettings();

  // Load saved data on mount
  useEffect(() => {
    const savedEmbedCode = localStorage.getItem(`tour-${tourId}-cloudpano-embed`);
    const savedTourId = localStorage.getItem(`tour-${tourId}-cloudpano-id`);
    const savedEditUrl = localStorage.getItem(`tour-${tourId}-cloudpano-edit-url`);
    const savedMode = localStorage.getItem(`tour-${tourId}-cloudpano-mode`) as 'widget' | 'api';
    
    if (savedEmbedCode) setWidgetEmbedCode(savedEmbedCode);
    if (savedTourId) setCloudPanoTourId(savedTourId);
    if (savedEditUrl) setCloudPanoEditUrl(savedEditUrl);
    if (savedMode) setMode(savedMode);

    setIsLoading(false);
  }, [tourId]);

  // Render widget when embed code changes
  useEffect(() => {
    if (widgetEmbedCode && widgetContainerRef.current && mode === 'widget') {
      renderWidget();
    }
  }, [widgetEmbedCode, mode]);

  const renderWidget = () => {
    if (!widgetContainerRef.current || !widgetEmbedCode) return;

    try {
      widgetContainerRef.current.innerHTML = '';
      
      const wrapper = document.createElement('div');
      wrapper.className = 'w-full h-full min-h-[600px] rounded-lg overflow-hidden';
      wrapper.innerHTML = widgetEmbedCode;
      
      widgetContainerRef.current.appendChild(wrapper);
      
      toast.success('CloudPano tour loaded successfully!');
    } catch (error) {
      console.error('Error rendering CloudPano widget:', error);
      toast.error('Failed to load CloudPano tour. Please check the embed code.');
    }
  };

  const handleEmbedCodeChange = (code: string) => {
    setWidgetEmbedCode(code);
    localStorage.setItem(`tour-${tourId}-cloudpano-embed`, code);
    
    // Extract CloudPano tour ID if possible
    const tourIdMatch = code.match(/tours\/([a-zA-Z0-9-_]+)/);
    if (tourIdMatch) {
      const extractedId = tourIdMatch[1];
      setCloudPanoTourId(extractedId);
      localStorage.setItem(`tour-${tourId}-cloudpano-id`, extractedId);
    }
  };

  const handleTourIdChange = (id: string) => {
    setCloudPanoTourId(id);
    localStorage.setItem(`tour-${tourId}-cloudpano-id`, id);
    
    if (id.trim()) {
      const generatedEmbed = `<iframe src="https://viewer.cloudpano.com/tours/${id}" width="100%" height="600" frameborder="0" allowfullscreen title="VirtualRealTour - ${tourData.title}"></iframe>`;
      setWidgetEmbedCode(generatedEmbed);
      localStorage.setItem(`tour-${tourId}-cloudpano-embed`, generatedEmbed);
    }
  };

  const createCloudPanoTour = async () => {
    if (!cloudPanoService.isConfigured) {
      toast.error('CloudPano API is not configured. Please use widget mode.');
      return;
    }

    setIsCreatingTour(true);
    try {
      const createdTour = await cloudPanoService.createTour({
        title: tourData.title,
        description: tourData.description,
        category: tourData.category,
        location: tourData.location,
        businessInfo: {
          name: tourData.business_name || '',
          type: '',
          phone: tourData.business_phone || '',
          email: tourData.business_email || '',
          website: tourData.business_website || '',
          hours: ''
        }
      });

      setCloudPanoTourId(createdTour.id);
      setCloudPanoEditUrl(createdTour.editUrl || '');
      setWidgetEmbedCode(createdTour.embedUrl);
      
      localStorage.setItem(`tour-${tourId}-cloudpano-id`, createdTour.id);
      localStorage.setItem(`tour-${tourId}-cloudpano-edit-url`, createdTour.editUrl || '');
      localStorage.setItem(`tour-${tourId}-cloudpano-embed`, createdTour.embedUrl);

      toast.success('CloudPano tour created successfully!');
    } catch (error) {
      console.error('Error creating CloudPano tour:', error);
      toast.error('Failed to create CloudPano tour. Please try widget mode.');
    } finally {
      setIsCreatingTour(false);
    }
  };

  const copyEmbedCode = async () => {
    if (!widgetEmbedCode) return;
    
    try {
      await navigator.clipboard.writeText(widgetEmbedCode);
      setIsCopied(true);
      toast.success('Embed code copied to clipboard!');
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy embed code');
    }
  };

  const handleSave = async () => {
    try {
      const tourSaveData = {
        id: tourId,
        cloudpano_tour_id: cloudPanoTourId,
        cloudpano_embed_url: widgetEmbedCode,
        cloudpano_edit_url: cloudPanoEditUrl,
        tour_platform: 'cloudpano',
        creation_method: mode,
        status: 'draft',
        approval_status: 'draft',
        updated_at: new Date().toISOString()
      };

      onSave?.(tourSaveData);
      toast.success('Tour saved successfully!');
    } catch (error) {
      console.error('Error saving tour:', error);
      toast.error('Failed to save tour');
    }
  };

  const handleSubmitForApproval = async () => {
    if (!widgetEmbedCode) {
      toast.error('Please add a CloudPano tour before submitting for approval');
      return;
    }

    try {
      const tourSubmitData = {
        id: tourId,
        cloudpano_tour_id: cloudPanoTourId,
        cloudpano_embed_url: widgetEmbedCode,
        cloudpano_edit_url: cloudPanoEditUrl,
        tour_platform: 'cloudpano',
        creation_method: mode,
        approval_status: 'pending_approval',
        submitted_for_approval_at: new Date().toISOString()
      };

      setApprovalStatus('pending_approval');
      onPublish?.(tourSubmitData);
      toast.success('Tour submitted for admin approval!');
    } catch (error) {
      console.error('Error submitting tour:', error);
      toast.error('Failed to submit tour for approval');
    }
  };

  const switchMode = (newMode: 'widget' | 'api') => {
    setMode(newMode);
    localStorage.setItem(`tour-${tourId}-cloudpano-mode`, newMode);
    toast.info(`Switched to ${newMode} mode`);
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading CloudPano editor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Sparkles className="h-5 w-5 text-primary" />
              <div>
                <CardTitle className="text-lg">{tourData.title}</CardTitle>
                <p className="text-sm text-muted-foreground">CloudPano 360° Tour Integration</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={mode === 'widget' ? 'default' : 'outline'}>
                {mode === 'widget' ? 'Widget Mode' : 'API Mode'}
              </Badge>
              <Badge variant={approvalStatus === 'approved' ? 'default' : 'secondary'}>
                {approvalStatus.replace('_', ' ').toUpperCase()}
              </Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="flex-1 flex flex-col lg:grid lg:grid-cols-3 gap-4">
        {/* Configuration Panel */}
        <Card className="w-full lg:col-span-1 order-2 lg:order-1">
          <CardHeader>
            <CardTitle className="text-base">CloudPano Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Mode Selection - API mode only for admins */}
            <Tabs value={mode} onValueChange={(value) => switchMode(value as 'widget' | 'api')}>
              {isAdmin ? (
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="widget">Widget</TabsTrigger>
                  <TabsTrigger value="api">API</TabsTrigger>
                </TabsList>
              ) : (
                <div className="text-sm text-muted-foreground mb-4">
                  Widget Mode - Paste your CloudPano embed code below
                </div>
              )}
              
              <TabsContent value="widget" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="embedCode">CloudPano Embed Code</Label>
                  <Textarea
                    id="embedCode"
                    placeholder="Paste your CloudPano tour embed code here..."
                    value={widgetEmbedCode}
                    onChange={(e) => handleEmbedCodeChange(e.target.value)}
                    rows={6}
                    className="font-mono text-xs"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="tourId">CloudPano Tour ID</Label>
                  <Input
                    id="tourId"
                    placeholder="Enter CloudPano tour ID..."
                    value={cloudPanoTourId}
                    onChange={(e) => handleTourIdChange(e.target.value)}
                  />
                </div>
              </TabsContent>
              
              <TabsContent value="api" className="space-y-4">
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">
                    Create a new CloudPano tour using the API integration.
                  </p>
                  <Button
                    onClick={createCloudPanoTour}
                    disabled={isCreatingTour || !cloudPanoService.isConfigured}
                    className="w-full"
                  >
                    {isCreatingTour ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Creating Tour...
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        Create CloudPano Tour
                      </>
                    )}
                  </Button>
                  
                  {cloudPanoEditUrl && (
                    <Button
                      onClick={() => window.open(cloudPanoEditUrl, '_blank')}
                      variant="outline"
                      className="w-full"
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Edit in CloudPano
                    </Button>
                  )}
                </div>
              </TabsContent>
            </Tabs>

            <Separator />

            <div className="space-y-2">
              <Button
                onClick={() => window.open(cloudPanoSettings?.createTourUrl || 'https://app.cloudpano.com/', '_blank')}
                className="w-full"
                variant="outline"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Create in CloudPano
              </Button>
              
              {widgetEmbedCode && (
                <Button
                  onClick={copyEmbedCode}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  {isCopied ? (
                    <Check className="h-4 w-4 mr-2" />
                  ) : (
                    <Copy className="h-4 w-4 mr-2" />
                  )}
                  {isCopied ? 'Copied!' : 'Copy Embed Code'}
                </Button>
              )}
              
              <div className="flex gap-2">
                <Button onClick={handleSave} size="sm" variant="outline" className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </Button>
                <Button 
                  onClick={handleSubmitForApproval} 
                  size="sm" 
                  className="flex-1"
                  disabled={approvalStatus === 'pending_approval'}
                >
                  <Share2 className="h-4 w-4 mr-2" />
                  {approvalStatus === 'pending_approval' ? 'Pending' : 'Submit'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tour Preview */}
        <Card className="w-full lg:col-span-2 order-1 lg:order-2">
          <CardHeader>
            <CardTitle className="text-base">Tour Preview</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {widgetEmbedCode ? (
              <div
                ref={widgetContainerRef}
                className="w-full h-full min-h-[600px] bg-gray-50 rounded-lg"
              />
            ) : (
              <div className="h-[600px] flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center max-w-md p-8">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No CloudPano Tour</h3>
                  <p className="text-muted-foreground mb-4">
                    Create a CloudPano tour or add an embed code to see the preview.
                  </p>
                  <Button
                    onClick={() => window.open(cloudPanoSettings?.createTourUrl || 'https://app.cloudpano.com/', '_blank')}
                    variant="outline"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Create CloudPano Tour
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CloudPanoTourEditor;
