
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://dnbjrfgfugpmyrconepx.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRuYmpyZmdmdWdwbXlyY29uZXB4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjY0ODUsImV4cCI6MjA2NDc0MjQ4NX0.Vo2qoT6Io3BZdaR4uWA7C3eZ19sg4y-zZYod73e3Bgs";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  }
});
