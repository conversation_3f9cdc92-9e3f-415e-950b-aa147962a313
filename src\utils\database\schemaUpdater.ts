/**
 * Database Schema Updater
 * Utility to apply schema updates for the streamlined VirtualRealTour architecture
 */

import { supabase } from '@/lib/supabase';

export interface SchemaUpdateResult {
  success: boolean;
  message: string;
  errors?: string[];
  appliedUpdates?: string[];
}

export class SchemaUpdater {
  private appliedUpdates: string[] = [];
  private errors: string[] = [];

  /**
   * Apply all schema updates for the streamlined architecture
   */
  async applyStreamlinedSchemaUpdates(): Promise<SchemaUpdateResult> {
    try {
      console.log('Starting schema updates for streamlined VirtualRealTour architecture...');

      // Check if we have admin privileges
      const hasAdminAccess = await this.checkAdminAccess();
      if (!hasAdminAccess) {
        return {
          success: false,
          message: 'Admin access required to apply schema updates'
        };
      }

      // Apply updates in order
      await this.updateToursTable();
      await this.updateVendorsTable();
      await this.createOverlayTemplatesTable();
      await this.createTourHotspotsTable();
      await this.updateProductsTable();
      await this.updateOrdersTable();
      await this.createOrderItemsTable();
      await this.createAdminSettingsTable();
      await this.createWhatsAppMessagesTable();
      await this.createTourAnalyticsTable();
      await this.createPerformanceViews();
      await this.setupRowLevelSecurity();
      await this.insertDefaultSettings();

      return {
        success: true,
        message: `Schema updates completed successfully. Applied ${this.appliedUpdates.length} updates.`,
        appliedUpdates: this.appliedUpdates,
        errors: this.errors.length > 0 ? this.errors : undefined
      };
    } catch (error) {
      console.error('Error applying schema updates:', error);
      return {
        success: false,
        message: 'Failed to apply schema updates',
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        appliedUpdates: this.appliedUpdates
      };
    }
  }

  /**
   * Check if current user has admin access
   */
  private async checkAdminAccess(): Promise<boolean> {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', (await supabase.auth.getUser()).data.user?.id)
        .single();

      if (error) {
        console.warn('Could not verify admin access:', error);
        return false;
      }

      return profile?.role === 'admin';
    } catch (error) {
      console.warn('Error checking admin access:', error);
      return false;
    }
  }

  /**
   * Update tours table for tour-centric architecture
   */
  private async updateToursTable(): Promise<void> {
    try {
      const updates = [
        'ALTER TABLE tours ADD COLUMN IF NOT EXISTS platform_type VARCHAR(50) DEFAULT \'custom\'',
        'ALTER TABLE tours ADD COLUMN IF NOT EXISTS source_platform_id VARCHAR(255)',
        'ALTER TABLE tours ADD COLUMN IF NOT EXISTS embed_config JSONB DEFAULT \'{}\'',
        'ALTER TABLE tours ADD COLUMN IF NOT EXISTS overlay_settings JSONB DEFAULT \'{}\'',
        'ALTER TABLE tours ADD COLUMN IF NOT EXISTS seo_metadata JSONB DEFAULT \'{}\'',
        'ALTER TABLE tours ADD COLUMN IF NOT EXISTS analytics_data JSONB DEFAULT \'{}\'',
        'ALTER TABLE tours ADD COLUMN IF NOT EXISTS featured_priority INTEGER DEFAULT 0',
        'ALTER TABLE tours ADD COLUMN IF NOT EXISTS auto_approved BOOLEAN DEFAULT false'
      ];

      for (const update of updates) {
        await this.executeSQL(update);
      }

      // Create indexes
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_tours_platform_type ON tours(platform_type)',
        'CREATE INDEX IF NOT EXISTS idx_tours_featured ON tours(featured, featured_priority)',
        'CREATE INDEX IF NOT EXISTS idx_tours_status_created ON tours(status, created_at)'
      ];

      for (const index of indexes) {
        await this.executeSQL(index);
      }

      this.appliedUpdates.push('Updated tours table with tour-centric fields');
    } catch (error) {
      this.errors.push(`Error updating tours table: ${error}`);
    }
  }

  /**
   * Update vendors table for enhanced vendor management
   */
  private async updateVendorsTable(): Promise<void> {
    try {
      const updates = [
        'ALTER TABLE vendors ADD COLUMN IF NOT EXISTS business_type VARCHAR(100)',
        'ALTER TABLE vendors ADD COLUMN IF NOT EXISTS commission_rate DECIMAL(5,4) DEFAULT 0.05',
        'ALTER TABLE vendors ADD COLUMN IF NOT EXISTS auto_approve_tours BOOLEAN DEFAULT false',
        'ALTER TABLE vendors ADD COLUMN IF NOT EXISTS whatsapp_number VARCHAR(20)',
        'ALTER TABLE vendors ADD COLUMN IF NOT EXISTS business_address TEXT',
        'ALTER TABLE vendors ADD COLUMN IF NOT EXISTS business_description TEXT',
        'ALTER TABLE vendors ADD COLUMN IF NOT EXISTS verification_status VARCHAR(50) DEFAULT \'pending\'',
        'ALTER TABLE vendors ADD COLUMN IF NOT EXISTS verification_documents JSONB DEFAULT \'[]\'',
        'ALTER TABLE vendors ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT \'{}\'',
        'ALTER TABLE vendors ADD COLUMN IF NOT EXISTS performance_metrics JSONB DEFAULT \'{}\''
      ];

      for (const update of updates) {
        await this.executeSQL(update);
      }

      this.appliedUpdates.push('Updated vendors table with enhanced fields');
    } catch (error) {
      this.errors.push(`Error updating vendors table: ${error}`);
    }
  }

  /**
   * Create overlay templates table
   */
  private async createOverlayTemplatesTable(): Promise<void> {
    try {
      const createTable = `
        CREATE TABLE IF NOT EXISTS overlay_templates (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name VARCHAR(255) NOT NULL,
          description TEXT,
          template_type VARCHAR(100) NOT NULL,
          style_config JSONB NOT NULL DEFAULT '{}',
          animation_config JSONB DEFAULT '{}',
          icon_config JSONB DEFAULT '{}',
          is_system_template BOOLEAN DEFAULT false,
          created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
      `;

      await this.executeSQL(createTable);
      this.appliedUpdates.push('Created overlay_templates table');
    } catch (error) {
      this.errors.push(`Error creating overlay_templates table: ${error}`);
    }
  }

  /**
   * Create tour hotspots table
   */
  private async createTourHotspotsTable(): Promise<void> {
    try {
      const createTable = `
        CREATE TABLE IF NOT EXISTS tour_hotspots (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tour_id UUID NOT NULL REFERENCES tours(id) ON DELETE CASCADE,
          scene_id VARCHAR(255),
          position JSONB NOT NULL,
          hotspot_type VARCHAR(100) NOT NULL,
          title VARCHAR(255),
          description TEXT,
          icon_config JSONB DEFAULT '{}',
          animation_config JSONB DEFAULT '{}',
          overlay_template_id UUID REFERENCES overlay_templates(id) ON DELETE SET NULL,
          product_id UUID REFERENCES products(id) ON DELETE SET NULL,
          action_config JSONB DEFAULT '{}',
          visibility_rules JSONB DEFAULT '{}',
          analytics_data JSONB DEFAULT '{}',
          is_active BOOLEAN DEFAULT true,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
      `;

      await this.executeSQL(createTable);

      // Create indexes
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_hotspots_tour_id ON tour_hotspots(tour_id)',
        'CREATE INDEX IF NOT EXISTS idx_hotspots_type ON tour_hotspots(hotspot_type)',
        'CREATE INDEX IF NOT EXISTS idx_hotspots_product_id ON tour_hotspots(product_id)'
      ];

      for (const index of indexes) {
        await this.executeSQL(index);
      }

      this.appliedUpdates.push('Created tour_hotspots table');
    } catch (error) {
      this.errors.push(`Error creating tour_hotspots table: ${error}`);
    }
  }

  /**
   * Update products table for better tour integration
   */
  private async updateProductsTable(): Promise<void> {
    try {
      const updates = [
        'ALTER TABLE products ADD COLUMN IF NOT EXISTS tour_context JSONB DEFAULT \'{}\'',
        'ALTER TABLE products ADD COLUMN IF NOT EXISTS overlay_config JSONB DEFAULT \'{}\'',
        'ALTER TABLE products ADD COLUMN IF NOT EXISTS whatsapp_config JSONB DEFAULT \'{}\'',
        'ALTER TABLE products ADD COLUMN IF NOT EXISTS seo_metadata JSONB DEFAULT \'{}\''
      ];

      for (const update of updates) {
        await this.executeSQL(update);
      }

      this.appliedUpdates.push('Updated products table with tour integration fields');
    } catch (error) {
      this.errors.push(`Error updating products table: ${error}`);
    }
  }

  /**
   * Update orders table for unified commerce
   */
  private async updateOrdersTable(): Promise<void> {
    try {
      const updates = [
        'ALTER TABLE orders ADD COLUMN IF NOT EXISTS tour_context JSONB DEFAULT \'{}\'',
        'ALTER TABLE orders ADD COLUMN IF NOT EXISTS whatsapp_data JSONB DEFAULT \'{}\'',
        'ALTER TABLE orders ADD COLUMN IF NOT EXISTS fulfillment_data JSONB DEFAULT \'{}\'',
        'ALTER TABLE orders ADD COLUMN IF NOT EXISTS analytics_data JSONB DEFAULT \'{}\''
      ];

      for (const update of updates) {
        await this.executeSQL(update);
      }

      this.appliedUpdates.push('Updated orders table with unified commerce fields');
    } catch (error) {
      this.errors.push(`Error updating orders table: ${error}`);
    }
  }

  /**
   * Create order items table
   */
  private async createOrderItemsTable(): Promise<void> {
    try {
      const createTable = `
        CREATE TABLE IF NOT EXISTS order_items (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
          product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
          quantity INTEGER NOT NULL DEFAULT 1,
          unit_price DECIMAL(10,2) NOT NULL,
          total_price DECIMAL(10,2) NOT NULL,
          tour_context JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
      `;

      await this.executeSQL(createTable);

      // Create indexes
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id)',
        'CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id)'
      ];

      for (const index of indexes) {
        await this.executeSQL(index);
      }

      this.appliedUpdates.push('Created order_items table');
    } catch (error) {
      this.errors.push(`Error creating order_items table: ${error}`);
    }
  }

  /**
   * Create admin settings table
   */
  private async createAdminSettingsTable(): Promise<void> {
    try {
      const createTable = `
        CREATE TABLE IF NOT EXISTS admin_settings (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          key VARCHAR(255) UNIQUE NOT NULL,
          value JSONB NOT NULL,
          description TEXT,
          category VARCHAR(100) DEFAULT 'general',
          is_public BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
      `;

      await this.executeSQL(createTable);
      this.appliedUpdates.push('Created admin_settings table');
    } catch (error) {
      this.errors.push(`Error creating admin_settings table: ${error}`);
    }
  }

  /**
   * Create WhatsApp messages table
   */
  private async createWhatsAppMessagesTable(): Promise<void> {
    try {
      const createTable = `
        CREATE TABLE IF NOT EXISTS whatsapp_messages (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          to_number VARCHAR(20) NOT NULL,
          from_number VARCHAR(20),
          message_content TEXT NOT NULL,
          message_type VARCHAR(50) DEFAULT 'text',
          template_name VARCHAR(100),
          status VARCHAR(50) DEFAULT 'pending',
          external_id VARCHAR(255),
          order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
          sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          delivered_at TIMESTAMP WITH TIME ZONE,
          read_at TIMESTAMP WITH TIME ZONE,
          error_message TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
      `;

      await this.executeSQL(createTable);

      // Create indexes
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_to_number ON whatsapp_messages(to_number)',
        'CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_order_id ON whatsapp_messages(order_id)',
        'CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_status ON whatsapp_messages(status)'
      ];

      for (const index of indexes) {
        await this.executeSQL(index);
      }

      this.appliedUpdates.push('Created whatsapp_messages table');
    } catch (error) {
      this.errors.push(`Error creating whatsapp_messages table: ${error}`);
    }
  }

  /**
   * Create tour analytics table
   */
  private async createTourAnalyticsTable(): Promise<void> {
    try {
      const createTable = `
        CREATE TABLE IF NOT EXISTS tour_analytics (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tour_id UUID NOT NULL REFERENCES tours(id) ON DELETE CASCADE,
          event_type VARCHAR(100) NOT NULL,
          event_data JSONB DEFAULT '{}',
          user_session VARCHAR(255),
          user_agent TEXT,
          ip_address INET,
          referrer TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        )
      `;

      await this.executeSQL(createTable);

      // Create indexes
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_tour_analytics_tour_id ON tour_analytics(tour_id)',
        'CREATE INDEX IF NOT EXISTS idx_tour_analytics_event_type ON tour_analytics(event_type)',
        'CREATE INDEX IF NOT EXISTS idx_tour_analytics_created_at ON tour_analytics(created_at)'
      ];

      for (const index of indexes) {
        await this.executeSQL(index);
      }

      this.appliedUpdates.push('Created tour_analytics table');
    } catch (error) {
      this.errors.push(`Error creating tour_analytics table: ${error}`);
    }
  }

  /**
   * Create performance views
   */
  private async createPerformanceViews(): Promise<void> {
    try {
      // Note: Views might need to be created through direct SQL execution
      // This is a placeholder for the view creation logic
      this.appliedUpdates.push('Performance views creation queued (requires direct SQL execution)');
    } catch (error) {
      this.errors.push(`Error creating performance views: ${error}`);
    }
  }

  /**
   * Setup Row Level Security
   */
  private async setupRowLevelSecurity(): Promise<void> {
    try {
      // Note: RLS policies might need to be created through direct SQL execution
      // This is a placeholder for the RLS setup logic
      this.appliedUpdates.push('Row Level Security setup queued (requires direct SQL execution)');
    } catch (error) {
      this.errors.push(`Error setting up RLS: ${error}`);
    }
  }

  /**
   * Insert default settings
   */
  private async insertDefaultSettings(): Promise<void> {
    try {
      const defaultSettings = [
        {
          key: 'site_config',
          value: {
            title: 'VirtualRealTour Nigeria',
            description: 'Experience Nigeria through immersive virtual tours',
            contact_email: '<EMAIL>'
          },
          description: 'Basic site configuration',
          category: 'general',
          is_public: true
        },
        {
          key: 'whatsapp_config',
          value: {
            business_number: '+234XXXXXXXXXX',
            enabled: true,
            auto_respond: false
          },
          description: 'WhatsApp commerce configuration',
          category: 'commerce',
          is_public: false
        },
        {
          key: 'tour_defaults',
          value: {
            auto_approve_vendor_tours: false,
            featured_limit: 6,
            demo_tours_enabled: true
          },
          description: 'Default tour settings',
          category: 'tours',
          is_public: false
        }
      ];

      for (const setting of defaultSettings) {
        const { error } = await supabase
          .from('admin_settings')
          .upsert(setting, { onConflict: 'key' });

        if (error) {
          this.errors.push(`Error inserting setting ${setting.key}: ${error.message}`);
        }
      }

      this.appliedUpdates.push('Inserted default admin settings');
    } catch (error) {
      this.errors.push(`Error inserting default settings: ${error}`);
    }
  }

  /**
   * Execute SQL command
   */
  private async executeSQL(sql: string): Promise<void> {
    const { error } = await supabase.rpc('execute_sql', { sql_query: sql });
    if (error) {
      throw new Error(`SQL execution failed: ${error.message}`);
    }
  }
}

export const schemaUpdater = new SchemaUpdater();
