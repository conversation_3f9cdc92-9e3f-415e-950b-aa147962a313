
import { useState, useCallback, useMemo } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { ExtendedFormData, SceneConfig, HotspotConfig, getTourCreationService, TourPlatform } from '@/services/tour-creation';

export interface FormData extends ExtendedFormData {
  // Keep existing interface for backward compatibility
}

export const useUploadForm = (
  onSuccess?: () => void,
  onClose?: () => void,
  initialData?: {
    title?: string;
    description?: string;
    category?: string;
    location?: string;
    businessInfo?: any;
    tourPlatform?: 'commonninja' | 'custom';
  }
) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();
  
  const [formData, setFormData] = useState<FormData>({
    title: initialData?.title || '',
    description: initialData?.description || '',
    category: initialData?.category || '',
    location: initialData?.location || '',
    businessName: initialData?.businessInfo?.name || '',
    businessType: initialData?.businessInfo?.type || '',
    contactPhone: initialData?.businessInfo?.phone || '',
    contactEmail: initialData?.businessInfo?.email || '',
    website: initialData?.businessInfo?.website || '',
    businessHours: initialData?.businessInfo?.hours || '',
    files: [],
    embedUrl: '',
    embedType: '',
    // New native tour creation fields
    creationMethod: 'upload',
    tourPlatform: initialData?.tourPlatform || 'commonninja',
    nativeTourConfig: {
      enableHotspots: true,
      enableEcommerce: false,
      autoRotate: false,
      showControls: true,
      enableVR: true,
      customBranding: false,
    },
    scenes: [],
    hotspots: {},
  });

  const handleNext = useCallback(() => {
    if (currentStep < 6) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep]);

  const handlePrev = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const handleSubmit = async () => {
    if (!user) {
      toast.error('You must be logged in to create a tour');
      return;
    }

    setIsSubmitting(true);

    try {
      let thumbnailUrl = null;
      let uploadedScenes = [];
      let nativeTourData = null;

      // Handle CommonNinja tour creation (when uploading 360° images)
      if (formData.tourPlatform === 'commonninja' && formData.files.length > 0) {
        try {
          const tourService = getTourCreationService(formData.tourPlatform);

          // Create tour on the platform
          const createdTour = await tourService.createTour({
            title: formData.title,
            description: formData.description,
            category: formData.category,
            location: formData.location,
            businessInfo: {
              name: formData.businessName,
              type: formData.businessType,
              phone: formData.contactPhone,
              email: formData.contactEmail,
              website: formData.website,
              hours: formData.businessHours,
            },
            settings: formData.nativeTourConfig,
          });

          // Add scenes to the tour
          for (let i = 0; i < formData.files.length; i++) {
            const file = formData.files[i];
            const scene = await tourService.addScene(createdTour.id, {
              name: `Scene ${i + 1}`,
              description: `360° view from ${file.name}`,
              imageFile: file,
              orderIndex: i,
            });

            if (i === 0) {
              thumbnailUrl = scene.imageUrl;
            }

            uploadedScenes.push({
              name: scene.name,
              description: scene.description,
              image_url: scene.imageUrl,
              order_index: scene.orderIndex,
              platform_scene_id: scene.id,
            });
          }

          nativeTourData = {
            platform_tour_id: createdTour.id,
            embed_url: createdTour.embedUrl,
            edit_url: createdTour.editUrl,
            preview_url: createdTour.previewUrl,
            platform: createdTour.platform,
            platform_specific: createdTour.platformSpecific,
          };

          toast.success(`Tour created successfully with CommonNinja integration!`);
        } catch (error) {
          console.error('CommonNinja tour creation error:', error);
          toast.error(`Failed to create enhanced tour. Falling back to basic upload.`);
          // Fall back to regular file upload
          formData.tourPlatform = 'custom';
        }
      }

      // Handle regular file uploads (fallback or when not using CommonNinja)
      if (formData.tourPlatform !== 'commonninja' && formData.files.length > 0) {
        for (let i = 0; i < formData.files.length; i++) {
          const file = formData.files[i];
          const fileExt = file.name.split('.').pop();
          const fileName = `${user.id}/${Date.now()}-${i}.${fileExt}`;

          const { error: uploadError } = await supabase.storage
            .from('tour-media')
            .upload(fileName, file);

          if (uploadError) {
            console.error('Upload error:', uploadError);
            continue;
          }

          const { data: { publicUrl } } = supabase.storage
            .from('tour-media')
            .getPublicUrl(fileName);

          if (i === 0 && !thumbnailUrl) {
            thumbnailUrl = publicUrl;
          }

          uploadedScenes.push({
            name: `Scene ${i + 1}`,
            description: `360° view from ${file.name}`,
            image_url: publicUrl,
            order_index: i
          });
        }
      }

      // Create tour in database with business information
      const tourData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        location: formData.location,
        business_name: formData.businessName || null,
        business_type: formData.businessType || null,
        contact_phone: formData.contactPhone || null,
        contact_email: formData.contactEmail || null,
        website: formData.website || null,
        business_hours: formData.businessHours || null,
        thumbnail_url: thumbnailUrl,
        embed_url: nativeTourData?.embed_url || formData.embedUrl || null,
        embed_type: formData.embedType || null,
        user_id: user.id,
        status: 'draft',
        scenes_count: Math.max(uploadedScenes.length, formData.embedUrl ? 1 : 0),
        // Native tour creation fields
        creation_method: formData.creationMethod,
        tour_platform: formData.tourPlatform || null,
        platform_tour_id: nativeTourData?.platform_tour_id || null,
        platform_specific: nativeTourData?.platform_specific || null,
        tour_config: {
          ...formData.nativeTourConfig,
          businessInfo: {
            name: formData.businessName,
            type: formData.businessType,
            phone: formData.contactPhone,
            email: formData.contactEmail,
            website: formData.website,
            hours: formData.businessHours,
          }
        }
      };

      const { data: tour, error: tourError } = await supabase
        .from('tours')
        .insert(tourData)
        .select()
        .single();

      if (tourError) {
        throw tourError;
      }

      // Create scenes for uploaded files
      if (uploadedScenes.length > 0) {
        const scenesWithTourId = uploadedScenes.map(scene => ({
          ...scene,
          tour_id: tour.id
        }));

        const { error: scenesError } = await supabase
          .from('scenes')
          .insert(scenesWithTourId);

        if (scenesError) {
          console.error('Scenes error:', scenesError);
        }
      }

      toast.success('Tour created successfully! It will be reviewed before publishing.');
      
      resetForm();
      onSuccess?.();
      onClose?.();
      
    } catch (error) {
      console.error('Error creating tour:', error);
      toast.error('Failed to create tour. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setCurrentStep(1);
    setFormData({
      title: '',
      description: '',
      category: '',
      location: '',
      businessName: '',
      businessType: '',
      contactPhone: '',
      contactEmail: '',
      website: '',
      businessHours: '',
      files: [],
      embedUrl: '',
      embedType: '',
      // Reset native tour creation fields
      creationMethod: 'upload',
      tourPlatform: 'commonninja',
      nativeTourConfig: {
        enableHotspots: true,
        enableEcommerce: false,
        autoRotate: false,
        showControls: true,
        enableVR: true,
        customBranding: false,
      },
      scenes: [],
      hotspots: {},
    });
  };

  const canProceed = useMemo(() => {
    switch (currentStep) {
      case 1:
        return formData.title && formData.category && formData.location;
      case 2:
        return formData.tourPlatform; // Platform must be selected
      case 3:
        // For 360° images, we need files
        if (formData.tourPlatform === 'commonninja' || formData.tourPlatform === 'custom') {
          return formData.files.length > 0;
        }
        // For embed method, we need embed URL
        return formData.embedUrl && formData.embedType;
      case 4:
        return true; // Review step
      case 5:
        return true; // Editor step
      case 6:
        return formData.files.length > 0 || (formData.embedUrl && formData.embedType);
      default:
        return false;
    }
  }, [currentStep, formData.title, formData.category, formData.location, formData.tourPlatform, formData.files.length, formData.embedUrl, formData.embedType]);

  return {
    currentStep,
    setCurrentStep,
    formData,
    setFormData,
    isSubmitting,
    handleNext,
    handlePrev,
    handleSubmit,
    resetForm,
    canProceed: () => canProceed
  };
};
