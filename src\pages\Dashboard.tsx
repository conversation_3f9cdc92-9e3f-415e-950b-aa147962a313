
import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Upload,
  Plus,
  Eye,
  BarChart3,
  MapPin,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Globe,
  Edit,
  ExternalLink
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import UserLayout from '@/components/user/UserLayout';
import StreamlinedTourCreationModal from '@/components/user/StreamlinedTourCreationModal';
import EnhancedTourCard from '@/components/EnhancedTourCard';
import { getTourCardSettings } from '@/config/tourCardSettings';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { supabase, Tour } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';

const Dashboard = () => {
  const [isTourCreationOpen, setIsTourCreationOpen] = useState(false);
  const { user, profile } = useAuth();
  const navigate = useNavigate();

  // Check if user is admin
  const isAdmin = profile?.role === 'admin';

  // Fetch tours (all for admin, user's own for regular users)
  const { data: tours = [], isLoading: toursLoading, refetch: refetchTours } = useQuery({
    queryKey: ['user-tours', user?.id, isAdmin],
    queryFn: async () => {
      if (!user?.id) return [];

      let query = supabase
        .from('tours')
        .select(`
          *,
          profiles (
            full_name,
            email
          )
        `);

      // Admin sees all tours, regular users see only their own
      if (!isAdmin) {
        query = query.eq('user_id', user.id);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching tours:', error);
        throw error;
      }

      return data as Tour[];
    },
    enabled: !!user?.id,
  });

  // Fetch analytics data (all for admin, user's own for regular users)
  const { data: analytics } = useQuery({
    queryKey: ['user-analytics', user?.id, isAdmin],
    queryFn: async () => {
      if (!user?.id) return null;

      let query = supabase
        .from('tour_analytics')
        .select(`
          *,
          tours!inner (
            user_id,
            title
          )
        `);

      // Admin sees all analytics, regular users see only their own
      if (!isAdmin) {
        query = query.eq('tours.user_id', user.id);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching analytics:', error);
        return null;
      }

      return data;
    },
    enabled: !!user?.id,
  });

  const handleEditTour = (tour: Tour) => {
    // Navigate to tour editor based on platform
    if (tour.tour_platform === 'commonninja') {
      navigate(`/dashboard/tours/${tour.id}/edit`);
    } else {
      toast.info('Custom tour editing coming soon!');
    }
  };

  const handleViewTour = (tour: Tour) => {
    if (tour.status === 'published') {
      window.open(`/tour/${tour.slug}`, '_blank');
    } else {
      toast.info('Tour must be published to view publicly');
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800 border-green-200"><CheckCircle className="w-3 h-3 mr-1" />Published</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200"><Clock className="w-3 h-3 mr-1" />Pending Approval</Badge>;
      case 'draft':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200"><Edit className="w-3 h-3 mr-1" />Draft</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const handleDeleteTour = async (tour: Tour) => {
    if (!confirm('Are you sure you want to delete this tour?')) return;
    
    try {
      const { error } = await supabase
        .from('tours')
        .delete()
        .eq('id', tour.id);

      if (error) throw error;
      
      toast.success('Tour deleted successfully');
      refetchTours();
    } catch (error) {
      console.error('Error deleting tour:', error);
      toast.error('Failed to delete tour');
    }
  };

  const stats = [
    { 
      label: "Total Tours", 
      value: tours.length.toString(), 
      icon: MapPin,
      color: "text-blue-600"
    },
    { 
      label: "Total Views", 
      value: tours.reduce((sum, tour) => sum + tour.views, 0).toLocaleString(), 
      icon: Eye,
      color: "text-green-600"
    },
    { 
      label: "This Month", 
      value: analytics?.length.toString() || "0", 
      icon: TrendingUp,
      color: "text-purple-600"
    },
    { 
      label: "Published", 
      value: tours.filter(tour => tour.status === 'published').length.toString(), 
      icon: BarChart3,
      color: "text-orange-600"
    }
  ];

  if (toursLoading) {
    return (
      <UserLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" />
        </div>
      </UserLayout>
    );
  }

  return (
    <UserLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-xl md:text-2xl lg:text-3xl font-bold text-foreground">Dashboard</h1>
              {isAdmin && (
                <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
                  Admin View - All Data
                </Badge>
              )}
            </div>
            <p className="text-sm md:text-base text-muted-foreground mt-1">
              Manage your virtual tours and track performance
            </p>
          </div>
          <Button
            onClick={() => setIsTourCreationOpen(true)}
            className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="w-4 h-4 mr-2" />
            <span className="sm:hidden">Create Tour</span>
            <span className="hidden sm:inline">Create New Tour</span>
          </Button>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
          {stats.map((stat, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow duration-200">
              <CardContent className="p-4 md:p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-xs md:text-sm font-medium text-muted-foreground">{stat.label}</p>
                    <p className="text-xl md:text-2xl font-bold text-foreground">{stat.value}</p>
                  </div>
                  <stat.icon className={`${stat.color} w-6 h-6 md:w-8 md:h-8`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <Tabs defaultValue="tours" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="tours" className="text-xs sm:text-sm">
              <span className="hidden sm:inline">My Tours ({tours.length})</span>
              <span className="sm:hidden">Tours ({tours.length})</span>
            </TabsTrigger>
            <TabsTrigger value="explore" className="text-xs sm:text-sm">Explore</TabsTrigger>
            <TabsTrigger value="analytics" className="text-xs sm:text-sm">Analytics</TabsTrigger>
            <TabsTrigger value="settings" className="text-xs sm:text-sm">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="tours" className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
              <h2 className="text-lg sm:text-xl font-semibold">Your Virtual Tours</h2>
              <Button
                variant="outline"
                onClick={() => setIsTourCreationOpen(true)}
                className="w-full sm:w-auto"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create New
              </Button>
            </div>
            
            {tours.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <MapPin className="w-16 h-16 mx-auto mb-4 text-gray-400" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No tours yet</h3>
                  <p className="text-gray-600 mb-4">Create your first virtual tour to get started</p>
                  <Button onClick={() => setIsTourCreationOpen(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Create Your First Tour
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {tours.map((tour) => (
                  <Card key={tour.id} className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold">{tour.title}</h3>
                            {getStatusBadge(tour.status)}
                          </div>
                          <p className="text-muted-foreground text-sm mb-2">{tour.description}</p>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <MapPin className="w-4 h-4" />
                              {tour.location}
                            </span>
                            <span className="flex items-center gap-1">
                              <Eye className="w-4 h-4" />
                              {tour.views} views
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditTour(tour)}
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            Edit
                          </Button>
                          {tour.status === 'published' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewTour(tour)}
                            >
                              <ExternalLink className="w-4 h-4 mr-1" />
                              View
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="explore" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Explore Tours</CardTitle>
                <CardDescription>Discover amazing virtual tours from other creators</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12">
                  <Globe className="w-16 h-16 mx-auto mb-4 text-blue-500" />
                  <h3 className="text-lg font-semibold mb-2">Coming Soon</h3>
                  <p className="text-muted-foreground mb-4">
                    Browse and discover virtual tours from our community
                  </p>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/showcase')}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Visit Showcase
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Tour Performance</CardTitle>
                <CardDescription>View insights for your virtual tours</CardDescription>
              </CardHeader>
              <CardContent>
                {analytics && analytics.length > 0 ? (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                      <Card className="bg-primary/5 border-primary/20">
                        <CardContent className="p-4">
                          <h4 className="font-semibold text-primary">Total Views</h4>
                          <p className="text-2xl font-bold text-primary">
                            {tours.reduce((sum, tour) => sum + tour.views, 0)}
                          </p>
                        </CardContent>
                      </Card>
                      <Card className="bg-green-50 border-green-200">
                        <CardContent className="p-4">
                          <h4 className="font-semibold text-green-700">Unique Visitors</h4>
                          <p className="text-2xl font-bold text-green-600">{analytics.length}</p>
                        </CardContent>
                      </Card>
                      <Card className="bg-purple-50 border-purple-200">
                        <CardContent className="p-4">
                          <h4 className="font-semibold text-purple-700">Avg. Time Spent</h4>
                          <p className="text-2xl font-bold text-purple-600">
                            {Math.round(analytics.reduce((sum, a) => sum + (a.time_spent_seconds || 0), 0) / analytics.length)}s
                          </p>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12 text-gray-500">
                    <BarChart3 className="w-16 h-16 mx-auto mb-4 opacity-50" />
                    <p>No analytics data available yet</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Account Settings</CardTitle>
                <CardDescription>Manage your account preferences and profile</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-center py-12 text-gray-500">
                  <p>Settings panel coming soon</p>
                  <p className="text-sm mt-2">Profile management, notifications, and more</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

        <StreamlinedTourCreationModal
          open={isTourCreationOpen}
          onOpenChange={setIsTourCreationOpen}
          onSuccess={refetchTours}
        />
      </div>
    </UserLayout>
  );
};

export default Dashboard;
