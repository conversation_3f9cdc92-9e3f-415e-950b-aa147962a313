/**
 * Order Tracking Page
 * Customer order tracking interface with real-time updates
 * Mobile-first responsive design with WhatsApp integration
 */

import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Package, 
  Truck, 
  CheckCircle, 
  Clock, 
  MapPin,
  MessageCircle,
  Phone,
  Calendar,
  User
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import OrderTrackingComponent from '@/components/commerce/OrderTracking';

interface OrderDetails {
  id: string;
  order_number: string;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
  created_at: string;
  customer: {
    name: string;
    phone: string;
    email?: string;
    address: string;
  };
  items: Array<{
    id: string;
    product_title: string;
    product_image: string;
    quantity: number;
    price: number;
    vendor_name: string;
  }>;
  shipping: {
    method: string;
    cost: number;
    estimated_delivery: string;
    tracking_number?: string;
  };
  payment: {
    method: string;
    status: 'pending' | 'paid' | 'failed';
    total: number;
  };
  timeline: Array<{
    status: string;
    timestamp: string;
    description: string;
    completed: boolean;
  }>;
}

// Sample order data
const sampleOrder: OrderDetails = {
  id: '1',
  order_number: 'VRT-20241221-1234',
  status: 'shipped',
  created_at: '2024-12-21T10:30:00Z',
  customer: {
    name: 'John Doe',
    phone: '+2348123456789',
    email: '<EMAIL>',
    address: '123 Victoria Island, Lagos, Nigeria'
  },
  items: [
    {
      id: '1',
      product_title: 'Premium Wireless Headphones',
      product_image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400',
      quantity: 1,
      price: 45000,
      vendor_name: 'TechHub Lagos'
    },
    {
      id: '2',
      product_title: 'Bluetooth Speaker',
      product_image: 'https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=400',
      quantity: 1,
      price: 25000,
      vendor_name: 'TechHub Lagos'
    }
  ],
  shipping: {
    method: 'Express Delivery',
    cost: 5000,
    estimated_delivery: '2024-12-23',
    tracking_number: 'VRT-TRACK-789456'
  },
  payment: {
    method: 'WhatsApp Order',
    status: 'paid',
    total: 75000
  },
  timeline: [
    {
      status: 'Order Placed',
      timestamp: '2024-12-21T10:30:00Z',
      description: 'Your order has been placed successfully',
      completed: true
    },
    {
      status: 'Order Confirmed',
      timestamp: '2024-12-21T11:15:00Z',
      description: 'Vendor has confirmed your order',
      completed: true
    },
    {
      status: 'Processing',
      timestamp: '2024-12-21T14:20:00Z',
      description: 'Your order is being prepared for shipment',
      completed: true
    },
    {
      status: 'Shipped',
      timestamp: '2024-12-22T09:00:00Z',
      description: 'Your order has been shipped and is on the way',
      completed: true
    },
    {
      status: 'Out for Delivery',
      timestamp: '',
      description: 'Your order is out for delivery',
      completed: false
    },
    {
      status: 'Delivered',
      timestamp: '',
      description: 'Your order has been delivered',
      completed: false
    }
  ]
};

const OrderTracking = () => {
  const { id } = useParams<{ id: string }>();
  const [order, setOrder] = useState<OrderDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    const timer = setTimeout(() => {
      setOrder(sampleOrder);
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [id]);

  const getStatusColor = (status: OrderDetails['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'confirmed':
        return 'bg-blue-100 text-blue-800';
      case 'processing':
        return 'bg-purple-100 text-purple-800';
      case 'shipped':
        return 'bg-indigo-100 text-indigo-800';
      case 'delivered':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: OrderDetails['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'confirmed':
      case 'processing':
        return <Package className="w-4 h-4" />;
      case 'shipped':
        return <Truck className="w-4 h-4" />;
      case 'delivered':
        return <CheckCircle className="w-4 h-4" />;
      default:
        return <Package className="w-4 h-4" />;
    }
  };

  const handleWhatsAppContact = () => {
    const message = `Hi! I need help with my order ${order?.order_number}. Can you please assist me?`;
    const whatsappUrl = `https://wa.me/2349077776066?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="pt-24 pb-12">
          <div className="container mx-auto px-4">
            <div className="animate-pulse space-y-6">
              <div className="h-8 bg-gray-200 rounded w-1/3"></div>
              <div className="h-64 bg-gray-200 rounded-lg"></div>
              <div className="h-96 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="pt-24 pb-12">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-2xl font-bold mb-4">Order not found</h1>
            <p className="text-muted-foreground mb-6">
              The order you're looking for doesn't exist or has been removed.
            </p>
            <Link to="/shop">
              <Button>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Shop
              </Button>
            </Link>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="pt-24 pb-12">
        <div className="container mx-auto px-4">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-8"
          >
            <Link to="/shop" className="inline-flex items-center text-muted-foreground hover:text-primary mb-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Shop
            </Link>
            
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <h1 className="text-3xl font-bold text-foreground">
                  Order {order.order_number}
                </h1>
                <p className="text-muted-foreground">
                  Placed on {new Date(order.created_at).toLocaleDateString()}
                </p>
              </div>
              
              <div className="flex items-center gap-3">
                <Badge className={cn("px-3 py-1", getStatusColor(order.status))}>
                  {getStatusIcon(order.status)}
                  <span className="ml-2 capitalize">{order.status}</span>
                </Badge>
                <Button variant="outline" onClick={handleWhatsAppContact}>
                  <MessageCircle className="w-4 h-4 mr-2" />
                  Get Help
                </Button>
              </div>
            </div>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Order Timeline */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="lg:col-span-2"
            >
              <Card>
                <CardHeader>
                  <CardTitle>Order Timeline</CardTitle>
                  <CardDescription>
                    Track your order progress in real-time
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <OrderTrackingComponent
                    orderId={order.id}
                    orderNumber={order.order_number}
                    status={order.status}
                    timeline={order.timeline}
                  />
                </CardContent>
              </Card>

              {/* Order Items */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Order Items</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {order.items.map((item) => (
                      <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                        <img
                          src={item.product_image}
                          alt={item.product_title}
                          className="w-16 h-16 rounded-lg object-cover"
                        />
                        <div className="flex-1">
                          <h4 className="font-medium">{item.product_title}</h4>
                          <p className="text-sm text-muted-foreground">
                            Sold by {item.vendor_name}
                          </p>
                          <p className="text-sm">
                            Quantity: {item.quantity} × ₦{item.price.toLocaleString()}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">
                            ₦{(item.quantity * item.price).toLocaleString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Order Summary */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="space-y-6"
            >
              {/* Delivery Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Delivery Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-muted-foreground mt-0.5" />
                    <div>
                      <p className="font-medium">Delivery Address</p>
                      <p className="text-sm text-muted-foreground">
                        {order.customer.address}
                      </p>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="flex items-center gap-3">
                    <Truck className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{order.shipping.method}</p>
                      <p className="text-sm text-muted-foreground">
                        Estimated delivery: {new Date(order.shipping.estimated_delivery).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  {order.shipping.tracking_number && (
                    <>
                      <Separator />
                      <div>
                        <p className="font-medium text-sm">Tracking Number</p>
                        <p className="text-sm text-muted-foreground font-mono">
                          {order.shipping.tracking_number}
                        </p>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>

              {/* Customer Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Customer Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-3">
                    <User className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{order.customer.name}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="w-4 h-4 text-muted-foreground" />
                    <span className="text-sm">{order.customer.phone}</span>
                  </div>
                  {order.customer.email && (
                    <div className="flex items-center gap-3">
                      <span className="w-4 h-4 text-center text-xs text-muted-foreground">@</span>
                      <span className="text-sm">{order.customer.email}</span>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Order Summary */}
              <Card>
                <CardHeader>
                  <CardTitle>Order Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal</span>
                    <span>₦{(order.payment.total - order.shipping.cost).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Shipping</span>
                    <span>₦{order.shipping.cost.toLocaleString()}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-medium">
                    <span>Total</span>
                    <span>₦{order.payment.total.toLocaleString()}</span>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    Payment via {order.payment.method}
                  </div>
                </CardContent>
              </Card>

              {/* Contact Support */}
              <Card>
                <CardHeader>
                  <CardTitle>Need Help?</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Have questions about your order? Contact our support team.
                  </p>
                  <Button onClick={handleWhatsAppContact} className="w-full">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    WhatsApp Support
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default OrderTracking;
