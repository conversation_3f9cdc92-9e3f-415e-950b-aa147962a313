/**
 * useResponsiveDesign Hook
 * Comprehensive mobile-first responsive design utilities
 * Enhances existing mobile responsiveness without breaking current functionality
 */

import { useState, useEffect, useCallback } from 'react';

interface BreakpointConfig {
  mobile: number;
  tablet: number;
  desktop: number;
  wide: number;
}

interface ResponsiveState {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isWide: boolean;
  width: number;
  height: number;
  orientation: 'portrait' | 'landscape';
  isTouch: boolean;
  pixelRatio: number;
}

interface ResponsiveUtils {
  // Screen size utilities
  getResponsiveValue: <T>(values: {
    mobile: T;
    tablet?: T;
    desktop?: T;
    wide?: T;
  }) => T;
  
  // Layout utilities
  getGridColumns: (mobile: number, tablet?: number, desktop?: number) => string;
  getSpacing: (mobile: string, tablet?: string, desktop?: string) => string;
  getFontSize: (mobile: string, tablet?: string, desktop?: string) => string;
  
  // Touch utilities
  getTouchTargetSize: () => string;
  getHoverClasses: (hoverClasses: string) => string;
  
  // Overflow prevention
  getContainerClasses: () => string;
  getTextClasses: () => string;
  getImageClasses: () => string;
}

const defaultBreakpoints: BreakpointConfig = {
  mobile: 768,
  tablet: 1024,
  desktop: 1280,
  wide: 1536
};

export const useResponsiveDesign = (
  customBreakpoints?: Partial<BreakpointConfig>
): ResponsiveState & ResponsiveUtils => {
  const breakpoints = { ...defaultBreakpoints, ...customBreakpoints };
  
  const [state, setState] = useState<ResponsiveState>({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isWide: false,
    width: 0,
    height: 0,
    orientation: 'portrait',
    isTouch: false,
    pixelRatio: 1
  });

  const updateState = useCallback(() => {
    const width = window.innerWidth;
    const height = window.innerHeight;
    const orientation = width > height ? 'landscape' : 'portrait';
    const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    const pixelRatio = window.devicePixelRatio || 1;

    setState({
      isMobile: width < breakpoints.mobile,
      isTablet: width >= breakpoints.mobile && width < breakpoints.desktop,
      isDesktop: width >= breakpoints.desktop && width < breakpoints.wide,
      isWide: width >= breakpoints.wide,
      width,
      height,
      orientation,
      isTouch,
      pixelRatio
    });
  }, [breakpoints]);

  useEffect(() => {
    updateState();
    
    const handleResize = () => updateState();
    const handleOrientationChange = () => {
      // Delay to ensure dimensions are updated
      setTimeout(updateState, 100);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, [updateState]);

  // Responsive value utility
  const getResponsiveValue = useCallback(<T>(values: {
    mobile: T;
    tablet?: T;
    desktop?: T;
    wide?: T;
  }): T => {
    if (state.isWide && values.wide !== undefined) return values.wide;
    if (state.isDesktop && values.desktop !== undefined) return values.desktop;
    if (state.isTablet && values.tablet !== undefined) return values.tablet;
    return values.mobile;
  }, [state]);

  // Grid utilities
  const getGridColumns = useCallback((
    mobile: number,
    tablet?: number,
    desktop?: number
  ): string => {
    const cols = getResponsiveValue({
      mobile,
      tablet: tablet || mobile,
      desktop: desktop || tablet || mobile
    });
    return `grid-cols-${cols}`;
  }, [getResponsiveValue]);

  // Spacing utilities
  const getSpacing = useCallback((
    mobile: string,
    tablet?: string,
    desktop?: string
  ): string => {
    return getResponsiveValue({
      mobile,
      tablet: tablet || mobile,
      desktop: desktop || tablet || mobile
    });
  }, [getResponsiveValue]);

  // Font size utilities
  const getFontSize = useCallback((
    mobile: string,
    tablet?: string,
    desktop?: string
  ): string => {
    return getResponsiveValue({
      mobile,
      tablet: tablet || mobile,
      desktop: desktop || tablet || mobile
    });
  }, [getResponsiveValue]);

  // Touch target utilities
  const getTouchTargetSize = useCallback((): string => {
    return state.isTouch ? 'min-h-[44px] min-w-[44px]' : 'min-h-[40px] min-w-[40px]';
  }, [state.isTouch]);

  // Hover utilities (disable on touch devices)
  const getHoverClasses = useCallback((hoverClasses: string): string => {
    return state.isTouch ? '' : hoverClasses;
  }, [state.isTouch]);

  // Container utilities (prevent overflow)
  const getContainerClasses = useCallback((): string => {
    return 'w-full max-w-full overflow-x-hidden';
  }, []);

  // Text utilities (prevent overflow)
  const getTextClasses = useCallback((): string => {
    return 'word-wrap break-word overflow-wrap break-word hyphens-auto max-w-full';
  }, []);

  // Image utilities (responsive images)
  const getImageClasses = useCallback((): string => {
    return 'max-w-full h-auto block';
  }, []);

  return {
    ...state,
    getResponsiveValue,
    getGridColumns,
    getSpacing,
    getFontSize,
    getTouchTargetSize,
    getHoverClasses,
    getContainerClasses,
    getTextClasses,
    getImageClasses
  };
};

// Utility function for responsive class names
export const responsive = {
  // Grid utilities
  grid: (mobile: number, tablet?: number, desktop?: number) => {
    const classes = [`grid-cols-${mobile}`];
    if (tablet) classes.push(`sm:grid-cols-${tablet}`);
    if (desktop) classes.push(`lg:grid-cols-${desktop}`);
    return classes.join(' ');
  },
  
  // Spacing utilities
  spacing: (mobile: string, tablet?: string, desktop?: string) => {
    const classes = [mobile];
    if (tablet) classes.push(`sm:${tablet}`);
    if (desktop) classes.push(`lg:${desktop}`);
    return classes.join(' ');
  },
  
  // Text utilities
  text: (mobile: string, tablet?: string, desktop?: string) => {
    const classes = [mobile];
    if (tablet) classes.push(`sm:${tablet}`);
    if (desktop) classes.push(`lg:${desktop}`);
    return classes.join(' ');
  },
  
  // Container utilities
  container: 'w-full max-w-full overflow-x-hidden px-4 sm:px-6 lg:px-8',
  
  // Touch targets
  touch: 'min-h-[44px] min-w-[44px] touch-manipulation',
  
  // Safe areas (for mobile notches)
  safeArea: 'pt-safe-top pb-safe-bottom pl-safe-left pr-safe-right'
};

// Media query utilities for CSS-in-JS
export const mediaQueries = {
  mobile: '(max-width: 767px)',
  tablet: '(min-width: 768px) and (max-width: 1023px)',
  desktop: '(min-width: 1024px) and (max-width: 1279px)',
  wide: '(min-width: 1280px)',
  touch: '(hover: none) and (pointer: coarse)',
  hover: '(hover: hover) and (pointer: fine)',
  portrait: '(orientation: portrait)',
  landscape: '(orientation: landscape)',
  reducedMotion: '(prefers-reduced-motion: reduce)',
  highContrast: '(prefers-contrast: high)'
};
