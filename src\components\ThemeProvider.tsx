
import { createContext, useContext, useEffect } from "react"
import { initializeTheme, applyTheme, type ThemeColor, CURRENT_THEME } from "@/lib/theme"

type Theme = "light"

type ThemeProviderProps = {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

type ThemeProviderState = {
  theme: Theme
  setTheme: (theme: Theme) => void
  themeColor: ThemeColor
  setThemeColor: (color: ThemeColor) => void
}

const initialState: ThemeProviderState = {
  theme: "light",
  setTheme: () => null,
  themeColor: CURRENT_THEME,
  setThemeColor: () => null,
}

const ThemeProviderContext = createContext<ThemeProviderState>(initialState)

export function ThemeProvider({
  children,
  defaultTheme = "light",
  storageKey = "vrt-ui-theme",
  ...props
}: ThemeProviderProps) {
  // Initialize theme on mount
  useEffect(() => {
    initializeTheme();
  }, []);

  const value = {
    theme: "light" as Theme,
    setTheme: () => {
      // No-op since we only support light theme now
    },
    themeColor: CURRENT_THEME,
    setThemeColor: (color: ThemeColor) => {
      applyTheme(color);
    },
  }

  return (
    <ThemeProviderContext.Provider {...props} value={value}>
      {children}
    </ThemeProviderContext.Provider>
  )
}

export const useTheme = () => {
  const context = useContext(ThemeProviderContext)

  if (context === undefined)
    throw new Error("useTheme must be used within a ThemeProvider")

  return context
}
