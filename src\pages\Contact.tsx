
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import ContactInfo from '@/components/contact/ContactInfo';
import ContactForm from '@/components/contact/ContactForm';
import SupportOptions from '@/components/contact/SupportOptions';
import BusinessHours from '@/components/contact/BusinessHours';
import FAQ from '@/components/contact/FAQ';

const Contact = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      {/* Enhanced Hero Section with Background */}
      <section className="relative pt-32 lg:pt-40 pb-16 overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.unsplash.com/photo-1556761175-b413da4baf72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80"
            alt="Professional team ready to help with virtual tours"
            className="w-full h-full object-cover object-center"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-black/70"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center mb-12">
            <div className="inline-flex items-center rounded-full bg-white/10 backdrop-blur-sm border border-white/20 px-4 py-2 text-sm font-medium text-white mb-6">
              💬 Let's Connect
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 drop-shadow-lg">
              Get in Touch
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto drop-shadow-md">
              Have questions about virtual tours? Need technical support? Want to explore partnership opportunities?
              We're here to help you succeed with immersive 360° experiences.
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 sm:px-6 py-8">

        {/* Contact Info Cards */}
        <ContactInfo />

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {/* Contact Form */}
          <div className="lg:col-span-2">
            <ContactForm />
          </div>

          {/* Support Options */}
          <div className="space-y-6">
            <SupportOptions />
            <BusinessHours />
          </div>
        </div>

        {/* FAQ Section */}
        <FAQ />
      </div>

      <Footer />
    </div>
  );
};

export default Contact;
