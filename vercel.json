{"version": 2, "buildCommand": "bun run build", "outputDirectory": "dist", "regions": ["iad1"], "framework": "vite", "headers": [{"source": "/sw.js", "headers": [{"key": "Service-Worker-Allowed", "value": "/"}, {"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}, {"source": "/manifest.json", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "/offline.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}, {"key": "Access-Control-Allow-Origin", "value": "*"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://dnbjrfgfugpmyrconepx.supabase.co https://cdn.jsdelivr.net https://vercel.live; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.jsdelivr.net; img-src 'self' data: blob: https: https://dnbjrfgfugpmyrconepx.supabase.co https://images.unsplash.com https://via.placeholder.com https://picsum.photos; font-src 'self' https://fonts.gstatic.com https://cdn.jsdelivr.net data:; connect-src 'self' https://dnbjrfgfugpmyrconepx.supabase.co wss://dnbjrfgfugpmyrconepx.supabase.co https://api.vercel.com https://vitals.vercel-analytics.com https://vercel.live; frame-src 'self' https://my.matterport.com https://kuula.co https://momento360.com https://panoee.com https://tourmkr.com https://massinteract.com https://vercel.live; worker-src 'self' blob:; manifest-src 'self'; media-src 'self' https: blob: data:; object-src 'none'; base-uri 'self'; form-action 'self';"}, {"key": "Permissions-Policy", "value": "camera=self, microphone=self, geolocation=self, payment=none, usb=none, magnetometer=none, gyroscope=none, accelerometer=none"}, {"key": "Cross-Origin-Embedder-Policy", "value": "unsafe-none"}, {"key": "Cross-Origin-Opener-Policy", "value": "same-origin-allow-popups"}, {"key": "Cross-Origin-Resource-Policy", "value": "cross-origin"}]}, {"source": "/admin/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate, private"}, {"key": "Pragma", "value": "no-cache"}, {"key": "X-Robots-Tag", "value": "noindex, nofollow, noarchive, nosnippet"}]}, {"source": "/dashboard/(.*)", "headers": [{"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Cache-Control", "value": "no-store, no-cache, must-revalidate, private"}, {"key": "Pragma", "value": "no-cache"}]}], "rewrites": [{"source": "/((?!api/).*)", "destination": "/index.html"}]}