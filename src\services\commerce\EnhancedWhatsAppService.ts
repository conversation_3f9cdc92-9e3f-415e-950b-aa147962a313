/**
 * Enhanced WhatsApp Service
 * Advanced WhatsApp Business API integration with templates and automation
 * Optimized for Nigerian e-commerce market
 */

import type { Order, OrderItem } from '@/components/commerce/OrderSummary';
import type { Vendor } from '@/components/commerce/VendorProfile';
import type { Product } from '@/components/commerce/ProductCard';

export interface WhatsAppTemplate {
  name: string;
  language: string;
  components: WhatsAppComponent[];
}

export interface WhatsAppComponent {
  type: 'header' | 'body' | 'footer' | 'buttons';
  parameters?: WhatsAppParameter[];
  buttons?: WhatsAppButton[];
}

export interface WhatsAppParameter {
  type: 'text' | 'currency' | 'date_time' | 'image' | 'document';
  text?: string;
  currency?: {
    fallback_value: string;
    code: string;
    amount_1000: number;
  };
  date_time?: {
    fallback_value: string;
  };
  image?: {
    link: string;
  };
}

export interface WhatsAppButton {
  type: 'reply' | 'url';
  reply?: {
    id: string;
    title: string;
  };
  url?: {
    url: string;
    text: string;
  };
}

export interface MessageTemplate {
  id: string;
  name: string;
  description: string;
  category: 'order' | 'vendor' | 'customer_service' | 'marketing';
  template: WhatsAppTemplate;
}

export class EnhancedWhatsAppService {
  private readonly baseUrl = 'https://graph.facebook.com/v18.0';
  private readonly phoneNumberId = process.env.VITE_WHATSAPP_BUSINESS_PHONE_ID;
  private readonly accessToken = process.env.VITE_WHATSAPP_ACCESS_TOKEN;

  /**
   * Send order confirmation to customer with enhanced template
   */
  async sendOrderConfirmation(order: Order, items: OrderItem[]): Promise<void> {
    try {
      if (!order.customer_phone) return;

      const template = this.getOrderConfirmationTemplate(order, items);
      await this.sendTemplateMessage(order.customer_phone, template);

      // Send follow-up message with tracking info
      setTimeout(() => {
        this.sendOrderTrackingInfo(order);
      }, 5000); // 5 seconds delay

    } catch (error) {
      console.error('Error sending order confirmation:', error);
    }
  }

  /**
   * Send order notification to vendor with action buttons
   */
  async sendOrderToVendor(order: Order, vendorItems: OrderItem[], vendor: Vendor): Promise<void> {
    try {
      if (!vendor.whatsapp_number) return;

      const template = this.getVendorOrderTemplate(order, vendorItems, vendor);
      await this.sendTemplateMessage(vendor.whatsapp_number, template);

    } catch (error) {
      console.error('Error sending vendor notification:', error);
    }
  }

  /**
   * Send order status update with tracking timeline
   */
  async sendOrderStatusUpdate(order: Order, newStatus: string, estimatedDelivery?: string): Promise<void> {
    try {
      if (!order.customer_phone) return;

      const template = this.getStatusUpdateTemplate(order, newStatus, estimatedDelivery);
      await this.sendTemplateMessage(order.customer_phone, template);

    } catch (error) {
      console.error('Error sending status update:', error);
    }
  }

  /**
   * Send product inquiry response
   */
  async sendProductInquiry(
    customerPhone: string, 
    product: Product, 
    vendor: Vendor,
    tourContext?: any
  ): Promise<void> {
    try {
      const template = this.getProductInquiryTemplate(product, vendor, tourContext);
      await this.sendTemplateMessage(customerPhone, template);

    } catch (error) {
      console.error('Error sending product inquiry:', error);
    }
  }

  /**
   * Send abandoned cart reminder
   */
  async sendAbandonedCartReminder(
    customerPhone: string,
    items: any[],
    cartValue: number
  ): Promise<void> {
    try {
      const template = this.getAbandonedCartTemplate(items, cartValue);
      await this.sendTemplateMessage(customerPhone, template);

    } catch (error) {
      console.error('Error sending cart reminder:', error);
    }
  }

  /**
   * Send vendor welcome message with setup guide
   */
  async sendVendorWelcome(vendor: Vendor): Promise<void> {
    try {
      if (!vendor.whatsapp_number) return;

      const template = this.getVendorWelcomeTemplate(vendor);
      await this.sendTemplateMessage(vendor.whatsapp_number, template);

      // Send setup guide after welcome
      setTimeout(() => {
        this.sendVendorSetupGuide(vendor);
      }, 10000); // 10 seconds delay

    } catch (error) {
      console.error('Error sending vendor welcome:', error);
    }
  }

  /**
   * Send customer support message
   */
  async sendCustomerSupport(
    customerPhone: string,
    issueType: string,
    orderNumber?: string
  ): Promise<void> {
    try {
      const template = this.getCustomerSupportTemplate(issueType, orderNumber);
      await this.sendTemplateMessage(customerPhone, template);

    } catch (error) {
      console.error('Error sending customer support:', error);
    }
  }

  /**
   * Send promotional message with product recommendations
   */
  async sendPromotionalMessage(
    customerPhone: string,
    promoCode: string,
    discountPercent: number,
    products: Product[]
  ): Promise<void> {
    try {
      const template = this.getPromotionalTemplate(promoCode, discountPercent, products);
      await this.sendTemplateMessage(customerPhone, template);

    } catch (error) {
      console.error('Error sending promotional message:', error);
    }
  }

  /**
   * Get order confirmation template
   */
  private getOrderConfirmationTemplate(order: Order, items: OrderItem[]): WhatsAppTemplate {
    const itemsList = items.map(item => 
      `• ${item.product.title} (${item.quantity}x) - ₦${(item.price * item.quantity).toLocaleString()}`
    ).join('\n');

    return {
      name: 'order_confirmation',
      language: 'en',
      components: [
        {
          type: 'header',
          parameters: [
            {
              type: 'text',
              text: '🎉 Order Confirmed!'
            }
          ]
        },
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: order.customer_name
            },
            {
              type: 'text',
              text: order.order_number
            },
            {
              type: 'currency',
              currency: {
                fallback_value: `₦${order.total_amount.toLocaleString()}`,
                code: 'NGN',
                amount_1000: order.total_amount * 1000
              }
            },
            {
              type: 'text',
              text: itemsList
            }
          ]
        },
        {
          type: 'footer',
          parameters: [
            {
              type: 'text',
              text: 'Track your order anytime at virtualrealtour.com/track'
            }
          ]
        },
        {
          type: 'buttons',
          buttons: [
            {
              type: 'url',
              url: {
                url: `https://virtualrealtour.com/track-order?order=${order.order_number}`,
                text: 'Track Order'
              }
            },
            {
              type: 'reply',
              reply: {
                id: 'support',
                title: 'Need Help?'
              }
            }
          ]
        }
      ]
    };
  }

  /**
   * Get vendor order notification template
   */
  private getVendorOrderTemplate(order: Order, items: OrderItem[], vendor: Vendor): WhatsAppTemplate {
    const vendorItems = items.filter(item => item.vendor.id === vendor.id);
    const vendorTotal = vendorItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    
    const itemsList = vendorItems.map(item => 
      `• ${item.product.title} (${item.quantity}x) - ₦${(item.price * item.quantity).toLocaleString()}`
    ).join('\n');

    return {
      name: 'vendor_order_notification',
      language: 'en',
      components: [
        {
          type: 'header',
          parameters: [
            {
              type: 'text',
              text: '📦 New Order Received!'
            }
          ]
        },
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: vendor.name
            },
            {
              type: 'text',
              text: order.order_number
            },
            {
              type: 'text',
              text: order.customer_name
            },
            {
              type: 'text',
              text: order.customer_phone
            },
            {
              type: 'currency',
              currency: {
                fallback_value: `₦${vendorTotal.toLocaleString()}`,
                code: 'NGN',
                amount_1000: vendorTotal * 1000
              }
            },
            {
              type: 'text',
              text: itemsList
            }
          ]
        },
        {
          type: 'buttons',
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'confirm_order',
                title: 'Confirm Order'
              }
            },
            {
              type: 'reply',
              reply: {
                id: 'contact_customer',
                title: 'Contact Customer'
              }
            },
            {
              type: 'url',
              url: {
                url: 'https://virtualrealtour.com/vendor/orders',
                text: 'Manage Orders'
              }
            }
          ]
        }
      ]
    };
  }

  /**
   * Get status update template
   */
  private getStatusUpdateTemplate(order: Order, status: string, estimatedDelivery?: string): WhatsAppTemplate {
    const statusMessages: Record<string, string> = {
      confirmed: '✅ Your order has been confirmed and is being prepared.',
      processing: '📦 Your order is being processed and packaged.',
      shipped: '🚚 Great news! Your order has been shipped and is on its way.',
      delivered: '🎉 Your order has been delivered successfully!'
    };

    const statusEmojis: Record<string, string> = {
      confirmed: '✅',
      processing: '📦',
      shipped: '🚚',
      delivered: '🎉'
    };

    return {
      name: 'order_status_update',
      language: 'en',
      components: [
        {
          type: 'header',
          parameters: [
            {
              type: 'text',
              text: `${statusEmojis[status]} Order Update`
            }
          ]
        },
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: order.customer_name
            },
            {
              type: 'text',
              text: order.order_number
            },
            {
              type: 'text',
              text: statusMessages[status]
            },
            {
              type: 'text',
              text: estimatedDelivery || 'We\'ll keep you updated on the progress.'
            }
          ]
        },
        {
          type: 'buttons',
          buttons: [
            {
              type: 'url',
              url: {
                url: `https://virtualrealtour.com/track-order?order=${order.order_number}`,
                text: 'Track Order'
              }
            },
            {
              type: 'reply',
              reply: {
                id: 'contact_support',
                title: 'Contact Support'
              }
            }
          ]
        }
      ]
    };
  }

  /**
   * Get product inquiry template
   */
  private getProductInquiryTemplate(product: Product, vendor: Vendor, tourContext?: any): WhatsAppTemplate {
    return {
      name: 'product_inquiry',
      language: 'en',
      components: [
        {
          type: 'header',
          parameters: [
            {
              type: 'image',
              image: {
                link: product.images[0] || 'https://via.placeholder.com/300'
              }
            }
          ]
        },
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: product.title
            },
            {
              type: 'currency',
              currency: {
                fallback_value: `₦${product.price.toLocaleString()}`,
                code: 'NGN',
                amount_1000: product.price * 1000
              }
            },
            {
              type: 'text',
              text: vendor.name
            },
            {
              type: 'text',
              text: tourContext ? `Seen in: ${tourContext.tourTitle}` : 'Available now'
            }
          ]
        },
        {
          type: 'buttons',
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'add_to_cart',
                title: 'Add to Cart'
              }
            },
            {
              type: 'reply',
              reply: {
                id: 'more_info',
                title: 'More Info'
              }
            },
            {
              type: 'url',
              url: {
                url: `https://virtualrealtour.com/products/${product.id}`,
                text: 'View Product'
              }
            }
          ]
        }
      ]
    };
  }

  /**
   * Get abandoned cart template
   */
  private getAbandonedCartTemplate(items: any[], cartValue: number): WhatsAppTemplate {
    const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);

    return {
      name: 'abandoned_cart',
      language: 'en',
      components: [
        {
          type: 'header',
          parameters: [
            {
              type: 'text',
              text: '🛒 Don\'t forget your items!'
            }
          ]
        },
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: itemCount.toString()
            },
            {
              type: 'currency',
              currency: {
                fallback_value: `₦${cartValue.toLocaleString()}`,
                code: 'NGN',
                amount_1000: cartValue * 1000
              }
            }
          ]
        },
        {
          type: 'buttons',
          buttons: [
            {
              type: 'url',
              url: {
                url: 'https://virtualrealtour.com/cart',
                text: 'Complete Purchase'
              }
            },
            {
              type: 'reply',
              reply: {
                id: 'remove_reminder',
                title: 'Remove Reminder'
              }
            }
          ]
        }
      ]
    };
  }

  /**
   * Get vendor welcome template
   */
  private getVendorWelcomeTemplate(vendor: Vendor): WhatsAppTemplate {
    return {
      name: 'vendor_welcome',
      language: 'en',
      components: [
        {
          type: 'header',
          parameters: [
            {
              type: 'text',
              text: '🎉 Welcome to VirtualRealTour!'
            }
          ]
        },
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: vendor.name
            },
            {
              type: 'text',
              text: (vendor.commission_rate * 100).toFixed(1)
            }
          ]
        },
        {
          type: 'buttons',
          buttons: [
            {
              type: 'url',
              url: {
                url: 'https://virtualrealtour.com/vendor/dashboard',
                text: 'Access Dashboard'
              }
            },
            {
              type: 'reply',
              reply: {
                id: 'setup_help',
                title: 'Need Help?'
              }
            }
          ]
        }
      ]
    };
  }

  /**
   * Get customer support template
   */
  private getCustomerSupportTemplate(issueType: string, orderNumber?: string): WhatsAppTemplate {
    return {
      name: 'customer_support',
      language: 'en',
      components: [
        {
          type: 'header',
          parameters: [
            {
              type: 'text',
              text: '🤝 We\'re here to help!'
            }
          ]
        },
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: issueType
            },
            {
              type: 'text',
              text: orderNumber || 'General inquiry'
            }
          ]
        },
        {
          type: 'buttons',
          buttons: [
            {
              type: 'reply',
              reply: {
                id: 'speak_to_agent',
                title: 'Speak to Agent'
              }
            },
            {
              type: 'url',
              url: {
                url: 'https://virtualrealtour.com/help',
                text: 'Help Center'
              }
            }
          ]
        }
      ]
    };
  }

  /**
   * Get promotional template
   */
  private getPromotionalTemplate(promoCode: string, discountPercent: number, products: Product[]): WhatsAppTemplate {
    return {
      name: 'promotional_offer',
      language: 'en',
      components: [
        {
          type: 'header',
          parameters: [
            {
              type: 'text',
              text: `🎁 ${discountPercent}% OFF Special Offer!`
            }
          ]
        },
        {
          type: 'body',
          parameters: [
            {
              type: 'text',
              text: discountPercent.toString()
            },
            {
              type: 'text',
              text: promoCode
            },
            {
              type: 'text',
              text: products.length.toString()
            }
          ]
        },
        {
          type: 'buttons',
          buttons: [
            {
              type: 'url',
              url: {
                url: 'https://virtualrealtour.com/shop',
                text: 'Shop Now'
              }
            },
            {
              type: 'reply',
              reply: {
                id: 'unsubscribe',
                title: 'Unsubscribe'
              }
            }
          ]
        }
      ]
    };
  }

  /**
   * Send template message via WhatsApp Business API
   */
  private async sendTemplateMessage(phoneNumber: string, template: WhatsAppTemplate): Promise<void> {
    try {
      if (!this.phoneNumberId || !this.accessToken) {
        console.log('WhatsApp credentials not configured, simulating message send');
        console.log(`Would send template "${template.name}" to ${phoneNumber}`);
        return;
      }

      const cleanPhone = phoneNumber.replace(/[^\d]/g, '');
      
      const payload = {
        messaging_product: 'whatsapp',
        to: cleanPhone,
        type: 'template',
        template: {
          name: template.name,
          language: {
            code: template.language
          },
          components: template.components
        }
      };

      const response = await fetch(`${this.baseUrl}/${this.phoneNumberId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`WhatsApp API error: ${response.statusText}`);
      }

      console.log(`Template message sent successfully to ${phoneNumber}`);
    } catch (error) {
      console.error('Error sending template message:', error);
      throw error;
    }
  }

  /**
   * Send order tracking info
   */
  private async sendOrderTrackingInfo(order: Order): Promise<void> {
    try {
      const message = `📱 *Track Your Order*

Order: ${order.order_number}
Status: ${order.status.charAt(0).toUpperCase() + order.status.slice(1)}

🔗 Track online: https://virtualrealtour.com/track-order?order=${order.order_number}

💬 Need help? Just reply to this message!`;

      await this.sendTextMessage(order.customer_phone, message);
    } catch (error) {
      console.error('Error sending tracking info:', error);
    }
  }

  /**
   * Send vendor setup guide
   */
  private async sendVendorSetupGuide(vendor: Vendor): Promise<void> {
    try {
      const message = `📚 *Quick Setup Guide*

Hi ${vendor.name}! Here's how to get started:

1️⃣ Add your first product
2️⃣ Link products to virtual tours
3️⃣ Set up your profile
4️⃣ Start receiving orders!

🔗 Dashboard: https://virtualrealtour.com/vendor/dashboard
📞 Need help? Reply to this message!`;

      await this.sendTextMessage(vendor.whatsapp_number!, message);
    } catch (error) {
      console.error('Error sending setup guide:', error);
    }
  }

  /**
   * Send simple text message
   */
  private async sendTextMessage(phoneNumber: string, message: string): Promise<void> {
    try {
      if (!this.phoneNumberId || !this.accessToken) {
        console.log(`Would send text message to ${phoneNumber}: ${message}`);
        return;
      }

      const cleanPhone = phoneNumber.replace(/[^\d]/g, '');

      const payload = {
        messaging_product: 'whatsapp',
        to: cleanPhone,
        type: 'text',
        text: {
          body: message
        }
      };

      const response = await fetch(`${this.baseUrl}/${this.phoneNumberId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload)
      });

      if (!response.ok) {
        throw new Error(`WhatsApp API error: ${response.statusText}`);
      }

      console.log(`Text message sent successfully to ${phoneNumber}`);
    } catch (error) {
      console.error('Error sending text message:', error);
      throw error;
    }
  }

  /**
   * Get WhatsApp analytics and metrics
   */
  async getAnalytics(dateFrom: string, dateTo: string): Promise<any> {
    try {
      if (!this.phoneNumberId || !this.accessToken) {
        // Return mock data for development
        return {
          messages_sent: 1234,
          messages_delivered: 1215,
          messages_read: 1089,
          messages_replied: 827,
          delivery_rate: 98.5,
          read_rate: 88.2,
          response_rate: 67.0,
          daily_stats: [
            { date: '2024-01-01', sent: 45, delivered: 44, read: 39, replied: 26 },
            { date: '2024-01-02', sent: 52, delivered: 51, read: 46, replied: 31 },
            { date: '2024-01-03', sent: 38, delivered: 37, read: 33, replied: 22 }
          ]
        };
      }

      // In a real implementation, this would call WhatsApp Business API analytics endpoint
      const response = await fetch(`${this.baseUrl}/${this.phoneNumberId}/analytics`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`WhatsApp API error: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching analytics:', error);
      throw error;
    }
  }

  /**
   * Handle incoming webhook messages
   */
  async handleWebhook(webhookData: any): Promise<void> {
    try {
      const { entry } = webhookData;

      for (const entryItem of entry) {
        const { changes } = entryItem;

        for (const change of changes) {
          if (change.field === 'messages') {
            const { messages, statuses } = change.value;

            // Handle incoming messages
            if (messages) {
              for (const message of messages) {
                await this.processIncomingMessage(message);
              }
            }

            // Handle message status updates
            if (statuses) {
              for (const status of statuses) {
                await this.processMessageStatus(status);
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error handling webhook:', error);
      throw error;
    }
  }

  /**
   * Process incoming message
   */
  private async processIncomingMessage(message: any): Promise<void> {
    try {
      const { from, text, type } = message;

      if (type === 'text' && text?.body) {
        const messageText = text.body.toLowerCase();

        // Auto-respond to common queries
        if (messageText.includes('order') || messageText.includes('track')) {
          await this.sendAutoResponse(from, 'order_inquiry');
        } else if (messageText.includes('help') || messageText.includes('support')) {
          await this.sendAutoResponse(from, 'support_request');
        } else if (messageText.includes('product') || messageText.includes('price')) {
          await this.sendAutoResponse(from, 'product_inquiry');
        }
      }

      // Log message for analytics
      console.log(`Received message from ${from}: ${text?.body || type}`);
    } catch (error) {
      console.error('Error processing incoming message:', error);
    }
  }

  /**
   * Process message status update
   */
  private async processMessageStatus(status: any): Promise<void> {
    try {
      const { id, status: messageStatus, timestamp } = status;

      // Update message status in database
      console.log(`Message ${id} status: ${messageStatus} at ${timestamp}`);

      // TODO: Update message status in database for analytics
    } catch (error) {
      console.error('Error processing message status:', error);
    }
  }

  /**
   * Send auto-response based on message type
   */
  private async sendAutoResponse(phoneNumber: string, responseType: string): Promise<void> {
    try {
      const responses: Record<string, string> = {
        order_inquiry: `Hi! 👋 To track your order, please visit: https://virtualrealtour.com/track-order

Or reply with your order number and I'll help you track it!`,

        support_request: `Hello! 🤝 I'm here to help you.

For immediate assistance:
📞 Call: +234-XXX-XXX-XXXX
💬 Live chat: https://virtualrealtour.com/support
📧 Email: <EMAIL>

What can I help you with today?`,

        product_inquiry: `Thanks for your interest! 🛍️

Browse our products:
🔗 https://virtualrealtour.com/shop

Or tell me what you're looking for and I'll help you find it!`
      };

      const message = responses[responseType];
      if (message) {
        await this.sendTextMessage(phoneNumber, message);
      }
    } catch (error) {
      console.error('Error sending auto-response:', error);
    }
  }
}

export const enhancedWhatsAppService = new EnhancedWhatsAppService();
