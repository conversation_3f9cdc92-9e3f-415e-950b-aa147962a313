/**
 * Product Service
 * Handles all product-related operations for the e-commerce system
 * Integrates with Supabase backend and follows VRT patterns
 */

import { supabase } from '@/lib/supabase';
import type { Product } from '@/components/commerce/ProductCard';

export interface ProductInput {
  title: string;
  description?: string;
  price: number;
  compare_at_price?: number;
  sku?: string;
  inventory_quantity: number;
  images: string[];
  category?: string;
  tags?: string[];
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  metadata?: Record<string, any>;
}

export interface ProductFilters {
  vendorId?: string;
  category?: string;
  status?: 'draft' | 'active' | 'inactive' | 'out_of_stock';
  minPrice?: number;
  maxPrice?: number;
  inStock?: boolean;
  search?: string;
  tags?: string[];
}

export interface ProductUpdate {
  title?: string;
  description?: string;
  price?: number;
  compare_at_price?: number;
  inventory_quantity?: number;
  images?: string[];
  status?: 'draft' | 'active' | 'inactive' | 'out_of_stock';
  category?: string;
  tags?: string[];
  metadata?: Record<string, any>;
}

export class ProductService {
  /**
   * Create a new product
   */
  async createProduct(vendorId: string, productData: ProductInput): Promise<Product> {
    try {
      const sku = productData.sku || this.generateSKU(vendorId, productData.title);
      
      const { data, error } = await supabase
        .from('products')
        .insert({
          vendor_id: vendorId,
          title: productData.title,
          description: productData.description,
          price: productData.price,
          compare_at_price: productData.compare_at_price,
          sku,
          inventory_quantity: productData.inventory_quantity,
          images: productData.images,
          category: productData.category,
          tags: productData.tags,
          weight: productData.weight,
          dimensions: productData.dimensions,
          metadata: productData.metadata || {},
          status: 'draft'
        })
        .select(`
          *,
          vendor:vendors(id, name, whatsapp_number)
        `)
        .single();

      if (error) throw error;
      return this.mapSupabaseProductToProduct(data);
    } catch (error) {
      console.error('Error creating product:', error);
      throw new Error('Failed to create product');
    }
  }

  /**
   * Get product by ID
   */
  async getProduct(productId: string): Promise<Product | null> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select(`
          *,
          vendor:vendors(id, name, whatsapp_number)
        `)
        .eq('id', productId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null; // Not found
        throw error;
      }

      return this.mapSupabaseProductToProduct(data);
    } catch (error) {
      console.error('Error fetching product:', error);
      throw new Error('Failed to fetch product');
    }
  }

  /**
   * Get products with filters
   */
  async getProducts(filters: ProductFilters = {}, limit = 20, offset = 0): Promise<Product[]> {
    try {
      let query = supabase
        .from('products')
        .select(`
          *,
          vendor:vendors(id, name, whatsapp_number)
        `)
        .range(offset, offset + limit - 1)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.vendorId) {
        query = query.eq('vendor_id', filters.vendorId);
      }

      if (filters.category) {
        query = query.eq('category', filters.category);
      }

      if (filters.status) {
        query = query.eq('status', filters.status);
      } else {
        // Default to active products only
        query = query.eq('status', 'active');
      }

      if (filters.minPrice !== undefined) {
        query = query.gte('price', filters.minPrice);
      }

      if (filters.maxPrice !== undefined) {
        query = query.lte('price', filters.maxPrice);
      }

      if (filters.inStock) {
        query = query.gt('inventory_quantity', 0);
      }

      if (filters.search) {
        query = query.or(`title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`);
      }

      if (filters.tags && filters.tags.length > 0) {
        query = query.overlaps('tags', filters.tags);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data.map(item => this.mapSupabaseProductToProduct(item));
    } catch (error) {
      console.error('Error fetching products:', error);
      throw new Error('Failed to fetch products');
    }
  }

  /**
   * Update product
   */
  async updateProduct(productId: string, updates: ProductUpdate): Promise<Product> {
    try {
      const { data, error } = await supabase
        .from('products')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', productId)
        .select(`
          *,
          vendor:vendors(id, name, whatsapp_number)
        `)
        .single();

      if (error) throw error;
      return this.mapSupabaseProductToProduct(data);
    } catch (error) {
      console.error('Error updating product:', error);
      throw new Error('Failed to update product');
    }
  }

  /**
   * Delete product
   */
  async deleteProduct(productId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('products')
        .delete()
        .eq('id', productId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting product:', error);
      throw new Error('Failed to delete product');
    }
  }

  /**
   * Link product to tour hotspot
   */
  async linkProductToTour(
    productId: string, 
    tourId: string, 
    sceneId: string, 
    position: { x: number; y: number; z: number },
    hotspotStyle?: Record<string, any>
  ): Promise<void> {
    try {
      const { error } = await supabase
        .from('tour_product_hotspots')
        .insert({
          tour_id: tourId,
          product_id: productId,
          scene_id: sceneId,
          position,
          hotspot_style: hotspotStyle || {},
          is_active: true
        });

      if (error) throw error;
    } catch (error) {
      console.error('Error linking product to tour:', error);
      throw new Error('Failed to link product to tour');
    }
  }

  /**
   * Get products linked to a tour
   */
  async getTourProducts(tourId: string): Promise<Array<Product & { hotspot: any }>> {
    try {
      const { data, error } = await supabase
        .from('tour_product_hotspots')
        .select(`
          *,
          product:products(
            *,
            vendor:vendors(id, name, whatsapp_number)
          )
        `)
        .eq('tour_id', tourId)
        .eq('is_active', true);

      if (error) throw error;

      return data.map(item => ({
        ...this.mapSupabaseProductToProduct(item.product),
        hotspot: {
          id: item.id,
          sceneId: item.scene_id,
          position: item.position,
          style: item.hotspot_style,
          displayOrder: item.display_order
        }
      }));
    } catch (error) {
      console.error('Error fetching tour products:', error);
      throw new Error('Failed to fetch tour products');
    }
  }

  /**
   * Update product inventory
   */
  async updateInventory(productId: string, quantity: number): Promise<void> {
    try {
      const { error } = await supabase
        .from('products')
        .update({ 
          inventory_quantity: quantity,
          status: quantity > 0 ? 'active' : 'out_of_stock',
          updated_at: new Date().toISOString()
        })
        .eq('id', productId);

      if (error) throw error;
    } catch (error) {
      console.error('Error updating inventory:', error);
      throw new Error('Failed to update inventory');
    }
  }

  /**
   * Get product categories
   */
  async getCategories(): Promise<string[]> {
    try {
      const { data, error } = await supabase
        .from('products')
        .select('category')
        .not('category', 'is', null)
        .eq('status', 'active');

      if (error) throw error;

      const categories = [...new Set(data.map(item => item.category))];
      return categories.filter(Boolean);
    } catch (error) {
      console.error('Error fetching categories:', error);
      return [];
    }
  }

  /**
   * Generate SKU for product
   */
  private generateSKU(vendorId: string, title: string): string {
    const vendorCode = vendorId.slice(0, 8).toUpperCase();
    const titleCode = title.replace(/\s+/g, '').slice(0, 6).toUpperCase();
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    const timestamp = Date.now().toString().slice(-4);
    return `${vendorCode}-${titleCode}-${random}${timestamp}`;
  }

  /**
   * Map Supabase product data to Product interface
   */
  private mapSupabaseProductToProduct(data: any): Product {
    return {
      id: data.id,
      title: data.title,
      description: data.description,
      price: data.price,
      compare_at_price: data.compare_at_price,
      images: data.images || [],
      category: data.category,
      vendor: {
        id: data.vendor.id,
        name: data.vendor.name,
        whatsapp_number: data.vendor.whatsapp_number
      },
      inventory_quantity: data.inventory_quantity,
      status: data.status,
      tags: data.tags || []
    };
  }
}

export const productService = new ProductService();
