import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Plus, FileImage, Link2, MapPin, Building, Phone, Mail, Globe, Clock, Info, HelpCircle } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { createTourSlug } from '@/lib/slugUtils';

interface CreateTourModalProps {
  onSuccess?: () => void;
}

const CreateTourModal = ({ onSuccess }: CreateTourModalProps) => {
  const [open, setOpen] = useState(false);
  const [uploadMethod, setUploadMethod] = useState<'images' | 'embed'>('images');
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    location: '',
    businessName: '',
    businessType: '',
    contactPhone: '',
    contactEmail: '',
    website: '',
    businessHours: '',
    embedUrl: '',
    embedType: '' as 'iframe' | 'link' | 'custom' | ''
  });

  const createTourMutation = useMutation({
    mutationFn: async () => {
      if (!user) throw new Error('User not authenticated');

      // Generate unique slug for the tour
      const baseSlug = createTourSlug(formData.title);

      // Check if slug already exists and make it unique
      const { data: existingTours } = await supabase
        .from('tours')
        .select('slug')
        .like('slug', `${baseSlug}%`);

      const existingSlugs = existingTours?.map(t => t.slug).filter(Boolean) || [];
      let uniqueSlug = baseSlug;
      let counter = 1;

      while (existingSlugs.includes(uniqueSlug)) {
        uniqueSlug = `${baseSlug}-${counter}`;
        counter++;
      }

      const tourData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        location: formData.location,
        business_name: formData.businessName || null,
        business_type: formData.businessType || null,
        contact_phone: formData.contactPhone || null,
        contact_email: formData.contactEmail || null,
        website: formData.website || null,
        business_hours: formData.businessHours || null,
        embed_url: uploadMethod === 'embed' ? formData.embedUrl : null,
        embed_type: uploadMethod === 'embed' ? formData.embedType : null,
        creation_method: uploadMethod,
        tour_platform: uploadMethod === 'cloudpano' ? 'cloudpano' : 'commonninja',
        slug: uniqueSlug, // Add the unique slug
        user_id: user.id,
        status: 'draft',
        scenes_count: 0
      };

      const { data, error } = await supabase
        .from('tours')
        .insert(tourData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['admin-all-tours'] });
      queryClient.invalidateQueries({ queryKey: ['admin-tours'] });
      toast.success('Tour created successfully! Opening tour editor...');
      setOpen(false);
      resetForm();
      onSuccess?.();

      // Redirect to tour editor for visual tour creation
      if (uploadMethod === 'images' || uploadMethod === 'cloudpano') {
        navigate(`/admin/tours/${data.id}/edit`);
      }
    },
    onError: (error) => {
      toast.error(`Failed to create tour: ${error.message}`);
    },
  });

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      category: '',
      location: '',
      businessName: '',
      businessType: '',
      contactPhone: '',
      contactEmail: '',
      website: '',
      businessHours: '',
      embedUrl: '',
      embedType: ''
    });
    setUploadMethod('images');
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.title || !formData.category || !formData.location) {
      toast.error('Please fill in all required fields');
      return;
    }

    // For now, create tour directly regardless of method
    createTourMutation.mutate();
  };

  const uploadMethods = [
    {
      id: 'images' as const,
      title: 'CommonNinja Widget',
      description: 'Create tours using CommonNinja platform with widget integration',
      icon: FileImage,
      recommended: true
    },
    {
      id: 'cloudpano' as const,
      title: 'CloudPano 360°',
      description: 'Professional 360° tours with CloudPano platform integration',
      icon: Globe,
      new: true
    },
    {
      id: 'embed' as const,
      title: 'Embed External Tour',
      description: 'Embed tours from Matterport, Kuula, or other platforms',
      icon: Link2
    }
  ];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          Create Tour
        </Button>
      </DialogTrigger>
      <DialogContent className="w-[95vw] max-w-2xl max-h-[95vh] overflow-y-auto sm:w-full">
        <DialogHeader>
          <DialogTitle>Create New Virtual Tour</DialogTitle>
          <DialogDescription>
            Choose your tour creation method and provide basic information
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Simplified Method Selection */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-base font-semibold">Creation Method</Label>
              <Badge variant="outline" className="text-xs">Admin Access</Badge>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {uploadMethods.map((method) => (
                <Card
                  key={method.id}
                  className={`cursor-pointer transition-all ${
                    uploadMethod === method.id
                      ? 'ring-2 ring-blue-500 border-blue-500 bg-blue-50'
                      : 'hover:border-blue-300'
                  }`}
                  onClick={() => setUploadMethod(method.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 rounded-lg bg-blue-100">
                        <method.icon className="w-4 h-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium text-sm">{method.title}</h4>
                          {method.recommended && (
                            <Badge variant="secondary" className="text-xs">Default</Badge>
                          )}
                          {method.new && (
                            <Badge variant="default" className="text-xs bg-green-600">NEW</Badge>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground">{method.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          <Separator />

          {/* Basic Information */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">Basic Information</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title">Tour Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="e.g., Luxury Villa in Lekki Phase 1"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="property">Property</SelectItem>
                    <SelectItem value="education">Education</SelectItem>
                    <SelectItem value="hospitality">Hospitality</SelectItem>
                    <SelectItem value="tourism">Tourism</SelectItem>
                    <SelectItem value="culture">Culture</SelectItem>
                    <SelectItem value="commercial">Commercial</SelectItem>
                    <SelectItem value="healthcare">Healthcare</SelectItem>
                    <SelectItem value="government">Government</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="Describe what visitors will experience in this virtual tour..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="location">Location *</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                  placeholder="e.g., Victoria Island, Lagos"
                  className="pl-10"
                  required
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Business Information */}
          <div className="space-y-4">
            <div>
              <Label className="text-base font-semibold">Business Information</Label>
              <p className="text-sm text-muted-foreground mt-1">
                Optional information for Google Business integration and SEO optimization
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="businessName">Business Name</Label>
                <div className="relative">
                  <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    id="businessName"
                    value={formData.businessName}
                    onChange={(e) => setFormData({ ...formData, businessName: e.target.value })}
                    placeholder="e.g., Luxury Hotels Lagos"
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessType">Business Type</Label>
                <Input
                  id="businessType"
                  value={formData.businessType}
                  onChange={(e) => setFormData({ ...formData, businessType: e.target.value })}
                  placeholder="e.g., Hotel, Restaurant, Office"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="contactPhone">Contact Phone</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    id="contactPhone"
                    value={formData.contactPhone}
                    onChange={(e) => setFormData({ ...formData, contactPhone: e.target.value })}
                    placeholder="e.g., +234 ************"
                    className="pl-10"
                    type="tel"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="contactEmail">Contact Email</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    id="contactEmail"
                    value={formData.contactEmail}
                    onChange={(e) => setFormData({ ...formData, contactEmail: e.target.value })}
                    placeholder="e.g., <EMAIL>"
                    className="pl-10"
                    type="email"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <div className="relative">
                  <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    id="website"
                    value={formData.website}
                    onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                    placeholder="e.g., https://www.business.com"
                    className="pl-10"
                    type="url"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="businessHours">Business Hours</Label>
                <div className="relative">
                  <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    id="businessHours"
                    value={formData.businessHours}
                    onChange={(e) => setFormData({ ...formData, businessHours: e.target.value })}
                    placeholder="e.g., Mon-Fri 9AM-6PM"
                    className="pl-10"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Embed URL for external tours */}
          {uploadMethod === 'embed' && (
            <>
              <Separator />
              <div className="space-y-4">
                <Label className="text-base font-semibold">External Tour Details</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="embedUrl">Tour URL *</Label>
                    <Input
                      id="embedUrl"
                      value={formData.embedUrl}
                      onChange={(e) => setFormData({ ...formData, embedUrl: e.target.value })}
                      placeholder="e.g., https://my.matterport.com/show/?m=..."
                      type="url"
                      required={uploadMethod === 'embed'}
                    />
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Label htmlFor="embedType">Embed Type *</Label>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <HelpCircle className="w-4 h-4 text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent className="max-w-sm">
                            <div className="space-y-2">
                              <p className="font-semibold">Choose how to display your tour:</p>
                              <div className="space-y-1 text-sm">
                                <p><strong>Iframe Embed:</strong> Embeds the tour directly in your page (best for Matterport, Kuula)</p>
                                <p><strong>Direct Link:</strong> Opens tour in a new window/tab</p>
                                <p><strong>Custom Integration:</strong> For advanced setups or custom tour viewers</p>
                              </div>
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Select
                      value={formData.embedType}
                      onValueChange={(value: 'iframe' | 'link' | 'custom') => setFormData({ ...formData, embedType: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select embed type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="iframe">
                          <div className="flex flex-col">
                            <span>Iframe Embed</span>
                            <span className="text-xs text-muted-foreground">Embeds tour directly in page</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="link">
                          <div className="flex flex-col">
                            <span>Direct Link</span>
                            <span className="text-xs text-muted-foreground">Opens in new window/tab</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="custom">
                          <div className="flex flex-col">
                            <span>Custom Integration</span>
                            <span className="text-xs text-muted-foreground">For advanced setups</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={createTourMutation.isPending}>
              {createTourMutation.isPending ? 'Creating...' : 'Create Tour'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateTourModal;
