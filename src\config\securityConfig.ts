/**
 * Security Configuration for Virtual Real Tour
 * Implements CSP, SRI, and other security measures
 */

// Content Security Policy configuration
export const CSP_CONFIG = {
  // Base directives for all environments
  base: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'", // Required for Vite in development
      "'unsafe-eval'", // Required for development
      'https://dnbjrfgfugpmyrconepx.supabase.co',
      'https://cdn.jsdelivr.net',
      'https://unpkg.com',
      'https://vercel.live'
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'", // Required for styled components and CSS-in-JS
      'https://fonts.googleapis.com',
      'https://cdn.jsdelivr.net'
    ],
    'img-src': [
      "'self'",
      'data:',
      'blob:',
      'https:',
      'https://dnbjrfgfugpmyrconepx.supabase.co',
      'https://images.unsplash.com',
      'https://via.placeholder.com',
      'https://picsum.photos'
    ],
    'font-src': [
      "'self'",
      'https://fonts.gstatic.com',
      'https://cdn.jsdelivr.net',
      'data:'
    ],
    'connect-src': [
      "'self'",
      'https://dnbjrfgfugpmyrconepx.supabase.co',
      'wss://dnbjrfgfugpmyrconepx.supabase.co',
      'https://api.vercel.com',
      'https://vitals.vercel-analytics.com'
    ],
    'frame-src': [
      "'self'",
      'https://my.matterport.com',
      'https://kuula.co',
      'https://momento360.com',
      'https://panoee.com',
      'https://tourmkr.com',
      'https://vercel.live'
    ],
    'worker-src': [
      "'self'",
      'blob:'
    ],
    'manifest-src': ["'self'"],
    'media-src': [
      "'self'",
      'https:',
      'blob:',
      'data:'
    ]
  },

  // Production-specific overrides
  production: {
    'script-src': [
      "'self'",
      'https://dnbjrfgfugpmyrconepx.supabase.co',
      'https://cdn.jsdelivr.net',
      'https://vercel.live'
    ],
    'style-src': [
      "'self'",
      'https://fonts.googleapis.com',
      'https://cdn.jsdelivr.net'
    ]
  },

  // Development-specific overrides
  development: {
    'script-src': [
      "'self'",
      "'unsafe-inline'",
      "'unsafe-eval'",
      'https://dnbjrfgfugpmyrconepx.supabase.co',
      'http://localhost:*',
      'ws://localhost:*'
    ],
    'connect-src': [
      "'self'",
      'https://dnbjrfgfugpmyrconepx.supabase.co',
      'wss://dnbjrfgfugpmyrconepx.supabase.co',
      'http://localhost:*',
      'ws://localhost:*',
      'ws://127.0.0.1:*'
    ]
  }
};

// Security headers configuration
export const SECURITY_HEADERS = {
  // Prevent clickjacking
  'X-Frame-Options': 'SAMEORIGIN',
  
  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  
  // Enable XSS protection
  'X-XSS-Protection': '1; mode=block',
  
  // Referrer policy
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  
  // Permissions policy
  'Permissions-Policy': [
    'camera=self',
    'microphone=self',
    'geolocation=self',
    'payment=none',
    'usb=none'
  ].join(', '),
  
  // Strict Transport Security (HTTPS only)
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
};

// Subresource Integrity (SRI) hashes for external resources
export const SRI_HASHES = {
  // CDN resources with their integrity hashes
  'https://cdn.jsdelivr.net/npm/three@0.160.1/build/three.min.js': 
    'sha384-example-hash-for-three-js',
  
  'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap':
    'sha384-example-hash-for-inter-font',
    
  // Add more external resources as needed
};

// Rate limiting configuration
export const RATE_LIMIT_CONFIG = {
  // API endpoints rate limits (requests per minute)
  api: {
    '/api/tours': 60,
    '/api/upload': 10,
    '/api/auth': 20,
    '/api/admin': 30
  },
  
  // Global rate limit
  global: 1000, // requests per minute per IP
  
  // Burst allowance
  burst: 10
};

// Input validation patterns
export const VALIDATION_PATTERNS = {
  // Tour-related validation
  tourTitle: /^[a-zA-Z0-9\s\-_.,!?()]{1,100}$/,
  tourDescription: /^[a-zA-Z0-9\s\-_.,!?()\n]{1,1000}$/,
  
  // URL validation for embeds
  embedUrl: /^https:\/\/(my\.matterport\.com|kuula\.co|momento360\.com|panoee\.com|tourmkr\.com)\/.+$/,
  
  // File validation
  imageFile: /\.(jpg|jpeg|png|webp|avif)$/i,
  maxFileSize: 10 * 1024 * 1024, // 10MB
  
  // User input validation
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s\-()]{10,15}$/
};

// Generate CSP header string
export function generateCSPHeader(environment: 'development' | 'production' = 'production'): string {
  const config = { ...CSP_CONFIG.base };
  
  // Apply environment-specific overrides
  if (environment === 'production') {
    Object.assign(config, CSP_CONFIG.production);
  } else {
    Object.assign(config, CSP_CONFIG.development);
  }
  
  // Convert to CSP header format
  const directives = Object.entries(config).map(([directive, sources]) => {
    return `${directive} ${sources.join(' ')}`;
  });
  
  return directives.join('; ');
}

// Validate input against security patterns
export function validateInput(input: string, pattern: keyof typeof VALIDATION_PATTERNS): boolean {
  const regex = VALIDATION_PATTERNS[pattern];
  if (regex instanceof RegExp) {
    return regex.test(input);
  }
  return false;
}

// Sanitize user input
export function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

// Check if URL is allowed for embedding
export function isAllowedEmbedUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    const allowedDomains = [
      'my.matterport.com',
      'kuula.co',
      'momento360.com',
      'panoee.com',
      'tourmkr.com'
    ];
    
    return allowedDomains.some(domain => urlObj.hostname === domain);
  } catch {
    return false;
  }
}

// Security monitoring and logging
export class SecurityMonitor {
  private static violations: Array<{
    type: string;
    details: string;
    timestamp: Date;
    userAgent?: string;
  }> = [];

  static logViolation(type: string, details: string, userAgent?: string) {
    this.violations.push({
      type,
      details,
      timestamp: new Date(),
      userAgent
    });

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.warn(`Security violation: ${type}`, details);
    }

    // In production, you might want to send this to a monitoring service
    if (process.env.NODE_ENV === 'production') {
      // Send to monitoring service
      this.sendToMonitoringService({ type, details, timestamp: new Date(), userAgent });
    }
  }

  static getViolations() {
    return [...this.violations];
  }

  static clearViolations() {
    this.violations = [];
  }

  private static async sendToMonitoringService(violation: any) {
    try {
      // Example: Send to your monitoring service
      // await fetch('/api/security/violations', {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(violation)
      // });
    } catch (error) {
      console.error('Failed to send security violation to monitoring service:', error);
    }
  }
}
