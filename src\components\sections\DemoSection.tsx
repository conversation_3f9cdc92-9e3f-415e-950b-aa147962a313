
import { useQuery } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useRef } from 'react'
import { getCleanEmbedUrl } from '@/utils/cleanTourEmbedding'

const DemoSection = () => {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Fetch demo tour from database
  const { data: demoTourData } = useQuery({
    queryKey: ['demo-tour-direct'],
    queryFn: async () => {
      try {
        // Get demo tour directly from tours table
        const { data: assignmentData, error: assignmentError } = await supabase
          .from('featured_tour_assignments')
          .select('tour_id')
          .eq('section_type', 'demo')
          .eq('is_active', true)
          .order('display_order')
          .limit(1);

        if (assignmentError || !assignmentData?.[0]) {
          return null;
        }

        // Get the actual tour data
        const { data: tourData, error: tourError } = await supabase
          .from('tours')
          .select('*')
          .eq('id', assignmentData[0].tour_id)
          .single();

        if (tourError || !tourData) {
          return null;
        }

        return tourData;


      } catch (err) {
        return null;
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime)
    refetchOnMount: true,
    refetchOnWindowFocus: false
  });



  return (
    <section className="bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-grid-slate-100"></div>
      </div>

      <div className="container py-20 lg:py-32 relative z-10">
        <div className="text-center mb-16 lg:mb-20">
          <div className="inline-flex items-center rounded-full bg-white/10 backdrop-blur-sm border border-white/20 px-4 py-2 text-sm font-medium text-white mb-6">
            🎬 Interactive Demo
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
            Experience Our
            <br />
            <span className="text-theme-primary">Virtual Tours</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto leading-relaxed">
            Step into immersive 360° experiences that showcase spaces in stunning detail.
            See how our technology transforms ordinary spaces into extraordinary virtual journeys.
          </p>
        </div>
        
        <div className="max-w-7xl mx-auto px-4">
          {demoTourData && demoTourData.embed_url ? (
            <>
              <div className="overflow-hidden shadow-2xl border-0 bg-white/5 backdrop-blur-sm rounded-lg">
                <div className="relative aspect-video demo-tour-mobile-height">
                  <iframe
                    ref={iframeRef}
                    src={getCleanEmbedUrl(demoTourData.embed_url)}
                    className="w-full h-full border-0 outline-none bg-transparent demo-tour-iframe"
                    allowFullScreen
                    title="Virtual Tour"
                    allow="vr; xr; accelerometer; gyroscope; fullscreen; camera; geolocation;"
                    sandbox="allow-scripts allow-same-origin allow-presentation allow-orientation-lock allow-pointer-lock"
                    onLoad={() => {
                      if (iframeRef.current) {
                        iframeRef.current.classList.add('loaded');
                      }
                    }}
                  />
                </div>
              </div>
            </>
          ) : null}
        </div>
      </div>
    </section>
  )
}

export default DemoSection
