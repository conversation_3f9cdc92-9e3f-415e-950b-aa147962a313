// Utility to format WhatsApp checkout message URL
export interface ProductMetaData {
  key: string;
  value: string;
}

export interface WhatsAppProduct {
  name: string;
  price: string;
  meta_data?: ProductMetaData[];
}

export interface WhatsAppMessageParams {
  product: WhatsAppProduct;
  tourSlug: string;
  customerName?: string;
}

export function formatWhatsAppMessage({ product, tourSlug, customerName }: WhatsAppMessageParams): string {
  const vendorNumber = product.meta_data?.find((d: ProductMetaData) => d.key === 'vendor_whatsapp')?.value || '';
  const text = `Hello, I'm interested in:\n🛍️ *${product.name}*\n💰 Price: ${product.price}\n🔗 Tour: https://virtualrealtour.ng/tour/${tourSlug}${customerName ? `\n👤 Name: ${customerName}` : ''}`;
  return `https://wa.me/${vendorNumber}?text=${encodeURIComponent(text)}`;
}
