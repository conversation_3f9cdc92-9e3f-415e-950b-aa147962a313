/**
 * Edge Caching Utilities for Virtual Real Tour
 * Implements advanced caching strategies with CDN integration
 */

import { PERFORMANCE_CONFIG } from '@/config/performanceConfig';

// Cache strategies enum
export enum CacheStrategy {
  CACHE_FIRST = 'cache-first',
  NETWORK_FIRST = 'network-first',
  STALE_WHILE_REVALIDATE = 'stale-while-revalidate',
  NETWORK_ONLY = 'network-only',
  CACHE_ONLY = 'cache-only'
}

// Cache configuration interface
interface CacheConfig {
  strategy: CacheStrategy;
  maxAge: number;
  staleWhileRevalidate?: number;
  cacheName: string;
  networkTimeoutMs?: number;
}

// Predefined cache configurations for different resource types
export const CACHE_CONFIGS: Record<string, CacheConfig> = {
  tours: {
    strategy: CacheStrategy.STALE_WHILE_REVALIDATE,
    maxAge: 60 * 60 * 1000, // 1 hour
    staleWhileRevalidate: 24 * 60 * 60 * 1000, // 24 hours
    cacheName: 'tours-cache',
    networkTimeoutMs: 5000
  },
  images: {
    strategy: CacheStrategy.CACHE_FIRST,
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    cacheName: 'images-cache',
    networkTimeoutMs: 10000
  },
  api: {
    strategy: CacheStrategy.STALE_WHILE_REVALIDATE,
    maxAge: 5 * 60 * 1000, // 5 minutes
    staleWhileRevalidate: 60 * 60 * 1000, // 1 hour
    cacheName: 'api-cache',
    networkTimeoutMs: 3000
  },
  static: {
    strategy: CacheStrategy.CACHE_FIRST,
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    cacheName: 'static-cache',
    networkTimeoutMs: 5000
  }
};

// Edge caching manager class
export class EdgeCacheManager {
  private static instance: EdgeCacheManager;
  private cacheStorage: CacheStorage;

  private constructor() {
    this.cacheStorage = caches;
  }

  static getInstance(): EdgeCacheManager {
    if (!EdgeCacheManager.instance) {
      EdgeCacheManager.instance = new EdgeCacheManager();
    }
    return EdgeCacheManager.instance;
  }

  // Main caching method with strategy selection
  async handleRequest(request: Request, config: CacheConfig): Promise<Response> {
    switch (config.strategy) {
      case CacheStrategy.CACHE_FIRST:
        return this.cacheFirst(request, config);
      case CacheStrategy.NETWORK_FIRST:
        return this.networkFirst(request, config);
      case CacheStrategy.STALE_WHILE_REVALIDATE:
        return this.staleWhileRevalidate(request, config);
      case CacheStrategy.NETWORK_ONLY:
        return this.networkOnly(request);
      case CacheStrategy.CACHE_ONLY:
        return this.cacheOnly(request, config);
      default:
        return this.networkFirst(request, config);
    }
  }

  // Cache-first strategy
  private async cacheFirst(request: Request, config: CacheConfig): Promise<Response> {
    const cache = await this.cacheStorage.open(config.cacheName);
    const cachedResponse = await cache.match(request);

    if (cachedResponse && !this.isExpired(cachedResponse, config.maxAge)) {
      return cachedResponse;
    }

    try {
      const networkResponse = await this.fetchWithTimeout(request, config.networkTimeoutMs);
      if (networkResponse.ok) {
        await this.cacheResponse(cache, request, networkResponse.clone(), config);
      }
      return networkResponse;
    } catch (error) {
      if (cachedResponse) {
        return cachedResponse; // Return stale cache if network fails
      }
      throw error;
    }
  }

  // Network-first strategy
  private async networkFirst(request: Request, config: CacheConfig): Promise<Response> {
    const cache = await this.cacheStorage.open(config.cacheName);

    try {
      const networkResponse = await this.fetchWithTimeout(request, config.networkTimeoutMs);
      if (networkResponse.ok) {
        await this.cacheResponse(cache, request, networkResponse.clone(), config);
      }
      return networkResponse;
    } catch (error) {
      const cachedResponse = await cache.match(request);
      if (cachedResponse) {
        return cachedResponse;
      }
      throw error;
    }
  }

  // Stale-while-revalidate strategy
  private async staleWhileRevalidate(request: Request, config: CacheConfig): Promise<Response> {
    const cache = await this.cacheStorage.open(config.cacheName);
    const cachedResponse = await cache.match(request);

    // If we have a cached response, check if it's fresh or stale
    if (cachedResponse) {
      const isStale = this.isExpired(cachedResponse, config.maxAge);
      
      if (!isStale) {
        // Fresh cache - return immediately
        return cachedResponse;
      } else if (config.staleWhileRevalidate && 
                 !this.isExpired(cachedResponse, config.staleWhileRevalidate)) {
        // Stale but within revalidate window - return stale and update in background
        this.revalidateInBackground(request, cache, config);
        return cachedResponse;
      }
    }

    // No cache or expired - fetch from network
    try {
      const networkResponse = await this.fetchWithTimeout(request, config.networkTimeoutMs);
      if (networkResponse.ok) {
        await this.cacheResponse(cache, request, networkResponse.clone(), config);
      }
      return networkResponse;
    } catch (error) {
      if (cachedResponse) {
        return cachedResponse; // Return stale cache if network fails
      }
      throw error;
    }
  }

  // Network-only strategy
  private async networkOnly(request: Request): Promise<Response> {
    return fetch(request);
  }

  // Cache-only strategy
  private async cacheOnly(request: Request, config: CacheConfig): Promise<Response> {
    const cache = await this.cacheStorage.open(config.cacheName);
    const cachedResponse = await cache.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    throw new Error('No cached response available');
  }

  // Helper methods
  private async fetchWithTimeout(request: Request, timeoutMs: number = 5000): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

    try {
      const response = await fetch(request, { signal: controller.signal });
      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  private async cacheResponse(
    cache: Cache, 
    request: Request, 
    response: Response, 
    config: CacheConfig
  ): Promise<void> {
    // Add metadata headers
    const headers = new Headers(response.headers);
    headers.set('sw-cached-date', new Date().toISOString());
    headers.set('sw-cache-strategy', config.strategy);
    headers.set('sw-max-age', config.maxAge.toString());

    const responseToCache = new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: headers
    });

    await cache.put(request, responseToCache);
  }

  private isExpired(response: Response, maxAge: number): boolean {
    const cachedDate = response.headers.get('sw-cached-date');
    if (!cachedDate) return true;

    const cacheTime = new Date(cachedDate).getTime();
    return Date.now() - cacheTime > maxAge;
  }

  private async revalidateInBackground(
    request: Request, 
    cache: Cache, 
    config: CacheConfig
  ): Promise<void> {
    try {
      const networkResponse = await fetch(request);
      if (networkResponse.ok) {
        await this.cacheResponse(cache, request, networkResponse, config);
      }
    } catch (error) {
      console.warn('Background revalidation failed:', error);
    }
  }

  // Cache management methods
  async clearCache(cacheName?: string): Promise<void> {
    if (cacheName) {
      await this.cacheStorage.delete(cacheName);
    } else {
      const cacheNames = await this.cacheStorage.keys();
      await Promise.all(cacheNames.map(name => this.cacheStorage.delete(name)));
    }
  }

  async getCacheSize(cacheName: string): Promise<number> {
    const cache = await this.cacheStorage.open(cacheName);
    const requests = await cache.keys();
    let totalSize = 0;

    for (const request of requests) {
      const response = await cache.match(request);
      if (response) {
        const blob = await response.blob();
        totalSize += blob.size;
      }
    }

    return totalSize;
  }

  async cleanupExpiredEntries(cacheName: string, maxAge: number): Promise<number> {
    const cache = await this.cacheStorage.open(cacheName);
    const requests = await cache.keys();
    let deletedCount = 0;

    for (const request of requests) {
      const response = await cache.match(request);
      if (response && this.isExpired(response, maxAge)) {
        await cache.delete(request);
        deletedCount++;
      }
    }

    return deletedCount;
  }
}

// Utility functions for easy access
export const edgeCache = EdgeCacheManager.getInstance();

// Preload critical resources with edge caching
export async function preloadCriticalResources(urls: string[]): Promise<void> {
  const promises = urls.map(async (url) => {
    try {
      const request = new Request(url);
      const config = CACHE_CONFIGS.static;
      await edgeCache.handleRequest(request, config);
    } catch (error) {
      console.warn(`Failed to preload resource: ${url}`, error);
    }
  });

  await Promise.allSettled(promises);
}

// Cache tour data with optimized strategy
export async function cacheTourData(tourId: string): Promise<void> {
  const tourUrl = `/api/tours/${tourId}`;
  const request = new Request(tourUrl);
  const config = CACHE_CONFIGS.tours;
  
  try {
    await edgeCache.handleRequest(request, config);
  } catch (error) {
    console.warn(`Failed to cache tour data: ${tourId}`, error);
  }
}

// Periodic cache cleanup
export function startCacheCleanup(): void {
  // Clean up expired entries every hour
  setInterval(async () => {
    try {
      for (const [type, config] of Object.entries(CACHE_CONFIGS)) {
        const deletedCount = await edgeCache.cleanupExpiredEntries(
          config.cacheName, 
          config.maxAge
        );
        
        if (deletedCount > 0) {
          console.log(`Cleaned up ${deletedCount} expired entries from ${type} cache`);
        }
      }
    } catch (error) {
      console.warn('Cache cleanup failed:', error);
    }
  }, 60 * 60 * 1000); // 1 hour
}
