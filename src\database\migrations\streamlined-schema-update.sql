-- VirtualRealTour Streamlined Schema Update
-- Tour-centric architecture with proper vendor relationships and overlay management

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Update tours table for tour-centric architecture
ALTER TABLE tours 
ADD COLUMN IF NOT EXISTS platform_type VARCHAR(50) DEFAULT 'custom',
ADD COLUMN IF NOT EXISTS source_platform_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS embed_config J<PERSON>NB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS overlay_settings JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS seo_metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS analytics_data JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS featured_priority INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS auto_approved BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS vendor_id UUID REFERENCES vendors(id) ON DELETE SET NULL;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_tours_platform_type ON tours(platform_type);
CREATE INDEX IF NOT EXISTS idx_tours_vendor_id ON tours(vendor_id);
CREATE INDEX IF NOT EXISTS idx_tours_featured ON tours(featured, featured_priority);
CREATE INDEX IF NOT EXISTS idx_tours_status_created ON tours(status, created_at);

-- Update vendors table for enhanced vendor management
ALTER TABLE vendors 
ADD COLUMN IF NOT EXISTS business_type VARCHAR(100),
ADD COLUMN IF NOT EXISTS commission_rate DECIMAL(5,4) DEFAULT 0.05,
ADD COLUMN IF NOT EXISTS auto_approve_tours BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS whatsapp_number VARCHAR(20),
ADD COLUMN IF NOT EXISTS business_address TEXT,
ADD COLUMN IF NOT EXISTS business_description TEXT,
ADD COLUMN IF NOT EXISTS verification_status VARCHAR(50) DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS verification_documents JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS settings JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS performance_metrics JSONB DEFAULT '{}';

-- Create overlay templates table
CREATE TABLE IF NOT EXISTS overlay_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_type VARCHAR(100) NOT NULL, -- 'product_card', 'info_tooltip', 'glass_overlay', etc.
    style_config JSONB NOT NULL DEFAULT '{}',
    animation_config JSONB DEFAULT '{}',
    icon_config JSONB DEFAULT '{}',
    is_system_template BOOLEAN DEFAULT false,
    created_by UUID REFERENCES profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create hotspots table for tour hotspot management
CREATE TABLE IF NOT EXISTS tour_hotspots (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tour_id UUID NOT NULL REFERENCES tours(id) ON DELETE CASCADE,
    scene_id VARCHAR(255), -- Reference to scene within the tour
    position JSONB NOT NULL, -- {x, y, z} or {pitch, yaw} coordinates
    hotspot_type VARCHAR(100) NOT NULL, -- 'product', 'info', 'navigation', 'media'
    title VARCHAR(255),
    description TEXT,
    icon_config JSONB DEFAULT '{}',
    animation_config JSONB DEFAULT '{}',
    overlay_template_id UUID REFERENCES overlay_templates(id) ON DELETE SET NULL,
    product_id UUID REFERENCES products(id) ON DELETE SET NULL,
    action_config JSONB DEFAULT '{}', -- Action when hotspot is clicked
    visibility_rules JSONB DEFAULT '{}', -- When/how to show hotspot
    analytics_data JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for hotspots
CREATE INDEX IF NOT EXISTS idx_hotspots_tour_id ON tour_hotspots(tour_id);
CREATE INDEX IF NOT EXISTS idx_hotspots_type ON tour_hotspots(hotspot_type);
CREATE INDEX IF NOT EXISTS idx_hotspots_product_id ON tour_hotspots(product_id);

-- Update products table for better tour integration
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS vendor_id UUID REFERENCES vendors(id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS tour_context JSONB DEFAULT '{}', -- Which tours this product appears in
ADD COLUMN IF NOT EXISTS overlay_config JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS whatsapp_config JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS seo_metadata JSONB DEFAULT '{}';

-- Create index for products vendor relationship
CREATE INDEX IF NOT EXISTS idx_products_vendor_id ON products(vendor_id);

-- Enhanced orders table for unified commerce
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS vendor_id UUID REFERENCES vendors(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS tour_context JSONB DEFAULT '{}', -- Tour and scene where order originated
ADD COLUMN IF NOT EXISTS whatsapp_data JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS fulfillment_data JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS analytics_data JSONB DEFAULT '{}';

-- Create order_items table if it doesn't exist
CREATE TABLE IF NOT EXISTS order_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    vendor_id UUID REFERENCES vendors(id) ON DELETE SET NULL,
    tour_context JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for order items
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);
CREATE INDEX IF NOT EXISTS idx_order_items_vendor_id ON order_items(vendor_id);

-- Create admin settings table for centralized configuration
CREATE TABLE IF NOT EXISTS admin_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    category VARCHAR(100) DEFAULT 'general',
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default admin settings
INSERT INTO admin_settings (key, value, description, category, is_public) VALUES
('site_config', '{"title": "VirtualRealTour Nigeria", "description": "Experience Nigeria through immersive virtual tours", "contact_email": "<EMAIL>"}', 'Basic site configuration', 'general', true),
('whatsapp_config', '{"business_number": "+234XXXXXXXXXX", "enabled": true, "auto_respond": false}', 'WhatsApp commerce configuration', 'commerce', false),
('tour_defaults', '{"auto_approve_vendor_tours": false, "featured_limit": 6, "demo_tours_enabled": true}', 'Default tour settings', 'tours', false),
('commerce_config', '{"default_commission_rate": 0.05, "payment_methods": ["whatsapp", "bank_transfer", "card"], "auto_fulfill": false}', 'Commerce configuration', 'commerce', false)
ON CONFLICT (key) DO NOTHING;

-- Create WhatsApp messages table for tracking
CREATE TABLE IF NOT EXISTS whatsapp_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    to_number VARCHAR(20) NOT NULL,
    from_number VARCHAR(20),
    message_content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text',
    template_name VARCHAR(100),
    status VARCHAR(50) DEFAULT 'pending',
    external_id VARCHAR(255),
    order_id UUID REFERENCES orders(id) ON DELETE SET NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    delivered_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for WhatsApp messages
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_to_number ON whatsapp_messages(to_number);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_order_id ON whatsapp_messages(order_id);
CREATE INDEX IF NOT EXISTS idx_whatsapp_messages_status ON whatsapp_messages(status);

-- Create tour analytics table
CREATE TABLE IF NOT EXISTS tour_analytics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tour_id UUID NOT NULL REFERENCES tours(id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL, -- 'view', 'hotspot_click', 'product_view', 'order_initiated'
    event_data JSONB DEFAULT '{}',
    user_session VARCHAR(255),
    user_agent TEXT,
    ip_address INET,
    referrer TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for analytics
CREATE INDEX IF NOT EXISTS idx_tour_analytics_tour_id ON tour_analytics(tour_id);
CREATE INDEX IF NOT EXISTS idx_tour_analytics_event_type ON tour_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_tour_analytics_created_at ON tour_analytics(created_at);

-- Create vendor performance view
CREATE OR REPLACE VIEW vendor_performance AS
SELECT 
    v.id as vendor_id,
    v.name as vendor_name,
    v.status as vendor_status,
    COUNT(DISTINCT t.id) as total_tours,
    COUNT(DISTINCT CASE WHEN t.status = 'published' THEN t.id END) as published_tours,
    COUNT(DISTINCT p.id) as total_products,
    COUNT(DISTINCT o.id) as total_orders,
    COALESCE(SUM(o.total_amount), 0) as total_revenue,
    COALESCE(AVG(o.total_amount), 0) as avg_order_value,
    COUNT(DISTINCT CASE WHEN o.status = 'completed' THEN o.id END)::FLOAT / NULLIF(COUNT(DISTINCT o.id), 0) * 100 as completion_rate
FROM vendors v
LEFT JOIN tours t ON v.id = t.vendor_id
LEFT JOIN products p ON v.id = p.vendor_id
LEFT JOIN orders o ON v.id = o.vendor_id
GROUP BY v.id, v.name, v.status;

-- Create tour performance view
CREATE OR REPLACE VIEW tour_performance AS
SELECT 
    t.id as tour_id,
    t.title as tour_title,
    t.status as tour_status,
    t.featured,
    v.name as vendor_name,
    COUNT(DISTINCT th.id) as total_hotspots,
    COUNT(DISTINCT CASE WHEN th.hotspot_type = 'product' THEN th.id END) as product_hotspots,
    COUNT(DISTINCT ta.id) as total_views,
    COUNT(DISTINCT CASE WHEN ta.event_type = 'hotspot_click' THEN ta.id END) as hotspot_clicks,
    COUNT(DISTINCT o.id) as orders_generated,
    COALESCE(SUM(o.total_amount), 0) as revenue_generated
FROM tours t
LEFT JOIN vendors v ON t.vendor_id = v.id
LEFT JOIN tour_hotspots th ON t.id = th.tour_id
LEFT JOIN tour_analytics ta ON t.id = ta.tour_id
LEFT JOIN orders o ON (o.tour_context->>'tour_id')::UUID = t.id
GROUP BY t.id, t.title, t.status, t.featured, v.name;

-- Update RLS policies for new tables
ALTER TABLE overlay_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE tour_hotspots ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE whatsapp_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE tour_analytics ENABLE ROW LEVEL SECURITY;

-- RLS policies for overlay_templates
CREATE POLICY "Public can view system templates" ON overlay_templates
    FOR SELECT USING (is_system_template = true);

CREATE POLICY "Users can view their own templates" ON overlay_templates
    FOR SELECT USING (created_by = auth.uid());

CREATE POLICY "Users can create templates" ON overlay_templates
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can update their own templates" ON overlay_templates
    FOR UPDATE USING (created_by = auth.uid());

-- RLS policies for tour_hotspots
CREATE POLICY "Public can view hotspots for published tours" ON tour_hotspots
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM tours 
            WHERE tours.id = tour_hotspots.tour_id 
            AND tours.status = 'published'
        )
    );

CREATE POLICY "Vendors can manage their tour hotspots" ON tour_hotspots
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM tours 
            WHERE tours.id = tour_hotspots.tour_id 
            AND tours.vendor_id = auth.uid()
        )
    );

-- RLS policies for admin_settings
CREATE POLICY "Public can view public settings" ON admin_settings
    FOR SELECT USING (is_public = true);

CREATE POLICY "Admins can manage all settings" ON admin_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers to relevant tables
CREATE TRIGGER update_overlay_templates_updated_at BEFORE UPDATE ON overlay_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tour_hotspots_updated_at BEFORE UPDATE ON tour_hotspots FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_admin_settings_updated_at BEFORE UPDATE ON admin_settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT SELECT ON vendor_performance TO authenticated;
GRANT SELECT ON tour_performance TO authenticated;

-- Create function to get tour embed configuration
CREATE OR REPLACE FUNCTION get_tour_embed_config(tour_uuid UUID)
RETURNS JSONB AS $$
DECLARE
    tour_record RECORD;
    embed_config JSONB;
BEGIN
    SELECT * INTO tour_record FROM tours WHERE id = tour_uuid AND status = 'published';
    
    IF NOT FOUND THEN
        RETURN NULL;
    END IF;
    
    embed_config := jsonb_build_object(
        'tour_id', tour_record.id,
        'title', tour_record.title,
        'description', tour_record.description,
        'thumbnail_url', tour_record.thumbnail_url,
        'platform_type', tour_record.platform_type,
        'embed_url', tour_record.embed_url,
        'cloudpano_embed_url', tour_record.cloudpano_embed_url,
        'embed_config', tour_record.embed_config,
        'overlay_settings', tour_record.overlay_settings
    );
    
    RETURN embed_config;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
