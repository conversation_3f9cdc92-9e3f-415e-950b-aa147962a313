/**
 * ProductOverlay Component
 * Glass morphism overlay for product details in virtual tours
 * Integrates with WooCommerce and WhatsApp checkout
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  X, 
  ShoppingCart, 
  MessageCircle, 
  Star, 
  Plus, 
  Minus,
  ExternalLink,
  Heart,
  Share2,
  Info,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

// Import enhanced image component
import EnhancedImage from '@/components/ui/enhanced-image';
import type { WooCommerceProduct } from '@/services/commerce/WooCommerceService';

interface ProductOverlayProps {
  product: WooCommerceProduct | null;
  isOpen: boolean;
  isLoading?: boolean;
  onClose: () => void;
  onAddToCart?: (product: WooCommerceProduct, quantity: number) => void;
  className?: string;
}

const ProductOverlay: React.FC<ProductOverlayProps> = ({
  product,
  isOpen,
  isLoading = false,
  onClose,
  onAddToCart,
  className = ''
}) => {
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isWishlisted, setIsWishlisted] = useState(false);

  // Reset state when product changes
  useEffect(() => {
    if (product) {
      setQuantity(1);
      setSelectedImageIndex(0);
    }
  }, [product]);

  // Handle WhatsApp checkout
  const handleWhatsAppCheckout = () => {
    if (!product) return;

    const whatsappNumber = product.vendor_whatsapp || product.meta_data?.find(
      meta => meta.key === 'vendor_whatsapp' || meta.key === '_vendor_whatsapp'
    )?.value;

    if (!whatsappNumber) {
      toast.error('Vendor WhatsApp number not available');
      return;
    }

    const message = encodeURIComponent(
      `Hi! I'm interested in purchasing:\n\n` +
      `Product: ${product.name}\n` +
      `Price: ₦${product.price}\n` +
      `Quantity: ${quantity}\n` +
      `Total: ₦${(parseFloat(product.price) * quantity).toLocaleString()}\n\n` +
      `Product Link: ${product.permalink}\n\n` +
      `Please let me know how to proceed with the order.`
    );

    const whatsappUrl = `https://wa.me/${whatsappNumber.replace(/[^\d]/g, '')}?text=${message}`;
    window.open(whatsappUrl, '_blank');
    
    toast.success('Opening WhatsApp...');
  };

  // Handle add to cart
  const handleAddToCart = () => {
    if (!product || !onAddToCart) return;
    
    onAddToCart(product, quantity);
    toast.success(`Added ${quantity} item(s) to cart`);
  };

  // Handle share
  const handleShare = async () => {
    if (!product) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: product.name,
          text: product.short_description,
          url: product.permalink
        });
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      await navigator.clipboard.writeText(product.permalink);
      toast.success('Product link copied to clipboard');
    }
  };

  // Format price
  const formatPrice = (price: string) => {
    return `₦${parseFloat(price).toLocaleString()}`;
  };

  // Calculate discount percentage
  const getDiscountPercentage = () => {
    if (!product?.regular_price || !product?.sale_price) return 0;
    const regular = parseFloat(product.regular_price);
    const sale = parseFloat(product.sale_price);
    return Math.round(((regular - sale) / regular) * 100);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className={cn(
            'fixed inset-0 z-50 flex items-center justify-center p-4',
            'bg-black/60 backdrop-blur-sm',
            className
          )}
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Glass morphism card */}
            <Card className="bg-white/95 backdrop-blur-xl border-white/20 shadow-2xl">
              {/* Close button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="absolute top-4 right-4 z-10 bg-black/10 hover:bg-black/20 text-gray-700"
              >
                <X className="w-4 h-4" />
              </Button>

              {/* Loading state */}
              {isLoading && (
                <div className="flex items-center justify-center p-12">
                  <div className="text-center">
                    <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
                    <p className="text-gray-600">Loading product...</p>
                  </div>
                </div>
              )}

              {/* Product content */}
              {!isLoading && product && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 p-6">
                  {/* Product Images */}
                  <div className="space-y-4">
                    {/* Main image */}
                    <div className="aspect-square rounded-xl overflow-hidden bg-gray-100">
                      <EnhancedImage
                        src={product.images[selectedImageIndex]?.src || '/placeholder-product.jpg'}
                        alt={product.images[selectedImageIndex]?.alt || product.name}
                        className="w-full h-full object-cover"
                        priority={true}
                      />
                    </div>

                    {/* Thumbnail gallery */}
                    {product.images.length > 1 && (
                      <div className="flex gap-2 overflow-x-auto">
                        {product.images.map((image, index) => (
                          <button
                            key={image.id}
                            onClick={() => setSelectedImageIndex(index)}
                            className={cn(
                              'flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all',
                              selectedImageIndex === index
                                ? 'border-blue-500 ring-2 ring-blue-200'
                                : 'border-gray-200 hover:border-gray-300'
                            )}
                          >
                            <img
                              src={image.src}
                              alt={image.alt}
                              className="w-full h-full object-cover"
                            />
                          </button>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Product Details */}
                  <div className="space-y-6">
                    {/* Header */}
                    <div>
                      <div className="flex items-start justify-between mb-2">
                        <h2 className="text-2xl font-bold text-gray-900 leading-tight">
                          {product.name}
                        </h2>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setIsWishlisted(!isWishlisted)}
                            className="text-gray-500 hover:text-red-500"
                          >
                            <Heart className={cn('w-4 h-4', isWishlisted && 'fill-red-500 text-red-500')} />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={handleShare}
                            className="text-gray-500 hover:text-blue-500"
                          >
                            <Share2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Price */}
                      <div className="flex items-center gap-3 mb-3">
                        <span className="text-3xl font-bold text-blue-600">
                          {formatPrice(product.price)}
                        </span>
                        {product.on_sale && product.regular_price && (
                          <>
                            <span className="text-lg text-gray-500 line-through">
                              {formatPrice(product.regular_price)}
                            </span>
                            <Badge variant="destructive" className="text-xs">
                              {getDiscountPercentage()}% OFF
                            </Badge>
                          </>
                        )}
                      </div>

                      {/* Stock status */}
                      <div className="flex items-center gap-2 mb-4">
                        <Badge 
                          variant={product.stock_status === 'instock' ? 'default' : 'secondary'}
                          className="text-xs"
                        >
                          {product.stock_status === 'instock' ? 'In Stock' : 'Out of Stock'}
                        </Badge>
                        {product.stock_quantity && (
                          <span className="text-sm text-gray-500">
                            {product.stock_quantity} available
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Description */}
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-2">Description</h3>
                      <div 
                        className="text-gray-600 text-sm leading-relaxed"
                        dangerouslySetInnerHTML={{ 
                          __html: product.short_description || product.description 
                        }}
                      />
                    </div>

                    {/* Quantity selector */}
                    <div className="flex items-center gap-4">
                      <span className="font-medium text-gray-900">Quantity:</span>
                      <div className="flex items-center border border-gray-300 rounded-lg">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setQuantity(Math.max(1, quantity - 1))}
                          disabled={quantity <= 1}
                          className="px-3 py-1"
                        >
                          <Minus className="w-4 h-4" />
                        </Button>
                        <span className="px-4 py-2 font-medium min-w-[3rem] text-center">
                          {quantity}
                        </span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setQuantity(quantity + 1)}
                          disabled={product.stock_quantity ? quantity >= product.stock_quantity : false}
                          className="px-3 py-1"
                        >
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>

                    {/* Action buttons */}
                    <div className="space-y-3">
                      <Button
                        onClick={handleWhatsAppCheckout}
                        className="w-full bg-green-600 hover:bg-green-700 text-white"
                        size="lg"
                        disabled={product.stock_status !== 'instock'}
                      >
                        <MessageCircle className="w-5 h-5 mr-2" />
                        Order via WhatsApp
                      </Button>

                      {onAddToCart && (
                        <Button
                          onClick={handleAddToCart}
                          variant="outline"
                          className="w-full"
                          size="lg"
                          disabled={product.stock_status !== 'instock'}
                        >
                          <ShoppingCart className="w-5 h-5 mr-2" />
                          Add to Cart
                        </Button>
                      )}
                    </div>

                    {/* Total price */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <div className="flex justify-between items-center">
                        <span className="font-medium text-gray-900">Total:</span>
                        <span className="text-xl font-bold text-blue-600">
                          {formatPrice((parseFloat(product.price) * quantity).toString())}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </Card>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default ProductOverlay;
