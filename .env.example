# Supabase Configuration
VITE_SUPABASE_URL=https://dnbjrfgfugpmyrconepx.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRuYmpyZmdmdWdwbXlyY29uZXB4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NzE5NzQsImV4cCI6MjA1MDU0Nzk3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8

# WordPress + WooCommerce Configuration (Local Development)
VITE_WORDPRESS_URL=http://localhost:10090
VITE_WOO_CONSUMER_KEY=ck_405631cb8df6b2245d0695f814f05e47ca6befbd
VITE_WOO_CONSUMER_SECRET=cs_51c2556e9ad57d2ef40c82f6e3bc266e99103f98

# WordPress Application Password (for REST API)
VITE_WP_USERNAME=iwalk
VITE_WP_APP_PASSWORD=JiQN tXNq ieO1 hRDv 6gjd dtQj

# JWT Authentication (WordPress) - Optional
VITE_JWT_SECRET=your_jwt_secret_key

# WPVR Plugin Configuration
VITE_WPVR_ENABLED=true
VITE_WPVR_API_KEY=your_wpvr_api_key

# Application Configuration
VITE_APP_NAME=VirtualRealTour
VITE_APP_URL=https://virtualrealtour.ng
VITE_APP_DESCRIPTION=Professional 360° Virtual Tours in Nigeria

# API Configuration
VITE_API_URL=https://dnbjrfgfugpmyrconepx.supabase.co/rest/v1

# Storage Configuration
VITE_STORAGE_BUCKET=tour-media

# Analytics (Optional)
VITE_GOOGLE_ANALYTICS_ID=
VITE_HOTJAR_ID=

# Social Media (Optional)
VITE_FACEBOOK_APP_ID=
VITE_TWITTER_HANDLE=@virtualrealtour

# Contact Information
VITE_CONTACT_EMAIL=<EMAIL>
VITE_CONTACT_PHONE=+234 ************
VITE_CONTACT_ADDRESS=No 3 Kwame Nkrumah Crescent, Asokoro, Abuja, Nigeria

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_CHAT_SUPPORT=true
VITE_ENABLE_NOTIFICATIONS=true

# Tour Creation Services (Production Ready)
NEXT_PUBLIC_COMMONNINJA_API_KEY=66d8963a1bdabe5738077c64c3f8e6aa21690d651bcb3c04
NEXT_PUBLIC_ENABLE_CUSTOM_TOUR_CREATION=true

# E-commerce Integration (WhatsApp-First Approach)
VITE_WHATSAPP_BUSINESS_PHONE_ID=your_whatsapp_phone_number_id
VITE_WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
VITE_ENABLE_WHATSAPP_COMMERCE=true

# WhatsApp Business API
WHATSAPP_BUSINESS_PHONE_NUMBER_ID=your_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_webhook_verify_token

# Development Settings
VITE_DEBUG_MODE=false
VITE_LOG_LEVEL=info
