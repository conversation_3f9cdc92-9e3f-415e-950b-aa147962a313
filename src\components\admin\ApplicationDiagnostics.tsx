import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  Bug,
  Shield,
  Activity,
  FileText
} from 'lucide-react';
import { runQuickDiagnostic } from '@/utils/applicationDiagnostics';
import { brokenLinkChecker } from '@/utils/brokenLinkChecker';

const ApplicationDiagnostics: React.FC = () => {
  const [diagnosticResults, setDiagnosticResults] = useState<any>(null);
  const [isRunning, setIsRunning] = useState(false);

  const runDiagnostics = async () => {
    setIsRunning(true);
    try {
      // Run code diagnostics
      const codeDiagnostics = runQuickDiagnostic();
      
      // Run application health check
      const healthCheck = await brokenLinkChecker.checkApplicationHealth();
      
      setDiagnosticResults({
        code: codeDiagnostics,
        health: healthCheck
      });
    } catch (error) {
      console.error('Diagnostics failed:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      default:
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Application Diagnostics</h2>
          <p className="text-muted-foreground">
            Analyze application health and identify potential issues
          </p>
        </div>
        <Button onClick={runDiagnostics} disabled={isRunning}>
          {isRunning ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <Bug className="h-4 w-4 mr-2" />
          )}
          {isRunning ? 'Running...' : 'Run Diagnostics'}
        </Button>
      </div>

      {/* Placements Issue Analysis */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Placements Tab Issue Analysis
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <XCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Root Cause Identified:</strong> The Placements tab was causing blank screens due to:
              <ul className="mt-2 space-y-1 text-sm">
                <li>• Unsafe array operations on potentially undefined tour data</li>
                <li>• Complex Select component interactions without proper error handling</li>
                <li>• Missing null checks in dynamic rendering logic</li>
                <li>• Query failures causing component crashes</li>
              </ul>
            </AlertDescription>
          </Alert>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium">Problematic Code Patterns:</h4>
              <div className="text-sm space-y-1">
                <code className="bg-red-50 text-red-700 px-2 py-1 rounded">tours.filter(t => t.status)</code>
                <code className="bg-red-50 text-red-700 px-2 py-1 rounded">placements[placement.key]</code>
                <code className="bg-red-50 text-red-700 px-2 py-1 rounded">useQuery without error boundary</code>
              </div>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Safe Alternatives:</h4>
              <div className="text-sm space-y-1">
                <code className="bg-green-50 text-green-700 px-2 py-1 rounded">tours?.filter() || []</code>
                <code className="bg-green-50 text-green-700 px-2 py-1 rounded">placements?.[key] ?? ''</code>
                <code className="bg-green-50 text-green-700 px-2 py-1 rounded">Error boundary wrapper</code>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Diagnostic Results */}
      {diagnosticResults && (
        <div className="space-y-6">
          {/* Summary */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4 text-blue-500" />
                  <div>
                    <p className="text-sm font-medium">Overall Status</p>
                    <p className="text-2xl font-bold">
                      {diagnosticResults.health?.overallStatus === 'healthy' ? '✅' : 
                       diagnosticResults.health?.overallStatus === 'issues' ? '⚠️' : '❌'}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  <XCircle className="h-4 w-4 text-red-500" />
                  <div>
                    <p className="text-sm font-medium">Critical Issues</p>
                    <p className="text-2xl font-bold text-red-600">
                      {diagnosticResults.code?.summary?.critical || 0}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  <div>
                    <p className="text-sm font-medium">Warnings</p>
                    <p className="text-2xl font-bold text-yellow-600">
                      {diagnosticResults.code?.summary?.warnings || 0}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-green-500" />
                  <div>
                    <p className="text-sm font-medium">Link Health</p>
                    <p className="text-2xl font-bold text-green-600">
                      {diagnosticResults.health?.summary?.totalLinks - diagnosticResults.health?.summary?.brokenLinks || 0}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Code Issues */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bug className="h-5 w-5" />
                Code Health Issues
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {diagnosticResults.code?.placementsIssues?.map((issue: any, index: number) => (
                  <div key={index} className="border rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      {getSeverityIcon(issue.severity)}
                      <Badge className={getSeverityColor(issue.severity)}>
                        {issue.severity}
                      </Badge>
                      <span className="font-medium">{issue.category}</span>
                    </div>
                    <p className="text-sm text-gray-700 mb-1">{issue.description}</p>
                    <p className="text-xs text-gray-500 mb-2">Location: {issue.location}</p>
                    <p className="text-xs text-blue-600">💡 {issue.suggestion}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Recommendations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Immediate Fixes for Placements:</h4>
                  <ul className="space-y-1 text-sm">
                    {diagnosticResults.code?.placementsRecommendations?.map((rec: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-green-500 mt-1">•</span>
                        <span>{rec}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                <Separator />
                
                <div>
                  <h4 className="font-medium mb-2">Application-Wide Improvements:</h4>
                  <ul className="space-y-1 text-sm">
                    {diagnosticResults.code?.applicationRecommendations?.slice(0, 5).map((rec: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-blue-500 mt-1">•</span>
                        <span>{rec}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Empty State */}
      {!diagnosticResults && !isRunning && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <Bug className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h3 className="font-medium">No Diagnostics Run Yet</h3>
                <p className="text-sm text-muted-foreground">
                  Click "Run Diagnostics" to analyze your application health
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default ApplicationDiagnostics;
