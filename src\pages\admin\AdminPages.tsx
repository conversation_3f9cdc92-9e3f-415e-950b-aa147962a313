/**
 * Admin Pages Management
 * Consolidated page and content management for homepage, showcase, about, and all content sections
 */

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  FileText, 
  Star, 
  Eye, 
  Edit, 
  Plus,
  Home,
  Info,
  Image as ImageIcon,
  Settings,
  Globe
} from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';
import FeaturedTourManager from '@/components/admin/FeaturedTourManager';

const AdminPages = () => {
  const [activeTab, setActiveTab] = useState('homepage');

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Pages & Content Management</h1>
            <p className="text-muted-foreground">
              Manage homepage, showcase, about page, and all website content sections
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Eye className="w-4 h-4 mr-2" />
              Preview Site
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Content
            </Button>
          </div>
        </div>

        {/* Content Management Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-5">
            <TabsTrigger value="homepage" className="flex items-center gap-2">
              <Home className="w-4 h-4" />
              <span className="hidden sm:inline">Homepage</span>
            </TabsTrigger>
            <TabsTrigger value="featured" className="flex items-center gap-2">
              <Star className="w-4 h-4" />
              <span className="hidden sm:inline">Featured</span>
            </TabsTrigger>
            <TabsTrigger value="showcase" className="flex items-center gap-2">
              <Eye className="w-4 h-4" />
              <span className="hidden sm:inline">Showcase</span>
            </TabsTrigger>
            <TabsTrigger value="about" className="flex items-center gap-2">
              <Info className="w-4 h-4" />
              <span className="hidden sm:inline">About</span>
            </TabsTrigger>
            <TabsTrigger value="global" className="flex items-center gap-2">
              <Globe className="w-4 h-4" />
              <span className="hidden sm:inline">Global</span>
            </TabsTrigger>
          </TabsList>

          {/* Homepage Content */}
          <TabsContent value="homepage" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Home className="w-5 h-5" />
                    Hero Section
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Main Headline</label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="text-sm">Experience Nigeria Like Never Before</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Subtitle</label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="text-sm">Immersive 360° virtual tours of Nigeria's most beautiful destinations</p>
                    </div>
                  </div>
                  <Button variant="outline" className="w-full">
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Hero Section
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ImageIcon className="w-5 h-5" />
                    Hero Media
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="aspect-video bg-muted rounded-md flex items-center justify-center">
                    <ImageIcon className="w-8 h-8 text-muted-foreground" />
                  </div>
                  <Button variant="outline" className="w-full">
                    <Plus className="w-4 h-4 mr-2" />
                    Upload Hero Image/Video
                  </Button>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Homepage Sections</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { name: 'Featured Tours', status: 'active', tours: 6 },
                    { name: 'Categories', status: 'active', tours: 8 },
                    { name: 'Demo Tours', status: 'active', tours: 3 },
                    { name: 'About Section', status: 'active', tours: 0 },
                    { name: 'Contact Section', status: 'active', tours: 0 }
                  ].map((section, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        <div>
                          <h4 className="font-medium">{section.name}</h4>
                          {section.tours > 0 && (
                            <p className="text-sm text-muted-foreground">{section.tours} tours</p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">{section.status}</Badge>
                        <Button size="sm" variant="outline">
                          <Edit className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Featured Tours Management */}
          <TabsContent value="featured" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5" />
                  Featured Tours & Placements
                </CardTitle>
              </CardHeader>
              <CardContent>
                <FeaturedTourManager />
              </CardContent>
            </Card>
          </TabsContent>

          {/* Showcase Page */}
          <TabsContent value="showcase" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="w-5 h-5" />
                  Showcase Page Content
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Page Title</label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="text-sm">Tour Showcase</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Page Description</label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="text-sm">Explore our collection of virtual tours</p>
                    </div>
                  </div>
                </div>
                <Button variant="outline">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit Showcase Content
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* About Page */}
          <TabsContent value="about" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Info className="w-5 h-5" />
                  About Page Content
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">About Section</label>
                    <div className="p-4 bg-muted rounded-md">
                      <p className="text-sm">VirtualRealTour is Nigeria's premier virtual tour platform...</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Mission Statement</label>
                    <div className="p-4 bg-muted rounded-md">
                      <p className="text-sm">To showcase Nigeria's beauty through immersive technology...</p>
                    </div>
                  </div>
                </div>
                <Button variant="outline">
                  <Edit className="w-4 h-4 mr-2" />
                  Edit About Content
                </Button>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Global Settings */}
          <TabsContent value="global" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Site Settings
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Site Title</label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="text-sm">VirtualRealTour Nigeria</p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Site Description</label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="text-sm">Experience Nigeria through immersive virtual tours</p>
                    </div>
                  </div>
                  <Button variant="outline" className="w-full">
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Site Settings
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="w-5 h-5" />
                    Contact Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Contact Email</label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="text-sm"><EMAIL></p>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">WhatsApp Number</label>
                    <div className="p-3 bg-muted rounded-md">
                      <p className="text-sm">+234 XXX XXX XXXX</p>
                    </div>
                  </div>
                  <Button variant="outline" className="w-full">
                    <Edit className="w-4 h-4 mr-2" />
                    Edit Contact Info
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminPages;
