/**
 * VendorOnboarding Component
 * Complete vendor onboarding experience with welcome and setup
 * Mobile-first responsive design following VRT patterns
 */

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle, 
  Clock, 
  Star, 
  Package, 
  MessageCircle, 
  TrendingUp,
  Gift,
  Lightbulb,
  Target,
  Users,
  ArrowRight,
  Play,
  BookOpen,
  Headphones
} from 'lucide-react';
import { cn } from '@/lib/utils';
import VendorRegistrationForm from './VendorRegistrationForm';
import type { Vendor } from './VendorProfile';

interface VendorOnboardingProps {
  vendor?: Vendor;
  onComplete?: () => void;
  className?: string;
}

const VendorOnboarding = ({
  vendor,
  onComplete,
  className
}: VendorOnboardingProps) => {
  const [currentStep, setCurrentStep] = useState<'welcome' | 'register' | 'pending' | 'approved' | 'setup'>('welcome');
  const [registeredVendor, setRegisteredVendor] = useState<Vendor | null>(vendor || null);

  useEffect(() => {
    if (vendor) {
      setRegisteredVendor(vendor);
      switch (vendor.status) {
        case 'pending':
          setCurrentStep('pending');
          break;
        case 'approved':
          setCurrentStep('setup');
          break;
        default:
          setCurrentStep('welcome');
      }
    }
  }, [vendor]);

  const handleRegistrationSuccess = (vendorId: string) => {
    // In a real app, you'd fetch the vendor data
    setCurrentStep('pending');
  };

  const WelcomeStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-8"
    >
      <div className="space-y-4">
        <div className="w-20 h-20 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
          <Package className="w-10 h-10 text-blue-600" />
        </div>
        <h1 className="text-3xl font-bold">Welcome to VirtualRealTour</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Join our marketplace and showcase your products in immersive 360° virtual tours. 
          Reach customers like never before with our innovative platform.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        <Card className="text-center">
          <CardContent className="p-6">
            <TrendingUp className="w-12 h-12 mx-auto mb-4 text-green-600" />
            <h3 className="font-semibold mb-2">Increase Sales</h3>
            <p className="text-sm text-muted-foreground">
              Showcase products in virtual tours and boost customer engagement
            </p>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardContent className="p-6">
            <Users className="w-12 h-12 mx-auto mb-4 text-blue-600" />
            <h3 className="font-semibold mb-2">Reach More Customers</h3>
            <p className="text-sm text-muted-foreground">
              Connect with customers exploring virtual spaces and experiences
            </p>
          </CardContent>
        </Card>

        <Card className="text-center">
          <CardContent className="p-6">
            <MessageCircle className="w-12 h-12 mx-auto mb-4 text-purple-600" />
            <h3 className="font-semibold mb-2">Direct Communication</h3>
            <p className="text-sm text-muted-foreground">
              WhatsApp integration for instant customer communication
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">How It Works</h2>
        <div className="flex flex-col md:flex-row items-center justify-center gap-4 max-w-3xl mx-auto">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-semibold">1</div>
            <span>Register Your Business</span>
          </div>
          <ArrowRight className="w-5 h-5 text-muted-foreground rotate-90 md:rotate-0" />
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-semibold">2</div>
            <span>Get Approved</span>
          </div>
          <ArrowRight className="w-5 h-5 text-muted-foreground rotate-90 md:rotate-0" />
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-semibold">3</div>
            <span>Start Selling</span>
          </div>
        </div>
      </div>

      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button size="lg" onClick={() => setCurrentStep('register')}>
          Get Started
          <ArrowRight className="w-4 h-4 ml-2" />
        </Button>
        <Button variant="outline" size="lg">
          <Play className="w-4 h-4 mr-2" />
          Watch Demo
        </Button>
      </div>
    </motion.div>
  );

  const PendingStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="text-center space-y-8 max-w-2xl mx-auto"
    >
      <div className="space-y-4">
        <div className="w-20 h-20 mx-auto bg-yellow-100 rounded-full flex items-center justify-center">
          <Clock className="w-10 h-10 text-yellow-600" />
        </div>
        <h1 className="text-3xl font-bold">Application Under Review</h1>
        <p className="text-lg text-muted-foreground">
          Thank you for your application! Our team is reviewing your submission and will get back to you soon.
        </p>
      </div>

      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            <h3 className="font-semibold">What happens next?</h3>
            <div className="space-y-3 text-left">
              <div className="flex items-start gap-3">
                <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <p className="font-medium">Application Submitted</p>
                  <p className="text-sm text-muted-foreground">Your application has been received</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Clock className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div>
                  <p className="font-medium">Under Review</p>
                  <p className="text-sm text-muted-foreground">Our team is reviewing your business details</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <MessageCircle className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="font-medium">WhatsApp Notification</p>
                  <p className="text-sm text-muted-foreground">You'll receive approval notification via WhatsApp</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <h3 className="font-semibold">While you wait...</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card className="text-center">
            <CardContent className="p-4">
              <BookOpen className="w-8 h-8 mx-auto mb-2 text-blue-600" />
              <h4 className="font-medium mb-1">Read Our Guide</h4>
              <p className="text-sm text-muted-foreground">Learn best practices for selling on our platform</p>
              <Button variant="outline" size="sm" className="mt-2">
                View Guide
              </Button>
            </CardContent>
          </Card>
          
          <Card className="text-center">
            <CardContent className="p-4">
              <Headphones className="w-8 h-8 mx-auto mb-2 text-green-600" />
              <h4 className="font-medium mb-1">Contact Support</h4>
              <p className="text-sm text-muted-foreground">Have questions? Our team is here to help</p>
              <Button variant="outline" size="sm" className="mt-2">
                Get Help
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      <div className="text-sm text-muted-foreground">
        <p>Review typically takes 1-2 business days</p>
      </div>
    </motion.div>
  );

  const SetupStep = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-8 max-w-4xl mx-auto"
    >
      <div className="text-center space-y-4">
        <div className="w-20 h-20 mx-auto bg-green-100 rounded-full flex items-center justify-center">
          <CheckCircle className="w-10 h-10 text-green-600" />
        </div>
        <h1 className="text-3xl font-bold">Welcome to the Platform!</h1>
        <p className="text-lg text-muted-foreground">
          Congratulations! Your vendor account has been approved. Let's get you set up for success.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <Package className="w-5 h-5 text-blue-600" />
              </div>
              <h3 className="font-semibold">Add Your First Product</h3>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Start by adding products to your catalog with high-quality images and descriptions.
            </p>
            <Button className="w-full">
              Add Product
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <Target className="w-5 h-5 text-purple-600" />
              </div>
              <h3 className="font-semibold">Link to Tours</h3>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Connect your products to virtual tours for immersive shopping experiences.
            </p>
            <Button variant="outline" className="w-full">
              Browse Tours
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                <MessageCircle className="w-5 h-5 text-green-600" />
              </div>
              <h3 className="font-semibold">Setup WhatsApp</h3>
            </div>
            <p className="text-sm text-muted-foreground mb-4">
              Ensure your WhatsApp is ready to receive order notifications and customer inquiries.
            </p>
            <Button variant="outline" className="w-full">
              Test WhatsApp
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="w-5 h-5 text-yellow-600" />
            Quick Tips for Success
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <Star className="w-4 h-4 text-yellow-600 mt-1" />
                <div>
                  <p className="font-medium text-sm">High-Quality Images</p>
                  <p className="text-xs text-muted-foreground">Use clear, well-lit photos from multiple angles</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Star className="w-4 h-4 text-yellow-600 mt-1" />
                <div>
                  <p className="font-medium text-sm">Detailed Descriptions</p>
                  <p className="text-xs text-muted-foreground">Include specifications, materials, and dimensions</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Star className="w-4 h-4 text-yellow-600 mt-1" />
                <div>
                  <p className="font-medium text-sm">Competitive Pricing</p>
                  <p className="text-xs text-muted-foreground">Research market prices and offer fair value</p>
                </div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <Star className="w-4 h-4 text-yellow-600 mt-1" />
                <div>
                  <p className="font-medium text-sm">Quick Response</p>
                  <p className="text-xs text-muted-foreground">Reply to customer inquiries within 2 hours</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Star className="w-4 h-4 text-yellow-600 mt-1" />
                <div>
                  <p className="font-medium text-sm">Accurate Inventory</p>
                  <p className="text-xs text-muted-foreground">Keep stock levels updated to avoid disappointment</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Star className="w-4 h-4 text-yellow-600 mt-1" />
                <div>
                  <p className="font-medium text-sm">Professional Service</p>
                  <p className="text-xs text-muted-foreground">Maintain high standards for customer satisfaction</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="text-center">
        <Button size="lg" onClick={onComplete}>
          Start Selling
          <Gift className="w-4 h-4 ml-2" />
        </Button>
        <p className="text-sm text-muted-foreground mt-2">
          You can always access these resources from your dashboard
        </p>
      </div>
    </motion.div>
  );

  return (
    <div className={cn("min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6", className)}>
      <div className="max-w-6xl mx-auto">
        <AnimatePresence mode="wait">
          {currentStep === 'welcome' && <WelcomeStep key="welcome" />}
          {currentStep === 'register' && (
            <VendorRegistrationForm
              key="register"
              onSuccess={handleRegistrationSuccess}
              onCancel={() => setCurrentStep('welcome')}
            />
          )}
          {currentStep === 'pending' && <PendingStep key="pending" />}
          {currentStep === 'setup' && <SetupStep key="setup" />}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default VendorOnboarding;
