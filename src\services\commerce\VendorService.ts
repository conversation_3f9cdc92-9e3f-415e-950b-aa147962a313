/**
 * Vendor Service
 * Comprehensive vendor management service
 * Handles registration, approval, analytics, and communication
 */

import { supabase } from '@/lib/supabase';
import { whatsappService } from './WhatsAppService';
import type { Vendor } from '@/components/commerce/VendorProfile';

export interface VendorRegistration {
  name: string;
  email: string;
  phone?: string;
  whatsapp_number?: string;
  business_address?: string;
  business_description?: string;
  logo_url?: string;
}

export interface VendorUpdate {
  name?: string;
  phone?: string;
  whatsapp_number?: string;
  business_address?: string;
  business_description?: string;
  logo_url?: string;
  commission_rate?: number;
  status?: 'pending' | 'approved' | 'suspended' | 'rejected';
}

export interface VendorStats {
  total_products: number;
  active_products: number;
  draft_products: number;
  total_orders: number;
  pending_orders: number;
  completed_orders: number;
  total_revenue: number;
  monthly_revenue: number;
  commission_paid: number;
  rating: number;
  response_time: string;
}

export interface VendorFilters {
  status?: 'pending' | 'approved' | 'suspended' | 'rejected';
  search?: string;
  commission_min?: number;
  commission_max?: number;
  created_after?: string;
  created_before?: string;
}

export class VendorService {
  /**
   * Register a new vendor
   */
  async registerVendor(vendorData: VendorRegistration): Promise<Vendor> {
    try {
      const { data, error } = await supabase
        .from('vendors')
        .insert({
          name: vendorData.name,
          email: vendorData.email,
          phone: vendorData.phone,
          whatsapp_number: vendorData.whatsapp_number,
          business_address: vendorData.business_address,
          business_description: vendorData.business_description,
          logo_url: vendorData.logo_url,
          status: 'pending',
          commission_rate: 0.10 // Default 10%
        })
        .select()
        .single();

      if (error) throw error;

      // Send notification to admin
      await this.notifyAdminNewVendor(data);

      // Send confirmation to vendor
      await this.sendVendorRegistrationConfirmation(data);

      return data;
    } catch (error) {
      console.error('Error registering vendor:', error);
      throw new Error('Failed to register vendor');
    }
  }

  /**
   * Get vendor by ID
   */
  async getVendor(vendorId: string): Promise<Vendor | null> {
    try {
      const { data, error } = await supabase
        .from('vendors')
        .select('*')
        .eq('id', vendorId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error fetching vendor:', error);
      throw new Error('Failed to fetch vendor');
    }
  }

  /**
   * Get vendors with filters
   */
  async getVendors(filters: VendorFilters = {}): Promise<Vendor[]> {
    try {
      let query = supabase
        .from('vendors')
        .select('*')
        .order('created_at', { ascending: false });

      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.search) {
        query = query.or(`name.ilike.%${filters.search}%,email.ilike.%${filters.search}%`);
      }

      if (filters.commission_min !== undefined) {
        query = query.gte('commission_rate', filters.commission_min);
      }

      if (filters.commission_max !== undefined) {
        query = query.lte('commission_rate', filters.commission_max);
      }

      if (filters.created_after) {
        query = query.gte('created_at', filters.created_after);
      }

      if (filters.created_before) {
        query = query.lte('created_at', filters.created_before);
      }

      const { data, error } = await query;

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching vendors:', error);
      throw new Error('Failed to fetch vendors');
    }
  }

  /**
   * Update vendor information
   */
  async updateVendor(vendorId: string, updates: VendorUpdate): Promise<Vendor> {
    try {
      const { data, error } = await supabase
        .from('vendors')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', vendorId)
        .select()
        .single();

      if (error) throw error;

      // Send notification if status changed
      if (updates.status) {
        await this.sendStatusChangeNotification(data, updates.status);
      }

      return data;
    } catch (error) {
      console.error('Error updating vendor:', error);
      throw new Error('Failed to update vendor');
    }
  }

  /**
   * Approve vendor
   */
  async approveVendor(vendorId: string, commissionRate: number = 0.10): Promise<Vendor> {
    try {
      const vendor = await this.updateVendor(vendorId, {
        status: 'approved',
        commission_rate: commissionRate
      });

      // Send welcome message
      await this.sendVendorWelcomeMessage(vendor);

      return vendor;
    } catch (error) {
      console.error('Error approving vendor:', error);
      throw new Error('Failed to approve vendor');
    }
  }

  /**
   * Reject vendor
   */
  async rejectVendor(vendorId: string, reason?: string): Promise<Vendor> {
    try {
      const vendor = await this.updateVendor(vendorId, {
        status: 'rejected'
      });

      // Send rejection notification
      await this.sendVendorRejectionMessage(vendor, reason);

      return vendor;
    } catch (error) {
      console.error('Error rejecting vendor:', error);
      throw new Error('Failed to reject vendor');
    }
  }

  /**
   * Suspend vendor
   */
  async suspendVendor(vendorId: string, reason?: string): Promise<Vendor> {
    try {
      const vendor = await this.updateVendor(vendorId, {
        status: 'suspended'
      });

      // Send suspension notification
      await this.sendVendorSuspensionMessage(vendor, reason);

      return vendor;
    } catch (error) {
      console.error('Error suspending vendor:', error);
      throw new Error('Failed to suspend vendor');
    }
  }

  /**
   * Get vendor statistics
   */
  async getVendorStats(vendorId: string): Promise<VendorStats> {
    try {
      // Get product stats
      const { data: productStats } = await supabase
        .from('products')
        .select('status')
        .eq('vendor_id', vendorId);

      // Get order stats
      const { data: orderStats } = await supabase
        .from('order_items')
        .select(`
          quantity,
          price,
          vendor_commission,
          order:orders(status, created_at)
        `)
        .eq('vendor_id', vendorId);

      // Calculate stats
      const total_products = productStats?.length || 0;
      const active_products = productStats?.filter(p => p.status === 'active').length || 0;
      const draft_products = productStats?.filter(p => p.status === 'draft').length || 0;

      const total_orders = orderStats?.length || 0;
      const pending_orders = orderStats?.filter(o => 
        ['pending', 'confirmed', 'processing'].includes(o.order.status)
      ).length || 0;
      const completed_orders = orderStats?.filter(o => 
        ['delivered', 'shipped'].includes(o.order.status)
      ).length || 0;

      const total_revenue = orderStats?.reduce((sum, item) => 
        sum + (item.price * item.quantity), 0
      ) || 0;

      const currentMonth = new Date().getMonth();
      const monthly_revenue = orderStats?.filter(item => 
        new Date(item.order.created_at).getMonth() === currentMonth
      ).reduce((sum, item) => sum + (item.price * item.quantity), 0) || 0;

      const commission_paid = orderStats?.reduce((sum, item) => 
        sum + item.vendor_commission, 0
      ) || 0;

      return {
        total_products,
        active_products,
        draft_products,
        total_orders,
        pending_orders,
        completed_orders,
        total_revenue,
        monthly_revenue,
        commission_paid,
        rating: 4.5, // TODO: Implement rating system
        response_time: '< 2 hours' // TODO: Calculate actual response time
      };
    } catch (error) {
      console.error('Error fetching vendor stats:', error);
      throw new Error('Failed to fetch vendor stats');
    }
  }

  /**
   * Get vendor analytics
   */
  async getVendorAnalytics(vendorId: string, period: 'week' | 'month' | 'year' = 'month') {
    try {
      const startDate = new Date();
      switch (period) {
        case 'week':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case 'month':
          startDate.setMonth(startDate.getMonth() - 1);
          break;
        case 'year':
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
      }

      const { data, error } = await supabase
        .from('order_items')
        .select(`
          quantity,
          price,
          vendor_commission,
          order:orders(created_at, status)
        `)
        .eq('vendor_id', vendorId)
        .gte('order.created_at', startDate.toISOString());

      if (error) throw error;

      // Process analytics data
      const analytics = {
        revenue: data?.reduce((sum, item) => sum + (item.price * item.quantity), 0) || 0,
        orders: data?.length || 0,
        commission: data?.reduce((sum, item) => sum + item.vendor_commission, 0) || 0,
        // TODO: Add more analytics like conversion rates, popular products, etc.
      };

      return analytics;
    } catch (error) {
      console.error('Error fetching vendor analytics:', error);
      throw new Error('Failed to fetch vendor analytics');
    }
  }

  /**
   * Send vendor registration confirmation
   */
  private async sendVendorRegistrationConfirmation(vendor: Vendor): Promise<void> {
    try {
      if (vendor.whatsapp_number) {
        const message = `🎉 Thank you for registering with VirtualRealTour!

Your vendor application has been submitted successfully.

📋 *Application Details:*
Business: ${vendor.name}
Email: ${vendor.email}

⏳ *Next Steps:*
• Our team will review your application
• You'll receive approval notification within 1-2 business days
• Once approved, you can start adding products

Questions? Reply to this message!

---
*VirtualRealTour Team*`;

        // TODO: Send WhatsApp message
        console.log('Registration confirmation sent to:', vendor.whatsapp_number);
      }
    } catch (error) {
      console.error('Error sending registration confirmation:', error);
    }
  }

  /**
   * Send vendor welcome message
   */
  private async sendVendorWelcomeMessage(vendor: Vendor): Promise<void> {
    try {
      if (vendor.whatsapp_number) {
        const message = `🎉 *Welcome to VirtualRealTour!*

Congratulations ${vendor.name}! Your vendor account has been approved.

🚀 *You can now:*
• Add products to your catalog
• Link products to virtual tours
• Receive orders from customers
• Manage your business dashboard

💰 *Commission Rate:* ${(vendor.commission_rate * 100).toFixed(1)}%

🔗 *Get Started:*
Visit your vendor dashboard to add your first product.

Need help? Our support team is here for you!

---
*VirtualRealTour Team*`;

        // TODO: Send WhatsApp message
        console.log('Welcome message sent to:', vendor.whatsapp_number);
      }
    } catch (error) {
      console.error('Error sending welcome message:', error);
    }
  }

  /**
   * Send vendor rejection message
   */
  private async sendVendorRejectionMessage(vendor: Vendor, reason?: string): Promise<void> {
    try {
      if (vendor.whatsapp_number) {
        const message = `📋 *Application Update - VirtualRealTour*

Hello ${vendor.name},

Unfortunately, we cannot approve your vendor application at this time.

${reason ? `*Reason:* ${reason}` : ''}

📞 *Next Steps:*
• You can reapply after addressing any issues
• Contact our support team for guidance
• We're here to help you succeed

Thank you for your interest in VirtualRealTour.

---
*VirtualRealTour Team*`;

        // TODO: Send WhatsApp message
        console.log('Rejection message sent to:', vendor.whatsapp_number);
      }
    } catch (error) {
      console.error('Error sending rejection message:', error);
    }
  }

  /**
   * Send vendor suspension message
   */
  private async sendVendorSuspensionMessage(vendor: Vendor, reason?: string): Promise<void> {
    try {
      if (vendor.whatsapp_number) {
        const message = `⚠️ *Account Suspended - VirtualRealTour*

Hello ${vendor.name},

Your vendor account has been temporarily suspended.

${reason ? `*Reason:* ${reason}` : ''}

📞 *To resolve this:*
• Contact our support team immediately
• Provide any requested information
• Follow our community guidelines

We want to help you get back to selling.

---
*VirtualRealTour Support*`;

        // TODO: Send WhatsApp message
        console.log('Suspension message sent to:', vendor.whatsapp_number);
      }
    } catch (error) {
      console.error('Error sending suspension message:', error);
    }
  }

  /**
   * Send status change notification
   */
  private async sendStatusChangeNotification(vendor: Vendor, newStatus: string): Promise<void> {
    try {
      switch (newStatus) {
        case 'approved':
          await this.sendVendorWelcomeMessage(vendor);
          break;
        case 'rejected':
          await this.sendVendorRejectionMessage(vendor);
          break;
        case 'suspended':
          await this.sendVendorSuspensionMessage(vendor);
          break;
      }
    } catch (error) {
      console.error('Error sending status change notification:', error);
    }
  }

  /**
   * Notify admin about new vendor registration
   */
  private async notifyAdminNewVendor(vendor: Vendor): Promise<void> {
    try {
      // TODO: Implement admin notification system
      // This could be email, WhatsApp, or in-app notification
      console.log('New vendor registration:', vendor.name, vendor.email);
    } catch (error) {
      console.error('Error notifying admin:', error);
    }
  }
}

export const vendorService = new VendorService();
