/**
 * Tour Embed API
 * Secure tour embedding service that wraps external platforms
 */

import { supabase } from '@/lib/supabase';

export interface TourEmbedConfig {
  tour_id: string;
  platform: 'cloudpano' | 'commonninja' | 'wpvr' | 'custom';
  autoplay: boolean;
  controls: boolean;
  branding: boolean;
  muted: boolean;
}

export interface SecureTourData {
  id: string;
  title: string;
  description?: string;
  thumbnail_url?: string;
  platform: string;
  embed_url?: string;
  cloudpano_embed_url?: string;
  platform_specific?: Record<string, any>;
  status: string;
}

/**
 * Generate secure embed HTML for a tour
 */
export const generateSecureEmbedHtml = async (config: TourEmbedConfig): Promise<string> => {
  try {
    // Fetch tour data from database
    const { data: tour, error } = await supabase
      .from('tours')
      .select('*')
      .eq('id', config.tour_id)
      .eq('status', 'published')
      .single();

    if (error || !tour) {
      throw new Error('Tour not found or not published');
    }

    const tourData = tour as SecureTourData;

    // Get the actual embed URL based on platform
    const actualEmbedUrl = getActualEmbedUrl(tourData, config);
    
    if (!actualEmbedUrl) {
      throw new Error('No embed URL available for this tour');
    }

    // Generate secure wrapper HTML
    const embedHtml = generateWrapperHtml(tourData, actualEmbedUrl, config);
    
    return embedHtml;
  } catch (error) {
    console.error('Error generating secure embed:', error);
    throw error;
  }
};

/**
 * Get the actual embed URL from the tour data
 */
const getActualEmbedUrl = (tour: SecureTourData, config: TourEmbedConfig): string | null => {
  switch (config.platform) {
    case 'cloudpano':
      return tour.cloudpano_embed_url || null;
    
    case 'commonninja':
      // CommonNinja embed URL from platform_specific data
      return tour.platform_specific?.commonninja?.embed_url || null;
    
    case 'wpvr':
      // WPVR embed URL
      return tour.platform_specific?.wpvr?.embed_url || tour.embed_url || null;
    
    case 'custom':
      // Custom/native tour embed
      return `/tour/${tour.id}/viewer` || null;
    
    default:
      return tour.embed_url || null;
  }
};

/**
 * Generate wrapper HTML that masks the source platform
 */
const generateWrapperHtml = (tour: SecureTourData, embedUrl: string, config: TourEmbedConfig): string => {
  const {
    autoplay,
    controls,
    branding,
    muted
  } = config;

  // Sanitize and prepare the embed URL
  const sanitizedUrl = sanitizeEmbedUrl(embedUrl, config);

  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${escapeHtml(tour.title)} - VirtualRealTour</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #000;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .tour-container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        .tour-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: #000;
        }
        
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            z-index: 10;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #333;
            border-top: 3px solid #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .controls-overlay {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            padding: 12px;
            display: ${controls ? 'flex' : 'none'};
            align-items: center;
            justify-content: space-between;
            z-index: 20;
        }
        
        .branding {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            display: ${branding ? 'block' : 'none'};
            z-index: 20;
        }
        
        .control-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 8px;
        }
        
        .control-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .error-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: #000;
            display: none;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            z-index: 30;
        }
    </style>
</head>
<body>
    <div class="tour-container">
        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div>
                <div class="loading-spinner"></div>
                <p style="margin-top: 16px;">Loading virtual tour...</p>
            </div>
        </div>
        
        <!-- Error Overlay -->
        <div class="error-overlay" id="errorOverlay">
            <div>
                <p>Failed to load tour</p>
                <button onclick="location.reload()" style="margin-top: 16px; padding: 8px 16px; background: #333; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Retry
                </button>
            </div>
        </div>
        
        <!-- Tour Iframe -->
        <iframe 
            id="tourIframe"
            class="tour-iframe"
            src="${sanitizedUrl}"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowfullscreen
            sandbox="allow-scripts allow-same-origin allow-presentation"
        ></iframe>
        
        <!-- Controls Overlay -->
        <div class="controls-overlay">
            <div>
                <button class="control-btn" onclick="togglePlay()">⏯️</button>
                <button class="control-btn" onclick="toggleMute()">${muted ? '🔇' : '🔊'}</button>
            </div>
            <div>
                <button class="control-btn" onclick="shareTour()">📤</button>
                <button class="control-btn" onclick="toggleFullscreen()">⛶</button>
            </div>
        </div>
        
        <!-- Branding -->
        <div class="branding">
            VirtualRealTour
        </div>
    </div>

    <script>
        let isPlaying = ${autoplay};
        let isMuted = ${muted};
        
        // Hide loading overlay when iframe loads
        document.getElementById('tourIframe').onload = function() {
            document.getElementById('loadingOverlay').style.display = 'none';
            
            // Notify parent window
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'TOUR_LOADED',
                    tourId: '${tour.id}'
                }, '*');
            }
        };
        
        // Handle iframe errors
        document.getElementById('tourIframe').onerror = function() {
            document.getElementById('loadingOverlay').style.display = 'none';
            document.getElementById('errorOverlay').style.display = 'flex';
            
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'TOUR_ERROR',
                    tourId: '${tour.id}'
                }, '*');
            }
        };
        
        // Control functions
        function togglePlay() {
            isPlaying = !isPlaying;
            // Send message to tour iframe if needed
            const iframe = document.getElementById('tourIframe');
            if (iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: isPlaying ? 'PLAY' : 'PAUSE'
                }, '*');
            }
        }
        
        function toggleMute() {
            isMuted = !isMuted;
            const iframe = document.getElementById('tourIframe');
            if (iframe.contentWindow) {
                iframe.contentWindow.postMessage({
                    type: 'MUTE',
                    muted: isMuted
                }, '*');
            }
        }
        
        function shareTour() {
            const shareUrl = '${process.env.NODE_ENV === 'production' ? 'https://virtualrealtour.ng' : 'http://localhost:3000'}/tour/${tour.id}';
            
            if (navigator.share) {
                navigator.share({
                    title: '${escapeHtml(tour.title)}',
                    text: '${escapeHtml(tour.description || 'Check out this amazing virtual tour!')}',
                    url: shareUrl
                });
            } else {
                navigator.clipboard.writeText(shareUrl);
                alert('Tour link copied to clipboard!');
            }
        }
        
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        // Listen for messages from parent
        window.addEventListener('message', function(event) {
            if (event.data.tourId !== '${tour.id}') return;
            
            switch (event.data.type) {
                case 'TOUR_PLAY':
                    togglePlay();
                    break;
                case 'TOUR_PAUSE':
                    togglePlay();
                    break;
                case 'TOUR_MUTE':
                    if (event.data.muted !== isMuted) {
                        toggleMute();
                    }
                    break;
            }
        });
    </script>
</body>
</html>`;
};

/**
 * Sanitize embed URL and add necessary parameters
 */
const sanitizeEmbedUrl = (url: string, config: TourEmbedConfig): string => {
  try {
    const urlObj = new URL(url);
    
    // Add common parameters
    if (config.autoplay) {
      urlObj.searchParams.set('autoplay', '1');
    }
    
    if (config.muted) {
      urlObj.searchParams.set('muted', '1');
    }
    
    // Remove branding parameters from source platforms
    urlObj.searchParams.delete('branding');
    urlObj.searchParams.delete('logo');
    urlObj.searchParams.delete('watermark');
    
    return urlObj.toString();
  } catch (error) {
    // If URL parsing fails, return original URL
    return url;
  }
};

/**
 * Escape HTML to prevent XSS
 */
const escapeHtml = (text: string): string => {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
};

/**
 * Validate tour access permissions
 */
export const validateTourAccess = async (tourId: string): Promise<boolean> => {
  try {
    const { data: tour, error } = await supabase
      .from('tours')
      .select('status')
      .eq('id', tourId)
      .single();

    if (error || !tour) {
      return false;
    }

    return tour.status === 'published';
  } catch (error) {
    console.error('Error validating tour access:', error);
    return false;
  }
};
