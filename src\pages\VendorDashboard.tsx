/**
 * Vendor Dashboard Page
 * Main dashboard for vendors to manage their business
 * Mobile-first responsive design with comprehensive vendor tools
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Package, 
  TrendingUp, 
  DollarSign, 
  Users, 
  Plus, 
  Eye, 
  Edit, 
  BarChart3,
  Calendar,
  Clock,
  Star,
  MessageCircle,
  Settings,
  Download
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { cn } from '@/lib/utils';
import VendorLayout from '@/components/vendor/VendorLayout';
import VendorDashboardComponent from '@/components/commerce/VendorDashboard';
import { useAuth } from '@/hooks/useAuth';

interface DashboardStats {
  products: {
    total: number;
    active: number;
    draft: number;
    out_of_stock: number;
  };
  orders: {
    pending: number;
    processing: number;
    completed: number;
    total_today: number;
  };
  revenue: {
    today: number;
    week: number;
    month: number;
    growth: number;
  };
  customers: {
    total: number;
    new_today: number;
    returning: number;
  };
}

const VendorDashboard = () => {
  const { user, profile } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    products: {
      total: 12,
      active: 8,
      draft: 3,
      out_of_stock: 1
    },
    orders: {
      pending: 5,
      processing: 8,
      completed: 45,
      total_today: 3
    },
    revenue: {
      today: 125000,
      week: 850000,
      month: 3200000,
      growth: 12.5
    },
    customers: {
      total: 156,
      new_today: 4,
      returning: 89
    }
  });

  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <VendorLayout>
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </VendorLayout>
    );
  }

  return (
    <VendorLayout>
      <div className="space-y-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="flex flex-col sm:flex-row sm:items-center justify-between gap-4"
        >
          <div>
            <h1 className="text-3xl font-bold text-foreground">
              Vendor Dashboard
            </h1>
            <p className="text-muted-foreground">
              Welcome back, {profile?.full_name || user?.email}
            </p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export Data
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Add Product
            </Button>
          </div>
        </motion.div>

          {/* Stats Cards */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            {/* Revenue Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">₦{stats.revenue.month.toLocaleString()}</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-green-600">+{stats.revenue.growth}%</span> from last month
                </p>
              </CardContent>
            </Card>

            {/* Orders Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Orders</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.orders.pending + stats.orders.processing}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.orders.total_today} new today
                </p>
              </CardContent>
            </Card>

            {/* Products Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Products</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.products.total}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.products.active} active, {stats.products.draft} draft
                </p>
              </CardContent>
            </Card>

            {/* Customers Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Customers</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.customers.total}</div>
                <p className="text-xs text-muted-foreground">
                  {stats.customers.new_today} new today
                </p>
              </CardContent>
            </Card>
          </motion.div>

          {/* Quick Actions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8"
          >
            <Button variant="outline" className="h-20 flex-col gap-2">
              <Plus className="w-6 h-6" />
              <span className="text-sm">Add Product</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <Eye className="w-6 h-6" />
              <span className="text-sm">View Orders</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <BarChart3 className="w-6 h-6" />
              <span className="text-sm">Analytics</span>
            </Button>
            <Button variant="outline" className="h-20 flex-col gap-2">
              <Settings className="w-6 h-6" />
              <span className="text-sm">Settings</span>
            </Button>
          </motion.div>

          {/* Main Dashboard Content */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <VendorDashboardComponent />
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-8"
          >
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>
                  Your latest business activities and updates
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      icon: Package,
                      title: 'New order received',
                      description: 'Order #VRT-20241221-1234 for Premium Headphones',
                      time: '2 minutes ago',
                      type: 'order'
                    },
                    {
                      icon: Star,
                      title: 'Product review',
                      description: 'New 5-star review for Wireless Speaker',
                      time: '1 hour ago',
                      type: 'review'
                    },
                    {
                      icon: MessageCircle,
                      title: 'WhatsApp inquiry',
                      description: 'Customer asking about product availability',
                      time: '3 hours ago',
                      type: 'message'
                    },
                    {
                      icon: TrendingUp,
                      title: 'Sales milestone',
                      description: 'Reached ₦1M in monthly sales!',
                      time: '1 day ago',
                      type: 'milestone'
                    }
                  ].map((activity, index) => (
                    <div key={index} className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors">
                      <div className={cn(
                        "p-2 rounded-full",
                        activity.type === 'order' && "bg-blue-100 text-blue-600",
                        activity.type === 'review' && "bg-yellow-100 text-yellow-600",
                        activity.type === 'message' && "bg-green-100 text-green-600",
                        activity.type === 'milestone' && "bg-purple-100 text-purple-600"
                      )}>
                        <activity.icon className="w-4 h-4" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-sm">{activity.title}</p>
                        <p className="text-sm text-muted-foreground">{activity.description}</p>
                        <p className="text-xs text-muted-foreground mt-1">{activity.time}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </VendorLayout>
  );
};

export default VendorDashboard;
