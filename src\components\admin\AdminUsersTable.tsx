
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Profile, Tour } from '@/lib/supabase';

interface AdminUsersTableProps {
  users: Profile[];
  tours: Tour[];
}

const AdminUsersTable = ({ users, tours }: AdminUsersTableProps) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Platform Users</h2>
        <Badge variant="secondary">
          {users.length} users
        </Badge>
      </div>
      
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b">
                <tr>
                  <th className="text-left p-4 font-medium text-gray-900">User</th>
                  <th className="text-left p-4 font-medium text-gray-900">Role</th>
                  <th className="text-left p-4 font-medium text-gray-900">Tours</th>
                  <th className="text-left p-4 font-medium text-gray-900">Joined</th>
                </tr>
              </thead>
              <tbody className="divide-y">
                {users.map((user) => {
                  const userTours = tours.filter(tour => tour.user_id === user.id);
                  return (
                    <tr key={user.id} className="hover:bg-gray-50">
                      <td className="p-4">
                        <div>
                          <p className="font-medium text-gray-900">{user.full_name || 'Unknown'}</p>
                          <p className="text-sm text-gray-500">{user.email}</p>
                        </div>
                      </td>
                      <td className="p-4">
                        <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                          {user.role}
                        </Badge>
                      </td>
                      <td className="p-4">
                        <span className="text-gray-900">{userTours.length}</span>
                      </td>
                      <td className="p-4">
                        <span className="text-gray-500">
                          {new Date(user.created_at).toLocaleDateString()}
                        </span>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminUsersTable;
