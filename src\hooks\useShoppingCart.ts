/**
 * useShoppingCart Hook
 * React hook for managing shopping cart state and operations
 * Integrates with CartService and provides reactive cart management
 */

import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { cartService, type CartState } from '@/services/commerce/CartService';
import type { CustomerInfo } from '@/components/commerce/ShoppingCart';

export const useShoppingCart = () => {
  const [cartState, setCartState] = useState<CartState>({
    items: [],
    sessionId: '',
    totalPrice: 0,
    itemCount: 0,
    vendorGroups: new Map()
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);

  // Initialize cart on mount
  useEffect(() => {
    const initializeCart = async () => {
      try {
        setIsLoading(true);
        const state = await cartService.loadFromDatabase();
        setCartState(state);
      } catch (error) {
        console.error('Failed to initialize cart:', error);
        // Fallback to local storage
        const state = cartService.getCartState();
        setCartState(state);
      } finally {
        setIsLoading(false);
      }
    };

    initializeCart();
  }, []);

  /**
   * Add product to cart
   */
  const addToCart = useCallback(async (
    productId: string,
    quantity: number = 1,
    tourContext?: {
      tourId: string;
      tourTitle: string;
      sceneId: string;
    }
  ) => {
    try {
      setIsLoading(true);
      const newState = await cartService.addToCart(productId, quantity, tourContext);
      setCartState(newState);
      
      toast.success('Added to cart!', {
        description: `${quantity} item${quantity > 1 ? 's' : ''} added to your cart`,
        action: {
          label: 'View Cart',
          onClick: () => setIsCartOpen(true)
        }
      });
    } catch (error) {
      console.error('Failed to add to cart:', error);
      toast.error('Failed to add item to cart');
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Update item quantity
   */
  const updateQuantity = useCallback(async (itemId: string, quantity: number) => {
    try {
      setIsLoading(true);
      const newState = await cartService.updateQuantity(itemId, quantity);
      setCartState(newState);
      
      if (quantity === 0) {
        toast.success('Item removed from cart');
      }
    } catch (error) {
      console.error('Failed to update quantity:', error);
      toast.error('Failed to update cart');
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Remove item from cart
   */
  const removeItem = useCallback(async (itemId: string) => {
    try {
      setIsLoading(true);
      const newState = await cartService.removeItem(itemId);
      setCartState(newState);
      toast.success('Item removed from cart');
    } catch (error) {
      console.error('Failed to remove item:', error);
      toast.error('Failed to remove item');
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Clear entire cart
   */
  const clearCart = useCallback(async () => {
    try {
      setIsLoading(true);
      const newState = await cartService.clearCart();
      setCartState(newState);
      toast.success('Cart cleared');
    } catch (error) {
      console.error('Failed to clear cart:', error);
      toast.error('Failed to clear cart');
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Checkout cart
   */
  const checkout = useCallback(async (customerInfo: CustomerInfo) => {
    try {
      setIsLoading(true);
      const orderNumber = await cartService.checkout(customerInfo);
      
      // Update cart state after successful checkout
      const newState = cartService.getCartState();
      setCartState(newState);
      
      toast.success('Order placed successfully!', {
        description: `Order ${orderNumber} has been created. You will receive WhatsApp confirmation.`
      });
      
      return orderNumber;
    } catch (error) {
      console.error('Checkout failed:', error);
      toast.error('Failed to place order');
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Get items for a specific vendor
   */
  const getVendorItems = useCallback((vendorId: string) => {
    return cartState.vendorGroups.get(vendorId) || [];
  }, [cartState.vendorGroups]);

  /**
   * Get total for a specific vendor
   */
  const getVendorTotal = useCallback((vendorId: string) => {
    const items = getVendorItems(vendorId);
    return items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  }, [getVendorItems]);

  /**
   * Check if product is in cart
   */
  const isInCart = useCallback((productId: string, tourContext?: any) => {
    return cartState.items.some(item => 
      item.product.id === productId && 
      JSON.stringify(item.tourContext) === JSON.stringify(tourContext)
    );
  }, [cartState.items]);

  /**
   * Get quantity of product in cart
   */
  const getProductQuantity = useCallback((productId: string, tourContext?: any) => {
    const item = cartState.items.find(item => 
      item.product.id === productId && 
      JSON.stringify(item.tourContext) === JSON.stringify(tourContext)
    );
    return item?.quantity || 0;
  }, [cartState.items]);

  /**
   * Get cart summary
   */
  const getCartSummary = useCallback(() => {
    const vendorCount = cartState.vendorGroups.size;
    const uniqueProducts = new Set(cartState.items.map(item => item.product.id)).size;
    
    return {
      totalItems: cartState.itemCount,
      uniqueProducts,
      vendorCount,
      totalPrice: cartState.totalPrice,
      isEmpty: cartState.items.length === 0
    };
  }, [cartState]);

  /**
   * Refresh cart state
   */
  const refreshCart = useCallback(async () => {
    try {
      setIsLoading(true);
      const state = await cartService.loadFromDatabase();
      setCartState(state);
    } catch (error) {
      console.error('Failed to refresh cart:', error);
      const state = cartService.getCartState();
      setCartState(state);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return {
    // State
    cartState,
    isLoading,
    isCartOpen,
    setIsCartOpen,
    
    // Actions
    addToCart,
    updateQuantity,
    removeItem,
    clearCart,
    checkout,
    refreshCart,
    
    // Utilities
    getVendorItems,
    getVendorTotal,
    isInCart,
    getProductQuantity,
    getCartSummary,
    
    // Computed values
    items: cartState.items,
    itemCount: cartState.itemCount,
    totalPrice: cartState.totalPrice,
    vendorGroups: cartState.vendorGroups,
    isEmpty: cartState.items.length === 0
  };
};
