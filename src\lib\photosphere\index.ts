/**
 * Photo Sphere Viewer Library
 * Production-ready PSV v5 integration for VirtualRealTour
 */

// Core exports
export { PSVFactory, createSimpleViewer, createVirtualTour, createViewer } from './factory';
export { 
  PSVErrorBoundary, 
  PSVMemoryManager, 
  PSVPerformanceMonitor,
  validateContainer,
  validatePanorama,
  createPSVConfig,
  setupWebGLErrorHandling,
  createProgressiveLoader,
  createErrorHandler
} from './utils';

// Type exports
export type {
  PSVConfig,
  PSVInstance,
  PSVError,
  PSVEvents,
  PSVPlugins,
  TourScene,
  TourMarker,
  TourLink,
  VirtualTourConfig,
  GalleryConfig,
  SettingsConfig,
  AssetValidation,
  PSVPerformanceMetrics
} from './types';

// Factory options
export type { PSVFactoryOptions } from './factory';

// Re-export PSV core types for convenience
export type { Viewer } from '@photo-sphere-viewer/core';
export type { VirtualTourPlugin } from '@photo-sphere-viewer/virtual-tour-plugin';
export type { MarkersPlugin } from '@photo-sphere-viewer/markers-plugin';
export type { AutorotatePlugin } from '@photo-sphere-viewer/autorotate-plugin';
export type { GalleryPlugin } from '@photo-sphere-viewer/gallery-plugin';
export type { SettingsPlugin } from '@photo-sphere-viewer/settings-plugin';
