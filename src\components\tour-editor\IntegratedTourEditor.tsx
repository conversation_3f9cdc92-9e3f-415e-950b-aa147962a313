/**
 * Integrated Tour Editor
 * Seamless tour editing experience entirely within our application
 * No external redirects, no visible third-party URLs
 */

import { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Upload, 
  Plus, 
  Save, 
  Eye, 
  Share2, 
  MapPin,
  ShoppingCart,
  Link,
  Info,
  X,
  Settings,
  Camera,
  Layers,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';
import { tourDataSyncService } from '@/services/data-sync/TourDataSyncService';

interface Scene {
  id: string;
  name: string;
  imageUrl: string;
  hotspots: Hotspot[];
  thumbnail?: string;
}

interface Hotspot {
  id: string;
  type: 'navigation' | 'product' | 'info' | 'link';
  x: number;
  y: number;
  title: string;
  content: string;
  targetSceneId?: string;
  productData?: {
    name: string;
    price: number;
    description: string;
    whatsappMessage?: string;
  };
  linkUrl?: string;
}

interface IntegratedTourEditorProps {
  tourId?: string;
  tourData: {
    title: string;
    description: string;
    category: string;
    location: string;
  };
  onSave?: (tourData: any) => void;
  onPublish?: (tourData: any) => void;
  onClose?: () => void;
}

const IntegratedTourEditor = ({
  tourId,
  tourData,
  onSave,
  onPublish,
  onClose
}: IntegratedTourEditorProps) => {
  const [scenes, setScenes] = useState<Scene[]>([]);
  const [currentSceneIndex, setCurrentSceneIndex] = useState(0);
  const [isAddingHotspot, setIsAddingHotspot] = useState(false);
  const [hotspotType, setHotspotType] = useState<Hotspot['type']>('info');
  const [selectedHotspot, setSelectedHotspot] = useState<Hotspot | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [editorMode, setEditorMode] = useState<'basic' | 'advanced'>('basic');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const currentScene = scenes[currentSceneIndex];

  // Auto-save functionality with data sync
  useEffect(() => {
    if (scenes.length > 0 && tourId) {
      const autoSaveTimer = setTimeout(async () => {
        try {
          await tourDataSyncService.updateTour(tourId, {
            custom_scenes: scenes,
            tour_platform: 'custom',
            updated_at: new Date().toISOString()
          });

          const tourData = {
            scenes,
            embedUrl: `https://virtualrealtour.ng/tour/${tourId}`,
            totalScenes: scenes.length,
            totalHotspots: scenes.reduce((total, scene) => total + scene.hotspots.length, 0),
            lastAutoSave: new Date().toISOString()
          };
          
          onSave?.(tourData);
          toast.success('Auto-saved', { duration: 2000 });
        } catch (error) {
          console.error('Auto-save error:', error);
        }
      }, 5000);

      return () => clearTimeout(autoSaveTimer);
    }
  }, [scenes, tourId, onSave]);

  const processFiles = (files: FileList) => {
    Array.from(files).forEach((file, index) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const imageUrl = e.target?.result as string;
          
          // Generate thumbnail
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d')!;
          const img = new Image();
          
          img.onload = () => {
            canvas.width = 120;
            canvas.height = 60;
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            const thumbnail = canvas.toDataURL('image/jpeg', 0.7);
            
            const newScene: Scene = {
              id: `scene-${Date.now()}-${index}`,
              name: `Scene ${scenes.length + index + 1}`,
              imageUrl,
              thumbnail,
              hotspots: []
            };
            
            setScenes(prev => [...prev, newScene]);
            toast.success(`Scene "${newScene.name}" added successfully!`);
          };
          
          img.src = imageUrl;
        };
        reader.readAsDataURL(file);
      } else {
        toast.error(`${file.name} is not a valid image file`);
      }
    });
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;
    processFiles(files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    if (files) {
      processFiles(files);
    }
  };

  const handleSceneClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!isAddingHotspot || !currentScene) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;

    const newHotspot: Hotspot = {
      id: `hotspot-${Date.now()}`,
      type: hotspotType,
      x,
      y,
      title: `${hotspotType.charAt(0).toUpperCase() + hotspotType.slice(1)} Hotspot`,
      content: 'Click to edit this hotspot'
    };

    setScenes(prev => prev.map((scene, index) => 
      index === currentSceneIndex 
        ? { ...scene, hotspots: [...scene.hotspots, newHotspot] }
        : scene
    ));

    setIsAddingHotspot(false);
    setSelectedHotspot(newHotspot);
    toast.success('Hotspot added! Configure it in the properties panel.');
  };

  const removeHotspot = (hotspotId: string) => {
    setScenes(prev => prev.map((scene, index) => 
      index === currentSceneIndex 
        ? { ...scene, hotspots: scene.hotspots.filter(h => h.id !== hotspotId) }
        : scene
    ));
    setSelectedHotspot(null);
    toast.success('Hotspot removed');
  };

  const updateHotspot = (hotspotId: string, updates: Partial<Hotspot>) => {
    setScenes(prev => prev.map((scene, index) => 
      index === currentSceneIndex 
        ? { 
            ...scene, 
            hotspots: scene.hotspots.map(h => 
              h.id === hotspotId ? { ...h, ...updates } : h
            ) 
          }
        : scene
    ));
  };

  const handleSave = async () => {
    try {
      if (tourId) {
        await tourDataSyncService.updateTour(tourId, {
          custom_scenes: scenes,
          tour_platform: 'custom',
          updated_at: new Date().toISOString()
        });
      }

      const tourData = {
        scenes,
        embedUrl: `https://virtualrealtour.ng/tour/${tourId}`,
        totalScenes: scenes.length,
        totalHotspots: scenes.reduce((total, scene) => total + scene.hotspots.length, 0)
      };
      
      onSave?.(tourData);
      toast.success('Tour saved successfully!');
    } catch (error) {
      console.error('Save error:', error);
      toast.error('Failed to save tour');
    }
  };

  const handlePublish = async () => {
    if (scenes.length === 0) {
      toast.error('Please add at least one scene before publishing');
      return;
    }
    
    try {
      if (tourId) {
        await tourDataSyncService.updateTour(tourId, {
          custom_scenes: scenes,
          status: 'published',
          tour_platform: 'custom',
          updated_at: new Date().toISOString()
        });
      }

      const tourData = {
        scenes,
        embedUrl: `https://virtualrealtour.ng/tour/${tourId}`,
        totalScenes: scenes.length,
        totalHotspots: scenes.reduce((total, scene) => total + scene.hotspots.length, 0),
        publishedAt: new Date().toISOString()
      };
      
      onPublish?.(tourData);
      toast.success('Tour published successfully!');
    } catch (error) {
      console.error('Publish error:', error);
      toast.error('Failed to publish tour');
    }
  };

  const handlePreview = () => {
    if (scenes.length === 0) {
      toast.error('Please add at least one scene to preview');
      return;
    }
    
    // Open preview in new tab with our custom URL
    const previewUrl = `https://virtualrealtour.ng/tour/${tourId}`;
    window.open(previewUrl, '_blank');
    toast.success('Opening tour preview...');
  };

  const getHotspotIcon = (type: Hotspot['type']) => {
    switch (type) {
      case 'navigation': return <MapPin className="w-4 h-4" />;
      case 'product': return <ShoppingCart className="w-4 h-4" />;
      case 'link': return <Link className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Minimized Header */}
      <div className="flex items-center justify-between p-4 border-b bg-muted/30">
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-green-100 text-green-800">
            <Zap className="w-3 h-3 mr-1" />
            VirtualRealTour Custom Editor
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handlePreview}>
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          <Button variant="outline" size="sm" onClick={handleSave}>
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          <Button size="sm" onClick={handlePublish}>
            <Share2 className="w-4 h-4 mr-2" />
            Publish
          </Button>
        </div>
      </div>

      <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-4">
        {/* Left Panel - Scene Management */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Layers className="w-4 h-4" />
              Scenes ({scenes.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer ${
                isDragOver ? 'border-primary bg-primary/5' : 'border-muted-foreground/25 hover:border-primary/50'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm font-medium mb-1">Upload 360° Images</p>
              <p className="text-xs text-muted-foreground">
                Drag & drop or click to browse
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Supports: JPG, PNG, WebP
              </p>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handleImageUpload}
              className="hidden"
              aria-label="Upload 360 degree panoramic images"
            />

            {/* Scene List */}
            <div className="space-y-2">
              {scenes.map((scene, index) => (
                <div
                  key={scene.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    index === currentSceneIndex ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
                  }`}
                  onClick={() => setCurrentSceneIndex(index)}
                >
                  <div className="flex items-center gap-2">
                    {scene.thumbnail && (
                      <img 
                        src={scene.thumbnail} 
                        alt={scene.name}
                        className="w-12 h-6 rounded object-cover"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{scene.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {scene.hotspots.length} hotspots
                      </p>
                    </div>
                  </div>
                </div>
              ))}
              
              {scenes.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Camera className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-sm">Upload 360° images to start</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Main Editor Area */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">
                {currentScene ? currentScene.name : 'Select a scene to edit'}
              </CardTitle>
              {currentScene && (
                <div className="flex items-center gap-2">
                  <Label htmlFor="hotspot-type" className="text-sm">Add:</Label>
                  <select
                    id="hotspot-type"
                    value={hotspotType}
                    onChange={(e) => setHotspotType(e.target.value as Hotspot['type'])}
                    className="text-sm border rounded px-2 py-1"
                    title="Select hotspot type"
                  >
                    <option value="info">Info Point</option>
                    <option value="product">Product</option>
                    <option value="navigation">Navigation</option>
                    <option value="link">External Link</option>
                  </select>
                  <Button
                    size="sm"
                    variant={isAddingHotspot ? "default" : "outline"}
                    onClick={() => setIsAddingHotspot(!isAddingHotspot)}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {isAddingHotspot ? 'Click on image' : 'Add Hotspot'}
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent className="p-0">
            {currentScene ? (
              <div className="h-96 relative">
                <div
                  className="w-full h-full bg-muted rounded-lg overflow-hidden cursor-crosshair relative"
                  onClick={handleSceneClick}
                >
                  <img
                    src={currentScene.imageUrl}
                    alt={currentScene.name}
                    className="w-full h-full object-cover"
                  />
                  {/* Hotspots */}
                  {currentScene.hotspots.map((hotspot) => (
                    <div
                      key={hotspot.id}
                      className="absolute transform -translate-x-1/2 -translate-y-1/2 group"
                      style={{
                        left: `${Math.max(0, Math.min(100, hotspot.x))}%`,
                        top: `${Math.max(0, Math.min(100, hotspot.y))}%`
                      }}
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedHotspot(hotspot);
                      }}
                    >
                      <div className="relative">
                        <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white shadow-lg cursor-pointer hover:scale-110 transition-transform">
                          {getHotspotIcon(hotspot.type)}
                        </div>
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeHotspot(hotspot.id);
                          }}
                          className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity"
                          title={`Remove ${hotspot.title} hotspot`}
                        >
                          <X className="w-2 h-2" />
                        </button>
                        <div className="absolute top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                          {hotspot.title}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                {isAddingHotspot && (
                  <div className="absolute bottom-4 left-4 right-4 bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <p className="text-sm text-blue-800">
                      Click anywhere on the image to place a {hotspotType} hotspot
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="h-96 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <Camera className="w-12 h-12 mx-auto mb-4" />
                  <p>Upload 360° images to start creating your virtual tour</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Right Panel - Properties */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-base flex items-center gap-2">
              <Settings className="w-4 h-4" />
              Properties
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedHotspot ? (
              <HotspotProperties 
                hotspot={selectedHotspot}
                scenes={scenes}
                onUpdate={(updates) => updateHotspot(selectedHotspot.id, updates)}
                onDelete={() => removeHotspot(selectedHotspot.id)}
              />
            ) : currentScene ? (
              <SceneProperties 
                scene={currentScene}
                onUpdate={(updates) => {
                  setScenes(prev => prev.map((scene, index) => 
                    index === currentSceneIndex ? { ...scene, ...updates } : scene
                  ));
                }}
              />
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Settings className="w-8 h-8 mx-auto mb-2" />
                <p className="text-sm">Select a scene or hotspot to edit properties</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

// Helper Components (will be added in next file due to length limit)
const HotspotProperties = ({ hotspot, scenes, onUpdate, onDelete }: any) => (
  <div className="space-y-4">
    <div className="flex items-center justify-between">
      <h3 className="font-medium">Hotspot Settings</h3>
      <Button variant="destructive" size="sm" onClick={onDelete}>
        <X className="w-4 h-4" />
      </Button>
    </div>
    
    <div className="space-y-3">
      <div>
        <Label>Title</Label>
        <Input 
          value={hotspot.title}
          onChange={(e) => onUpdate({ title: e.target.value })}
        />
      </div>
      
      <div>
        <Label>Content</Label>
        <Input 
          value={hotspot.content}
          onChange={(e) => onUpdate({ content: e.target.value })}
        />
      </div>
      
      {hotspot.type === 'product' && (
        <div className="space-y-3">
          <div>
            <Label>Product Name</Label>
            <Input 
              value={hotspot.productData?.name || ''}
              onChange={(e) => onUpdate({ 
                productData: { ...hotspot.productData, name: e.target.value }
              })}
            />
          </div>
          <div>
            <Label>Price</Label>
            <Input 
              type="number"
              value={hotspot.productData?.price || ''}
              onChange={(e) => onUpdate({ 
                productData: { ...hotspot.productData, price: parseFloat(e.target.value) }
              })}
            />
          </div>
        </div>
      )}
      
      {hotspot.type === 'navigation' && (
        <div>
          <Label>Target Scene</Label>
          <select
            value={hotspot.targetSceneId || ''}
            onChange={(e) => onUpdate({ targetSceneId: e.target.value })}
            className="w-full border rounded px-3 py-2"
            title="Select target scene for navigation"
          >
            <option value="">Select scene...</option>
            {scenes.map((scene: any) => (
              <option key={scene.id} value={scene.id}>{scene.name}</option>
            ))}
          </select>
        </div>
      )}
    </div>
  </div>
);

const SceneProperties = ({ scene, onUpdate }: any) => (
  <div className="space-y-4">
    <h3 className="font-medium">Scene Settings</h3>
    
    <div className="space-y-3">
      <div>
        <Label>Scene Name</Label>
        <Input 
          value={scene.name}
          onChange={(e) => onUpdate({ name: e.target.value })}
        />
      </div>
    </div>
  </div>
);

export default IntegratedTourEditor;
