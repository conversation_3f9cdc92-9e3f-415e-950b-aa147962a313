
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { ArrowRight } from 'lucide-react'
import { Link } from 'react-router-dom'
import { supabase } from '@/lib/supabase'
import EnhancedTourCard from '@/components/EnhancedTourCard'
import { getTourCardSettings } from '@/config/tourCardSettings'

const TourShowcaseSection = () => {
  // Fetch real published tours from database
  const { data: tours = [] } = useQuery({
    queryKey: ['showcase-tours'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tours')
        .select('*')
        .eq('status', 'published')
        .order('views', { ascending: false })
        .limit(4);

      if (error) {
        console.error('Error fetching showcase tours:', error);
        return [];
      }

      return data || [];
    },
  });

  return (
    <section className="py-16 lg:py-24 bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="container px-4 sm:px-6 lg:px-8">
        {/* Enhanced Header */}
        <div className="text-center mb-16 lg:mb-20">
          <div className="inline-flex items-center rounded-full bg-theme-primary-light border border-theme-primary-border px-4 py-2 text-sm font-medium text-theme-primary mb-6">
            🔥 Popular Tours
          </div>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Most Viewed
            <br />
            <span className="text-theme-primary">Experiences</span>
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover breathtaking virtual tours that showcase spaces in stunning detail.
            From luxury properties to cultural landmarks, explore the world from anywhere.
          </p>
        </div>

        {/* Enhanced Tours Grid with Real Tours */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8 mb-16 max-w-7xl mx-auto">
          {tours.length > 0 ? (
            tours.map((tour) => {
              const settings = getTourCardSettings('showcase');
              return (
                <EnhancedTourCard
                  key={tour.id}
                  tour={tour}
                  showEmbedded={settings.showEmbedded}
                  showActions={settings.showActions}
                  autoLoad={settings.autoLoad}
                  className="hover-lift"
                />
              );
            })
          ) : (
            // Fallback when no tours available
            <div className="col-span-full text-center py-12">
              <p className="text-gray-500 mb-4">No tours available yet</p>
              <Button asChild>
                <Link to="/showcase">Browse All Tours</Link>
              </Button>
            </div>
          )}
        </div>

        {/* Enhanced Bottom CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-theme-primary-light via-white to-theme-primary-light rounded-2xl p-8 border border-theme-primary-border">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Explore More?
            </h3>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              Discover hundreds of immersive virtual tours from around the world
            </p>
            <Button
              size="lg"
              className="px-8 py-4 rounded-xl bg-theme-primary hover:bg-theme-primary-hover text-theme-primary-foreground font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
              asChild
            >
              <Link to="/showcase" className="flex items-center">
                Explore All Tours
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default TourShowcaseSection
