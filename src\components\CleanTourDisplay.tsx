import React from 'react';
import { getCleanEmbedUrl, getCleanIframeProps } from '@/utils/cleanTourEmbedding';
import { cn } from '@/lib/utils';

interface CleanTourDisplayProps {
  tour: {
    id: string;
    title: string;
    embed_url?: string;
    thumbnail_url?: string;
  };
  className?: string;
  containerClassName?: string;
  aspectRatio?: 'video' | 'square' | 'wide' | 'custom';
  rounded?: boolean;
  fallbackImage?: string;
  onClick?: () => void;
}

/**
 * Clean Tour Display Component
 * 
 * Displays tours as clean iframe embeds without cards, overlays, or controls.
 * Uses the same logic as the featured tour displays for consistency.
 */
const CleanTourDisplay: React.FC<CleanTourDisplayProps> = ({
  tour,
  className = '',
  containerClassName = '',
  aspectRatio = 'video',
  rounded = true,
  fallbackImage = "https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=1200&h=675&fit=crop",
  onClick
}) => {
  const getAspectRatioClass = () => {
    switch (aspectRatio) {
      case 'square':
        return 'aspect-square';
      case 'wide':
        return 'aspect-[21/9]';
      case 'video':
        return 'aspect-video';
      case 'custom':
        return '';
      default:
        return 'aspect-video';
    }
  };

  const containerClasses = cn(
    'relative overflow-hidden bg-gray-800',
    getAspectRatioClass(),
    rounded && 'rounded-xl',
    onClick && 'cursor-pointer',
    containerClassName
  );

  const iframeClasses = cn(
    'w-full h-full border-0 clean-tour-iframe',
    rounded && 'rounded-xl',
    className
  );

  const imageClasses = cn(
    'w-full h-full object-cover',
    rounded && 'rounded-xl',
    className
  );

  return (
    <div className={containerClasses} onClick={onClick}>
      {tour.embed_url && !tour.embed_url.includes('metaport') ? (
        // Clean embedded tour using iframe
        <iframe
          src={getCleanEmbedUrl(tour.embed_url)}
          className={iframeClasses}
          title={tour.title}
          {...getCleanIframeProps()}
        />
      ) : (
        // Fallback to image for restricted tours or when no embed_url
        <img
          src={tour.thumbnail_url || fallbackImage}
          alt={tour.title}
          className={imageClasses}
        />
      )}
    </div>
  );
};

export default CleanTourDisplay;
