/**
 * WooCommerce REST API Service
 * Handles all WooCommerce API interactions with JWT authentication
 */

interface WooCommerceConfig {
  baseUrl: string;
  consumerKey: string;
  consumerSecret: string;
  jwtToken?: string;
  wpUsername?: string;
  wpAppPassword?: string;
}

interface WooCommerceProduct {
  id: number;
  name: string;
  slug: string;
  permalink: string;
  date_created: string;
  date_modified: string;
  type: string;
  status: string;
  featured: boolean;
  catalog_visibility: string;
  description: string;
  short_description: string;
  sku: string;
  price: string;
  regular_price: string;
  sale_price: string;
  on_sale: boolean;
  purchasable: boolean;
  total_sales: number;
  virtual: boolean;
  downloadable: boolean;
  downloads: any[];
  download_limit: number;
  download_expiry: number;
  external_url: string;
  button_text: string;
  tax_status: string;
  tax_class: string;
  manage_stock: boolean;
  stock_quantity: number | null;
  stock_status: string;
  backorders: string;
  backorders_allowed: boolean;
  backordered: boolean;
  sold_individually: boolean;
  weight: string;
  dimensions: {
    length: string;
    width: string;
    height: string;
  };
  shipping_required: boolean;
  shipping_taxable: boolean;
  shipping_class: string;
  shipping_class_id: number;
  reviews_allowed: boolean;
  average_rating: string;
  rating_count: number;
  related_ids: number[];
  upsell_ids: number[];
  cross_sell_ids: number[];
  parent_id: number;
  purchase_note: string;
  categories: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  tags: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  images: Array<{
    id: number;
    date_created: string;
    date_modified: string;
    src: string;
    name: string;
    alt: string;
  }>;
  attributes: any[];
  default_attributes: any[];
  variations: number[];
  grouped_products: number[];
  menu_order: number;
  meta_data: Array<{
    id: number;
    key: string;
    value: string;
  }>;
  vendor_whatsapp?: string;
}

interface WooCommerceOrder {
  id: number;
  parent_id: number;
  number: string;
  order_key: string;
  created_via: string;
  version: string;
  status: string;
  currency: string;
  date_created: string;
  date_modified: string;
  discount_total: string;
  discount_tax: string;
  shipping_total: string;
  shipping_tax: string;
  cart_tax: string;
  total: string;
  total_tax: string;
  prices_include_tax: boolean;
  customer_id: number;
  customer_ip_address: string;
  customer_user_agent: string;
  customer_note: string;
  billing: any;
  shipping: any;
  payment_method: string;
  payment_method_title: string;
  transaction_id: string;
  date_paid: string | null;
  date_completed: string | null;
  cart_hash: string;
  meta_data: Array<{
    id: number;
    key: string;
    value: string;
  }>;
  line_items: any[];
  tax_lines: any[];
  shipping_lines: any[];
  fee_lines: any[];
  coupon_lines: any[];
  refunds: any[];
}

class WooCommerceService {
  private config: WooCommerceConfig;
  private jwtToken: string | null = null;

  constructor(config: WooCommerceConfig) {
    this.config = config;
    this.jwtToken = config.jwtToken || null;
  }

  /**
   * Authenticate with WordPress JWT
   */
  async authenticate(username: string, password: string): Promise<string> {
    try {
      const response = await fetch(`${this.config.baseUrl}/wp-json/jwt-auth/v1/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          password,
        }),
      });

      if (!response.ok) {
        throw new Error(`Authentication failed: ${response.statusText}`);
      }

      const data = await response.json();
      this.jwtToken = data.token;
      return data.token;
    } catch (error) {
      console.error('JWT Authentication error:', error);
      throw error;
    }
  }

  /**
   * Make authenticated API request
   */
  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.config.baseUrl}/wp-json/wc/v3${endpoint}`;

    // Get auth method from environment
    const authMethod = import.meta.env.VITE_WOO_AUTH_METHOD || 'consumer-key';
    let authHeader = '';

    switch (authMethod) {
      case 'app-password':
        if (this.config.wpUsername && this.config.wpAppPassword) {
          const cleanPassword = this.config.wpAppPassword.replace(/\s/g, '');
          const appAuth = btoa(`${this.config.wpUsername}:${cleanPassword}`);
          authHeader = `Basic ${appAuth}`;
        }
        break;

      case 'jwt':
        if (this.jwtToken) {
          authHeader = `Bearer ${this.jwtToken}`;
        }
        break;

      case 'consumer-key':
      default:
        const wooAuth = btoa(`${this.config.consumerKey}:${this.config.consumerSecret}`);
        authHeader = `Basic ${wooAuth}`;
        break;
    }

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': authHeader,
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error(`WooCommerce API error for ${endpoint}:`, error);
      throw error;
    }
  }

  /**
   * Get single product by ID
   */
  async getProduct(productId: string | number): Promise<WooCommerceProduct> {
    return this.makeRequest<WooCommerceProduct>(`/products/${productId}`);
  }

  /**
   * Get multiple products
   */
  async getProducts(params: {
    page?: number;
    per_page?: number;
    search?: string;
    category?: string;
    tag?: string;
    featured?: boolean;
    on_sale?: boolean;
    status?: string;
  } = {}): Promise<WooCommerceProduct[]> {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = `/products${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeRequest<WooCommerceProduct[]>(endpoint);
  }

  /**
   * Create order (for tracking purposes)
   */
  async createOrder(orderData: {
    customer_id?: number;
    billing: any;
    shipping?: any;
    line_items: Array<{
      product_id: number;
      quantity: number;
      price?: number;
    }>;
    shipping_lines?: any[];
    meta_data?: Array<{
      key: string;
      value: string;
    }>;
  }): Promise<WooCommerceOrder> {
    return this.makeRequest<WooCommerceOrder>('/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  /**
   * Get order by ID
   */
  async getOrder(orderId: string | number): Promise<WooCommerceOrder> {
    return this.makeRequest<WooCommerceOrder>(`/orders/${orderId}`);
  }

  /**
   * Update order status
   */
  async updateOrderStatus(orderId: string | number, status: string): Promise<WooCommerceOrder> {
    return this.makeRequest<WooCommerceOrder>(`/orders/${orderId}`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  }

  /**
   * Get product categories
   */
  async getCategories(): Promise<Array<{
    id: number;
    name: string;
    slug: string;
    description: string;
    image: any;
    count: number;
  }>> {
    return this.makeRequest('/products/categories');
  }

  /**
   * Search products
   */
  async searchProducts(query: string, limit: number = 10): Promise<WooCommerceProduct[]> {
    return this.getProducts({
      search: query,
      per_page: limit,
      status: 'publish',
    });
  }

  /**
   * Test WordPress connection
   */
  async testWordPressConnection(): Promise<{ status: string; data?: any }> {
    try {
      const response = await fetch(`${this.config.baseUrl}/wp-json/virtualtour/v1/ping`);
      if (!response.ok) {
        throw new Error(`WordPress ping failed: ${response.status}`);
      }
      const data = await response.json();
      return { status: 'connected', data };
    } catch (error) {
      console.error('WordPress connection test failed:', error);
      throw error;
    }
  }

  /**
   * Test WooCommerce connection
   */
  async testWooCommerceConnection(): Promise<{ status: string; data?: any }> {
    try {
      const response = await fetch(`${this.config.baseUrl}/wp-json/virtualtour/v1/woo-test`);
      if (!response.ok) {
        throw new Error(`WooCommerce test failed: ${response.status}`);
      }
      const data = await response.json();
      return { status: 'connected', data };
    } catch (error) {
      console.error('WooCommerce connection test failed:', error);
      throw error;
    }
  }
}

// Create singleton instance
const wooCommerceConfig: WooCommerceConfig = {
  baseUrl: import.meta.env.VITE_WORDPRESS_URL || 'http://localhost:10090',
  consumerKey: import.meta.env.VITE_WOO_CONSUMER_KEY || 'ck_405631cb8df6b2245d0695f814f05e47ca6befbd',
  consumerSecret: import.meta.env.VITE_WOO_CONSUMER_SECRET || 'cs_51c2556e9ad57d2ef40c82f6e3bc266e99103f98',
  wpUsername: import.meta.env.VITE_WP_USERNAME || 'iwalk',
  wpAppPassword: import.meta.env.VITE_WP_APP_PASSWORD || 'JiQN tXNq ieO1 hRDv 6gjd dtQj',
};

export const wooCommerceService = new WooCommerceService(wooCommerceConfig);
export type { WooCommerceProduct, WooCommerceOrder };
export default WooCommerceService;
