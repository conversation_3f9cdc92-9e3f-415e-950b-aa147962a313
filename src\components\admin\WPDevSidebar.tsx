import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Settings, FileText, Globe } from 'lucide-react';

const WPDevSidebar: React.FC = () => (
  <nav className="space-y-2 p-4 bg-muted rounded-lg">
    <h2 className="text-lg font-bold mb-2">WPDev & WordPress</h2>
    <ul className="space-y-1">
      <li><Link to="/admin/wordpress" className="flex items-center gap-2"><Globe className="w-4 h-4" />WordPress Content</Link></li>
      <li><Link to="/admin/woo-api" className="flex items-center gap-2"><FileText className="w-4 h-4" />Woo API Management</Link></li>
      <li><Link to="/admin/settings" className="flex items-center gap-2"><Settings className="w-4 h-4" />WP Settings</Link></li>
      {/* Add more WP/eco links as needed */}
    </ul>
  </nav>
);

export default WPDevSidebar;
