
import UploadStepOne from './UploadStepOne';
import UploadStepTwo from './UploadStepTwo';
import UploadStepThree from './UploadStepThree';
import UploadStepFour from './UploadStepFour';
import { FormData } from '@/hooks/useUploadForm';

interface UploadModalContentProps {
  currentStep: number;
  formData: FormData;
  setFormData: (data: FormData) => void;
}

const UploadModalContent = ({ currentStep, formData, setFormData }: UploadModalContentProps) => {
  switch (currentStep) {
    case 1:
      return <UploadStepOne formData={formData} setFormData={setFormData} />;
    case 2:
      return <UploadStepTwo formData={formData} setFormData={setFormData} />;
    case 3:
      return <UploadStepThree formData={formData} />;
    case 4:
      return <UploadStepFour formData={formData} setFormData={setFormData} />;
    default:
      return null;
  }
};

export default UploadModalContent;
