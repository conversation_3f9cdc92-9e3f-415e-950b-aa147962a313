/**
 * Admin Tour Enhancements Demo
 * Showcase of TourMkr-inspired improvements without breaking existing functionality
 */

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Eye, 
  Settings, 
  Palette, 
  Target, 
  ShoppingCart, 
  MessageCircle,
  Video,
  ExternalLink,
  Globe,
  Image as ImageIcon,
  FileText,
  Sparkles,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';
import TourWrapper from '@/components/tours/TourWrapper';

const AdminTourEnhancements = () => {
  const [selectedDemo, setSelectedDemo] = useState('overlay-studio');

  const enhancements = [
    {
      id: 'overlay-studio',
      title: 'Enhanced Overlay Studio',
      description: 'TourMkr-inspired overlay system with comprehensive hotspot types',
      status: 'complete',
      features: [
        '8 hotspot types (Product, Info, Video, Link, Scene Jump, Gallery, Text, Form)',
        'Enhanced animation presets with intensity levels',
        'Comprehensive popup style templates',
        'Feature-rich hotspot configuration',
        'Visual preview system'
      ]
    },
    {
      id: 'tour-wrapper',
      title: 'Secure Tour Embedding',
      description: 'Prevent URL leakage with branded tour wrapper',
      status: 'complete',
      features: [
        'Domain-locked tour URLs',
        'Branded overlay system',
        'Custom control bar',
        'Theme-based hotspot styling',
        'Loading and error states'
      ]
    },
    {
      id: 'mobile-responsive',
      title: 'Mobile-First Optimization',
      description: 'Enhanced mobile experience for tours and admin',
      status: 'ready',
      features: [
        'Responsive tour viewer',
        'Touch-optimized controls',
        'Mobile-friendly overlays',
        'Adaptive hotspot sizing',
        'Gesture support'
      ]
    },
    {
      id: 'vendor-integration',
      title: 'Enhanced Vendor Integration',
      description: 'Improved WooCommerce sync and product management',
      status: 'ready',
      features: [
        'Real-time product sync',
        'Hotspot-product assignment',
        'Vendor dashboard enhancements',
        'Order management integration',
        'WhatsApp commerce flow'
      ]
    }
  ];

  const hotspotTypes = [
    { id: 'product', name: 'Product Card', icon: ShoppingCart, color: 'text-green-600' },
    { id: 'info', name: 'Information', icon: MessageCircle, color: 'text-blue-600' },
    { id: 'video', name: 'Video Player', icon: Video, color: 'text-purple-600' },
    { id: 'link', name: 'External Link', icon: ExternalLink, color: 'text-orange-600' },
    { id: 'pano', name: 'Scene Jump', icon: Globe, color: 'text-cyan-600' },
    { id: 'image', name: 'Image Gallery', icon: ImageIcon, color: 'text-pink-600' },
    { id: 'text', name: 'Text Overlay', icon: FileText, color: 'text-gray-600' },
    { id: 'form', name: 'Contact Form', icon: FileText, color: 'text-indigo-600' }
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Tour Enhancements</h1>
            <p className="text-muted-foreground">
              TourMkr-inspired improvements integrated into existing VirtualRealTour system
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Eye className="w-4 h-4 mr-2" />
              View Original
            </Button>
            <Button>
              <Settings className="w-4 h-4 mr-2" />
              Configure
            </Button>
          </div>
        </div>

        {/* Enhancement Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {enhancements.map((enhancement) => (
            <Card 
              key={enhancement.id}
              className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
                selectedDemo === enhancement.id ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => setSelectedDemo(enhancement.id)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{enhancement.title}</CardTitle>
                  <Badge 
                    variant={enhancement.status === 'complete' ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {enhancement.status === 'complete' ? (
                      <CheckCircle className="w-3 h-3 mr-1" />
                    ) : null}
                    {enhancement.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-3">
                  {enhancement.description}
                </p>
                <div className="space-y-1">
                  {enhancement.features.slice(0, 3).map((feature, idx) => (
                    <div key={idx} className="flex items-center gap-2 text-xs">
                      <CheckCircle className="w-3 h-3 text-green-500" />
                      <span>{feature}</span>
                    </div>
                  ))}
                  {enhancement.features.length > 3 && (
                    <div className="text-xs text-muted-foreground">
                      +{enhancement.features.length - 3} more features
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Demo Content */}
        <Tabs value={selectedDemo} onValueChange={setSelectedDemo} className="space-y-6">
          {/* Enhanced Overlay Studio Demo */}
          <TabsContent value="overlay-studio" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="w-5 h-5" />
                  Enhanced Hotspot Types
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {hotspotTypes.map((type) => {
                    const Icon = type.icon;
                    return (
                      <Card key={type.id} className="p-4 text-center hover:shadow-md transition-shadow">
                        <Icon className={`w-8 h-8 mx-auto mb-2 ${type.color}`} />
                        <h4 className="font-medium text-sm">{type.name}</h4>
                      </Card>
                    );
                  })}
                </div>
                
                <div className="mt-6 flex gap-2">
                  <Button>
                    <ArrowRight className="w-4 h-4 mr-2" />
                    Open Overlay Studio
                  </Button>
                  <Button variant="outline">
                    <Eye className="w-4 h-4 mr-2" />
                    View Documentation
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Tour Wrapper Demo */}
          <TabsContent value="tour-wrapper" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  Secure Tour Embedding Demo
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    This demo shows how external tours are now wrapped with our secure embedding system,
                    preventing URL leakage while adding branded overlays and controls.
                  </p>
                  
                  {/* Demo Tour Wrapper */}
                  <div className="border rounded-lg overflow-hidden">
                    <TourWrapper
                      tourId="demo-tour"
                      src="https://example.com/demo-tour"
                      title="Demo Hotel Tour"
                      hotspotTheme="glass"
                      productSync={true}
                      showControls={true}
                      allowFullscreen={true}
                      className="h-96"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                    <Card className="p-4">
                      <h4 className="font-medium mb-2">Secure URLs</h4>
                      <p className="text-sm text-muted-foreground">
                        All tours use virtualrealtour.ng URLs, preventing source leakage
                      </p>
                    </Card>
                    <Card className="p-4">
                      <h4 className="font-medium mb-2">Branded Controls</h4>
                      <p className="text-sm text-muted-foreground">
                        Custom control bar with share, fullscreen, and audio controls
                      </p>
                    </Card>
                    <Card className="p-4">
                      <h4 className="font-medium mb-2">Overlay System</h4>
                      <p className="text-sm text-muted-foreground">
                        Theme-based hotspots with product integration
                      </p>
                    </Card>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Mobile Optimization Demo */}
          <TabsContent value="mobile-responsive" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Mobile-First Optimization</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Enhanced mobile experience with responsive design and touch-optimized controls.
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-medium mb-3">Mobile Improvements</h4>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          Responsive tour viewer
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          Touch-optimized hotspots
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          Adaptive overlay sizing
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          Gesture navigation support
                        </li>
                      </ul>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-3">Admin Mobile</h4>
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          Mobile-friendly admin interface
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          Responsive overlay studio
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          Touch-friendly controls
                        </li>
                        <li className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          Optimized form layouts
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Vendor Integration Demo */}
          <TabsContent value="vendor-integration" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Enhanced Vendor Integration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-sm text-muted-foreground">
                    Improved WooCommerce integration with real-time sync and enhanced vendor workflows.
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card className="p-4">
                      <h4 className="font-medium mb-3">Product Management</h4>
                      <ul className="space-y-2 text-sm">
                        <li>• Real-time WooCommerce sync</li>
                        <li>• Bulk product import</li>
                        <li>• Inventory management</li>
                        <li>• Product-hotspot assignment</li>
                      </ul>
                    </Card>
                    
                    <Card className="p-4">
                      <h4 className="font-medium mb-3">Commerce Flow</h4>
                      <ul className="space-y-2 text-sm">
                        <li>• WhatsApp checkout integration</li>
                        <li>• Order tracking system</li>
                        <li>• Vendor dashboard analytics</li>
                        <li>• Commission management</li>
                      </ul>
                    </Card>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminTourEnhancements;
