/**
 * Vendor Selector Component
 * Select vendor for tour creation and commerce integration
 */

import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Plus, 
  Store, 
  Phone, 
  Mail, 
  MapPin,
  CheckCircle,
  Search,
  DollarSign
} from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';

interface Vendor {
  id: string;
  name: string;
  email: string;
  phone: string;
  whatsapp_number: string;
  business_address: string;
  business_description: string;
  status: 'pending' | 'active' | 'suspended';
  commission_rate: number;
  created_at: string;
}

interface VendorSelectorProps {
  selectedVendorId?: string;
  onVendorSelect: (vendorId: string, vendor: Vendor) => void;
  onCreateNew?: () => void;
  showCreateButton?: boolean;
}

const VendorSelector = ({ 
  selectedVendorId, 
  onVendorSelect, 
  onCreateNew,
  showCreateButton = true 
}: VendorSelectorProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Fetch active vendors
  const { data: vendors = [], isLoading } = useQuery({
    queryKey: ['active-vendors'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('vendors')
        .select('*')
        .eq('status', 'active')
        .order('name', { ascending: true });

      if (error) throw error;
      return data as Vendor[];
    },
  });

  // Filter vendors based on search term
  const filteredVendors = vendors.filter(vendor =>
    vendor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.business_address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const selectedVendor = vendors.find(v => v.id === selectedVendorId);

  const handleVendorSelect = (vendor: Vendor) => {
    onVendorSelect(vendor.id, vendor);
    setIsDialogOpen(false);
    toast.success(`Selected vendor: ${vendor.name}`);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label>Select Vendor</Label>
        {showCreateButton && (
          <Button
            variant="outline"
            size="sm"
            onClick={onCreateNew}
          >
            <Plus className="w-4 h-4 mr-2" />
            Create New Vendor
          </Button>
        )}
      </div>

      {/* Selected Vendor Display */}
      {selectedVendor ? (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                  <Store className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h4 className="font-medium text-green-900">{selectedVendor.name}</h4>
                  <p className="text-sm text-green-700 flex items-center">
                    <DollarSign className="w-3 h-3 mr-1" />
                    {(selectedVendor.commission_rate * 100).toFixed(1)}% commission
                  </p>
                </div>
              </div>
              <Badge variant="default" className="bg-green-500">
                <CheckCircle className="w-3 h-3 mr-1" />
                Selected
              </Badge>
            </div>
            <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-green-700">
              <div className="flex items-center">
                <Mail className="w-3 h-3 mr-1" />
                {selectedVendor.email}
              </div>
              <div className="flex items-center">
                <Phone className="w-3 h-3 mr-1" />
                {selectedVendor.phone}
              </div>
              {selectedVendor.business_address && (
                <div className="flex items-center md:col-span-2">
                  <MapPin className="w-3 h-3 mr-1" />
                  {selectedVendor.business_address}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="border-dashed border-2 border-gray-300">
          <CardContent className="p-6 text-center">
            <Store className="w-12 h-12 mx-auto text-gray-400 mb-3" />
            <h4 className="font-medium text-gray-600 mb-2">No Vendor Selected</h4>
            <p className="text-sm text-gray-500 mb-4">
              Choose a vendor to enable commerce features for this tour
            </p>
            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Store className="w-4 h-4 mr-2" />
                  Select Vendor
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Select Vendor</DialogTitle>
                  <DialogDescription>
                    Choose a vendor for this tour's commerce integration
                  </DialogDescription>
                </DialogHeader>
                
                <div className="space-y-4">
                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      placeholder="Search vendors..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  {/* Vendor List */}
                  <div className="max-h-96 overflow-y-auto space-y-2">
                    {isLoading ? (
                      <div className="text-center py-8">Loading vendors...</div>
                    ) : filteredVendors.length === 0 ? (
                      <div className="text-center py-8 text-muted-foreground">
                        {searchTerm ? 'No vendors found matching your search.' : 'No active vendors available.'}
                      </div>
                    ) : (
                      filteredVendors.map((vendor) => (
                        <Card 
                          key={vendor.id} 
                          className="cursor-pointer hover:shadow-md transition-shadow"
                          onClick={() => handleVendorSelect(vendor)}
                        >
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                                  <Store className="w-5 h-5 text-white" />
                                </div>
                                <div>
                                  <h4 className="font-medium">{vendor.name}</h4>
                                  <p className="text-sm text-muted-foreground flex items-center">
                                    <DollarSign className="w-3 h-3 mr-1" />
                                    {(vendor.commission_rate * 100).toFixed(1)}% commission
                                  </p>
                                </div>
                              </div>
                              <Button variant="outline" size="sm">
                                Select
                              </Button>
                            </div>
                            <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-muted-foreground">
                              <div className="flex items-center">
                                <Mail className="w-3 h-3 mr-1" />
                                {vendor.email}
                              </div>
                              <div className="flex items-center">
                                <Phone className="w-3 h-3 mr-1" />
                                {vendor.phone}
                              </div>
                              {vendor.business_address && (
                                <div className="flex items-center md:col-span-2">
                                  <MapPin className="w-3 h-3 mr-1" />
                                  {vendor.business_address}
                                </div>
                              )}
                            </div>
                            {vendor.business_description && (
                              <p className="mt-2 text-sm text-muted-foreground">
                                {vendor.business_description}
                              </p>
                            )}
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </CardContent>
        </Card>
      )}

      {/* Quick Select Dropdown for existing selection */}
      {vendors.length > 0 && (
        <div className="space-y-2">
          <Label htmlFor="vendor-select">Quick Select</Label>
          <Select 
            value={selectedVendorId || ''} 
            onValueChange={(value) => {
              const vendor = vendors.find(v => v.id === value);
              if (vendor) {
                handleVendorSelect(vendor);
              }
            }}
          >
            <SelectTrigger>
              <SelectValue placeholder="Choose from existing vendors..." />
            </SelectTrigger>
            <SelectContent>
              {vendors.map((vendor) => (
                <SelectItem key={vendor.id} value={vendor.id}>
                  <div className="flex items-center space-x-2">
                    <Store className="w-4 h-4" />
                    <span>{vendor.name}</span>
                    <Badge variant="secondary" className="ml-auto">
                      {(vendor.commission_rate * 100).toFixed(1)}%
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}
    </div>
  );
};

export default VendorSelector;
