/**
 * Admin Integrations Management
 * Consolidates WPVR, WooCommerce, CloudPano, and API settings
 */

import { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { 
  Globe, 
  ShoppingCart, 
  Cloud, 
  Key,
  CheckCircle,
  XCircle,
  AlertCircle,
  Settings,
  Eye,
  EyeOff,
  RefreshCw,
  Download,
  Upload,
  Link,
  Zap
} from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';

const AdminIntegrations = () => {
  const [activeTab, setActiveTab] = useState('wpvr');
  const [showApiKeys, setShowApiKeys] = useState(false);

  const integrations = [
    {
      id: 'wpvr',
      name: 'WPVR Tours',
      description: 'WordPress Virtual Reality plugin integration',
      status: 'connected',
      icon: Globe,
      color: 'text-blue-600',
      lastSync: '2 hours ago',
      tours: 12
    },
    {
      id: 'woocommerce',
      name: 'WooCommerce',
      description: 'E-commerce product integration',
      status: 'connected',
      icon: ShoppingCart,
      color: 'text-purple-600',
      lastSync: '1 hour ago',
      products: 45
    },
    {
      id: 'cloudpano',
      name: 'CloudPano',
      description: 'Advanced tour creation platform',
      status: 'disconnected',
      icon: Cloud,
      color: 'text-orange-600',
      lastSync: 'Never',
      tours: 0
    },
    {
      id: 'commonninja',
      name: 'CommonNinja',
      description: 'Widget-based tour creation',
      status: 'connected',
      icon: Zap,
      color: 'text-green-600',
      lastSync: '30 minutes ago',
      tours: 8
    }
  ];

  const getStatusBadge = (status: string) => {
    const variants = {
      'connected': { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      'disconnected': { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' },
      'error': { variant: 'outline' as const, icon: AlertCircle, color: 'text-yellow-600' }
    };
    
    const config = variants[status as keyof typeof variants] || variants.disconnected;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`w-3 h-3 ${config.color}`} />
        {status}
      </Badge>
    );
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Integrations & API</h1>
            <p className="text-muted-foreground">
              Manage external platform integrations and API configurations
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Sync All
            </Button>
            <Button variant="outline">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* Integration Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {integrations.map((integration) => {
            const Icon = integration.icon;
            return (
              <Card key={integration.id} className="hover:shadow-md transition-shadow">
                <CardContent className="pt-6">
                  <div className="flex items-center justify-between mb-4">
                    <Icon className={`w-8 h-8 ${integration.color}`} />
                    {getStatusBadge(integration.status)}
                  </div>
                  <h3 className="font-semibold mb-2">{integration.name}</h3>
                  <p className="text-sm text-muted-foreground mb-4">{integration.description}</p>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Last Sync:</span>
                      <span>{integration.lastSync}</span>
                    </div>
                    {integration.tours > 0 && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Tours:</span>
                        <span>{integration.tours}</span>
                      </div>
                    )}
                    {integration.products && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Products:</span>
                        <span>{integration.products}</span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Integration Management Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
            <TabsTrigger value="wpvr" className="flex items-center gap-2">
              <Globe className="w-4 h-4" />
              <span className="hidden sm:inline">WPVR</span>
            </TabsTrigger>
            <TabsTrigger value="woocommerce" className="flex items-center gap-2">
              <ShoppingCart className="w-4 h-4" />
              <span className="hidden sm:inline">WooCommerce</span>
            </TabsTrigger>
            <TabsTrigger value="cloudpano" className="flex items-center gap-2">
              <Cloud className="w-4 h-4" />
              <span className="hidden sm:inline">CloudPano</span>
            </TabsTrigger>
            <TabsTrigger value="api" className="flex items-center gap-2">
              <Key className="w-4 h-4" />
              <span className="hidden sm:inline">API Settings</span>
            </TabsTrigger>
          </TabsList>

          {/* WPVR Integration */}
          <TabsContent value="wpvr" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="w-5 h-5" />
                    WPVR Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="wpvr-url">WordPress Site URL</Label>
                    <Input id="wpvr-url" placeholder="https://yoursite.com" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="wpvr-username">Username</Label>
                    <Input id="wpvr-username" placeholder="WordPress username" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="wpvr-password">App Password</Label>
                    <div className="relative">
                      <Input 
                        id="wpvr-password" 
                        type={showApiKeys ? "text" : "password"}
                        placeholder="WordPress app password" 
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowApiKeys(!showApiKeys)}
                      >
                        {showApiKeys ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="wpvr-auto-sync" />
                    <Label htmlFor="wpvr-auto-sync">Auto-sync tours</Label>
                  </div>
                  <div className="flex gap-2">
                    <Button className="flex-1">Test Connection</Button>
                    <Button variant="outline" className="flex-1">
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Sync Now
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>WPVR Tours</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Total Tours</span>
                      <span className="font-medium">12</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Last Import</span>
                      <span className="font-medium">2 hours ago</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Status</span>
                      {getStatusBadge('connected')}
                    </div>
                    <Button variant="outline" className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      Import Tours
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* WooCommerce Integration */}
          <TabsContent value="woocommerce" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <ShoppingCart className="w-5 h-5" />
                    WooCommerce Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="woo-url">Store URL</Label>
                    <Input id="woo-url" placeholder="https://yourstore.com" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="woo-key">Consumer Key</Label>
                    <div className="relative">
                      <Input 
                        id="woo-key" 
                        type={showApiKeys ? "text" : "password"}
                        placeholder="ck_xxxxxxxxxxxxxxxx" 
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowApiKeys(!showApiKeys)}
                      >
                        {showApiKeys ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="woo-secret">Consumer Secret</Label>
                    <div className="relative">
                      <Input 
                        id="woo-secret" 
                        type={showApiKeys ? "text" : "password"}
                        placeholder="cs_xxxxxxxxxxxxxxxx" 
                      />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="woo-auto-sync" />
                    <Label htmlFor="woo-auto-sync">Auto-sync products</Label>
                  </div>
                  <div className="flex gap-2">
                    <Button className="flex-1">Test Connection</Button>
                    <Button variant="outline" className="flex-1">
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Sync Now
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>WooCommerce Products</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Total Products</span>
                      <span className="font-medium">45</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Linked to Tours</span>
                      <span className="font-medium">23</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Last Sync</span>
                      <span className="font-medium">1 hour ago</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Status</span>
                      {getStatusBadge('connected')}
                    </div>
                    <Button variant="outline" className="w-full">
                      <Download className="w-4 h-4 mr-2" />
                      Import Products
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* CloudPano Integration */}
          <TabsContent value="cloudpano" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Cloud className="w-5 h-5" />
                    CloudPano Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="cloudpano-api">API Key</Label>
                    <div className="relative">
                      <Input 
                        id="cloudpano-api" 
                        type={showApiKeys ? "text" : "password"}
                        placeholder="Enter CloudPano API key" 
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowApiKeys(!showApiKeys)}
                      >
                        {showApiKeys ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cloudpano-webhook">Webhook URL</Label>
                    <Input id="cloudpano-webhook" placeholder="https://virtualrealtour.ng/webhook/cloudpano" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="cloudpano-auto-import" />
                    <Label htmlFor="cloudpano-auto-import">Auto-import tours</Label>
                  </div>
                  <div className="flex gap-2">
                    <Button className="flex-1">Connect CloudPano</Button>
                    <Button variant="outline" className="flex-1">
                      <Link className="w-4 h-4 mr-2" />
                      Setup Guide
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>CloudPano Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center py-8">
                    <Cloud className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">Not Connected</h3>
                    <p className="text-muted-foreground mb-4">
                      Connect your CloudPano account to import and manage tours
                    </p>
                    <Button>
                      <Link className="w-4 h-4 mr-2" />
                      Connect Now
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* API Settings */}
          <TabsContent value="api" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Key className="w-5 h-5" />
                    API Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="api-key">VirtualRealTour API Key</Label>
                    <div className="relative">
                      <Input 
                        id="api-key" 
                        type={showApiKeys ? "text" : "password"}
                        value="vrt_xxxxxxxxxxxxxxxxxxxxxxxx"
                        readOnly
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-0 top-0 h-full px-3"
                        onClick={() => setShowApiKeys(!showApiKeys)}
                      >
                        {showApiKeys ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="webhook-secret">Webhook Secret</Label>
                    <div className="relative">
                      <Input 
                        id="webhook-secret" 
                        type={showApiKeys ? "text" : "password"}
                        value="whsec_xxxxxxxxxxxxxxxxxxxxxxxx"
                        readOnly
                      />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="api-enabled" defaultChecked />
                    <Label htmlFor="api-enabled">API Access Enabled</Label>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" className="flex-1">
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Regenerate Key
                    </Button>
                    <Button variant="outline" className="flex-1">
                      <Download className="w-4 h-4 mr-2" />
                      API Docs
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>API Usage</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Requests Today</span>
                      <span className="font-medium">1,247</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Rate Limit</span>
                      <span className="font-medium">1000/hour</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Last Request</span>
                      <span className="font-medium">2 minutes ago</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground">Status</span>
                      {getStatusBadge('connected')}
                    </div>
                    <Button variant="outline" className="w-full">
                      <Eye className="w-4 h-4 mr-2" />
                      View Logs
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminIntegrations;
