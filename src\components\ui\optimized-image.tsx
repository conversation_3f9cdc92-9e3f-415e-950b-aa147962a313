import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { FormatSupport, imageUrlBuilder } from '@/utils/imageOptimization';

interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  className?: string;
  fallbackSrc?: string;
  blurDataURL?: string;
  priority?: boolean;
  quality?: number;
  sizes?: string;
}

const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src,
  alt,
  className,
  fallbackSrc,
  blurDataURL,
  priority = false,
  quality = 85,
  sizes,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(priority);
  const [optimizedSrc, setOptimizedSrc] = useState<string>('');
  const imgRef = useRef<HTMLImageElement>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isInView) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      {
        rootMargin: '50px',
        threshold: 0.1,
      }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, [priority, isInView]);

  // Generate optimized source when in view
  useEffect(() => {
    if (!isInView) return;

    const generateOptimizedSrc = async () => {
      const optimized = await getOptimizedSrc(src);
      setOptimizedSrc(optimized);
    };

    generateOptimizedSrc();
  }, [isInView, src, quality, sizes]);

  // Optimize image URL based on device capabilities and format support
  const getOptimizedSrc = async (originalSrc: string) => {
    try {
      // Use our advanced image optimization pipeline
      const supportsWebP = await FormatSupport.supportsWebP();
      const supportsAVIF = await FormatSupport.supportsAVIF();

      let format: 'avif' | 'webp' | 'jpeg' = 'jpeg';
      if (supportsAVIF) {
        format = 'avif';
      } else if (supportsWebP) {
        format = 'webp';
      }

      // Calculate optimal width from sizes
      let width: number | undefined;
      if (sizes) {
        width = sizes.includes('100vw') ? 1920 : 800;
      }

      return imageUrlBuilder.buildUrl(originalSrc, {
        width,
        quality,
        format: 'auto' // Let the builder choose the best format
      });
    } catch {
      return originalSrc;
    }
  };

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = () => {
    setHasError(true);
    if (fallbackSrc) {
      setIsLoaded(true);
    }
  };

  // Generate blur placeholder
  const blurPlaceholder = blurDataURL || 
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8ZGVmcz4KICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZGllbnQiIHgxPSIwJSIgeTE9IjAlIiB4Mj0iMTAwJSIgeTI9IjEwMCUiPgogICAgICA8c3RvcCBvZmZzZXQ9IjAlIiBzdHlsZT0ic3RvcC1jb2xvcjojZjNmNGY2O3N0b3Atb3BhY2l0eToxIiAvPgogICAgICA8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNlNWU3ZWI7c3RvcC1vcGFjaXR5OjEiIC8+CiAgICA8L2xpbmVhckdyYWRpZW50PgogIDwvZGVmcz4KICA8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgZmlsbD0idXJsKCNncmFkaWVudCkiIC8+Cjwvc3ZnPg==";

  return (
    <div 
      ref={imgRef}
      className={cn(
        "relative overflow-hidden bg-gray-100",
        className
      )}
    >
      {/* Blur placeholder */}
      {!isLoaded && (
        <img
          src={blurPlaceholder}
          alt=""
          className="absolute inset-0 w-full h-full object-cover filter blur-sm scale-110"
          aria-hidden="true"
        />
      )}
      
      {/* Loading skeleton */}
      {!isLoaded && !hasError && (
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-pulse" />
      )}
      
      {/* Main image with progressive enhancement */}
      {(isInView || priority) && optimizedSrc && (
        <picture>
          {/* AVIF source for modern browsers */}
          <source
            srcSet={`
              ${imageUrlBuilder.buildUrl(src, { format: 'avif', quality })} 1x,
              ${imageUrlBuilder.buildUrl(src, { format: 'avif', quality, width: sizes?.includes('100vw') ? 1920 : 800 })} 2x
            `}
            type="image/avif"
          />

          {/* WebP source for broader support */}
          <source
            srcSet={`
              ${imageUrlBuilder.buildUrl(src, { format: 'webp', quality })} 1x,
              ${imageUrlBuilder.buildUrl(src, { format: 'webp', quality, width: sizes?.includes('100vw') ? 1920 : 800 })} 2x
            `}
            type="image/webp"
          />

          {/* Fallback image */}
          <img
            src={hasError && fallbackSrc ? fallbackSrc : optimizedSrc}
            alt={alt}
            onLoad={handleLoad}
            onError={handleError}
            loading={priority ? 'eager' : 'lazy'}
            decoding="async"
            sizes={sizes}
            className={cn(
              "w-full h-full object-cover transition-opacity duration-300",
              isLoaded ? "opacity-100" : "opacity-0"
            )}
            {...props}
          />
        </picture>
      )}
      
      {/* Error state */}
      {hasError && !fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-400">
          <div className="text-center">
            <svg className="w-8 h-8 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
            </svg>
            <p className="text-xs">Image not available</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default OptimizedImage;
