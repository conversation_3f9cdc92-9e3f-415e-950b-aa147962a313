import React, { useEffect, useRef } from 'react';
import './TourEmbed.css';
import ProductHotspot, { ProductHotspotProps } from './ProductHotspot';

export interface TourEmbedProps {
  tourUrl: string; // Your custom slug/URL, never the source
  hotspots?: ProductHotspotProps[];
  width?: string | number;
  height?: string | number;
  className?: string;
  style?: React.CSSProperties;
  iframeTitle?: string;
}

const TourEmbed: React.FC<TourEmbedProps> = ({
  tourUrl,
  hotspots = [],
  width = '100%',
  height = '600px',
  className = '',
  style = {},
  iframeTitle = 'Virtual Tour',
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      if (width) containerRef.current.style.width = width;
      if (height) containerRef.current.style.height = height;

      // Apply additional styles if provided
      if (style) {
        Object.assign(containerRef.current.style, style);
      }
    }
  }, [width, height, style]);

  return (
    <div
      ref={containerRef}
      className={`tour-embed relative ${className}`}
      data-width={width}
      data-height={height}
    >
      <iframe
        src={tourUrl}
        title={iframeTitle}
        width="100%"
        height="100%"
        frameBorder={0}
        allow="fullscreen; accelerometer; gyroscope; magnetometer; vr; xr; xr-spatial-tracking; autoplay; camera; microphone"
        allowFullScreen
        className="tour-embed-iframe"
        sandbox="allow-scripts allow-same-origin allow-popups allow-forms"
      />
      {hotspots.map((hotspot, idx) => (
        <ProductHotspot key={hotspot.product?.id || idx} {...hotspot} />
      ))}
    </div>
  );
};

export default TourEmbed;
