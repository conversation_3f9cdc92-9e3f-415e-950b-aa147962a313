/**
 * TourWrapper Component - Secure Tour Embedding
 * Prevents URL leakage and provides branded embedding with overlay system
 * Enhances existing tour display without breaking current functionality
 */

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import {
  Maximize,
  Share2,
  Eye,
  EyeOff,
  Volume2,
  VolumeX,
  RotateCcw,
  Grid,
  Loader2,
  AlertCircle,
  Menu,
  X
} from 'lucide-react';
import { useResponsiveDesign } from '@/hooks/useResponsiveDesign';

interface TourWrapperProps {
  tourId: string;
  src: string;
  title?: string;
  overlayId?: string;
  hotspotTheme?: 'glass' | 'dark' | 'light' | 'minimal';
  productSync?: boolean;
  showControls?: boolean;
  allowFullscreen?: boolean;
  className?: string;
}

const TourWrapper = ({
  tourId,
  src,
  title = "Virtual Tour",
  overlayId,
  hotspotTheme = 'glass',
  productSync = false,
  showControls = true,
  allowFullscreen = true,
  className = ""
}: TourWrapperProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showOverlays, setShowOverlays] = useState(true);
  const [isMuted, setIsMuted] = useState(false);
  const [showMobileControls, setShowMobileControls] = useState(false);

  // Enhanced responsive design
  const {
    isMobile,
    isTablet,
    isTouch,
    getTouchTargetSize,
    getHoverClasses,
    getContainerClasses
  } = useResponsiveDesign();

  // Generate secure tour URL (prevents leakage)
  const secureUrl = `/tours/${tourId}`;
  const embedUrl = src.includes('virtualrealtour.ng') ? src : secureUrl;

  useEffect(() => {
    // Simulate loading time
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const handleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: title,
        url: secureUrl
      });
    } else {
      navigator.clipboard.writeText(window.location.origin + secureUrl);
    }
  };

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);
  };

  if (hasError) {
    return (
      <Card className={`relative bg-gray-100 flex items-center justify-center min-h-[400px] ${className}`}>
        <div className="text-center space-y-4">
          <AlertCircle className="w-12 h-12 text-gray-400 mx-auto" />
          <div>
            <h3 className="font-semibold text-gray-700">Tour Unavailable</h3>
            <p className="text-sm text-gray-500">Unable to load the virtual tour</p>
          </div>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className={`relative bg-black rounded-lg overflow-hidden ${className}`}>
      {/* Loading State */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-900 flex items-center justify-center z-50">
          <div className="text-center space-y-4">
            <Loader2 className="w-8 h-8 text-white animate-spin mx-auto" />
            <div className="text-white">
              <p className="font-medium">Loading Virtual Tour</p>
              <p className="text-sm text-gray-300">Please wait...</p>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Mobile-Responsive Controls */}
      {showControls && !isLoading && (
        <>
          {/* Desktop/Tablet Controls */}
          {!isMobile && (
            <div className="absolute top-4 left-4 right-4 z-40 flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="bg-black/50 text-white border-white/20">
                  {title}
                </Badge>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className={`text-white bg-black/30 ${getHoverClasses('hover:bg-white/20')}`}
                  onClick={() => setShowOverlays(!showOverlays)}
                >
                  {showOverlays ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  className={`text-white bg-black/30 ${getHoverClasses('hover:bg-white/20')}`}
                  onClick={() => setIsMuted(!isMuted)}
                >
                  {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  className={`text-white bg-black/30 ${getHoverClasses('hover:bg-white/20')}`}
                  onClick={handleShare}
                >
                  <Share2 className="w-4 h-4" />
                </Button>

                {allowFullscreen && (
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`text-white bg-black/30 ${getHoverClasses('hover:bg-white/20')}`}
                    onClick={handleFullscreen}
                  >
                    <Maximize className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          )}

          {/* Mobile Controls */}
          {isMobile && (
            <>
              {/* Mobile Header */}
              <div className="absolute top-2 left-2 right-2 z-40 flex items-center justify-between">
                <Badge variant="secondary" className="bg-black/70 text-white border-white/20 text-xs px-2 py-1">
                  {title}
                </Badge>

                <Button
                  variant="ghost"
                  size="sm"
                  className={`text-white bg-black/70 p-2 ${getTouchTargetSize()}`}
                  onClick={() => setShowMobileControls(!showMobileControls)}
                >
                  {showMobileControls ? <X className="w-4 h-4" /> : <Menu className="w-4 h-4" />}
                </Button>
              </div>

              {/* Mobile Control Panel */}
              {showMobileControls && (
                <div className="absolute bottom-4 left-2 right-2 z-40 bg-black/80 backdrop-blur-md rounded-lg p-3">
                  <div className="grid grid-cols-4 gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className={`text-white flex flex-col items-center gap-1 ${getTouchTargetSize()}`}
                      onClick={() => setShowOverlays(!showOverlays)}
                    >
                      {showOverlays ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                      <span className="text-xs">Overlays</span>
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      className={`text-white flex flex-col items-center gap-1 ${getTouchTargetSize()}`}
                      onClick={() => setIsMuted(!isMuted)}
                    >
                      {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                      <span className="text-xs">Audio</span>
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      className={`text-white flex flex-col items-center gap-1 ${getTouchTargetSize()}`}
                      onClick={handleShare}
                    >
                      <Share2 className="w-4 h-4" />
                      <span className="text-xs">Share</span>
                    </Button>

                    {allowFullscreen && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className={`text-white flex flex-col items-center gap-1 ${getTouchTargetSize()}`}
                        onClick={handleFullscreen}
                      >
                        <Maximize className="w-4 h-4" />
                        <span className="text-xs">Full</span>
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </>
          )}
        </>
      )}

      {/* Enhanced Responsive Tour Iframe */}
      <iframe
        src={embedUrl}
        className={`
          w-full h-full border-0 touch-manipulation
          ${isMobile ? 'min-h-[300px]' : 'min-h-[400px]'}
          ${getContainerClasses()}
        `}
        allowFullScreen={allowFullscreen}
        onLoad={() => setIsLoading(false)}
        onError={handleError}
        sandbox="allow-scripts allow-same-origin allow-presentation allow-pointer-lock"
        title={title}
        style={{
          // Optimize for mobile performance
          willChange: 'transform',
          backfaceVisibility: 'hidden'
        }}
      />

      {/* Enhanced Mobile-Responsive Overlay System */}
      {showOverlays && overlayId && (
        <div className="absolute inset-0 pointer-events-none z-30">
          {/* Mobile-Optimized Sample Hotspots */}
          <div className="absolute top-1/3 left-1/4 pointer-events-auto">
            <div className={`
              ${isMobile ? 'w-10 h-10' : 'w-8 h-8'}
              rounded-full flex items-center justify-center cursor-pointer
              transition-all duration-300
              ${getHoverClasses('hover:scale-110')}
              ${isTouch ? 'active:scale-95' : ''}
              ${getTouchTargetSize()}
              ${hotspotTheme === 'glass' ? 'bg-white/20 backdrop-blur-md border border-white/30' :
                hotspotTheme === 'dark' ? 'bg-gray-900/90 text-white' :
                hotspotTheme === 'light' ? 'bg-white shadow-lg border' :
                'bg-gray-50 border-l-4 border-blue-500'}
              animate-pulse
            `}>
              <div className={`${isMobile ? 'w-4 h-4' : 'w-3 h-3'} bg-green-500 rounded-full`}></div>
            </div>
          </div>

          <div className="absolute top-2/3 right-1/3 pointer-events-auto">
            <div className={`
              ${isMobile ? 'w-10 h-10' : 'w-8 h-8'}
              rounded-full flex items-center justify-center cursor-pointer
              transition-all duration-300
              ${getHoverClasses('hover:scale-110')}
              ${isTouch ? 'active:scale-95' : ''}
              ${getTouchTargetSize()}
              ${hotspotTheme === 'glass' ? 'bg-white/20 backdrop-blur-md border border-white/30' :
                hotspotTheme === 'dark' ? 'bg-gray-900/90 text-white' :
                hotspotTheme === 'light' ? 'bg-white shadow-lg border' :
                'bg-gray-50 border-l-4 border-blue-500'}
            `}>
              <div className={`${isMobile ? 'w-4 h-4' : 'w-3 h-3'} bg-blue-500 rounded-full`}></div>
            </div>
          </div>

          {/* Mobile-specific overlay hints */}
          {isMobile && (
            <div className="absolute bottom-16 left-1/2 transform -translate-x-1/2 pointer-events-auto">
              <div className="bg-black/70 text-white text-xs px-3 py-2 rounded-full backdrop-blur-sm">
                Tap hotspots to explore
              </div>
            </div>
          )}
        </div>
      )}

      {/* Branding Watermark */}
      <div className="absolute bottom-4 right-4 z-40">
        <Badge variant="secondary" className="bg-black/50 text-white border-white/20 text-xs">
          VirtualRealTour.ng
        </Badge>
      </div>
    </div>
  );
};

export default TourWrapper;
