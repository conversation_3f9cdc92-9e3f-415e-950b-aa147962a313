
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Headphones, Building, MessageCircle } from 'lucide-react';

const SupportOptions = () => {
  const supportCards = [
    {
      icon: Headphones,
      title: "Technical Support",
      description: "Get help with uploading tours, troubleshooting, and technical issues",
      action: "Get Technical Help"
    },
    {
      icon: Building,
      title: "Business Inquiries",
      description: "Partnership opportunities, enterprise solutions, and custom development",
      action: "Contact Sales"
    },
    {
      icon: MessageCircle,
      title: "Live Chat",
      description: "Chat with our support team in real-time for immediate assistance",
      action: "Start Live Chat"
    }
  ];

  return (
    <div>
      <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Support</h3>
      <div className="space-y-4">
        {supportCards.map((card, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start space-x-3">
                <card.icon className="w-6 h-6 text-blue-600 mt-1 flex-shrink-0" />
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900 mb-1">{card.title}</h4>
                  <p className="text-sm text-gray-600 mb-3">{card.description}</p>
                  <Button variant="outline" size="sm" className="w-full">
                    {card.action}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default SupportOptions;
