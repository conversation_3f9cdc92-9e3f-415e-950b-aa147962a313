import { useState, useRef, Suspense } from 'react';
import { Canvas, useFrame, useThree } from '@react-three/fiber';
import { OrbitControls, Html } from '@react-three/drei';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Share2, Info, MapPin, Volume2, VolumeX, ChevronLeft, ChevronRight } from 'lucide-react';
import * as THREE from 'three';
import { Tour, Scene as SceneType, Hotspot } from '@/lib/supabase';
import { toast } from 'sonner';
import TourWrapper from '@/components/tours/TourWrapper';
import { useTourEmbedding } from '@/hooks/useTourEmbedding';

interface TourViewerProps {
  tour: Tour;
  scenes: SceneType[];
  onBack: () => void;
}

const TourViewer = ({ tour, scenes = [], onBack }: TourViewerProps) => {
  const [currentSceneIndex, setCurrentSceneIndex] = useState(0);
  const [isAudioEnabled, setIsAudioEnabled] = useState(false);
  const [showInfo, setShowInfo] = useState(false);

  const currentScene = scenes[currentSceneIndex];

  // Enhanced tour embedding with security
  const { tourData, generateSecureUrl } = useTourEmbedding(tour.id);
  const isExternalTour = tour.embed_url && tour.embed_type;

  // Determine overlay theme based on tour settings
  const overlayTheme = tourData?.overlay_config?.theme || 'glass';

  const navigateToScene = (index: number) => {
    if (index >= 0 && index < scenes.length) {
      setCurrentSceneIndex(index);
    }
  };

  const handleShare = async () => {
    // Always use our platform URL, never the external URL
    const platformUrl = `${window.location.origin}/tour/${tour.slug || tour.id}`;
    try {
      if (navigator.share) {
        await navigator.share({
          title: tour.title,
          text: tour.description || `Check out this virtual tour: ${tour.title}`,
          url: platformUrl,
        });
      } else {
        navigator.clipboard.writeText(platformUrl);
        toast.success('Tour link copied to clipboard!');
      }
    } catch (error) {
      console.error('Error sharing:', error);
      navigator.clipboard.writeText(platformUrl);
      toast.success('Tour link copied to clipboard!');
    }
  };

  // If this is an external tour, render it in an iframe
  if (isExternalTour && tour.embed_type === 'iframe') {
    return (
      <div className="min-h-screen bg-black relative">
        {/* Header with our branding */}
        <div className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/70 to-transparent p-4">
          <div className="flex items-center justify-between text-white">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={onBack}
                className="text-white hover:bg-white/20"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
              <div>
                <h1 className="text-lg font-semibold">{tour.title}</h1>
                <p className="text-sm text-gray-300">{tour.location}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowInfo(!showInfo)}
                className="text-white hover:bg-white/20"
              >
                <Info className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleShare}
                className="text-white hover:bg-white/20"
              >
                <Share2 className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Info panel */}
        {showInfo && (
          <div className="absolute top-24 right-4 z-20 w-72">
            <Card className="bg-black/70 border-gray-700 text-white">
              <CardContent className="p-4 space-y-3">
                <div>
                  <h3 className="font-semibold text-lg">{tour.title}</h3>
                  {tour.description && <p className="text-sm text-gray-300 mt-1">{tour.description}</p>}
                </div>
                
                {tour.location && (
                  <div className="flex items-center text-sm">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span>{tour.location}</span>
                  </div>
                )}
                
                <div className="flex items-center gap-2">
                  <Badge className="bg-blue-500/70">{tour.category}</Badge>
                  <Badge className="bg-gray-500/70">Interactive Tour</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Enhanced Secure Tour Wrapper */}
        <TourWrapper
          tourId={tour.id}
          src={tour.embed_url || ''}
          title={tour.title}
          overlayId={tourData?.overlay_config ? 'overlay-' + tour.id : undefined}
          hotspotTheme={overlayTheme}
          productSync={true}
          showControls={true}
          allowFullscreen={true}
          className="w-full h-full mt-16 tour-viewer-wrapper"
        />
      </div>
    );
  }

  // For external link tours, redirect but keep our branding
  if (isExternalTour && tour.embed_type === 'link') {
    return (
      <div className="min-h-screen bg-black relative flex items-center justify-center">
        <Card className="max-w-md w-full mx-4">
          <CardContent className="p-8 text-center">
            <h1 className="text-xl font-semibold text-gray-900 mb-4">{tour.title}</h1>
            {tour.description && (
              <p className="text-gray-600 mb-6">{tour.description}</p>
            )}
            <div className="space-y-4">
              <Button
                onClick={onBack}
                className="w-full"
                size="lg"
              >
                View Tour on Our Platform
              </Button>
              <Button 
                variant="outline" 
                onClick={onBack}
                className="w-full"
              >
                Back to Tours
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Panorama component that renders the 360 environment
  const Panorama = ({ imageUrl }: { imageUrl: string }) => {
    const { scene } = useThree();
    const texture = useRef<THREE.Texture>();
    const material = useRef<THREE.MeshBasicMaterial>();
    
    // Create a sphere geometry
    const geometry = new THREE.SphereGeometry(500, 60, 40);
    // Flip the geometry inside out
    geometry.scale(-1, 1, 1);
    
    // Load texture
    texture.current = new THREE.TextureLoader().load(imageUrl);
    
    // Create material
    material.current = new THREE.MeshBasicMaterial({
      map: texture.current,
      side: THREE.BackSide // Render the material on the inside of the sphere
    });

    return (
      <mesh geometry={geometry} material={material.current} />
    );
  };

  // Hotspot component
  const Hotspot = ({ hotspot }: { hotspot: Hotspot }) => {
    const { camera } = useThree();
    const ref = useRef<THREE.Group>(null);
    
    // Billboard effect - always face the camera
    useFrame(() => {
      if (ref.current) {
        ref.current.quaternion.copy(camera.quaternion);
      }
    });

    const handleHotspotClick = () => {
      if (hotspot.type === 'navigation' && hotspot.target_scene_id) {
        // Find the scene index by target_scene_id
        const targetIndex = scenes.findIndex(scene => scene.id === hotspot.target_scene_id);
        if (targetIndex !== -1) {
          navigateToScene(targetIndex);
        }
      } else if (hotspot.type === 'info' && hotspot.content) {
        toast.info(hotspot.content);
      } else if (hotspot.type === 'whatsapp' && hotspot.whatsapp_phone) {
        const message = encodeURIComponent(hotspot.whatsapp_message || `Inquiry about ${tour.title}`);
        window.open(`https://wa.me/${hotspot.whatsapp_phone}?text=${message}`, '_blank');
      } else if (hotspot.type === 'link' && hotspot.link_url) {
        window.open(hotspot.link_url, '_blank');
      }
    };

    return (
      <group 
        ref={ref} 
        position={[hotspot.position_x, hotspot.position_y, hotspot.position_z]}
        onClick={handleHotspotClick}
      >
        <Html center>
          <div
            className={`
              w-12 h-12 rounded-full flex items-center justify-center cursor-pointer transition-transform duration-300 hover:scale-110
              ${hotspot.type === 'navigation' ? 'bg-blue-500' : ''}
              ${hotspot.type === 'info' ? 'bg-yellow-500' : ''}
              ${hotspot.type === 'whatsapp' ? 'bg-green-500' : ''}
              ${hotspot.type === 'link' ? 'bg-purple-500' : ''}
            `}
          >
            {hotspot.label && (
              <span className="absolute top-14 bg-black/70 text-white px-2 py-1 rounded whitespace-nowrap">
                {hotspot.label}
              </span>
            )}
          </div>
        </Html>
      </group>
    );
  };

  return (
    <div className="min-h-screen bg-black relative">
      <div className="absolute top-0 left-0 right-0 z-20 bg-gradient-to-b from-black/70 to-transparent p-4">
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="text-white hover:bg-white/20"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-lg font-semibold">{tour.title}</h1>
              <p className="text-sm text-gray-300">{currentScene?.name}</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsAudioEnabled(!isAudioEnabled)}
              className="text-white hover:bg-white/20"
            >
              {isAudioEnabled ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowInfo(!showInfo)}
              className="text-white hover:bg-white/20"
            >
              <Info className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleShare}
              className="text-white hover:bg-white/20"
            >
              <Share2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
      
      {/* Scene navigation (if multiple scenes) */}
      {scenes.length > 1 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20">
          <Card className="bg-black/70 border-gray-700">
            <CardContent className="p-2 flex items-center gap-2">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigateToScene(currentSceneIndex - 1)}
                disabled={currentSceneIndex === 0}
                className="text-white h-8 w-8 p-0 rounded-full"
              >
                <ChevronLeft className="w-5 h-5" />
              </Button>
              
              <div className="flex items-center gap-2 px-2 overflow-x-auto scrollbar-hide">
                {scenes.map((scene, index) => (
                  <Button
                    key={scene.id}
                    variant={index === currentSceneIndex ? "default" : "ghost"}
                    size="sm"
                    onClick={() => navigateToScene(index)}
                    className={index === currentSceneIndex 
                      ? "bg-blue-600 hover:bg-blue-700 text-white h-8 min-w-8 px-3" 
                      : "text-white h-8 min-w-8 px-3"
                    }
                  >
                    {index + 1}
                  </Button>
                ))}
              </div>
              
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigateToScene(currentSceneIndex + 1)}
                disabled={currentSceneIndex === scenes.length - 1}
                className="text-white h-8 w-8 p-0 rounded-full"
              >
                <ChevronRight className="w-5 h-5" />
              </Button>
            </CardContent>
          </Card>
        </div>
      )}
      
      {/* Info panel */}
      {showInfo && (
        <div className="absolute top-24 right-4 z-20 w-72">
          <Card className="bg-black/70 border-gray-700 text-white">
            <CardContent className="p-4 space-y-3">
              <div>
                <h3 className="font-semibold text-lg">{tour.title}</h3>
                {tour.description && <p className="text-sm text-gray-300 mt-1">{tour.description}</p>}
              </div>
              
              {tour.location && (
                <div className="flex items-center text-sm">
                  <MapPin className="w-4 h-4 mr-1" />
                  <span>{tour.location}</span>
                </div>
              )}
              
              <div className="flex items-center gap-2">
                <Badge className="bg-blue-500/70">{tour.category}</Badge>
                <Badge className="bg-gray-500/70">{scenes.length} scenes</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      
      {/* Main 3D Canvas */}
      <Canvas style={{ width: '100%', height: '100vh' }}>
        <Suspense fallback={null}>
          <Panorama imageUrl={currentScene?.image_url || ''} />
          {currentScene?.hotspots?.map((hotspot) => (
            <Hotspot key={hotspot.id} hotspot={hotspot} />
          ))}
          <OrbitControls 
            enableZoom={false}
            enablePan={false}
            rotateSpeed={0.5}
            autoRotate={false}
          />
        </Suspense>
      </Canvas>
    </div>
  );
};

export default TourViewer;
