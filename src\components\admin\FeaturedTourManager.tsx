
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import AddTourForm from './featured/AddTourForm';
import FeaturedSectionCard from './featured/FeaturedSectionCard';

interface FeaturedTourAssignment {
  id: string;
  tour_id: string;
  section_type: string;
  display_order: number;
  is_active: boolean;
  tours: {
    title: string;
    thumbnail_url: string;
    category: string;
  };
}

const FeaturedTourManager = () => {
  const { user } = useAuth();

  // Fetch all published tours
  const { data: availableTours = [] } = useQuery({
    queryKey: ['admin-tours'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tours')
        .select('id, title, thumbnail_url, category')
        .eq('status', 'published')
        .order('title');
      
      if (error) throw error;
      return data;
    },
    enabled: !!user,
  });

  // Fetch featured tour assignments
  const { data: assignments = [] } = useQuery({
    queryKey: ['featured-assignments'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('featured_tour_assignments')
        .select(`
          *,
          tours (
            title,
            thumbnail_url,
            category
          )
        `)
        .order('section_type', { ascending: true })
        .order('display_order', { ascending: true });
      
      if (error) throw error;
      return data as FeaturedTourAssignment[];
    },
    enabled: !!user,
  });

  const groupedAssignments = assignments.reduce((acc, assignment) => {
    if (!acc[assignment.section_type]) {
      acc[assignment.section_type] = [];
    }
    acc[assignment.section_type].push(assignment);
    return acc;
  }, {} as Record<string, FeaturedTourAssignment[]>);

  return (
    <div className="space-y-6">
      <AddTourForm availableTours={availableTours} />

      {Object.entries(groupedAssignments).map(([sectionType, sectionAssignments]) => (
        <FeaturedSectionCard 
          key={sectionType} 
          sectionType={sectionType} 
          assignments={sectionAssignments} 
        />
      ))}
    </div>
  );
};

export default FeaturedTourManager;
