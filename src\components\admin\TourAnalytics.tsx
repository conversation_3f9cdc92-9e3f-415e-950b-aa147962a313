/**
 * Tour Analytics Component
 * Display analytics and insights for tours
 */

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  Eye, 
  MapPin, 
  ShoppingCart,
  Users,
  Calendar,
  Globe
} from 'lucide-react';

interface Tour {
  id: string;
  title: string;
  category: string;
  location: string;
  status: 'draft' | 'published';
  created_at: string;
  views_count: number;
  commerce_enabled: boolean;
  vendor_id?: string;
}

interface TourAnalyticsProps {
  tours: Tour[];
}

const TourAnalytics = ({ tours }: TourAnalyticsProps) => {
  // Calculate analytics
  const totalViews = tours.reduce((sum, tour) => sum + (tour.views_count || 0), 0);
  const avgViewsPerTour = tours.length > 0 ? Math.round(totalViews / tours.length) : 0;
  
  const categoryStats = tours.reduce((acc, tour) => {
    acc[tour.category] = (acc[tour.category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const topCategories = Object.entries(categoryStats)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);

  const topTours = tours
    .filter(tour => tour.views_count > 0)
    .sort((a, b) => (b.views_count || 0) - (a.views_count || 0))
    .slice(0, 10);

  const recentTours = tours
    .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
    .slice(0, 5);

  const commerceStats = {
    enabled: tours.filter(t => t.commerce_enabled).length,
    withVendors: tours.filter(t => t.vendor_id).length,
    tourOnly: tours.filter(t => !t.commerce_enabled).length
  };

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Views</p>
                <p className="text-2xl font-bold">{totalViews.toLocaleString()}</p>
              </div>
              <Eye className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Views/Tour</p>
                <p className="text-2xl font-bold">{avgViewsPerTour}</p>
              </div>
              <BarChart3 className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Commerce Tours</p>
                <p className="text-2xl font-bold">{commerceStats.enabled}</p>
              </div>
              <ShoppingCart className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">With Vendors</p>
                <p className="text-2xl font-bold">{commerceStats.withVendors}</p>
              </div>
              <Users className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Tours */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Top Performing Tours
            </CardTitle>
            <CardDescription>Tours with the most views</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topTours.length > 0 ? (
                topTours.map((tour, index) => (
                  <div key={tour.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">{tour.title}</h4>
                        <p className="text-sm text-muted-foreground">{tour.location}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{tour.views_count?.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">views</p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-center text-muted-foreground py-4">No view data available</p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Category Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="w-5 h-5" />
              Category Distribution
            </CardTitle>
            <CardDescription>Tours by category</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topCategories.map(([category, count]) => (
                <div key={category} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-primary rounded-full"></div>
                    <span className="font-medium">{category}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary">{count}</Badge>
                    <span className="text-sm text-muted-foreground">
                      {Math.round((count / tours.length) * 100)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              Recent Tours
            </CardTitle>
            <CardDescription>Latest created tours</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentTours.map(tour => (
                <div key={tour.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{tour.title}</h4>
                    <p className="text-sm text-muted-foreground">{tour.location}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={tour.status === 'published' ? 'default' : 'secondary'}>
                      {tour.status}
                    </Badge>
                    {tour.commerce_enabled && (
                      <Badge variant="outline">
                        <ShoppingCart className="w-3 h-3 mr-1" />
                        Commerce
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Commerce Analytics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <ShoppingCart className="w-5 h-5" />
              Commerce Analytics
            </CardTitle>
            <CardDescription>E-commerce integration stats</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-3 gap-4 text-center">
                <div className="p-3 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{commerceStats.enabled}</div>
                  <div className="text-sm text-muted-foreground">Commerce Enabled</div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{commerceStats.withVendors}</div>
                  <div className="text-sm text-muted-foreground">With Vendors</div>
                </div>
                <div className="p-3 border rounded-lg">
                  <div className="text-2xl font-bold text-gray-600">{commerceStats.tourOnly}</div>
                  <div className="text-sm text-muted-foreground">Tour Only</div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Commerce Adoption</span>
                  <span className="text-sm font-medium">
                    {tours.length > 0 ? Math.round((commerceStats.enabled / tours.length) * 100) : 0}%
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ 
                      width: `${tours.length > 0 ? (commerceStats.enabled / tours.length) * 100 : 0}%` 
                    }}
                  ></div>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm">Vendor Integration</span>
                  <span className="text-sm font-medium">
                    {tours.length > 0 ? Math.round((commerceStats.withVendors / tours.length) * 100) : 0}%
                  </span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ 
                      width: `${tours.length > 0 ? (commerceStats.withVendors / tours.length) * 100 : 0}%` 
                    }}
                  ></div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TourAnalytics;
