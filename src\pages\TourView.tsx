
import { useParams, Navigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase, Tour, Scene } from '@/lib/supabase';
import TourViewer from '@/components/TourViewer';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle } from 'lucide-react';

const TourView = () => {
  const { slug } = useParams();

  const { data: tour, isLoading, error } = useQuery({
    queryKey: ['tour', slug],
    queryFn: async () => {
      if (!slug) throw new Error('Tour slug is required');

      // Find tour by slug (clean slug without ID)
      const { data, error } = await supabase
        .from('tours')
        .select(`
          *,
          profiles (
            full_name,
            email
          )
        `)
        .eq('slug', slug)
        .eq('status', 'published')
        .single();

      if (error) {
        console.error('Error fetching tour:', error);
        throw error;
      }

      return data as Tour;
    },
    enabled: !!slug,
  });

  const { data: scenes = [] } = useQuery({
    queryKey: ['tour-scenes', tour?.id],
    queryFn: async () => {
      if (!tour?.id) return [];
      
      const { data, error } = await supabase
        .from('scenes')
        .select(`
          *,
          hotspots (*)
        `)
        .eq('tour_id', tour.id)
        .order('order_index');

      if (error) {
        console.error('Error fetching scenes:', error);
        throw error;
      }
      
      return data as Scene[];
    },
    enabled: !!tour?.id,
  });

  // Increment view count when tour loads
  useQuery({
    queryKey: ['increment-view', tour?.id],
    queryFn: async () => {
      if (!tour?.id) return null;
      
      // Increment view count
      const { error: viewError } = await supabase.rpc('increment_tour_views', {
        tour_uuid: tour.id
      });

      if (viewError) {
        console.error('Error incrementing views:', viewError);
      }

      // Log analytics
      const { error: analyticsError } = await supabase
        .from('tour_analytics')
        .insert({
          tour_id: tour.id,
          viewer_ip: 'unknown',
          viewer_location: 'Nigeria',
          time_spent_seconds: 0
        });

      if (analyticsError) {
        console.error('Error logging analytics:', analyticsError);
      }

      return null;
    },
    enabled: !!tour?.id,
  });

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <LoadingSpinner size="lg" className="border-white border-t-blue-600" />
      </div>
    );
  }

  if (error || !tour) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardContent className="p-8 text-center">
            <AlertCircle className="w-16 h-16 mx-auto mb-4 text-red-400" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Tour Not Found</h1>
            <p className="text-gray-600 mb-4">
              The tour you're looking for doesn't exist or isn't published yet.
            </p>
            <a href="/showcase" className="text-blue-600 hover:underline">
              Browse other tours
            </a>
          </CardContent>
        </Card>
      </div>
    );
  }

  // For external tours without scenes, still render them through our viewer
  if (tour.embed_url && (!scenes || scenes.length === 0)) {
    return (
      <TourViewer 
        tour={tour} 
        scenes={[]}
        onBack={() => window.history.back()} 
      />
    );
  }

  if (scenes.length === 0 && !tour.embed_url) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardContent className="p-8 text-center">
            <AlertCircle className="w-16 h-16 mx-auto mb-4 text-yellow-400" />
            <h1 className="text-xl font-semibold text-gray-900 mb-2">Tour Being Prepared</h1>
            <p className="text-gray-600 mb-4">
              This tour is still being set up. Please check back later.
            </p>
            <a href="/showcase" className="text-blue-600 hover:underline">
              Browse other tours
            </a>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <TourViewer 
      tour={tour} 
      scenes={scenes}
      onBack={() => window.history.back()} 
    />
  );
};

export default TourView;
