/**
 * User Tour Editor Page
 * Simplified tour editing interface for users
 */

import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft, 
  Save, 
  Eye, 
  Share2, 
  Settings, 
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';
import Navigation from '@/components/Navigation';
import CommonNinjaTourEditor from '@/components/tour-editor/CommonNinjaTourEditor';
import CommonNinjaWidgetEditor from '@/components/tour-editor/CommonNinjaWidgetEditor';
import CloudPanoTourEditor from '@/components/tour-editor/CloudPanoTourEditor';
import { supabase, Tour } from '@/lib/supabase';
import { useAuth } from '@/hooks/useAuth';
import { LoadingSpinner } from '@/components/ui/loading-spinner';

const UserTourEditor = () => {
  const { tourId } = useParams<{ tourId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isPublishing, setIsPublishing] = useState(false);

  // Fetch tour data
  const { data: tour, isLoading, error } = useQuery({
    queryKey: ['user-tour', tourId],
    queryFn: async () => {
      if (!tourId || !user?.id) return null;

      const { data, error } = await supabase
        .from('tours')
        .select('*')
        .eq('id', tourId)
        .eq('user_id', user.id) // Ensure user owns the tour
        .single();

      if (error) throw error;
      return data as Tour;
    },
    enabled: !!tourId && !!user?.id,
  });

  // Update tour mutation
  const updateTourMutation = useMutation({
    mutationFn: async (updates: Partial<Tour>) => {
      if (!tourId) throw new Error('No tour ID');

      // Ensure users always use CommonNinja platform
      const tourUpdates = {
        ...updates,
        tour_platform: 'commonninja'
      };

      const { data, error } = await supabase
        .from('tours')
        .update(tourUpdates)
        .eq('id', tourId)
        .eq('user_id', user?.id) // Ensure user owns the tour
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-tour', tourId] });
      queryClient.invalidateQueries({ queryKey: ['user-tours', user?.id] });
    },
  });

  const handleSave = (tourData: any) => {
    updateTourMutation.mutate({
      commonninja_embed_code: tourData.embedUrl,
      commonninja_widget_id: tourData.widgetId,
      updated_at: new Date().toISOString()
    });
    toast.success('Tour saved successfully!');
  };

  const handlePublish = async (tourData: any) => {
    setIsPublishing(true);
    try {
      // Update tour with publish request
      await updateTourMutation.mutateAsync({
        commonninja_embed_code: tourData.embedUrl,
        commonninja_widget_id: tourData.widgetId,
        status: 'pending', // Set to pending for admin approval
        updated_at: new Date().toISOString()
      });

      toast.success('Tour submitted for review! You will be notified once approved.');
      navigate('/dashboard');
    } catch (error) {
      toast.error('Failed to submit tour for review');
    } finally {
      setIsPublishing(false);
    }
  };

  const handlePreview = () => {
    if (tour?.slug) {
      window.open(`/tour/${tour.slug}?preview=true`, '_blank');
    } else {
      toast.info('Save your tour first to preview');
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'published':
        return <Badge className="bg-green-100 text-green-800 border-green-200"><CheckCircle className="w-3 h-3 mr-1" />Published</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200"><Clock className="w-3 h-3 mr-1" />Pending Approval</Badge>;
      case 'draft':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200"><Settings className="w-3 h-3 mr-1" />Draft</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto pt-32 py-8">
          <div className="flex items-center justify-center h-64">
            <LoadingSpinner size="lg" />
          </div>
        </div>
      </div>
    );
  }

  if (error || !tour) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto pt-32 py-8">
          <Card>
            <CardContent className="p-12 text-center">
              <AlertCircle className="w-16 h-16 mx-auto mb-4 text-red-500" />
              <h3 className="text-lg font-semibold mb-2">Tour Not Found</h3>
              <p className="text-muted-foreground mb-4">
                The tour you're looking for doesn't exist or you don't have permission to edit it.
              </p>
              <Button onClick={() => navigate('/dashboard')}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto pt-20 sm:pt-32 px-4 py-8">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => navigate('/dashboard')}
              className="shrink-0"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
            <div>
              <div className="flex items-center gap-3 mb-1">
                <h1 className="text-xl md:text-2xl font-bold">{tour.title}</h1>
                {getStatusBadge(tour.status)}
              </div>
              <p className="text-sm text-muted-foreground">{tour.location}</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={handlePreview}
              disabled={!tour.commonninja_embed_code}
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
            
            {tour.status === 'draft' && (
              <Button
                onClick={() => handlePublish({ 
                  embedUrl: tour.commonninja_embed_code,
                  widgetId: tour.commonninja_widget_id 
                })}
                disabled={isPublishing || !tour.commonninja_embed_code}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isPublishing ? (
                  <>
                    <div className="w-4 h-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2" />
                    Submitting...
                  </>
                ) : (
                  <>
                    <Share2 className="w-4 h-4 mr-2" />
                    Submit for Review
                  </>
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Tour Editor - Mobile Responsive */}
        <Card className="w-full overflow-hidden">
          <CardHeader className="pb-4 px-4 sm:px-6">
            <CardTitle className="text-lg">Tour Editor</CardTitle>
            <p className="text-sm text-muted-foreground">
              Create and customize your virtual tour with interactive hotspots and e-commerce features
            </p>
          </CardHeader>
          <Separator />
          <CardContent className="p-0">
            <div className="min-h-[400px] sm:min-h-[600px] w-full">
              {tour.tour_platform === 'cloudpano' ? (
                <CloudPanoTourEditor
                  tourId={tour.id}
                  tourData={{
                    title: tour.title,
                    description: tour.description || '',
                    category: tour.category,
                    location: tour.location || '',
                    business_name: tour.business_name || '',
                    business_phone: tour.business_phone || '',
                    business_email: tour.business_email || '',
                    business_whatsapp: tour.business_whatsapp || tour.business_phone || '',
                    business_address: tour.business_address || tour.location || '',
                    business_website: tour.business_website || ''
                  }}
                  onSave={handleSave}
                  onPublish={handlePublish}
                  onClose={() => navigate('/dashboard')}
                  isAdmin={false}
                />
              ) : (
                <CommonNinjaWidgetEditor
                  tourId={tour.id}
                  tourData={{
                    title: tour.title,
                    description: tour.description || '',
                    category: tour.category,
                    location: tour.location || '',
                    business_name: tour.business_name || '',
                    business_phone: tour.contact_phone || '',
                    business_email: tour.contact_email || '',
                    business_whatsapp: tour.contact_phone || '',
                    business_address: tour.location || '',
                    business_website: tour.website || ''
                  }}
                  onSave={handleSave}
                  onPublish={handlePublish}
                />
              )}
            </div>
          </CardContent>
        </Card>

        {/* Help Text - Mobile Responsive */}
        <Card className="mt-6">
          <CardContent className="p-4">
            <div className="text-sm text-muted-foreground space-y-3">
              <h4 className="font-medium text-foreground mb-3 flex items-center gap-2">
                💡 Tips for creating great tours:
              </h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                <div className="flex items-start gap-2">
                  <span className="text-primary mt-1">•</span>
                  <span>Add interactive hotspots to highlight important features</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary mt-1">•</span>
                  <span>Include product information for e-commerce integration</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary mt-1">•</span>
                  <span>Test your tour before submitting for review</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-primary mt-1">•</span>
                  <span>Tours are reviewed within 24-48 hours</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default UserTourEditor;
