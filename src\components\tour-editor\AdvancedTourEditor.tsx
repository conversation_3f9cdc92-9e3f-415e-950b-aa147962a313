/**
 * Advanced Tour Editor
 * Production-ready tour editor with PSV v5 integration
 */

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Upload, 
  Plus, 
  Trash2, 
  Edit3, 
  Eye, 
  Save, 
  Play, 
  Settings,
  MapPin,
  ShoppingCart,
  Info,
  Link,
  Camera,
  Move,
  RotateCcw,
  Maximize2,
  Download
} from 'lucide-react';
import { toast } from 'sonner';

// Import our PSV components and hooks
import PhotoSphereTourViewer from '../tour-viewer/PhotoSphereTourViewer';
import { useVirtualTour } from '@/hooks/useVirtualTour';
import { useMarkers } from '@/hooks/useMarkers';
import type { TourScene, TourMarker, PSVInstance } from '@/lib/photosphere/types';
import type { ProductData } from '@/lib/photosphere/markerService';


export interface AdvancedTourEditorData {
  scenes: TourScene[];
  settings?: {
    autoRotate?: boolean;
    showControls?: boolean;
    enableVR?: boolean;
    [key: string]: unknown;
  };
  metadata?: {
    lastModified?: string;
    version?: string;
    [key: string]: unknown;
  };
  status?: string;
  publishedAt?: string;
  [key: string]: unknown;
}

export interface AdvancedTourEditorProps {
  tourId?: string;
  initialScenes?: TourScene[];
  onSave?: (tourData: AdvancedTourEditorData) => void;
  onPublish?: (tourData: AdvancedTourEditorData) => void;
  onClose?: () => void;
  className?: string;
}

interface EditorState {
  currentSceneIndex: number;
  selectedMarker: TourMarker | null;
  isAddingMarker: boolean;
  markerType: TourMarker['type'];
  isPreviewMode: boolean;
  isDragOver: boolean;
  hasUnsavedChanges: boolean;
}

const AdvancedTourEditor: React.FC<AdvancedTourEditorProps> = ({
  tourId,
  initialScenes = [],
  onSave,
  onPublish,
  onClose,
  className = ''
}) => {
  // State management
  const [scenes, setScenes] = useState<TourScene[]>(initialScenes);
  const [editorState, setEditorState] = useState<EditorState>({
    currentSceneIndex: 0,
    selectedMarker: null,
    isAddingMarker: false,
    markerType: 'info',
    isPreviewMode: false,
    isDragOver: false,
    hasUnsavedChanges: false
  });

  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  const psvInstanceRef = useRef<PSVInstance | null>(null);

  // Hooks
  const virtualTour = useVirtualTour({
    tourId: tourId || '',
    autoLoad: !!tourId,
    enableRealTimeSync: true,
    onNodeChange: (nodeId) => {
      const sceneIndex = scenes.findIndex(s => s.id === nodeId);
      if (sceneIndex !== -1) {
        setEditorState(prev => ({ ...prev, currentSceneIndex: sceneIndex }));
      }
    },
    onError: (error) => {
      toast.error(`Tour error: ${error.message}`);
    }
  });

  const markers = useMarkers({
    onMarkerClick: (marker) => {
      setEditorState(prev => ({ ...prev, selectedMarker: marker }));
    },
    onProductClick: (product) => {
      toast.success(`Product clicked: ${product.name}`);
    },
    onNavigationClick: (targetSceneId) => {
      const targetIndex = scenes.findIndex(s => s.id === targetSceneId);
      if (targetIndex !== -1) {
        setEditorState(prev => ({ ...prev, currentSceneIndex: targetIndex }));
      }
    }
  });

  // Current scene
  const currentScene = scenes[editorState.currentSceneIndex];

  // Handle PSV ready
  const handlePSVReady = useCallback((instance: PSVInstance) => {
    psvInstanceRef.current = instance;
    
    // Attach hooks to PSV
    if (tourId) {
      virtualTour.attachToPSV(instance);
    }
    markers.attachToPSV(instance);
    
    // Load markers for current scene
    if (currentScene?.markers) {
      markers.setMarkers(currentScene.markers);
    }
  }, [tourId, virtualTour, markers, currentScene]);

  // Handle file upload
  const handleFileUpload = useCallback((files: FileList) => {
    Array.from(files).forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const imageUrl = e.target?.result as string;
          const newScene: TourScene = {
            id: `scene-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            name: file.name.replace(/\.[^/.]+$/, ''),
            panorama: imageUrl,
            links: [],
            markers: [],
            data: {
              orderIndex: scenes.length
            }
          };
          
          setScenes(prev => [...prev, newScene]);
          setEditorState(prev => ({ 
            ...prev, 
            hasUnsavedChanges: true,
            currentSceneIndex: scenes.length 
          }));
          
          toast.success(`Added scene: ${newScene.name}`);
        };
        reader.readAsDataURL(file);
      }
    });
  }, [scenes.length]);

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setEditorState(prev => ({ ...prev, isDragOver: true }));
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setEditorState(prev => ({ ...prev, isDragOver: false }));
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setEditorState(prev => ({ ...prev, isDragOver: false }));
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileUpload(files);
    }
  }, [handleFileUpload]);

  // Handle marker placement
  const handleViewerClick = useCallback((event: React.MouseEvent) => {
    if (!editorState.isAddingMarker || !psvInstanceRef.current) return;

    // Get click position relative to viewer
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // Convert to spherical coordinates (simplified)
    const yaw = ((x / rect.width) - 0.5) * 360;
    const pitch = ((0.5 - y / rect.height) * 180);

    // Create marker based on type
    let newMarker: TourMarker;
    
    switch (editorState.markerType) {
      case 'product':
        newMarker = markers.createProductMarker(
          { yaw, pitch },
          {
            id: 'temp-product',
            name: 'New Product',
            price: 0,
            image: '',
            description: 'Product description'
          }
        );
        break;
      case 'navigation':
        newMarker = markers.createNavigationMarker(
          { yaw, pitch },
          scenes[0]?.id || '',
          'Go to scene'
        );
        break;
      case 'info':
        newMarker = markers.createInfoMarker(
          { yaw, pitch },
          'Information',
          'Click to edit this information'
        );
        break;
      case 'link':
        newMarker = markers.createLinkMarker(
          { yaw, pitch },
          'https://example.com',
          'External Link'
        );
        break;
      case 'media':
        newMarker = markers.createMediaMarker(
          { yaw, pitch },
          '',
          'Media',
          'image'
        );
        break;
      default:
        return;
    }

    // Add marker to current scene
    markers.addMarker(newMarker);
    
    // Update scene data
    setScenes(prev => prev.map((scene, index) => 
      index === editorState.currentSceneIndex
        ? { ...scene, markers: [...(scene.markers || []), newMarker] }
        : scene
    ));

    setEditorState(prev => ({
      ...prev,
      isAddingMarker: false,
      selectedMarker: newMarker,
      hasUnsavedChanges: true
    }));

    toast.success('Marker added! Configure it in the properties panel.');
  }, [editorState.isAddingMarker, editorState.markerType, editorState.currentSceneIndex, markers, scenes]);

  // Save tour
  const handleSave = useCallback(async () => {
    try {
      const tourData = {
        scenes,
        settings: {
          autoRotate: false,
          showControls: true,
          enableVR: false
        },
        metadata: {
          lastModified: new Date().toISOString(),
          version: '1.0'
        }
      };

      if (onSave) {
        await onSave(tourData);
      }

      setEditorState(prev => ({ ...prev, hasUnsavedChanges: false }));
      toast.success('Tour saved successfully!');
    } catch (error) {
      toast.error('Failed to save tour');
      console.error('Save error:', error);
    }
  }, [scenes, onSave]);

  // Publish tour
  const handlePublish = useCallback(async () => {
    try {
      await handleSave(); // Save first
      
      if (onPublish) {
        await onPublish({
          scenes,
          status: 'published',
          publishedAt: new Date().toISOString()
        });
      }

      toast.success('Tour published successfully!');
    } catch (error) {
      toast.error('Failed to publish tour');
      console.error('Publish error:', error);
    }
  }, [scenes, onPublish, handleSave]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (tourId) {
        virtualTour.detachFromPSV();
      }
      markers.detachFromPSV();
    };
  }, [tourId, virtualTour, markers]);

  return (
    <div className={`advanced-tour-editor h-screen flex flex-col ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center gap-4">
          <h1 className="text-xl font-semibold">Advanced Tour Editor</h1>
          {editorState.hasUnsavedChanges && (
            <Badge variant="secondary">Unsaved Changes</Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setEditorState(prev => ({ ...prev, isPreviewMode: !prev.isPreviewMode }))}
          >
            <Eye className="w-4 h-4 mr-2" />
            {editorState.isPreviewMode ? 'Edit' : 'Preview'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleSave}
            disabled={!editorState.hasUnsavedChanges}
          >
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          
          <Button
            size="sm"
            onClick={handlePublish}
          >
            <Play className="w-4 h-4 mr-2" />
            Publish
          </Button>
          
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
            >
              Close
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Scene Management */}
        <div className="w-80 border-r bg-muted/30 flex flex-col">
          <div className="p-4 border-b">
            <h2 className="font-semibold mb-4">Scenes ({scenes.length})</h2>
            
            {/* Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer ${
                editorState.isDragOver 
                  ? 'border-primary bg-primary/5' 
                  : 'border-muted-foreground/25 hover:border-primary/50'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
              role="region"
              aria-label="Upload 360 degree images"
              tabIndex={0}
              onKeyDown={e => {
                if (e.key === 'Enter' || e.key === ' ') fileInputRef.current?.click();
              }}
            >
              <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" aria-hidden="true" />
              <p className="text-sm text-muted-foreground" id="advanced-tour-upload-desc">
                Drop 360° images here or click to upload
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Supports JPG, PNG formats
              </p>
            </div>
            
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/*"
              className="hidden"
              onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
              aria-label="Select images to upload"
              title="Select images to upload"
              placeholder="Select images to upload"
            />
          </div>
          
          {/* Scene List */}
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-2">
              {scenes.map((scene, index) => (
                <Card
                  key={scene.id}
                  className={`cursor-pointer transition-colors ${
                    index === editorState.currentSceneIndex 
                      ? 'border-primary bg-primary/5' 
                      : 'hover:bg-muted/50'
                  }`}
                  onClick={() => setEditorState(prev => ({ ...prev, currentSceneIndex: index }))}
                >
                  <CardContent className="p-3">
                    <div className="flex items-center gap-3">
                      {scene.thumbnail && (
                        <img 
                          src={scene.thumbnail} 
                          alt={scene.name}
                          className="w-12 h-6 rounded object-cover"
                        />
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{scene.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {scene.markers?.length || 0} markers
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle scene deletion
                        }}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </div>

        {/* Center - Viewer */}
        <div className="flex-1 flex flex-col">
          {/* Viewer Controls */}
          <div className="p-4 border-b bg-background">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <h3 className="font-medium">
                  {currentScene?.name || 'No scene selected'}
                </h3>
                {currentScene && (
                  <Badge variant="outline">
                    Scene {editorState.currentSceneIndex + 1} of {scenes.length}
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                <Select
                  value={editorState.markerType}
                  onValueChange={(value) => 
                    setEditorState(prev => ({ ...prev, markerType: value as TourMarker['type'] }))
                  }
                >
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="info">
                      <div className="flex items-center gap-2">
                        <Info className="w-4 h-4" />
                        Info
                      </div>
                    </SelectItem>
                    <SelectItem value="product">
                      <div className="flex items-center gap-2">
                        <ShoppingCart className="w-4 h-4" />
                        Product
                      </div>
                    </SelectItem>
                    <SelectItem value="navigation">
                      <div className="flex items-center gap-2">
                        <MapPin className="w-4 h-4" />
                        Navigation
                      </div>
                    </SelectItem>
                    <SelectItem value="link">
                      <div className="flex items-center gap-2">
                        <Link className="w-4 h-4" />
                        Link
                      </div>
                    </SelectItem>
                    <SelectItem value="media">
                      <div className="flex items-center gap-2">
                        <Camera className="w-4 h-4" />
                        Media
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                
                <Button
                  variant={editorState.isAddingMarker ? "default" : "outline"}
                  size="sm"
                  onClick={() => 
                    setEditorState(prev => ({ ...prev, isAddingMarker: !prev.isAddingMarker }))
                  }
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Marker
                </Button>
              </div>
            </div>
            
            {editorState.isAddingMarker && (
              <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded text-sm text-blue-800">
                Click anywhere on the 360° image to place a {editorState.markerType} marker
              </div>
            )}
          </div>
          
          {/* Viewer */}
          <div className="flex-1 relative" onClick={handleViewerClick}>
            {currentScene ? (
              <PhotoSphereTourViewer
                scenes={scenes}
                tourId={tourId}
                initialSceneId={currentScene.id}
                enableVirtualTour={scenes.length > 1}
                enableGallery={scenes.length > 1}
                enableSettings={true}
                enableRealTimeSync={true}
                showControls={!editorState.isAddingMarker}
                onReady={() => handlePSVReady(psvInstanceRef.current!)}
                onSceneChange={(sceneId) => {
                  const sceneIndex = scenes.findIndex(s => s.id === sceneId);
                  if (sceneIndex !== -1) {
                    setEditorState(prev => ({ ...prev, currentSceneIndex: sceneIndex }));
                  }
                }}
                onMarkerClick={(markerId, markerData) => {
                  const marker = markers.getMarker(markerId);
                  if (marker) {
                    setEditorState(prev => ({ ...prev, selectedMarker: marker }));
                  }
                }}
                className="h-full"
              />
            ) : (
              <div className="h-full flex items-center justify-center bg-muted">
                <div className="text-center">
                  <Camera className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No Scene Selected</h3>
                  <p className="text-muted-foreground">Upload 360° images to start creating your tour</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Sidebar - Properties Panel */}
        <div className="w-80 border-l bg-muted/30 flex flex-col">
          <div className="p-4 border-b">
            <h2 className="font-semibold">Properties</h2>
          </div>

          <ScrollArea className="flex-1">
            <div className="p-4 space-y-6">
              {/* Scene Properties */}
              {currentScene && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Scene Settings</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="scene-name">Scene Name</Label>
                      <Input
                        id="scene-name"
                        value={currentScene.name}
                        onChange={(e) => {
                          const newName = e.target.value;
                          setScenes(prev => prev.map((scene, index) =>
                            index === editorState.currentSceneIndex
                              ? { ...scene, name: newName }
                              : scene
                          ));
                          setEditorState(prev => ({ ...prev, hasUnsavedChanges: true }));
                        }}
                      />
                    </div>

                    <div>
                      <Label htmlFor="scene-description">Description</Label>
                      <Textarea
                        id="scene-description"
                        value={currentScene.description || ''}
                        onChange={(e) => {
                          const newDescription = e.target.value;
                          setScenes(prev => prev.map((scene, index) =>
                            index === editorState.currentSceneIndex
                              ? { ...scene, description: newDescription }
                              : scene
                          ));
                          setEditorState(prev => ({ ...prev, hasUnsavedChanges: true }));
                        }}
                        rows={3}
                      />
                    </div>

                    <div>
                      <Label>Markers</Label>
                      <div className="text-sm text-muted-foreground">
                        {currentScene.markers?.length || 0} markers in this scene
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Marker Properties */}
              {editorState.selectedMarker && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm flex items-center justify-between">
                      Marker Properties
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          if (editorState.selectedMarker) {
                            markers.removeMarker(editorState.selectedMarker.id);
                            setScenes(prev => prev.map((scene, index) =>
                              index === editorState.currentSceneIndex
                                ? {
                                    ...scene,
                                    markers: scene.markers?.filter(m => m.id !== editorState.selectedMarker?.id) || []
                                  }
                                : scene
                            ));
                            setEditorState(prev => ({
                              ...prev,
                              selectedMarker: null,
                              hasUnsavedChanges: true
                            }));
                            toast.success('Marker deleted');
                          }
                        }}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label>Type</Label>
                      <Badge variant="outline" className="ml-2">
                        {editorState.selectedMarker.type}
                      </Badge>
                    </div>

                    <div>
                      <Label htmlFor="marker-title">Title</Label>
                      <Input
                        id="marker-title"
                        value={editorState.selectedMarker.title || ''}
                        onChange={(e) => {
                          const newTitle = e.target.value;
                          if (editorState.selectedMarker) {
                            const updatedMarker = { ...editorState.selectedMarker, title: newTitle };
                            markers.updateMarker(editorState.selectedMarker.id, { title: newTitle });
                            setEditorState(prev => ({
                              ...prev,
                              selectedMarker: updatedMarker,
                              hasUnsavedChanges: true
                            }));
                          }
                        }}
                      />
                    </div>

                    <div>
                      <Label htmlFor="marker-content">Content</Label>
                      <Textarea
                        id="marker-content"
                        value={editorState.selectedMarker.content || ''}
                        onChange={(e) => {
                          const newContent = e.target.value;
                          if (editorState.selectedMarker) {
                            const updatedMarker = { ...editorState.selectedMarker, content: newContent };
                            markers.updateMarker(editorState.selectedMarker.id, { content: newContent });
                            setEditorState(prev => ({
                              ...prev,
                              selectedMarker: updatedMarker,
                              hasUnsavedChanges: true
                            }));
                          }
                        }}
                        rows={3}
                      />
                    </div>

                    {/* Product-specific properties */}
                    {editorState.selectedMarker.type === 'product' && editorState.selectedMarker.productData && (
                      <>
                        <Separator />
                        <div className="space-y-4">
                          <h4 className="text-sm font-medium">Product Details</h4>

                          <div>
                            <Label htmlFor="product-name">Product Name</Label>
                            <Input
                              id="product-name"
                              value={editorState.selectedMarker.productData.name}
                              onChange={(e) => {
                                const newName = e.target.value;
                                if (editorState.selectedMarker?.productData) {
                                  const updatedProductData = {
                                    ...editorState.selectedMarker.productData,
                                    name: newName
                                  };
                                  markers.updateMarker(editorState.selectedMarker.id, {
                                    productData: updatedProductData
                                  });
                                  setEditorState(prev => ({
                                    ...prev,
                                    selectedMarker: {
                                      ...prev.selectedMarker!,
                                      productData: updatedProductData
                                    },
                                    hasUnsavedChanges: true
                                  }));
                                }
                              }}
                            />
                          </div>

                          <div>
                            <Label htmlFor="product-price">Price</Label>
                            <Input
                              id="product-price"
                              type="number"
                              value={editorState.selectedMarker.productData.price}
                              onChange={(e) => {
                                const newPrice = parseFloat(e.target.value) || 0;
                                if (editorState.selectedMarker?.productData) {
                                  const updatedProductData = {
                                    ...editorState.selectedMarker.productData,
                                    price: newPrice
                                  };
                                  markers.updateMarker(editorState.selectedMarker.id, {
                                    productData: updatedProductData
                                  });
                                  setEditorState(prev => ({
                                    ...prev,
                                    selectedMarker: {
                                      ...prev.selectedMarker!,
                                      productData: updatedProductData
                                    },
                                    hasUnsavedChanges: true
                                  }));
                                }
                              }}
                            />
                          </div>

                          <div>
                            <Label htmlFor="product-description">Product Description</Label>
                            <Textarea
                              id="product-description"
                              value={editorState.selectedMarker.productData.description}
                              onChange={(e) => {
                                const newDescription = e.target.value;
                                if (editorState.selectedMarker?.productData) {
                                  const updatedProductData = {
                                    ...editorState.selectedMarker.productData,
                                    description: newDescription
                                  };
                                  markers.updateMarker(editorState.selectedMarker.id, {
                                    productData: updatedProductData
                                  });
                                  setEditorState(prev => ({
                                    ...prev,
                                    selectedMarker: {
                                      ...prev.selectedMarker!,
                                      productData: updatedProductData
                                    },
                                    hasUnsavedChanges: true
                                  }));
                                }
                              }}
                              rows={3}
                            />
                          </div>
                        </div>
                      </>
                    )}

                    {/* Navigation-specific properties */}
                    {editorState.selectedMarker.type === 'navigation' && (
                      <>
                        <Separator />
                        <div>
                          <Label htmlFor="target-scene">Target Scene</Label>
                          <Select
                            value={editorState.selectedMarker.targetSceneId || ''}
                            onValueChange={(value) => {
                              if (editorState.selectedMarker) {
                                markers.updateMarker(editorState.selectedMarker.id, { targetSceneId: value });
                                setEditorState(prev => ({
                                  ...prev,
                                  selectedMarker: {
                                    ...prev.selectedMarker!,
                                    targetSceneId: value
                                  },
                                  hasUnsavedChanges: true
                                }));
                              }
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select target scene" />
                            </SelectTrigger>
                            <SelectContent>
                              {scenes.map((scene) => (
                                <SelectItem key={scene.id} value={scene.id}>
                                  {scene.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </>
                    )}

                    {/* Link-specific properties */}
                    {editorState.selectedMarker.type === 'link' && (
                      <>
                        <Separator />
                        <div>
                          <Label htmlFor="link-url">URL</Label>
                          <Input
                            id="link-url"
                            type="url"
                            value={editorState.selectedMarker.linkUrl || ''}
                            onChange={(e) => {
                              const newUrl = e.target.value;
                              if (editorState.selectedMarker) {
                                markers.updateMarker(editorState.selectedMarker.id, { linkUrl: newUrl });
                                setEditorState(prev => ({
                                  ...prev,
                                  selectedMarker: {
                                    ...prev.selectedMarker!,
                                    linkUrl: newUrl
                                  },
                                  hasUnsavedChanges: true
                                }));
                              }
                            }}
                            placeholder="https://example.com"
                          />
                        </div>
                      </>
                    )}

                    {/* Media-specific properties */}
                    {editorState.selectedMarker.type === 'media' && (
                      <>
                        <Separator />
                        <div className="space-y-4">
                          <div>
                            <Label htmlFor="media-url">Media URL</Label>
                            <Input
                              id="media-url"
                              type="url"
                              value={editorState.selectedMarker.mediaUrl || ''}
                              onChange={(e) => {
                                const newUrl = e.target.value;
                                if (editorState.selectedMarker) {
                                  markers.updateMarker(editorState.selectedMarker.id, { mediaUrl: newUrl });
                                  setEditorState(prev => ({
                                    ...prev,
                                    selectedMarker: {
                                      ...prev.selectedMarker!,
                                      mediaUrl: newUrl
                                    },
                                    hasUnsavedChanges: true
                                  }));
                                }
                              }}
                              placeholder="https://example.com/media.jpg"
                            />
                          </div>

                          <div>
                            <Label htmlFor="media-type">Media Type</Label>
                            <Select
                              value={editorState.selectedMarker.mediaType || 'image'}
                              onValueChange={(value) => {
                                if (editorState.selectedMarker) {
                                  const typedValue = value as 'image' | 'video' | 'audio';
                                  markers.updateMarker(editorState.selectedMarker.id, { mediaType: typedValue });
                                  setEditorState(prev => ({
                                    ...prev,
                                    selectedMarker: {
                                      ...prev.selectedMarker!,
                                      mediaType: typedValue
                                    },
                                    hasUnsavedChanges: true
                                  }));
                                }
                              }}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="image">Image</SelectItem>
                                <SelectItem value="video">Video</SelectItem>
                                <SelectItem value="audio">Audio</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Tour Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Tour Statistics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Total Scenes:</span>
                    <span>{scenes.length}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Total Markers:</span>
                    <span>{scenes.reduce((total, scene) => total + (scene.markers?.length || 0), 0)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Product Markers:</span>
                    <span>{scenes.reduce((total, scene) => total + (scene.markers?.filter(m => m.type === 'product').length || 0), 0)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Navigation Links:</span>
                    <span>{scenes.reduce((total, scene) => total + (scene.markers?.filter(m => m.type === 'navigation').length || 0), 0)}</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </ScrollArea>
        </div>
      </div>
    </div>
  );
};

export default AdvancedTourEditor;
