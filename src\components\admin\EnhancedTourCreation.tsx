/**
 * Enhanced Tour Creation Component
 * Complete tour creation flow with commerce integration
 * <PERSON><PERSON> can create tours and add commerce features
 */

import { useState, useCallback, useMemo } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  MapPin,
  Building,
  Phone,
  Mail,
  Globe,
  Clock,
  ShoppingCart,
  Users,
  Settings,
  Eye,
  Save,
  Plus,
  Upload,
  Link2,
  CheckCircle,
  AlertCircle,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { EnhancedTourCreationService, EnhancedTourConfig } from '@/services/tour-creation/EnhancedTourCreationService';
import VendorSelector from './VendorSelector';

interface TourCreationData {
  // Basic Tour Info
  title: string;
  description: string;
  category: string;
  location: string;

  // Business Info
  businessName: string;
  businessType: string;
  contactPhone: string;
  contactEmail: string;
  website: string;
  businessHours: string;

  // Tour Content
  uploadMethod: 'images' | 'embed' | 'commonninja';
  embedUrl: string;
  embedType: 'iframe' | 'link' | 'custom';
  images: File[];

  // Commerce Settings
  enableCommerce: boolean;
  vendorId: string;
  whatsappNumber: string;
  commissionRate: number;

  // Widget Integration
  enableProductHotspots: boolean;
  enableCatalog: boolean;
  enableBooking: boolean;

  // Advanced Settings
  status: 'draft' | 'published';
  featured: boolean;
  allowReviews: boolean;
}

interface EnhancedTourCreationProps {
  onSuccess?: (tourId: string) => void;
  onCancel?: () => void;
}

const EnhancedTourCreation = ({ onSuccess, onCancel }: EnhancedTourCreationProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [tourCreationService] = useState(() => new EnhancedTourCreationService());
  const [selectedVendor, setSelectedVendor] = useState<any>(null);
  const [formData, setFormData] = useState<TourCreationData>({
    title: '',
    description: '',
    category: '',
    location: '',
    businessName: '',
    businessType: '',
    contactPhone: '',
    contactEmail: '',
    website: '',
    businessHours: '',
    uploadMethod: 'commonninja', // Default to CommonNinja
    embedUrl: '',
    embedType: 'iframe',
    images: [],
    enableCommerce: false,
    vendorId: '',
    whatsappNumber: '',
    commissionRate: 0.10,
    enableProductHotspots: false,
    enableCatalog: false,
    enableBooking: false,
    status: 'draft',
    featured: false,
    allowReviews: true
  });

  const steps = [
    { id: 1, title: 'Basic Info', description: 'Tour details and location' },
    { id: 2, title: 'Business Info', description: 'Business details and contact' },
    { id: 3, title: 'Tour Content', description: 'Upload method and content' },
    { id: 4, title: 'Commerce Setup', description: 'E-commerce integration' },
    { id: 5, title: 'Review & Publish', description: 'Final review and settings' }
  ];

  const categories = [
    'Real Estate',
    'Retail Store',
    'Restaurant',
    'Hotel',
    'Event Venue',
    'Museum',
    'Office Space',
    'Educational',
    'Healthcare',
    'Entertainment',
    'Other'
  ];

  const businessTypes = [
    'Retail',
    'Restaurant',
    'Hotel',
    'Real Estate',
    'Services',
    'Manufacturing',
    'Technology',
    'Healthcare',
    'Education',
    'Entertainment',
    'Other'
  ];

  const updateFormData = useCallback((field: keyof TourCreationData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const nextStep = useCallback(() => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, steps.length]);

  const prevStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep]);

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      // Validate required fields
      if (!formData.title || !formData.category || !formData.location) {
        toast.error('Please fill in all required fields');
        return;
      }

      // Create enhanced tour configuration
      const tourConfig: EnhancedTourConfig = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        location: formData.location,
        businessName: formData.businessName,
        businessType: formData.businessType,
        contactPhone: formData.contactPhone,
        contactEmail: formData.contactEmail,
        website: formData.website,
        businessHours: formData.businessHours,
        uploadMethod: formData.uploadMethod,
        embedUrl: formData.embedUrl,
        embedType: formData.embedType,
        images: formData.images,
        enableCommerce: formData.enableCommerce,
        vendorId: formData.vendorId,
        whatsappNumber: formData.whatsappNumber,
        commissionRate: formData.commissionRate,
        enableProductHotspots: formData.enableProductHotspots,
        enableCatalog: formData.enableCatalog,
        enableBooking: formData.enableBooking,
        status: formData.status,
        featured: formData.featured,
        allowReviews: formData.allowReviews,
      };

      // Create the tour with all integrations
      const result = await tourCreationService.createEnhancedTour(tourConfig);

      if (result.success) {
        toast.success(result.message);
        onSuccess?.(result.tourId);
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error('Tour creation error:', error);
      toast.error('Failed to create tour. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title">Tour Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => updateFormData('title', e.target.value)}
                  placeholder="Enter tour title"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category *</Label>
                <Select value={formData.category} onValueChange={(value) => updateFormData('category', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => updateFormData('description', e.target.value)}
                placeholder="Describe your virtual tour"
                rows={4}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="location">Location *</Label>
              <Input
                id="location"
                value={formData.location}
                onChange={(e) => updateFormData('location', e.target.value)}
                placeholder="City, State, Country"
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="businessName">Business Name</Label>
                <Input
                  id="businessName"
                  value={formData.businessName}
                  onChange={(e) => updateFormData('businessName', e.target.value)}
                  placeholder="Business or property name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="businessType">Business Type</Label>
                <Select value={formData.businessType} onValueChange={(value) => updateFormData('businessType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select business type" />
                  </SelectTrigger>
                  <SelectContent>
                    {businessTypes.map(type => (
                      <SelectItem key={type} value={type}>{type}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="contactPhone">Contact Phone</Label>
                <Input
                  id="contactPhone"
                  value={formData.contactPhone}
                  onChange={(e) => updateFormData('contactPhone', e.target.value)}
                  placeholder="+234 XXX XXX XXXX"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactEmail">Contact Email</Label>
                <Input
                  id="contactEmail"
                  type="email"
                  value={formData.contactEmail}
                  onChange={(e) => updateFormData('contactEmail', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="website">Website</Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => updateFormData('website', e.target.value)}
                  placeholder="https://business.com"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="businessHours">Business Hours</Label>
                <Input
                  id="businessHours"
                  value={formData.businessHours}
                  onChange={(e) => updateFormData('businessHours', e.target.value)}
                  placeholder="Mon-Fri 9AM-5PM"
                />
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <Label>Upload Method</Label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card
                  className={cn(
                    "cursor-pointer transition-all",
                    formData.uploadMethod === 'commonninja' ? "ring-2 ring-primary" : ""
                  )}
                  onClick={() => updateFormData('uploadMethod', 'commonninja')}
                >
                  <CardHeader className="text-center">
                    <Sparkles className="w-8 h-8 mx-auto mb-2 text-blue-500" />
                    <CardTitle className="text-lg">CommonNinja Tour</CardTitle>
                    <CardDescription>Professional tour with widgets</CardDescription>
                    <Badge variant="secondary" className="mt-2">Recommended</Badge>
                  </CardHeader>
                </Card>

                <Card
                  className={cn(
                    "cursor-pointer transition-all",
                    formData.uploadMethod === 'images' ? "ring-2 ring-primary" : ""
                  )}
                  onClick={() => updateFormData('uploadMethod', 'images')}
                >
                  <CardHeader className="text-center">
                    <Upload className="w-8 h-8 mx-auto mb-2" />
                    <CardTitle className="text-lg">360° Images</CardTitle>
                    <CardDescription>Upload panoramic images</CardDescription>
                  </CardHeader>
                </Card>

                <Card
                  className={cn(
                    "cursor-pointer transition-all",
                    formData.uploadMethod === 'embed' ? "ring-2 ring-primary" : ""
                  )}
                  onClick={() => updateFormData('uploadMethod', 'embed')}
                >
                  <CardHeader className="text-center">
                    <Link2 className="w-8 h-8 mx-auto mb-2" />
                    <CardTitle className="text-lg">Embed External</CardTitle>
                    <CardDescription>Embed from other platforms</CardDescription>
                  </CardHeader>
                </Card>
              </div>
            </div>

            {formData.uploadMethod === 'commonninja' && (
              <div className="p-4 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">CommonNinja Integration Features</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Professional virtual tour creation</li>
                  <li>• Built-in product hotspot support</li>
                  <li>• Integrated catalog and booking widgets</li>
                  <li>• Advanced analytics and customization</li>
                  <li>• WhatsApp commerce integration</li>
                </ul>
              </div>
            )}

            {formData.uploadMethod === 'images' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="images">Upload 360° Images</Label>
                  <Input
                    id="images"
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => {
                      const files = Array.from(e.target.files || []);
                      updateFormData('images', files);
                    }}
                  />
                  <p className="text-sm text-muted-foreground">
                    Upload equirectangular panoramic images (JPEG, PNG, WebP)
                  </p>
                </div>
                {formData.images.length > 0 && (
                  <div className="text-sm text-green-600">
                    {formData.images.length} image(s) selected
                  </div>
                )}
              </div>
            )}
            
            {formData.uploadMethod === 'embed' && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="embedUrl">Tour URL</Label>
                  <Input
                    id="embedUrl"
                    value={formData.embedUrl}
                    onChange={(e) => updateFormData('embedUrl', e.target.value)}
                    placeholder="https://my.matterport.com/show/?m=..."
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="embedType">Embed Type</Label>
                  <Select value={formData.embedType} onValueChange={(value) => updateFormData('embedType', value as any)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="iframe">Iframe Embed</SelectItem>
                      <SelectItem value="link">Direct Link</SelectItem>
                      <SelectItem value="custom">Custom Integration</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold">Enable E-commerce</h3>
                <p className="text-sm text-muted-foreground">Allow shopping within this tour</p>
              </div>
              <Switch
                checked={formData.enableCommerce}
                onCheckedChange={(checked) => updateFormData('enableCommerce', checked)}
              />
            </div>
            
            {formData.enableCommerce && (
              <div className="space-y-6 p-4 border rounded-lg">
                {/* Vendor Selection */}
                <VendorSelector
                  selectedVendorId={formData.vendorId}
                  onVendorSelect={(vendorId, vendor) => {
                    updateFormData('vendorId', vendorId);
                    updateFormData('whatsappNumber', vendor.whatsapp_number || vendor.phone);
                    updateFormData('commissionRate', vendor.commission_rate);
                    setSelectedVendor(vendor);
                  }}
                  onCreateNew={() => {
                    toast.info('Please create vendors in the Vendor Management section first');
                  }}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="whatsappNumber">WhatsApp Number *</Label>
                    <Input
                      id="whatsappNumber"
                      value={formData.whatsappNumber}
                      onChange={(e) => updateFormData('whatsappNumber', e.target.value)}
                      placeholder="+234 XXX XXX XXXX"
                    />
                    <p className="text-xs text-muted-foreground">
                      Auto-filled from selected vendor
                    </p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="commissionRate">Commission Rate (%)</Label>
                    <Input
                      id="commissionRate"
                      type="number"
                      min="0"
                      max="100"
                      step="0.01"
                      value={formData.commissionRate * 100}
                      onChange={(e) => updateFormData('commissionRate', parseFloat(e.target.value) / 100)}
                      disabled={!!selectedVendor}
                    />
                    <p className="text-xs text-muted-foreground">
                      {selectedVendor ? 'Set by vendor configuration' : 'Custom commission rate'}
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Widget Integration</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <Label className="text-sm font-medium">Product Hotspots</Label>
                        <p className="text-xs text-muted-foreground">Interactive product placement</p>
                      </div>
                      <Switch
                        checked={formData.enableProductHotspots}
                        onCheckedChange={(checked) => updateFormData('enableProductHotspots', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <Label className="text-sm font-medium">Product Catalog</Label>
                        <p className="text-xs text-muted-foreground">Browsable product grid</p>
                      </div>
                      <Switch
                        checked={formData.enableCatalog}
                        onCheckedChange={(checked) => updateFormData('enableCatalog', checked)}
                      />
                    </div>

                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <Label className="text-sm font-medium">Booking Widget</Label>
                        <p className="text-xs text-muted-foreground">Appointment scheduling</p>
                      </div>
                      <Switch
                        checked={formData.enableBooking}
                        onCheckedChange={(checked) => updateFormData('enableBooking', checked)}
                      />
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Commerce Features</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Product hotspots within the tour</li>
                    <li>• WhatsApp checkout integration</li>
                    <li>• Inventory management</li>
                    <li>• Order tracking</li>
                    <li>• Vendor commission system</li>
                    <li>• Real-time analytics</li>
                  </ul>
                </div>
              </div>
            )}
          </div>
        );

      case 5:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>Publish Status</Label>
                  <Select value={formData.status} onValueChange={(value) => updateFormData('status', value as any)}>
                    <SelectTrigger className="w-32">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Featured Tour</Label>
                    <p className="text-sm text-muted-foreground">Show in featured sections</p>
                  </div>
                  <Switch
                    checked={formData.featured}
                    onCheckedChange={(checked) => updateFormData('featured', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Allow Reviews</Label>
                    <p className="text-sm text-muted-foreground">Enable customer reviews</p>
                  </div>
                  <Switch
                    checked={formData.allowReviews}
                    onCheckedChange={(checked) => updateFormData('allowReviews', checked)}
                  />
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-medium">Tour Summary</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Title:</span>
                    <span>{formData.title || 'Not set'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Category:</span>
                    <span>{formData.category || 'Not set'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Location:</span>
                    <span>{formData.location || 'Not set'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Commerce:</span>
                    <Badge variant={formData.enableCommerce ? "default" : "secondary"}>
                      {formData.enableCommerce ? 'Enabled' : 'Disabled'}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Status:</span>
                    <Badge variant={formData.status === 'published' ? "default" : "secondary"}>
                      {formData.status}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Progress Steps */}
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className={cn(
              "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
              currentStep >= step.id 
                ? "bg-primary text-primary-foreground" 
                : "bg-muted text-muted-foreground"
            )}>
              {currentStep > step.id ? <CheckCircle className="w-4 h-4" /> : step.id}
            </div>
            {index < steps.length - 1 && (
              <div className={cn(
                "w-16 h-0.5 mx-2",
                currentStep > step.id ? "bg-primary" : "bg-muted"
              )} />
            )}
          </div>
        ))}
      </div>

      {/* Step Content */}
      <Card>
        <CardHeader>
          <CardTitle>{steps[currentStep - 1].title}</CardTitle>
          <CardDescription>{steps[currentStep - 1].description}</CardDescription>
        </CardHeader>
        <CardContent>
          {renderStepContent()}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={currentStep === 1 ? onCancel : prevStep}
        >
          {currentStep === 1 ? 'Cancel' : 'Previous'}
        </Button>
        
        <Button 
          onClick={currentStep === steps.length ? handleSubmit : nextStep}
          disabled={isLoading}
        >
          {isLoading ? 'Creating...' : currentStep === steps.length ? 'Create Tour' : 'Next'}
        </Button>
      </div>
    </div>
  );
};

export default EnhancedTourCreation;
