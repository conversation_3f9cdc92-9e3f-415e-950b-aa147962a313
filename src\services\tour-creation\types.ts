/**
 * Tour Creation Service Types
 * Abstraction layer for multiple tour creation platforms (SeekBeak, CommonNinja)
 */

export interface TourConfig {
  title: string;
  description?: string;
  category: string;
  location?: string;
  businessInfo?: {
    name?: string;
    type?: string;
    phone?: string;
    email?: string;
    website?: string;
    hours?: string;
  };
  settings?: {
    autoRotate?: boolean;
    showControls?: boolean;
    enableVR?: boolean;
    customBranding?: boolean;
  };
}

export interface SceneConfig {
  name: string;
  description?: string;
  imageUrl?: string;
  imageFile?: File;
  orderIndex: number;
  settings?: {
    initialView?: {
      yaw: number;
      pitch: number;
      fov: number;
    };
    autoRotate?: boolean;
    hotspotDefaults?: HotspotStyle;
  };
}

export interface HotspotConfig {
  type: 'navigation' | 'info' | 'product' | 'whatsapp' | 'link' | 'audio' | 'video' | 'custom';
  label?: string;
  content?: string;
  position: {
    x: number;
    y: number;
    z: number;
  };
  style?: HotspotStyle;
  actions?: HotspotAction[];
  // Navigation specific
  targetSceneId?: string;
  // Product specific
  productId?: string;
  vendorId?: string;
  // Link specific
  linkUrl?: string;
  // WhatsApp specific
  whatsappPhone?: string;
  whatsappMessage?: string;
  // Media specific
  mediaUrl?: string;
}

export interface HotspotStyle {
  icon?: string;
  color?: string;
  size?: 'small' | 'medium' | 'large';
  animation?: 'none' | 'pulse' | 'bounce' | 'glow';
  opacity?: number;
}

export interface HotspotAction {
  type: 'navigate' | 'popup' | 'external' | 'api_call' | 'analytics';
  target?: string;
  data?: Record<string, any>;
}

export interface TourScene {
  id: string;
  name: string;
  description?: string;
  imageUrl: string;
  orderIndex: number;
  hotspots: TourHotspot[];
  platformSpecific?: {
    commonNinjaId?: string;
    customData?: Record<string, any>;
  };
}

export interface TourHotspot {
  id: string;
  type: HotspotConfig['type'];
  label?: string;
  content?: string;
  position: HotspotConfig['position'];
  style?: HotspotStyle;
  actions?: HotspotAction[];
  platformSpecific?: {
    commonNinjaId?: string;
    customData?: Record<string, any>;
  };
}

export interface CreatedTour {
  id: string;
  title: string;
  description?: string;
  embedUrl: string;
  editUrl?: string;
  previewUrl?: string;
  scenes: TourScene[];
  platform: 'commonninja' | 'cloudpano' | 'custom';
  platformSpecific?: {
    seekbeakGroupId?: string;
    commonNinjaWidgetId?: string;
    customData?: Record<string, any>;
  };
  settings?: TourConfig['settings'];
  createdAt: Date;
  updatedAt: Date;
}

export interface TourCreationProgress {
  step: 'initializing' | 'uploading_images' | 'creating_scenes' | 'adding_hotspots' | 'publishing' | 'complete' | 'error';
  progress: number; // 0-100
  message?: string;
  currentScene?: string;
  error?: string;
}

export interface TourAnalytics {
  views: number;
  uniqueViews: number;
  averageTimeSpent: number;
  hotspotInteractions: Record<string, number>;
  sceneViews: Record<string, number>;
  conversionEvents: Array<{
    type: string;
    timestamp: Date;
    data?: Record<string, any>;
  }>;
}

/**
 * Abstract interface for tour creation services
 */
export interface TourCreationService {
  readonly platform: 'commonninja' | 'custom';
  readonly isConfigured: boolean;

  // Tour Management
  createTour(config: TourConfig): Promise<CreatedTour>;
  updateTour(tourId: string, config: Partial<TourConfig>): Promise<CreatedTour>;
  deleteTour(tourId: string): Promise<void>;
  getTour(tourId: string): Promise<CreatedTour>;
  listTours(): Promise<CreatedTour[]>;

  // Scene Management
  addScene(tourId: string, scene: SceneConfig): Promise<TourScene>;
  updateScene(tourId: string, sceneId: string, scene: Partial<SceneConfig>): Promise<TourScene>;
  deleteScene(tourId: string, sceneId: string): Promise<void>;
  reorderScenes(tourId: string, sceneIds: string[]): Promise<void>;

  // Hotspot Management
  addHotspot(tourId: string, sceneId: string, hotspot: HotspotConfig): Promise<TourHotspot>;
  updateHotspot(tourId: string, sceneId: string, hotspotId: string, hotspot: Partial<HotspotConfig>): Promise<TourHotspot>;
  deleteHotspot(tourId: string, sceneId: string, hotspotId: string): Promise<void>;

  // Publishing
  publishTour(tourId: string): Promise<CreatedTour>;
  unpublishTour(tourId: string): Promise<CreatedTour>;

  // Analytics
  getAnalytics(tourId: string, dateRange?: { start: Date; end: Date }): Promise<TourAnalytics>;

  // Embed Integration
  getEmbedCode(tourId: string, options?: EmbedOptions): string;
  setupEmbedAPI(tourId: string, callbacks?: EmbedCallbacks): void;
}

export interface EmbedOptions {
  width?: string;
  height?: string;
  autoplay?: boolean;
  controls?: boolean;
  customCSS?: string;
  allowFullscreen?: boolean;
}

export interface EmbedCallbacks {
  onHotspotClick?: (data: { hotspotId: string; hotspotType: string; data?: any }) => void;
  onSceneChange?: (data: { sceneId: string; sceneName: string }) => void;
  onTourComplete?: () => void;
  onError?: (error: string) => void;
}

export interface ServiceError {
  code: string;
  message: string;
  details?: any;
  platform?: string;
}

export class TourCreationError extends Error {
  constructor(
    public code: string,
    message: string,
    public details?: any,
    public platform?: string
  ) {
    super(message);
    this.name = 'TourCreationError';
  }
}

// Extended FormData interface to include native tour creation
export interface ExtendedFormData {
  // Existing fields
  title: string;
  description: string;
  category: string;
  location: string;
  businessName: string;
  businessType: string;
  contactPhone: string;
  contactEmail: string;
  website: string;
  businessHours: string;
  files: File[];
  embedUrl: string;
  embedType: 'iframe' | 'link' | 'custom' | '';

  // New native tour creation fields
  creationMethod: 'upload' | 'embed' | 'native';
  tourPlatform?: 'commonninja' | 'custom';
  nativeTourConfig?: {
    enableHotspots: boolean;
    enableEcommerce: boolean;
    autoRotate: boolean;
    showControls: boolean;
    enableVR: boolean;
    customBranding: boolean;
  };
  scenes?: SceneConfig[];
  hotspots?: Record<string, HotspotConfig[]>; // sceneId -> hotspots
}
