/**
 * ShoppingCart Component
 * Mobile-first responsive shopping cart with WhatsApp checkout
 * Follows VRT design principles and integrates with existing patterns
 */

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Sheet, 
  SheetContent, 
  SheetDescription, 
  SheetHeader, 
  SheetTitle, 
  SheetTrigger 
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  ShoppingCart as CartIcon, 
  Plus, 
  Minus, 
  Trash2, 
  MessageCircle,
  Package,
  MapPin,
  Phone,
  Mail,
  User
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

export interface CartItem {
  id: string;
  product: {
    id: string;
    title: string;
    price: number;
    images: string[];
    vendor: {
      id: string;
      name: string;
      whatsapp_number?: string;
    };
  };
  quantity: number;
  tourContext?: {
    tourId: string;
    tourTitle: string;
    sceneId: string;
  };
}

export interface CustomerInfo {
  name: string;
  phone: string;
  email?: string;
  address?: string;
}

interface ShoppingCartProps {
  items: CartItem[];
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdateQuantity: (itemId: string, quantity: number) => void;
  onRemoveItem: (itemId: string) => void;
  onCheckout: (customerInfo: CustomerInfo) => void;
  className?: string;
}

const ShoppingCart = ({
  items,
  isOpen,
  onOpenChange,
  onUpdateQuantity,
  onRemoveItem,
  onCheckout,
  className
}: ShoppingCartProps) => {
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    phone: '',
    email: '',
    address: ''
  });
  const [isCheckingOut, setIsCheckingOut] = useState(false);

  // Calculate totals
  const subtotal = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);

  // Group items by vendor
  const vendorGroups = items.reduce((groups, item) => {
    const vendorId = item.product.vendor.id;
    if (!groups[vendorId]) {
      groups[vendorId] = {
        vendor: item.product.vendor,
        items: []
      };
    }
    groups[vendorId].items.push(item);
    return groups;
  }, {} as Record<string, { vendor: CartItem['product']['vendor'], items: CartItem[] }>);

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      onRemoveItem(itemId);
    } else {
      onUpdateQuantity(itemId, newQuantity);
    }
  };

  const handleCheckout = async () => {
    if (!customerInfo.name || !customerInfo.phone) {
      toast.error('Please fill in your name and phone number');
      return;
    }

    if (items.length === 0) {
      toast.error('Your cart is empty');
      return;
    }

    setIsCheckingOut(true);
    try {
      await onCheckout(customerInfo);
      toast.success('Order placed successfully! You will receive WhatsApp confirmation.');
      onOpenChange(false);
    } catch (error) {
      toast.error('Failed to place order. Please try again.');
    } finally {
      setIsCheckingOut(false);
    }
  };

  const CartItemComponent = ({ item }: { item: CartItem }) => (
    <motion.div
      layout
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      className="flex gap-3 p-3 border rounded-lg"
    >
      {/* Product Image */}
      <div className="w-16 h-16 bg-gray-100 rounded-md overflow-hidden flex-shrink-0">
        {item.product.images[0] ? (
          <img
            src={item.product.images[0]}
            alt={item.product.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <Package className="w-6 h-6 text-gray-400" />
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-sm line-clamp-2 mb-1">
          {item.product.title}
        </h4>
        <div className="flex items-center gap-1 text-xs text-muted-foreground mb-2">
          <MapPin className="w-3 h-3" />
          <span>{item.product.vendor.name}</span>
        </div>
        {item.tourContext && (
          <div className="text-xs text-blue-600 mb-2">
            From: {item.tourContext.tourTitle}
          </div>
        )}
        <div className="flex items-center justify-between">
          <span className="font-semibold text-sm">
            ₦{(item.product.price * item.quantity).toLocaleString()}
          </span>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              className="w-6 h-6 p-0"
              onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
            >
              <Minus className="w-3 h-3" />
            </Button>
            <span className="text-sm font-medium w-8 text-center">
              {item.quantity}
            </span>
            <Button
              size="sm"
              variant="outline"
              className="w-6 h-6 p-0"
              onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
            >
              <Plus className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="w-6 h-6 p-0 text-red-500 hover:text-red-700"
              onClick={() => onRemoveItem(item.id)}
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  );

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className={cn("relative", className)}>
          <CartIcon className="w-4 h-4" />
          {itemCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 w-5 h-5 p-0 flex items-center justify-center text-xs"
            >
              {itemCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>

      <SheetContent className="w-full sm:max-w-lg flex flex-col">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <CartIcon className="w-5 h-5" />
            Shopping Cart ({itemCount} items)
          </SheetTitle>
          <SheetDescription>
            Review your items and proceed to checkout
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto py-4">
          {items.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <CartIcon className="w-12 h-12 text-gray-400 mb-4" />
              <h3 className="font-medium text-lg mb-2">Your cart is empty</h3>
              <p className="text-muted-foreground text-sm">
                Add some products to get started
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Cart Items */}
              <AnimatePresence>
                {items.map((item) => (
                  <CartItemComponent key={item.id} item={item} />
                ))}
              </AnimatePresence>

              <Separator />

              {/* Vendor Groups Summary */}
              <div className="space-y-2">
                <h4 className="font-medium text-sm">Order Summary by Vendor</h4>
                {Object.values(vendorGroups).map((group) => {
                  const vendorTotal = group.items.reduce(
                    (sum, item) => sum + (item.product.price * item.quantity), 
                    0
                  );
                  return (
                    <div key={group.vendor.id} className="flex justify-between text-sm">
                      <span>{group.vendor.name}</span>
                      <span>₦{vendorTotal.toLocaleString()}</span>
                    </div>
                  );
                })}
              </div>

              <Separator />

              {/* Total */}
              <div className="flex justify-between items-center font-semibold">
                <span>Total</span>
                <span className="text-lg">₦{subtotal.toLocaleString()}</span>
              </div>

              <Separator />

              {/* Customer Information Form */}
              <div className="space-y-4">
                <h4 className="font-medium text-sm">Customer Information</h4>
                
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="customer-name" className="text-xs">
                      Full Name *
                    </Label>
                    <Input
                      id="customer-name"
                      placeholder="Enter your full name"
                      value={customerInfo.name}
                      onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="customer-phone" className="text-xs">
                      Phone Number *
                    </Label>
                    <Input
                      id="customer-phone"
                      placeholder="+234 xxx xxx xxxx"
                      value={customerInfo.phone}
                      onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="customer-email" className="text-xs">
                      Email (Optional)
                    </Label>
                    <Input
                      id="customer-email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={customerInfo.email}
                      onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="customer-address" className="text-xs">
                      Delivery Address (Optional)
                    </Label>
                    <Input
                      id="customer-address"
                      placeholder="Enter delivery address"
                      value={customerInfo.address}
                      onChange={(e) => setCustomerInfo(prev => ({ ...prev, address: e.target.value }))}
                      className="mt-1"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Checkout Button */}
        {items.length > 0 && (
          <div className="border-t pt-4">
            <Button
              className="w-full"
              onClick={handleCheckout}
              disabled={isCheckingOut || !customerInfo.name || !customerInfo.phone}
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              {isCheckingOut ? 'Processing...' : 'Checkout via WhatsApp'}
            </Button>
            <p className="text-xs text-muted-foreground text-center mt-2">
              You'll receive order confirmation via WhatsApp
            </p>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
};

export default ShoppingCart;
