const CallUsSection = () => {
  return (
    <section className="py-16 lg:py-20 bg-gradient-to-br from-theme-primary-light via-white to-theme-primary-light">
      <div className="container px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          {/* Enhanced Header */}
          <div className="text-center mb-12">
            <div className="inline-flex items-center rounded-full bg-theme-primary/10 border border-theme-primary-border px-4 py-2 text-sm font-medium text-theme-primary mb-6">
              📞 Expert Support
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Need Help Getting Started?
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Our virtual tour experts are ready to help you create stunning immersive experiences
            </p>
          </div>

          {/* Enhanced Contact Card */}
          <div className="bg-white rounded-2xl p-8 lg:p-12 border border-theme-primary-border shadow-xl">
            <div className="flex flex-col lg:flex-row items-center lg:items-start space-y-8 lg:space-y-0 lg:space-x-12">
              {/* Contact Info */}
              <div className="flex-1 text-center lg:text-left">
                <div className="w-20 h-20 bg-gradient-to-br from-theme-primary to-theme-primary-hover rounded-2xl flex items-center justify-center mx-auto lg:mx-0 mb-6 shadow-lg">
                  <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-3">Speak with Our Experts</h3>
                <p className="text-gray-600 mb-6 leading-relaxed">
                  Get personalized guidance on creating virtual tours that showcase your space perfectly.
                  Our team is available to discuss your project and provide expert recommendations.
                </p>
                <a
                  href="tel:+2349077776066"
                  className="inline-flex items-center justify-center px-8 py-4 bg-theme-primary hover:bg-theme-primary-hover text-theme-primary-foreground rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 text-lg"
                >
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
                  </svg>
                  +234 ************
                </a>
              </div>

              {/* Additional Info */}
              <div className="flex-1 space-y-6">
                <div className="bg-gray-50 rounded-xl p-6">
                  <h4 className="font-semibold text-gray-900 mb-3">🕒 Available Hours</h4>
                  <p className="text-gray-600">Monday - Friday: 9:00 AM - 6:00 PM (WAT)</p>
                  <p className="text-gray-600">Saturday: 10:00 AM - 4:00 PM (WAT)</p>
                </div>

                <div className="bg-gray-50 rounded-xl p-6">
                  <h4 className="font-semibold text-gray-900 mb-3">💬 What We'll Discuss</h4>
                  <ul className="text-gray-600 space-y-2">
                    <li>• Your project requirements</li>
                    <li>• Pricing and packages</li>
                    <li>• Timeline and delivery</li>
                    <li>• Technical specifications</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default CallUsSection
