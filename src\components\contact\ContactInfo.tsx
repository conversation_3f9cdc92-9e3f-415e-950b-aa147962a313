
import { Card, CardContent } from '@/components/ui/card';
import { Mail, Phone, MapPin, MessageCircle } from 'lucide-react';

const ContactInfo = () => {
  const contactInfo = [
    {
      icon: Mail,
      title: "Email Support",
      details: "<EMAIL>",
      description: "Get help within 24 hours",
      color: "text-blue-600"
    },
    {
      icon: Phone,
      title: "Phone Support",
      details: "+234 ************",
      description: "Mon-Fri, 9AM-6PM WAT",
      color: "text-theme-primary"
    },
    {
      icon: MessageCircle,
      title: "WhatsApp Business",
      details: "+234 ************",
      description: "Instant messaging support",
      color: "text-emerald-600"
    },
    {
      icon: MapPin,
      title: "Head Office",
      details: "No 3 Kwame Nkrumah Crescent",
      description: "Asokoro, Abuja, Nigeria",
      color: "text-purple-600"
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
      {contactInfo.map((info, index) => (
        <Card key={index} className="hover:shadow-lg transition-shadow text-center">
          <CardContent className="p-6">
            <info.icon className={`w-10 h-10 mx-auto mb-3 ${info.color}`} />
            <h3 className="font-semibold text-gray-900 mb-1">{info.title}</h3>
            <p className="text-gray-900 font-medium mb-1">{info.details}</p>
            <p className="text-sm text-gray-600">{info.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default ContactInfo;
