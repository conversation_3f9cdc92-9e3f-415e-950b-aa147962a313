
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const FAQ = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl">Frequently Asked Questions</CardTitle>
        <CardDescription>
          Quick answers to common questions about VirtualRealTour
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">How do I create my first virtual tour?</h4>
            <p className="text-sm text-gray-600 mb-4">
              Sign up for an account, upload your 360° images or videos, add interactive hotspots, 
              and publish your tour. Our step-by-step guide makes it easy.
            </p>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">What file formats do you support?</h4>
            <p className="text-sm text-gray-600 mb-4">
              We support JPEG, PNG, and WebP for images, and MP4, WebM, and QuickTime for videos. 
              Maximum file size is 100MB per file.
            </p>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">Can I use this on mobile devices?</h4>
            <p className="text-sm text-gray-600 mb-4">
              Yes! Our platform is fully responsive and optimized for mobile devices. 
              Your virtual tours work seamlessly on phones and tablets.
            </p>
          </div>
          <div>
            <h4 className="font-semibold text-gray-900 mb-2">Do you offer enterprise solutions?</h4>
            <p className="text-sm text-gray-600 mb-4">
              Yes, we provide custom enterprise solutions with advanced features, 
              dedicated support, and custom integrations. Contact our sales team.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default FAQ;
