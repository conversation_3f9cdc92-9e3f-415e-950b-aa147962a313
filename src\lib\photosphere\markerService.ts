/**
 * Marker Service for Photo Sphere Viewer
 * Handles interactive hotspots with e-commerce integration
 */

import type { MarkersPlugin } from '@photo-sphere-viewer/markers-plugin';
import type { Viewer } from '@photo-sphere-viewer/core';
import type { TourMarker } from './types';
import { toast } from 'sonner';

export interface MarkerClickData {
  marker: TourMarker;
  viewer: Viewer;
  event: Event;
}

export interface ProductData {
  id: string;
  name: string;
  price: number;
  currency?: string;
  image: string;
  description: string;
  whatsappMessage?: string;
  vendorId?: string;
  vendorPhone?: string;
}

export interface MarkerServiceOptions {
  onMarkerClick?: (data: MarkerClickData) => void;
  onProductClick?: (product: ProductData) => void;
  onNavigationClick?: (targetSceneId: string) => void;
  onInfoClick?: (title: string, content: string) => void;
  onLinkClick?: (url: string) => void;
  onMediaClick?: (mediaUrl: string, mediaType: string) => void;
  whatsappNumber?: string;
}

/**
 * Service for managing PSV markers with e-commerce integration
 */
export class MarkerService {
  private markersPlugin: MarkersPlugin | null = null;
  private viewer: Viewer | null = null;
  private options: MarkerServiceOptions;

  constructor(options: MarkerServiceOptions = {}) {
    this.options = {
      whatsappNumber: '2349077776066', // Default WhatsApp number
      ...options
    };
  }

  /**
   * Initialize marker service with PSV instance
   */
  initialize(viewer: Viewer, markersPlugin: MarkersPlugin): void {
    this.viewer = viewer;
    this.markersPlugin = markersPlugin;
    this.setupEventListeners();
  }

  /**
   * Add markers to the viewer
   */
  addMarkers(markers: TourMarker[]): void {
    if (!this.markersPlugin) {
      console.warn('Markers plugin not initialized');
      return;
    }

    const psvMarkers = markers.map(marker => this.convertToMarkerConfig(marker));
    this.markersPlugin.setMarkers(psvMarkers);
  }

  /**
   * Add a single marker
   */
  addMarker(marker: TourMarker): void {
    if (!this.markersPlugin) {
      console.warn('Markers plugin not initialized');
      return;
    }

    const psvMarker = this.convertToMarkerConfig(marker);
    this.markersPlugin.addMarker(psvMarker);
  }

  /**
   * Remove a marker by ID
   */
  removeMarker(markerId: string): void {
    if (!this.markersPlugin) return;
    
    try {
      this.markersPlugin.removeMarker(markerId);
    } catch (error) {
      console.warn('Failed to remove marker:', error);
    }
  }

  /**
   * Update marker data
   */
  updateMarker(markerId: string, updates: Partial<TourMarker>): void {
    if (!this.markersPlugin) return;

    try {
      const existingMarker = this.markersPlugin.getMarker(markerId);
      if (existingMarker) {
        const updatedMarker = { ...existingMarker.data, ...updates };
        this.markersPlugin.updateMarker(markerId, this.convertToMarkerConfig(updatedMarker));
      }
    } catch (error) {
      console.warn('Failed to update marker:', error);
    }
  }

  /**
   * Clear all markers
   */
  clearMarkers(): void {
    if (!this.markersPlugin) return;
    this.markersPlugin.clearMarkers();
  }

  /**
   * Get marker by ID
   */
  getMarker(markerId: string): TourMarker | null {
    if (!this.markersPlugin) return null;
    
    try {
      const marker = this.markersPlugin.getMarker(markerId);
      return marker?.data || null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get all markers
   */
  getAllMarkers(): TourMarker[] {
    if (!this.markersPlugin) return [];
    
    return this.markersPlugin.getMarkers().map(marker => marker.data).filter(Boolean);
  }

  /**
   * Convert TourMarker to PSV marker configuration
   */
  private convertToMarkerConfig(marker: TourMarker): any {
    return {
      id: marker.id,
      position: marker.position,
      html: marker.html || this.generateMarkerHTML(marker),
      className: marker.className || `psv-marker psv-marker-${marker.type}`,
      style: {
        cursor: 'pointer',
        ...marker.style
      },
      tooltip: marker.tooltip || marker.title,
      data: marker,
      visible: marker.visible !== false
    };
  }

  /**
   * Generate HTML for marker based on type
   */
  private generateMarkerHTML(marker: TourMarker): string {
    const baseClasses = 'psv-marker-content';
    const typeClasses = `psv-marker-${marker.type}`;
    
    switch (marker.type) {
      case 'product':
        return this.generateProductMarkerHTML(marker);
      case 'navigation':
        return this.generateNavigationMarkerHTML(marker);
      case 'info':
        return this.generateInfoMarkerHTML(marker);
      case 'link':
        return this.generateLinkMarkerHTML(marker);
      case 'media':
        return this.generateMediaMarkerHTML(marker);
      default:
        return this.generateDefaultMarkerHTML(marker);
    }
  }

  /**
   * Generate product marker HTML with e-commerce styling
   */
  private generateProductMarkerHTML(marker: TourMarker): string {
    const product = marker.productData;
    if (!product) return this.generateDefaultMarkerHTML(marker);

    return `
      <div class="psv-product-marker">
        <div class="psv-product-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"/>
            <line x1="3" y1="6" x2="21" y2="6"/>
            <path d="M16 10a4 4 0 0 1-8 0"/>
          </svg>
        </div>
        <div class="psv-product-info">
          <div class="psv-product-name">${product.name}</div>
          <div class="psv-product-price">${product.currency || '₦'}${product.price.toLocaleString()}</div>
        </div>
        <div class="psv-product-pulse"></div>
      </div>
    `;
  }

  /**
   * Generate navigation marker HTML
   */
  private generateNavigationMarkerHTML(marker: TourMarker): string {
    return `
      <div class="psv-navigation-marker">
        <div class="psv-navigation-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
            <polyline points="9,22 9,12 15,12 15,22"/>
          </svg>
        </div>
        <div class="psv-navigation-label">${marker.title || 'Go to scene'}</div>
        <div class="psv-navigation-pulse"></div>
      </div>
    `;
  }

  /**
   * Generate info marker HTML
   */
  private generateInfoMarkerHTML(marker: TourMarker): string {
    return `
      <div class="psv-info-marker">
        <div class="psv-info-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <circle cx="12" cy="12" r="10"/>
            <line x1="12" y1="16" x2="12" y2="12"/>
            <line x1="12" y1="8" x2="12.01" y2="8"/>
          </svg>
        </div>
        <div class="psv-info-label">${marker.title || 'Information'}</div>
      </div>
    `;
  }

  /**
   * Generate link marker HTML
   */
  private generateLinkMarkerHTML(marker: TourMarker): string {
    return `
      <div class="psv-link-marker">
        <div class="psv-link-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
            <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
          </svg>
        </div>
        <div class="psv-link-label">${marker.title || 'External link'}</div>
      </div>
    `;
  }

  /**
   * Generate media marker HTML
   */
  private generateMediaMarkerHTML(marker: TourMarker): string {
    return `
      <div class="psv-media-marker">
        <div class="psv-media-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polygon points="23 7 16 12 23 17 23 7"/>
            <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
          </svg>
        </div>
        <div class="psv-media-label">${marker.title || 'Media'}</div>
      </div>
    `;
  }

  /**
   * Generate default marker HTML
   */
  private generateDefaultMarkerHTML(marker: TourMarker): string {
    return `
      <div class="psv-default-marker">
        <div class="psv-default-icon">📍</div>
        <div class="psv-default-label">${marker.title || 'Marker'}</div>
      </div>
    `;
  }

  /**
   * Setup event listeners for marker interactions
   */
  private setupEventListeners(): void {
    if (!this.markersPlugin) return;

    this.markersPlugin.addEventListener('select-marker', (e: any) => {
      const marker: TourMarker = e.marker.data;
      this.handleMarkerClick(marker, e);
    });

    this.markersPlugin.addEventListener('over-marker', (e: any) => {
      const marker: TourMarker = e.marker.data;
      this.handleMarkerHover(marker, true);
    });

    this.markersPlugin.addEventListener('leave-marker', (e: any) => {
      const marker: TourMarker = e.marker.data;
      this.handleMarkerHover(marker, false);
    });
  }

  /**
   * Handle marker click events
   */
  private handleMarkerClick(marker: TourMarker, event: any): void {
    // Call custom click handler if provided
    if (this.options.onMarkerClick && this.viewer) {
      this.options.onMarkerClick({
        marker,
        viewer: this.viewer,
        event: event.originalEvent
      });
    }

    // Handle specific marker types
    switch (marker.type) {
      case 'product':
        this.handleProductMarkerClick(marker);
        break;
      case 'navigation':
        this.handleNavigationMarkerClick(marker);
        break;
      case 'info':
        this.handleInfoMarkerClick(marker);
        break;
      case 'link':
        this.handleLinkMarkerClick(marker);
        break;
      case 'media':
        this.handleMediaMarkerClick(marker);
        break;
    }

    // Execute custom onClick if provided
    if (marker.onClick && this.viewer) {
      marker.onClick(marker, this.viewer);
    }
  }

  /**
   * Handle product marker clicks
   */
  private handleProductMarkerClick(marker: TourMarker): void {
    if (!marker.productData) return;

    const product = marker.productData;
    
    if (this.options.onProductClick) {
      this.options.onProductClick(product);
    } else {
      // Default WhatsApp integration
      const message = product.whatsappMessage || 
        `Hi! I'm interested in ${product.name} (${product.currency || '₦'}${product.price.toLocaleString()}) from your virtual tour.`;
      
      const whatsappUrl = `https://wa.me/${this.options.whatsappNumber}?text=${encodeURIComponent(message)}`;
      window.open(whatsappUrl, '_blank');
      
      toast.success(`Opening WhatsApp for ${product.name}`);
    }
  }

  /**
   * Handle navigation marker clicks
   */
  private handleNavigationMarkerClick(marker: TourMarker): void {
    if (!marker.targetSceneId) return;

    if (this.options.onNavigationClick) {
      this.options.onNavigationClick(marker.targetSceneId);
    }
  }

  /**
   * Handle info marker clicks
   */
  private handleInfoMarkerClick(marker: TourMarker): void {
    if (this.options.onInfoClick) {
      this.options.onInfoClick(marker.title || 'Information', marker.content || '');
    } else {
      toast.info(marker.content || marker.title || 'Information');
    }
  }

  /**
   * Handle link marker clicks
   */
  private handleLinkMarkerClick(marker: TourMarker): void {
    if (!marker.linkUrl) return;

    if (this.options.onLinkClick) {
      this.options.onLinkClick(marker.linkUrl);
    } else {
      window.open(marker.linkUrl, marker.linkTarget || '_blank');
      toast.success('Opening external link');
    }
  }

  /**
   * Handle media marker clicks
   */
  private handleMediaMarkerClick(marker: TourMarker): void {
    if (!marker.mediaUrl) return;

    if (this.options.onMediaClick) {
      this.options.onMediaClick(marker.mediaUrl, marker.mediaType || 'unknown');
    } else {
      window.open(marker.mediaUrl, '_blank');
      toast.success('Opening media');
    }
  }

  /**
   * Handle marker hover events
   */
  private handleMarkerHover(marker: TourMarker, isEntering: boolean): void {
    if (isEntering && marker.onEnter && this.viewer) {
      marker.onEnter(marker, this.viewer);
    } else if (!isEntering && marker.onLeave && this.viewer) {
      marker.onLeave(marker, this.viewer);
    }
  }

  /**
   * Cleanup marker service
   */
  destroy(): void {
    if (this.markersPlugin) {
      this.markersPlugin.removeAllListeners();
      this.clearMarkers();
    }
    this.markersPlugin = null;
    this.viewer = null;
  }
}
