/**
 * PSV Error Boundary Component
 * React Error Boundary specifically designed for Photo Sphere Viewer errors
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  RefreshCw, 
  Home, 
  Bug, 
  Monitor,
  Wifi,
  HardDrive,
  Zap
} from 'lucide-react';
import { toast } from 'sonner';

import { psvErrorHandler, PSVErrorCode, type PSVErrorDetails } from '@/lib/photosphere/errorHandling';

interface PSVErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: PSVErrorDetails) => void;
  enableRecovery?: boolean;
  showErrorDetails?: boolean;
  className?: string;
}

interface PSVErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorDetails: PSVErrorDetails | null;
  retryCount: number;
  isRecovering: boolean;
}

class PSVErrorBoundary extends Component<PSVErrorBoundaryProps, PSVErrorBoundaryState> {
  private maxRetries = 3;
  private retryDelay = 1000;

  constructor(props: PSVErrorBoundaryProps) {
    super(props);
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorDetails: null,
      retryCount: 0,
      isRecovering: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<PSVErrorBoundaryState> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('PSV Error Boundary caught an error:', error, errorInfo);
    
    // Determine error code based on error message and stack
    const errorCode = this.determineErrorCode(error);
    
    // Create detailed error information
    const errorDetails: PSVErrorDetails = {
      code: errorCode,
      message: error.message,
      originalError: error,
      context: {
        componentStack: errorInfo.componentStack,
        errorBoundary: 'PSVErrorBoundary',
        retryCount: this.state.retryCount
      },
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      stack: error.stack,
      recoverable: this.isRecoverable(errorCode),
      retryable: this.isRetryable(errorCode)
    };

    this.setState({
      errorInfo,
      errorDetails
    });

    // Handle error through PSV error handler
    psvErrorHandler.handleError(error, errorCode, {
      componentStack: errorInfo.componentStack,
      errorBoundary: true
    });

    // Call custom error handler
    if (this.props.onError) {
      this.props.onError(errorDetails);
    }

    // Attempt automatic recovery if enabled
    if (this.props.enableRecovery && errorDetails.recoverable) {
      this.attemptRecovery();
    }
  }

  /**
   * Determine PSV error code from error
   */
  private determineErrorCode(error: Error): PSVErrorCode {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    if (message.includes('webgl') || message.includes('context')) {
      return PSVErrorCode.WEBGL_CONTEXT_LOST;
    }
    
    if (message.includes('panorama') || message.includes('image')) {
      return PSVErrorCode.PANORAMA_LOAD_FAILED;
    }
    
    if (message.includes('network') || message.includes('fetch')) {
      return PSVErrorCode.NETWORK_ERROR;
    }
    
    if (message.includes('memory') || message.includes('heap')) {
      return PSVErrorCode.MEMORY_LIMIT_EXCEEDED;
    }
    
    if (message.includes('plugin')) {
      return PSVErrorCode.PLUGIN_ERROR;
    }
    
    if (message.includes('container') || message.includes('element')) {
      return PSVErrorCode.CONTAINER_NOT_FOUND;
    }
    
    return PSVErrorCode.UNKNOWN_ERROR;
  }

  /**
   * Check if error is recoverable
   */
  private isRecoverable(code: PSVErrorCode): boolean {
    const recoverableCodes = [
      PSVErrorCode.WEBGL_CONTEXT_LOST,
      PSVErrorCode.PLUGIN_ERROR,
      PSVErrorCode.MEMORY_LIMIT_EXCEEDED
    ];
    
    return recoverableCodes.includes(code);
  }

  /**
   * Check if error is retryable
   */
  private isRetryable(code: PSVErrorCode): boolean {
    const retryableCodes = [
      PSVErrorCode.PANORAMA_LOAD_FAILED,
      PSVErrorCode.NETWORK_ERROR,
      PSVErrorCode.PLUGIN_ERROR
    ];
    
    return retryableCodes.includes(code);
  }

  /**
   * Attempt automatic recovery
   */
  private async attemptRecovery(): Promise<void> {
    if (this.state.isRecovering) return;

    this.setState({ isRecovering: true });

    try {
      // Wait a bit before attempting recovery
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Clear error state to trigger re-render
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorDetails: null,
        isRecovering: false
      });

      toast.success('Recovered from error automatically');
    } catch (recoveryError) {
      console.error('Recovery failed:', recoveryError);
      this.setState({ isRecovering: false });
      toast.error('Automatic recovery failed');
    }
  }

  /**
   * Manual retry
   */
  private handleRetry = async (): Promise<void> => {
    if (this.state.retryCount >= this.maxRetries) {
      toast.error(`Maximum retry attempts (${this.maxRetries}) exceeded`);
      return;
    }

    this.setState({ isRecovering: true });

    try {
      // Exponential backoff
      const delay = this.retryDelay * Math.pow(2, this.state.retryCount);
      await new Promise(resolve => setTimeout(resolve, delay));

      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorDetails: null,
        retryCount: this.state.retryCount + 1,
        isRecovering: false
      });

      toast.success('Retrying...');
    } catch (error) {
      console.error('Retry failed:', error);
      this.setState({ isRecovering: false });
      toast.error('Retry failed');
    }
  };

  /**
   * Reset error boundary
   */
  private handleReset = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorDetails: null,
      retryCount: 0,
      isRecovering: false
    });
  };

  /**
   * Get error icon based on error code
   */
  private getErrorIcon(code: PSVErrorCode): React.ReactNode {
    switch (code) {
      case PSVErrorCode.WEBGL_CONTEXT_LOST:
      case PSVErrorCode.WEBGL_NOT_SUPPORTED:
        return <Monitor className="w-6 h-6" />;
      case PSVErrorCode.NETWORK_ERROR:
      case PSVErrorCode.CORS_ERROR:
        return <Wifi className="w-6 h-6" />;
      case PSVErrorCode.MEMORY_LIMIT_EXCEEDED:
        return <HardDrive className="w-6 h-6" />;
      case PSVErrorCode.PLUGIN_ERROR:
        return <Zap className="w-6 h-6" />;
      default:
        return <AlertTriangle className="w-6 h-6" />;
    }
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyMessage(code: PSVErrorCode): string {
    const messages = {
      [PSVErrorCode.WEBGL_CONTEXT_LOST]: 'Graphics context was lost. This usually happens when switching between tabs or when the system is under heavy load.',
      [PSVErrorCode.WEBGL_NOT_SUPPORTED]: 'Your browser doesn\'t support 3D graphics required for the virtual tour.',
      [PSVErrorCode.PANORAMA_LOAD_FAILED]: 'Failed to load the 360° image. This might be due to a network issue or corrupted file.',
      [PSVErrorCode.NETWORK_ERROR]: 'Network connection issue prevented loading the tour content.',
      [PSVErrorCode.MEMORY_LIMIT_EXCEEDED]: 'The tour requires more memory than available. Try closing other browser tabs.',
      [PSVErrorCode.PLUGIN_ERROR]: 'A tour feature encountered an error. Some functionality may be limited.',
      [PSVErrorCode.CONTAINER_NOT_FOUND]: 'The tour container element was not found or is invalid.',
      [PSVErrorCode.UNKNOWN_ERROR]: 'An unexpected error occurred while loading the virtual tour.'
    };

    return messages[code] || messages[PSVErrorCode.UNKNOWN_ERROR];
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { error, errorDetails, isRecovering, retryCount } = this.state;
      const errorCode = errorDetails?.code || PSVErrorCode.UNKNOWN_ERROR;
      const canRetry = this.isRetryable(errorCode) && retryCount < this.maxRetries;
      const isRecoverable = this.isRecoverable(errorCode);

      return (
        <div className={`psv-error-boundary ${this.props.className || ''}`}>
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-destructive/10 rounded-full text-destructive">
                  {this.getErrorIcon(errorCode)}
                </div>
                <div>
                  <CardTitle className="text-lg">Virtual Tour Error</CardTitle>
                  <p className="text-sm text-muted-foreground">
                    Something went wrong while loading the tour
                  </p>
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Error Message */}
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {this.getUserFriendlyMessage(errorCode)}
                </AlertDescription>
              </Alert>

              {/* Error Details */}
              {this.props.showErrorDetails && errorDetails && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{errorCode}</Badge>
                    <Badge variant={isRecoverable ? 'default' : 'secondary'}>
                      {isRecoverable ? 'Recoverable' : 'Non-recoverable'}
                    </Badge>
                    {canRetry && (
                      <Badge variant="secondary">Retryable</Badge>
                    )}
                  </div>
                  
                  <details className="text-sm">
                    <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                      Technical Details
                    </summary>
                    <div className="mt-2 p-3 bg-muted rounded text-xs font-mono">
                      <div><strong>Error:</strong> {error?.message}</div>
                      <div><strong>Code:</strong> {errorCode}</div>
                      <div><strong>Time:</strong> {errorDetails.timestamp}</div>
                      {retryCount > 0 && (
                        <div><strong>Retry Count:</strong> {retryCount}</div>
                      )}
                    </div>
                  </details>
                </div>
              )}

              {/* Recovery Status */}
              {isRecovering && (
                <Alert>
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <AlertDescription>
                    Attempting to recover from the error...
                  </AlertDescription>
                </Alert>
              )}

              {/* Action Buttons */}
              <div className="flex flex-wrap gap-2">
                {canRetry && !isRecovering && (
                  <Button onClick={this.handleRetry} disabled={isRecovering}>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Retry ({this.maxRetries - retryCount} attempts left)
                  </Button>
                )}
                
                <Button variant="outline" onClick={this.handleReset}>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reset
                </Button>
                
                <Button 
                  variant="outline" 
                  onClick={() => window.location.reload()}
                >
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Reload Page
                </Button>
                
                <Button 
                  variant="ghost" 
                  onClick={() => window.history.back()}
                >
                  <Home className="w-4 h-4 mr-2" />
                  Go Back
                </Button>
              </div>

              {/* Support Information */}
              <div className="text-xs text-muted-foreground border-t pt-4">
                <p>
                  If this problem persists, please contact support with error code: <code>{errorCode}</code>
                </p>
                <p className="mt-1">
                  Time: {errorDetails?.timestamp}
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default PSVErrorBoundary;
