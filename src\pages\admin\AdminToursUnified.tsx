/**
 * Unified Admin Tours Management
 * Consolidates tour creation, editing, import, management, and analytics into a single interface
 */

import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MapPin, 
  Plus, 
  Search, 
  Filter,
  Eye,
  Edit,
  Star,
  Download,
  Upload,
  BarChart3,
  Settings,
  Globe,
  Image as ImageIcon,
  CheckCircle,
  Clock,
  XCircle
} from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';
import { supabase, Tour } from '@/lib/supabase';
import { toast } from 'sonner';

const AdminToursUnified = () => {
  const [activeTab, setActiveTab] = useState('all-tours');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');

  // Fetch tours
  const { data: tours = [], isLoading, refetch } = useQuery({
    queryKey: ['admin-tours'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tours')
        .select(`
          *,
          profiles:user_id (
            full_name,
            email
          )
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data as Tour[];
    },
  });

  // Filter tours
  const filteredTours = tours.filter(tour => {
    const matchesSearch = tour.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tour.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tour.location?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || tour.status === statusFilter;
    const matchesCategory = categoryFilter === 'all' || tour.category === categoryFilter;
    
    return matchesSearch && matchesStatus && matchesCategory;
  });

  const getStatusBadge = (status: string) => {
    const variants = {
      'published': { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      'draft': { variant: 'secondary' as const, icon: Clock, color: 'text-yellow-600' },
      'processing': { variant: 'outline' as const, icon: Clock, color: 'text-blue-600' },
      'archived': { variant: 'outline' as const, icon: XCircle, color: 'text-gray-600' }
    };
    
    const config = variants[status as keyof typeof variants] || variants.draft;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`w-3 h-3 ${config.color}`} />
        {status}
      </Badge>
    );
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Tours Management</h1>
            <p className="text-muted-foreground">
              Create, edit, import, and manage all virtual tours from one place
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Tour
            </Button>
          </div>
        </div>

        {/* Tours Management Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-5">
            <TabsTrigger value="all-tours" className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              <span className="hidden sm:inline">All Tours</span>
            </TabsTrigger>
            <TabsTrigger value="create" className="flex items-center gap-2">
              <Plus className="w-4 h-4" />
              <span className="hidden sm:inline">Create</span>
            </TabsTrigger>
            <TabsTrigger value="import" className="flex items-center gap-2">
              <Download className="w-4 h-4" />
              <span className="hidden sm:inline">Import</span>
            </TabsTrigger>
            <TabsTrigger value="featured" className="flex items-center gap-2">
              <Star className="w-4 h-4" />
              <span className="hidden sm:inline">Featured</span>
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <BarChart3 className="w-4 h-4" />
              <span className="hidden sm:inline">Analytics</span>
            </TabsTrigger>
          </TabsList>

          {/* All Tours Tab */}
          <TabsContent value="all-tours" className="space-y-6">
            {/* Filters */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        placeholder="Search tours..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="processing">Processing</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Filter by category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      <SelectItem value="property">Property</SelectItem>
                      <SelectItem value="education">Education</SelectItem>
                      <SelectItem value="hospitality">Hospitality</SelectItem>
                      <SelectItem value="tourism">Tourism</SelectItem>
                      <SelectItem value="culture">Culture</SelectItem>
                      <SelectItem value="commercial">Commercial</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Tours Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredTours.map((tour) => (
                <Card key={tour.id} className="group hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="space-y-1 flex-1 min-w-0">
                        <h3 className="font-semibold truncate">{tour.title}</h3>
                        <p className="text-sm text-muted-foreground truncate">
                          {tour.location || 'No location'}
                        </p>
                      </div>
                      {getStatusBadge(tour.status)}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Thumbnail */}
                    <div className="aspect-video bg-muted rounded-md flex items-center justify-center overflow-hidden">
                      {tour.thumbnail_url ? (
                        <img 
                          src={tour.thumbnail_url} 
                          alt={tour.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <ImageIcon className="w-8 h-8 text-muted-foreground" />
                      )}
                    </div>

                    {/* Tour Info */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Category:</span>
                        <Badge variant="outline">{tour.category}</Badge>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-muted-foreground">Views:</span>
                        <span>{tour.views || 0}</span>
                      </div>
                      {tour.featured && (
                        <div className="flex items-center gap-1 text-sm text-yellow-600">
                          <Star className="w-3 h-3 fill-current" />
                          Featured
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        <Eye className="w-4 h-4 mr-1" />
                        View
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        <Edit className="w-4 h-4 mr-1" />
                        Edit
                      </Button>
                      <Button size="sm" variant="outline">
                        <Settings className="w-4 h-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {filteredTours.length === 0 && (
              <Card>
                <CardContent className="py-12 text-center">
                  <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No tours found</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm || statusFilter !== 'all' || categoryFilter !== 'all'
                      ? 'Try adjusting your filters'
                      : 'Create your first tour to get started'
                    }
                  </p>
                  <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    Create Tour
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Create Tour Tab */}
          <TabsContent value="create" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="w-5 h-5" />
                  Create New Tour
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-blue-500">
                    <CardContent className="pt-6 text-center">
                      <Upload className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                      <h3 className="font-semibold mb-2">Upload Images</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Upload 360° images and create tour with our native editor
                      </p>
                      <Button className="w-full">Start Upload</Button>
                    </CardContent>
                  </Card>

                  <Card className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-green-500">
                    <CardContent className="pt-6 text-center">
                      <Globe className="w-12 h-12 text-green-600 mx-auto mb-4" />
                      <h3 className="font-semibold mb-2">CloudPano</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Create with CloudPano's advanced editor (embedded)
                      </p>
                      <Button className="w-full" variant="outline">Launch Editor</Button>
                    </CardContent>
                  </Card>

                  <Card className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-purple-500">
                    <CardContent className="pt-6 text-center">
                      <Settings className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                      <h3 className="font-semibold mb-2">CommonNinja</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Use CommonNinja widget system (API-based)
                      </p>
                      <Button className="w-full" variant="outline">Open Editor</Button>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>

            {/* Quick Tour Creation Form */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Tour Setup</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Tour Title</label>
                    <Input placeholder="Enter tour title" />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Category</label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="property">Property</SelectItem>
                        <SelectItem value="education">Education</SelectItem>
                        <SelectItem value="hospitality">Hospitality</SelectItem>
                        <SelectItem value="tourism">Tourism</SelectItem>
                        <SelectItem value="culture">Culture</SelectItem>
                        <SelectItem value="commercial">Commercial</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Location</label>
                    <Input placeholder="Enter location" />
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium">Business Name</label>
                    <Input placeholder="Enter business name" />
                  </div>
                </div>
                <div className="mt-4">
                  <label className="text-sm font-medium">Description</label>
                  <Input placeholder="Enter tour description" className="mt-2" />
                </div>
                <div className="flex gap-2 mt-6">
                  <Button className="flex-1">
                    <Plus className="w-4 h-4 mr-2" />
                    Create & Edit
                  </Button>
                  <Button variant="outline" className="flex-1">
                    Save as Draft
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Import Tab */}
          <TabsContent value="import" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Download className="w-5 h-5" />
                  Import Tours from External Platforms
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <Card className="border-2 hover:border-blue-500 transition-colors">
                    <CardContent className="pt-6 text-center">
                      <Globe className="w-10 h-10 text-blue-600 mx-auto mb-3" />
                      <h3 className="font-semibold mb-2">WPVR Tours</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Import tours from WordPress WPVR plugin
                      </p>
                      <div className="space-y-2 mb-4">
                        <div className="flex justify-between text-xs">
                          <span>Available:</span>
                          <span className="font-medium">12 tours</span>
                        </div>
                        <div className="flex justify-between text-xs">
                          <span>Last sync:</span>
                          <span className="font-medium">2 hours ago</span>
                        </div>
                      </div>
                      <Button className="w-full">Import WPVR</Button>
                    </CardContent>
                  </Card>

                  <Card className="border-2 hover:border-green-500 transition-colors">
                    <CardContent className="pt-6 text-center">
                      <Settings className="w-10 h-10 text-green-600 mx-auto mb-3" />
                      <h3 className="font-semibold mb-2">CloudPano</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Import existing CloudPano tours
                      </p>
                      <div className="space-y-2 mb-4">
                        <div className="flex justify-between text-xs">
                          <span>Status:</span>
                          <span className="font-medium text-red-600">Not connected</span>
                        </div>
                        <div className="flex justify-between text-xs">
                          <span>Available:</span>
                          <span className="font-medium">0 tours</span>
                        </div>
                      </div>
                      <Button className="w-full" variant="outline">Connect & Import</Button>
                    </CardContent>
                  </Card>

                  <Card className="border-2 hover:border-purple-500 transition-colors">
                    <CardContent className="pt-6 text-center">
                      <Upload className="w-10 h-10 text-purple-600 mx-auto mb-3" />
                      <h3 className="font-semibold mb-2">Manual Import</h3>
                      <p className="text-sm text-muted-foreground mb-4">
                        Import from URL or embed code
                      </p>
                      <div className="space-y-2 mb-4">
                        <Input placeholder="Tour URL or embed code" className="text-xs" />
                      </div>
                      <Button className="w-full" variant="outline">Import URL</Button>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
            </Card>

            {/* Import History */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Imports</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { name: 'Lagos Hotel Virtual Tour', source: 'WPVR', date: '2 hours ago', status: 'success' },
                    { name: 'University Campus Tour', source: 'CloudPano', date: '1 day ago', status: 'success' },
                    { name: 'Restaurant 360° Experience', source: 'Manual', date: '2 days ago', status: 'failed' }
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium">{item.name}</h4>
                        <p className="text-sm text-muted-foreground">{item.source} • {item.date}</p>
                      </div>
                      <Badge variant={item.status === 'success' ? 'default' : 'destructive'}>
                        {item.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Featured Tab */}
          <TabsContent value="featured" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="w-5 h-5" />
                  Featured Tours Management
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-6">
                  Manage which tours appear as featured on the homepage and showcase pages.
                </p>

                {/* Featured Tours Grid */}
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Currently Featured Tours</h3>
                    <Button size="sm">
                      <Plus className="w-4 h-4 mr-2" />
                      Add Featured Tour
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {tours.filter(tour => tour.featured).map((tour) => (
                      <Card key={tour.id} className="relative group">
                        <CardContent className="pt-4">
                          <div className="aspect-video bg-muted rounded-md mb-3 overflow-hidden">
                            {tour.thumbnail_url ? (
                              <img
                                src={tour.thumbnail_url}
                                alt={tour.title}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <ImageIcon className="w-8 h-8 text-muted-foreground" />
                              </div>
                            )}
                          </div>
                          <h4 className="font-medium truncate">{tour.title}</h4>
                          <p className="text-sm text-muted-foreground truncate">{tour.location}</p>
                          <div className="flex items-center justify-between mt-3">
                            <Badge variant="default" className="flex items-center gap-1">
                              <Star className="w-3 h-3 fill-current" />
                              Featured
                            </Badge>
                            <Button size="sm" variant="outline">
                              Remove
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {tours.filter(tour => tour.featured).length === 0 && (
                    <div className="text-center py-8 border-2 border-dashed border-muted rounded-lg">
                      <Star className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">No Featured Tours</h3>
                      <p className="text-muted-foreground mb-4">
                        Select tours to feature on your homepage and showcase pages
                      </p>
                      <Button>
                        <Plus className="w-4 h-4 mr-2" />
                        Add Featured Tour
                      </Button>
                    </div>
                  )}
                </div>

                {/* Available Tours to Feature */}
                <div className="space-y-4 mt-8">
                  <h3 className="text-lg font-semibold">Available Tours</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {tours.filter(tour => !tour.featured && tour.status === 'published').slice(0, 6).map((tour) => (
                      <Card key={tour.id} className="hover:shadow-md transition-shadow">
                        <CardContent className="pt-4">
                          <div className="aspect-video bg-muted rounded-md mb-3 overflow-hidden">
                            {tour.thumbnail_url ? (
                              <img
                                src={tour.thumbnail_url}
                                alt={tour.title}
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center">
                                <ImageIcon className="w-8 h-8 text-muted-foreground" />
                              </div>
                            )}
                          </div>
                          <h4 className="font-medium truncate">{tour.title}</h4>
                          <p className="text-sm text-muted-foreground truncate">{tour.location}</p>
                          <div className="flex items-center justify-between mt-3">
                            <Badge variant="outline">{tour.category}</Badge>
                            <Button size="sm">
                              <Star className="w-4 h-4 mr-1" />
                              Feature
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics Tab */}
          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="w-5 h-5" />
                  Tour Analytics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  View detailed analytics and performance metrics for all tours.
                </p>
                {/* Analytics will be implemented here */}
                <div className="text-center py-8">
                  <BarChart3 className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">Tour analytics coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminToursUnified;
