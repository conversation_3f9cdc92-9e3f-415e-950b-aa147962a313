/**
 * Product Hotspot Editor Component
 * Visual editor for placing product hotspots within tour images
 */

import './ProductHotspotEditor.css';
import './ProductHotspotEditor.position.css';
import { useState, useRef, useCallback, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Plus, 
  Edit, 
  Trash2, 
  ShoppingCart, 
  MapPin,
  Eye,
  Save,
  Undo,
  Target
} from 'lucide-react';
import { toast } from 'sonner';
import { supabase } from '@/lib/supabase';

interface Product {
  id: string;
  title: string;
  description: string;
  price: number;
  image_url: string;
  category: string;
  vendor_id: string;
}

interface Hotspot {
  id: string;
  x: number;
  y: number;
  label: string;
  productId: string;
  product?: Product;
}

interface ProductHotspotEditorProps {
  tourId: string;
  imageUrl: string;
  vendorId?: string;
  onHotspotsChange?: (hotspots: Hotspot[]) => void;
}

const ProductHotspotEditor = ({ 
  tourId, 
  imageUrl, 
  vendorId,
  onHotspotsChange 
}: ProductHotspotEditorProps) => {
  const [hotspots, setHotspots] = useState<Hotspot[]>([]);
  const [selectedHotspot, setSelectedHotspot] = useState<Hotspot | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isProductDialogOpen, setIsProductDialogOpen] = useState(false);
  const imageRef = useRef<HTMLImageElement>(null);

  // Fetch vendor products
  const { data: products = [], isLoading: productsLoading } = useQuery({
    queryKey: ['vendor-products', vendorId],
    queryFn: async () => {
      if (!vendorId) return [];
      
      const { data, error } = await supabase
        .from('products')
        .select('*')
        .eq('vendor_id', vendorId)
        .eq('status', 'active')
        .order('title', { ascending: true });

      if (error) throw error;
      return data as Product[];
    },
    enabled: !!vendorId,
  });

  // Position hotspots using data attributes
  useEffect(() => {
    const hotspotElements = document.querySelectorAll('.hotspotMarker');
    hotspotElements.forEach((element) => {
      const x = element.getAttribute('data-hotspot-x');
      const y = element.getAttribute('data-hotspot-y');
      if (x && y) {
        (element as HTMLElement).style.left = `${x}%`;
        (element as HTMLElement).style.top = `${y}%`;
      }
    });
  }, [hotspots]);

  // Handle image click to add hotspot
  const handleImageClick = useCallback((e: React.MouseEvent<HTMLImageElement>) => {
    if (!isEditing) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width) * 100;
    const y = ((e.clientY - rect.top) / rect.height) * 100;

    const newHotspot: Hotspot = {
      id: `hotspot-${Date.now()}`,
      x,
      y,
      label: 'New Product',
      productId: '',
    };

    setHotspots(prev => [...prev, newHotspot]);
    setSelectedHotspot(newHotspot);
    setIsProductDialogOpen(true);
  }, [isEditing]);

  // Update hotspot with product
  const updateHotspot = (hotspotId: string, productId: string) => {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    setHotspots(prev => prev.map(h => 
      h.id === hotspotId 
        ? { ...h, productId, label: product.title, product }
        : h
    ));

    setSelectedHotspot(null);
    setIsProductDialogOpen(false);
    toast.success('Product hotspot added successfully!');
  };

  // Remove hotspot
  const removeHotspot = (hotspotId: string) => {
    setHotspots(prev => prev.filter(h => h.id !== hotspotId));
    toast.success('Hotspot removed');
  };

  // Save hotspots
  const saveHotspots = async () => {
    try {
      // Here you would save to your backend
      // For now, we'll just call the callback
      onHotspotsChange?.(hotspots);
      toast.success('Hotspots saved successfully!');
    } catch (error) {
      toast.error('Failed to save hotspots');
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Product Hotspot Editor</h3>
          <p className="text-sm text-muted-foreground">
            Click on the image to add product hotspots
          </p>
        </div>
        <div className="flex space-x-2">
          <Button
            variant={isEditing ? "default" : "outline"}
            onClick={() => setIsEditing(!isEditing)}
          >
            <Edit className="w-4 h-4 mr-2" />
            {isEditing ? 'Exit Edit Mode' : 'Edit Mode'}
          </Button>
          {hotspots.length > 0 && (
            <Button onClick={saveHotspots}>
              <Save className="w-4 h-4 mr-2" />
              Save Hotspots
            </Button>
          )}
        </div>
      </div>

      {/* Image with Hotspots */}
      <Card>
        <CardContent className="p-0">
          <div className="relative">
            <img
              ref={imageRef}
              src={imageUrl}
              alt="Tour scene"
              className={`w-full h-auto max-h-[500px] object-contain ${isEditing ? 'cursor-crosshair' : ''}`}
              onClick={handleImageClick}
            />
            
            {/* Hotspot Markers */}
            {hotspots.map((hotspot) => (
              <div
                key={hotspot.id}
                className={['hotspotMarker', 'group'].join(' ')}
                data-hotspot-x={hotspot.x}
                data-hotspot-y={hotspot.y}
              >
                {/* Hotspot Marker */}
                <div className="relative">
                  <div className="hotspotMarkerDot">
                    <ShoppingCart className="w-3 h-3 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" />
                  </div>
                  {/* Hotspot Label */}
                  <div className="hotspotLabel">
                    <div>
                      {hotspot.label}
                      {hotspot.product && (
                        <div className="text-yellow-300">
                          ₦{hotspot.product.price.toLocaleString()}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Edit/Delete Controls */}
                  {isEditing && (
                    <div className="hotspotEditControls">
                      <Button
                        size="sm"
                        variant="outline"
                        className="w-6 h-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedHotspot(hotspot);
                          setIsProductDialogOpen(true);
                        }}
                      >
                        <Edit className="w-3 h-3" />
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        className="w-6 h-6 p-0"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeHotspot(hotspot.id);
                        }}
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}

            {/* Edit Mode Overlay */}
            {isEditing && (
              <div className="absolute top-4 left-4 bg-blue-500 text-white px-3 py-1 rounded-full text-sm">
                <Target className="w-4 h-4 inline mr-1" />
                Click to add hotspot
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Hotspots List */}
      {hotspots.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Product Hotspots ({hotspots.length})</CardTitle>
            <CardDescription>
              Manage product hotspots for this tour scene
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {hotspots.map((hotspot) => (
                <div key={hotspot.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                      <ShoppingCart className="w-4 h-4 text-white" />
                    </div>
                    <div>
                      <h4 className="font-medium">{hotspot.label}</h4>
                      <p className="text-sm text-muted-foreground">
                        Position: {hotspot.x.toFixed(1)}%, {hotspot.y.toFixed(1)}%
                      </p>
                      {hotspot.product && (
                        <Badge variant="secondary">
                          ₦{hotspot.product.price.toLocaleString()}
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedHotspot(hotspot);
                        setIsProductDialogOpen(true);
                      }}
                    >
                      <Edit className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeHotspot(hotspot.id)}
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Product Selection Dialog */}
      <Dialog open={isProductDialogOpen} onOpenChange={setIsProductDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Select Product for Hotspot</DialogTitle>
            <DialogDescription>
              Choose a product to link to this hotspot
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            {productsLoading ? (
              <div className="text-center py-8">Loading products...</div>
            ) : products.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No products available for this vendor
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {products.map((product) => (
                  <Card 
                    key={product.id} 
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => selectedHotspot && updateHotspot(selectedHotspot.id, product.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        {product.image_url && (
                          <img 
                            src={product.image_url} 
                            alt={product.title}
                            className="w-12 h-12 object-cover rounded"
                          />
                        )}
                        <div className="flex-1">
                          <h4 className="font-medium">{product.title}</h4>
                          <p className="text-sm text-muted-foreground">
                            ₦{product.price.toLocaleString()}
                          </p>
                          <Badge variant="secondary" className="mt-1">
                            {product.category}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProductHotspotEditor;
