/**
 * Photo Sphere Viewer Marker Styles
 * Custom styling for interactive hotspots
 */

/* Base marker styles */
.psv-marker-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  cursor: pointer;
  -webkit-user-select: none; /* Safari 3+, Safari on iOS 3+ */
  -moz-user-select: none; /* Firefox 2+ */
  -ms-user-select: none; /* IE 10+ */
  user-select: none; /* Standard syntax */
}

.psv-marker-content:hover {
  transform: scale(1.1);
}

/* Product markers */
.psv-product-marker {
  position: relative;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: white;
  border-radius: 12px;
  padding: 8px 12px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  min-width: 120px;
  transition: all 0.3s ease;
}

.psv-product-marker:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.6);
}

.psv-product-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-bottom: 4px;
}

.psv-product-info {
  text-align: center;
}

.psv-product-name {
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 2px;
  line-height: 1.2;
}

.psv-product-price {
  font-size: 14px;
  font-weight: 700;
  color: #fbbf24;
}

.psv-product-pulse {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid #3b82f6;
  border-radius: 16px;
  opacity: 0.6;
  animation: pulse-ring 2s infinite;
  will-change: transform, opacity; /* Optimize for animations */
}

/* Navigation markers */
.psv-navigation-marker {
  position: relative;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4);
  transition: all 0.3s ease;
}

.psv-navigation-marker:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.6);
}

.psv-navigation-icon {
  margin-bottom: 2px;
}

.psv-navigation-label {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  margin-top: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.psv-navigation-marker:hover .psv-navigation-label {
  opacity: 1;
}

.psv-navigation-pulse {
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid #10b981;
  border-radius: 50%;
  opacity: 0.6;
  animation: pulse-ring 2s infinite;
  will-change: transform, opacity; /* Optimize for animations */
}

/* Info markers */
.psv-info-marker {
  position: relative;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);
  transition: all 0.3s ease;
}

.psv-info-marker:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.6);
}

.psv-info-label {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  margin-top: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.psv-info-marker:hover .psv-info-label {
  opacity: 1;
}

/* Link markers */
.psv-link-marker {
  position: relative;
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
  transition: all 0.3s ease;
}

.psv-link-marker:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.6);
}

.psv-link-label {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  margin-top: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.psv-link-marker:hover .psv-link-label {
  opacity: 1;
}

/* Media markers */
.psv-media-marker {
  position: relative;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
  transition: all 0.3s ease;
}

.psv-media-marker:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.6);
}

.psv-media-label {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  margin-top: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.psv-media-marker:hover .psv-media-label {
  opacity: 1;
}

/* Default markers */
.psv-default-marker {
  position: relative;
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(107, 114, 128, 0.4);
  transition: all 0.3s ease;
}

.psv-default-marker:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(107, 114, 128, 0.6);
}

.psv-default-icon {
  font-size: 18px;
}

.psv-default-label {
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  margin-top: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.psv-default-marker:hover .psv-default-label {
  opacity: 1;
}

/* Pulse animation - optimized for performance */
@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .psv-product-marker {
    min-width: 100px;
    padding: 6px 10px;
  }
  
  .psv-product-name {
    font-size: 11px;
  }
  
  .psv-product-price {
    font-size: 12px;
  }
  
  .psv-navigation-marker,
  .psv-info-marker,
  .psv-link-marker,
  .psv-media-marker,
  .psv-default-marker {
    width: 36px;
    height: 36px;
  }
  
  .psv-navigation-label,
  .psv-info-label,
  .psv-link-label,
  .psv-media-label,
  .psv-default-label {
    font-size: 9px;
    padding: 3px 6px;
  }
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .psv-navigation-label,
  .psv-info-label,
  .psv-link-label,
  .psv-media-label,
  .psv-default-label {
    background: rgba(255, 255, 255, 0.9);
    color: #1f2937;
  }
}
