import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveAdminFormProps {
  children: React.ReactNode;
  className?: string;
  onSubmit?: React.FormEventHandler<HTMLFormElement>;
}

/**
 * A standardized, mobile-first responsive form for admin dashboard use.
 * - Full width on mobile, max-w on desktop
 * - Responsive padding and gap
 * - Stacks fields vertically on mobile, grid on desktop
 */
const ResponsiveAdminForm = ({ children, className, onSubmit }: ResponsiveAdminFormProps) => (
  <form
    onSubmit={onSubmit}
    className={cn(
      'w-full max-w-full sm:max-w-xl md:max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto flex flex-col gap-4 p-2 sm:p-4 md:p-6',
      className
    )}
    autoComplete="off"
  >
    {children}
  </form>
);

export default ResponsiveAdminForm;
