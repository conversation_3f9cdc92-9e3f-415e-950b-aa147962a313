/**
 * VendorProfile Component
 * Comprehensive vendor information display with product showcase
 * Mobile-first responsive design following VRT patterns
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  MapPin, 
  Phone, 
  Mail, 
  MessageCircle, 
  Star, 
  Package, 
  TrendingUp,
  Clock,
  CheckCircle,
  ExternalLink,
  Grid3X3,
  List
} from 'lucide-react';
import { cn } from '@/lib/utils';
import ProductCard, { type Product } from './ProductCard';
import { useShoppingCart } from '@/hooks/useShoppingCart';

export interface Vendor {
  id: string;
  name: string;
  email: string;
  phone?: string;
  whatsapp_number?: string;
  business_address?: string;
  business_description?: string;
  logo_url?: string;
  commission_rate: number;
  status: 'pending' | 'approved' | 'suspended' | 'rejected';
  created_at: string;
  stats?: {
    total_products: number;
    active_products: number;
    total_orders: number;
    total_revenue: number;
    rating: number;
    response_time: string;
  };
}

interface VendorProfileProps {
  vendor: Vendor;
  products?: Product[];
  variant?: 'full' | 'compact' | 'card';
  showProducts?: boolean;
  showStats?: boolean;
  showContact?: boolean;
  onContactVendor?: (vendorId: string) => void;
  onViewProduct?: (productId: string) => void;
  className?: string;
}

const VendorProfile = ({
  vendor,
  products = [],
  variant = 'full',
  showProducts = true,
  showStats = true,
  showContact = true,
  onContactVendor,
  onViewProduct,
  className
}: VendorProfileProps) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [activeTab, setActiveTab] = useState('overview');
  const { addToCart } = useShoppingCart();

  const handleContactVendor = () => {
    if (onContactVendor) {
      onContactVendor(vendor.id);
    } else if (vendor.whatsapp_number) {
      const message = `Hi ${vendor.name}! I'm interested in your products on VirtualRealTour. Can you help me?`;
      const whatsappUrl = `https://wa.me/${vendor.whatsapp_number.replace(/[^\d]/g, '')}?text=${encodeURIComponent(message)}`;
      window.open(whatsappUrl, '_blank');
    }
  };

  const handleAddToCart = (productId: string) => {
    addToCart(productId, 1);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      case 'rejected': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2);
  };

  if (variant === 'card') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={cn("w-full max-w-sm", className)}
      >
        <Card className="h-full hover:shadow-lg transition-all duration-300">
          <CardHeader className="pb-3">
            <div className="flex items-start gap-3">
              <Avatar className="w-12 h-12">
                <AvatarImage src={vendor.logo_url} alt={vendor.name} />
                <AvatarFallback>{getInitials(vendor.name)}</AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <CardTitle className="text-lg line-clamp-1">{vendor.name}</CardTitle>
                <div className="flex items-center gap-2 mt-1">
                  <Badge className={getStatusColor(vendor.status)} variant="secondary">
                    {vendor.status}
                  </Badge>
                  {vendor.stats?.rating && (
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                      <span>{vendor.stats.rating.toFixed(1)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {vendor.business_description && (
              <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                {vendor.business_description}
              </p>
            )}
            
            {vendor.stats && (
              <div className="grid grid-cols-2 gap-3 mb-4">
                <div className="text-center">
                  <div className="font-semibold text-lg">{vendor.stats.active_products}</div>
                  <div className="text-xs text-muted-foreground">Products</div>
                </div>
                <div className="text-center">
                  <div className="font-semibold text-lg">{vendor.stats.total_orders}</div>
                  <div className="text-xs text-muted-foreground">Orders</div>
                </div>
              </div>
            )}

            {showContact && vendor.whatsapp_number && (
              <Button 
                className="w-full" 
                onClick={handleContactVendor}
                size="sm"
              >
                <MessageCircle className="w-4 h-4 mr-2" />
                Contact Vendor
              </Button>
            )}
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn("w-full space-y-6", className)}
    >
      {/* Vendor Header */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row gap-4">
            <Avatar className="w-20 h-20 mx-auto sm:mx-0">
              <AvatarImage src={vendor.logo_url} alt={vendor.name} />
              <AvatarFallback className="text-lg">{getInitials(vendor.name)}</AvatarFallback>
            </Avatar>
            
            <div className="flex-1 text-center sm:text-left">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 mb-2">
                <CardTitle className="text-2xl">{vendor.name}</CardTitle>
                <Badge className={getStatusColor(vendor.status)} variant="secondary">
                  <CheckCircle className="w-3 h-3 mr-1" />
                  {vendor.status}
                </Badge>
              </div>
              
              {vendor.business_description && (
                <CardDescription className="text-base mb-3">
                  {vendor.business_description}
                </CardDescription>
              )}

              <div className="flex flex-wrap gap-4 text-sm text-muted-foreground justify-center sm:justify-start">
                {vendor.business_address && (
                  <div className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    <span>{vendor.business_address}</span>
                  </div>
                )}
                {vendor.stats?.response_time && (
                  <div className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    <span>Responds in {vendor.stats.response_time}</span>
                  </div>
                )}
                {vendor.stats?.rating && (
                  <div className="flex items-center gap-1">
                    <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                    <span>{vendor.stats.rating.toFixed(1)} rating</span>
                  </div>
                )}
              </div>
            </div>

            {showContact && (
              <div className="flex flex-col gap-2 sm:w-auto w-full">
                {vendor.whatsapp_number && (
                  <Button onClick={handleContactVendor} className="w-full sm:w-auto">
                    <MessageCircle className="w-4 h-4 mr-2" />
                    WhatsApp
                  </Button>
                )}
                {vendor.phone && vendor.phone !== vendor.whatsapp_number && (
                  <Button variant="outline" size="sm" className="w-full sm:w-auto">
                    <Phone className="w-4 h-4 mr-2" />
                    Call
                  </Button>
                )}
                {vendor.email && (
                  <Button variant="outline" size="sm" className="w-full sm:w-auto">
                    <Mail className="w-4 h-4 mr-2" />
                    Email
                  </Button>
                )}
              </div>
            )}
          </div>
        </CardHeader>
      </Card>

      {/* Stats Cards */}
      {showStats && vendor.stats && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4 text-center">
              <Package className="w-8 h-8 mx-auto mb-2 text-blue-600" />
              <div className="font-semibold text-2xl">{vendor.stats.active_products}</div>
              <div className="text-sm text-muted-foreground">Active Products</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <TrendingUp className="w-8 h-8 mx-auto mb-2 text-green-600" />
              <div className="font-semibold text-2xl">{vendor.stats.total_orders}</div>
              <div className="text-sm text-muted-foreground">Total Orders</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Star className="w-8 h-8 mx-auto mb-2 text-yellow-600" />
              <div className="font-semibold text-2xl">{vendor.stats.rating.toFixed(1)}</div>
              <div className="text-sm text-muted-foreground">Rating</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <Clock className="w-8 h-8 mx-auto mb-2 text-purple-600" />
              <div className="font-semibold text-lg">{vendor.stats.response_time}</div>
              <div className="text-sm text-muted-foreground">Response Time</div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Products Section */}
      {showProducts && (
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <CardTitle>Products</CardTitle>
                <CardDescription>
                  {products.length} product{products.length !== 1 ? 's' : ''} available
                </CardDescription>
              </div>
              
              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent>
            {products.length === 0 ? (
              <div className="text-center py-8">
                <Package className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <h3 className="font-medium text-lg mb-2">No products yet</h3>
                <p className="text-muted-foreground">
                  This vendor hasn't added any products yet.
                </p>
              </div>
            ) : (
              <div className={cn(
                viewMode === 'grid' 
                  ? "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
                  : "space-y-4"
              )}>
                {products.map((product) => (
                  <ProductCard
                    key={product.id}
                    product={product}
                    variant={viewMode === 'list' ? 'compact' : 'default'}
                    showVendor={false}
                    onAddToCart={handleAddToCart}
                    onQuickView={onViewProduct}
                  />
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </motion.div>
  );
};

export default VendorProfile;
