/**
 * Security Middleware for Virtual Real Tour
 * Implements CSP, rate limiting, and security headers
 */

import { 
  generateCS<PERSON><PERSON>ead<PERSON>, 
  SECURITY_HEADERS, 
  RATE_LIMIT_CONFIG,
  validateInput,
  sanitizeInput,
  isAllowedEmbedUrl,
  SecurityMonitor
} from '@/config/securityConfig';

// Rate limiting store (in production, use Redis or similar)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Security middleware class
export class SecurityMiddleware {
  private static instance: SecurityMiddleware;
  private environment: 'development' | 'production';

  private constructor() {
    this.environment = process.env.NODE_ENV === 'production' ? 'production' : 'development';
  }

  static getInstance(): SecurityMiddleware {
    if (!SecurityMiddleware.instance) {
      SecurityMiddleware.instance = new SecurityMiddleware();
    }
    return SecurityMiddleware.instance;
  }

  // Apply security headers to response
  applySecurityHeaders(response: Response): Response {
    const headers = new Headers(response.headers);

    // Add CSP header
    headers.set('Content-Security-Policy', generateCSPHeader(this.environment));

    // Add other security headers
    Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
      if (Array.isArray(value)) {
        headers.set(key, value.join(', '));
      } else {
        headers.set(key, value);
      }
    });

    // Add cache control for security-sensitive responses
    if (response.url.includes('/api/auth') || response.url.includes('/admin')) {
      headers.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
      headers.set('Pragma', 'no-cache');
    }

    return new Response(response.body, {
      status: response.status,
      statusText: response.statusText,
      headers: headers
    });
  }

  // Rate limiting check
  checkRateLimit(clientId: string, endpoint: string): { allowed: boolean; remaining: number } {
    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minute window
    
    // Get rate limit for specific endpoint or use global
    const limit = RATE_LIMIT_CONFIG.api[endpoint] || RATE_LIMIT_CONFIG.global;
    
    const key = `${clientId}:${endpoint}`;
    const current = rateLimitStore.get(key);

    if (!current || now > current.resetTime) {
      // New window or expired
      rateLimitStore.set(key, { count: 1, resetTime: now + windowMs });
      return { allowed: true, remaining: limit - 1 };
    }

    if (current.count >= limit) {
      // Rate limit exceeded
      SecurityMonitor.logViolation('rate_limit_exceeded', `${clientId} exceeded rate limit for ${endpoint}`);
      return { allowed: false, remaining: 0 };
    }

    // Increment count
    current.count++;
    rateLimitStore.set(key, current);
    
    return { allowed: true, remaining: limit - current.count };
  }

  // Validate and sanitize tour upload data
  validateTourData(data: any): { valid: boolean; errors: string[]; sanitized?: any } {
    const errors: string[] = [];
    const sanitized: any = {};

    // Validate title
    if (!data.title || !validateInput(data.title, 'tourTitle')) {
      errors.push('Invalid tour title');
    } else {
      sanitized.title = sanitizeInput(data.title);
    }

    // Validate description
    if (data.description && !validateInput(data.description, 'tourDescription')) {
      errors.push('Invalid tour description');
    } else {
      sanitized.description = data.description ? sanitizeInput(data.description) : '';
    }

    // Validate embed URL
    if (data.embed_url) {
      if (!isAllowedEmbedUrl(data.embed_url)) {
        errors.push('Embed URL from unauthorized domain');
      } else {
        sanitized.embed_url = data.embed_url;
      }
    }

    // Validate category
    const allowedCategories = [
      'property', 'education', 'hospitality', 'tourism', 
      'culture', 'commercial', 'healthcare', 'government'
    ];
    if (!data.category || !allowedCategories.includes(data.category)) {
      errors.push('Invalid category');
    } else {
      sanitized.category = data.category;
    }

    // Validate location
    if (data.location) {
      sanitized.location = sanitizeInput(data.location);
    }

    // Validate business information
    if (data.business_name) {
      sanitized.business_name = sanitizeInput(data.business_name);
    }

    if (data.contact_email && !validateInput(data.contact_email, 'email')) {
      errors.push('Invalid email format');
    } else if (data.contact_email) {
      sanitized.contact_email = data.contact_email;
    }

    if (data.contact_phone && !validateInput(data.contact_phone, 'phone')) {
      errors.push('Invalid phone format');
    } else if (data.contact_phone) {
      sanitized.contact_phone = data.contact_phone;
    }

    return {
      valid: errors.length === 0,
      errors,
      sanitized: errors.length === 0 ? sanitized : undefined
    };
  }

  // Validate file uploads
  validateFileUpload(file: File): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check file type
    if (!validateInput(file.name, 'imageFile')) {
      errors.push('Invalid file type. Only JPG, PNG, WebP, and AVIF files are allowed.');
    }

    // Check file size
    if (file.size > RATE_LIMIT_CONFIG.api.maxFileSize || 10 * 1024 * 1024) {
      errors.push('File size too large. Maximum size is 10MB.');
    }

    // Check for potential malicious content
    if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
      errors.push('Invalid file name.');
      SecurityMonitor.logViolation('suspicious_filename', `Suspicious filename: ${file.name}`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  // Generate nonce for inline scripts (CSP)
  generateNonce(): string {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  }

  // Check for suspicious activity
  detectSuspiciousActivity(request: Request, clientId: string): boolean {
    const url = new URL(request.url);
    const userAgent = request.headers.get('user-agent') || '';

    // Check for common attack patterns
    const suspiciousPatterns = [
      /\.\./,  // Directory traversal
      /<script/i,  // XSS attempts
      /union.*select/i,  // SQL injection
      /javascript:/i,  // JavaScript protocol
      /data:.*base64/i,  // Suspicious data URLs
    ];

    const fullUrl = url.href;
    const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(fullUrl));

    if (isSuspicious) {
      SecurityMonitor.logViolation('suspicious_request', `Suspicious request from ${clientId}: ${fullUrl}`, userAgent);
      return true;
    }

    // Check for rapid requests (potential bot)
    const rapidRequestKey = `rapid:${clientId}`;
    const rapidRequests = rateLimitStore.get(rapidRequestKey);
    const now = Date.now();

    if (!rapidRequests || now > rapidRequests.resetTime) {
      rateLimitStore.set(rapidRequestKey, { count: 1, resetTime: now + 10000 }); // 10 second window
    } else {
      rapidRequests.count++;
      if (rapidRequests.count > 50) { // More than 50 requests in 10 seconds
        SecurityMonitor.logViolation('rapid_requests', `Rapid requests detected from ${clientId}`, userAgent);
        return true;
      }
    }

    return false;
  }

  // Clean up rate limit store periodically
  startCleanup(): void {
    setInterval(() => {
      const now = Date.now();
      for (const [key, value] of rateLimitStore.entries()) {
        if (now > value.resetTime) {
          rateLimitStore.delete(key);
        }
      }
    }, 5 * 60 * 1000); // Clean up every 5 minutes
  }
}

// Utility functions
export const securityMiddleware = SecurityMiddleware.getInstance();

// CSP violation reporting
export function setupCSPReporting(): void {
  if (typeof window !== 'undefined') {
    // Listen for CSP violations
    document.addEventListener('securitypolicyviolation', (event) => {
      SecurityMonitor.logViolation('csp_violation', 
        `CSP violation: ${event.violatedDirective} - ${event.blockedURI}`,
        navigator.userAgent
      );

      // In production, send to monitoring service
      if (process.env.NODE_ENV === 'production') {
        fetch('/api/security/csp-violation', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            violatedDirective: event.violatedDirective,
            blockedURI: event.blockedURI,
            documentURI: event.documentURI,
            originalPolicy: event.originalPolicy,
            timestamp: new Date().toISOString()
          })
        }).catch(console.error);
      }
    });
  }
}

// Initialize security middleware
export function initializeSecurity(): void {
  securityMiddleware.startCleanup();
  setupCSPReporting();
  
  console.log('Security middleware initialized');
}

// Export for use in service worker
export { generateCSPHeader, SECURITY_HEADERS };
