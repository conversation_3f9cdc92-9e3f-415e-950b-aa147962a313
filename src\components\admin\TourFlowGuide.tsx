import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Plus, 
  Edit, 
  Eye, 
  Globe, 
  CheckCircle, 
  ArrowRight,
  FileText,
  Link2,
  Play
} from 'lucide-react';

const TourFlowGuide = () => {
  const steps = [
    {
      icon: Plus,
      title: "1. Create Tour",
      description: "Click 'Create Tour' to start",
      details: [
        "Choose content type (360° Images or External Embed)",
        "Fill in basic information (title, category, location)",
        "Add business information for Google integration",
        "For testing: Use 'Custom Integration' embed type"
      ],
      status: "start"
    },
    {
      icon: Edit,
      title: "2. Edit & Configure",
      description: "Click Edit button on any tour",
      details: [
        "Add your tour URL (Matterport, Kuula, etc.)",
        "Choose embed type with helpful tooltips",
        "Update business information",
        "Save changes"
      ],
      status: "edit"
    },
    {
      icon: Eye,
      title: "3. Preview Tour",
      description: "Use Preview tab to test",
      details: [
        "See exactly how visitors will experience your tour",
        "Test iframe embeds or external links",
        "Verify all information displays correctly",
        "Make adjustments as needed"
      ],
      status: "preview"
    },
    {
      icon: Globe,
      title: "4. Publish Tour",
      description: "Make tour live on website",
      details: [
        "Use Publishing tab to control visibility",
        "Published tours appear on homepage, showcase, and search",
        "Get direct link for sharing",
        "Monitor where tour appears"
      ],
      status: "publish"
    },
    {
      icon: CheckCircle,
      title: "5. Test Live Tour",
      description: "Verify everything works",
      details: [
        "Visit direct link to test tour",
        "Check tour appears in website sections",
        "Test interactive features",
        "Confirm mobile responsiveness"
      ],
      status: "complete"
    }
  ];

  const embedTypes = [
    {
      type: "Iframe Embed",
      description: "Best for Matterport, Kuula, and most 360° platforms",
      example: "https://my.matterport.com/show/?m=...",
      pros: ["Seamless integration", "Full interactivity", "No page redirects"],
      cons: ["Requires iframe-compatible URL"]
    },
    {
      type: "Direct Link", 
      description: "Opens tour in new window/tab",
      example: "https://kuula.co/share/...",
      pros: ["Works with any URL", "Simple setup", "Full platform features"],
      cons: ["Leaves your website", "Less seamless experience"]
    },
    {
      type: "Custom Integration",
      description: "For advanced setups or custom viewers",
      example: "https://your-custom-viewer.com/tour/123",
      pros: ["Maximum flexibility", "Custom branding", "Advanced features"],
      cons: ["Requires technical setup", "More complex implementation"]
    }
  ];

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Tour Management Flow
          </CardTitle>
          <CardDescription>
            Complete guide to creating, editing, and publishing virtual tours
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {steps.map((step, index) => (
              <div key={index} className="flex gap-4">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                    <step.icon className="w-5 h-5 text-blue-600" />
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-semibold">{step.title}</h3>
                    <Badge variant="outline">{step.description}</Badge>
                  </div>
                  <ul className="space-y-1 text-sm text-muted-foreground">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-start gap-2">
                        <ArrowRight className="w-3 h-3 mt-0.5 flex-shrink-0" />
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Link2 className="w-5 h-5" />
            Embed Type Guide
          </CardTitle>
          <CardDescription>
            Choose the right embed type for your tour platform
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {embedTypes.map((embed, index) => (
              <Card key={index} className="border-2">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">{embed.type}</CardTitle>
                  <CardDescription className="text-sm">
                    {embed.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <p className="text-xs font-medium text-muted-foreground mb-1">Example URL:</p>
                    <code className="text-xs bg-muted px-2 py-1 rounded block break-all">
                      {embed.example}
                    </code>
                  </div>
                  
                  <div>
                    <p className="text-xs font-medium text-green-700 mb-1">Pros:</p>
                    <ul className="text-xs space-y-0.5">
                      {embed.pros.map((pro, proIndex) => (
                        <li key={proIndex} className="flex items-start gap-1">
                          <CheckCircle className="w-3 h-3 text-green-600 mt-0.5 flex-shrink-0" />
                          {pro}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <p className="text-xs font-medium text-orange-700 mb-1">Considerations:</p>
                    <ul className="text-xs space-y-0.5">
                      {embed.cons.map((con, conIndex) => (
                        <li key={conIndex} className="flex items-start gap-1">
                          <ArrowRight className="w-3 h-3 text-orange-600 mt-0.5 flex-shrink-0" />
                          {con}
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="w-5 h-5" />
            Quick Testing Tips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3">For Testing (Recommended)</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  Use "Custom Integration" embed type
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  Add any valid URL (even your website)
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  Fill in business information
                </li>
                <li className="flex items-start gap-2">
                  <CheckCircle className="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                  Save and preview before publishing
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-3">Where Published Tours Appear</h4>
              <ul className="space-y-2 text-sm">
                <li className="flex items-start gap-2">
                  <Globe className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  Homepage (if featured)
                </li>
                <li className="flex items-start gap-2">
                  <Globe className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  Tours Showcase page
                </li>
                <li className="flex items-start gap-2">
                  <Globe className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  Category-specific pages
                </li>
                <li className="flex items-start gap-2">
                  <Globe className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  Search results
                </li>
                <li className="flex items-start gap-2">
                  <Globe className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  Direct link sharing
                </li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TourFlowGuide;
