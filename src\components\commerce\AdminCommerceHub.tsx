/**
 * AdminCommerceHub Component
 * Central admin interface for all e-commerce management
 * Mobile-first responsive design with comprehensive commerce controls
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  Package, 
  Users, 
  ShoppingCart,
  MessageCircle,
  Settings,
  TrendingUp,
  DollarSign,
  AlertCircle,
  CheckCircle,
  Clock,
  Plus,
  Eye,
  Download,
  RefreshCw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import CommerceAnalyticsDashboard from './CommerceAnalyticsDashboard';
import AdminVendorManagement from './AdminVendorManagement';
import OrderManagement from './OrderManagement';
import WhatsAppAutomationManager from './WhatsAppAutomationManager';

interface DashboardStats {
  revenue: {
    today: number;
    week: number;
    month: number;
    growth: number;
  };
  orders: {
    pending: number;
    processing: number;
    completed: number;
    total_today: number;
  };
  vendors: {
    total: number;
    pending_approval: number;
    active: number;
    suspended: number;
  };
  customers: {
    total: number;
    new_today: number;
    active_sessions: number;
  };
  whatsapp: {
    messages_sent_today: number;
    automation_rules_active: number;
    response_rate: number;
  };
}

interface RecentActivity {
  id: string;
  type: 'order' | 'vendor' | 'customer' | 'whatsapp';
  title: string;
  description: string;
  timestamp: string;
  status: 'success' | 'warning' | 'error' | 'info';
}

interface AdminCommerceHubProps {
  className?: string;
}

const AdminCommerceHub = ({ className }: AdminCommerceHubProps) => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call - replace with actual service
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStats({
        revenue: {
          today: 125000,
          week: 890000,
          month: 2450000,
          growth: 15.2
        },
        orders: {
          pending: 23,
          processing: 45,
          completed: 156,
          total_today: 34
        },
        vendors: {
          total: 45,
          pending_approval: 8,
          active: 37,
          suspended: 0
        },
        customers: {
          total: 856,
          new_today: 12,
          active_sessions: 89
        },
        whatsapp: {
          messages_sent_today: 234,
          automation_rules_active: 12,
          response_rate: 67.2
        }
      });

      setRecentActivity([
        {
          id: '1',
          type: 'order',
          title: 'New Order #VRT-20240121-1234',
          description: 'Order placed by John Doe for ₦45,000',
          timestamp: '2 minutes ago',
          status: 'success'
        },
        {
          id: '2',
          type: 'vendor',
          title: 'Vendor Application',
          description: 'TechHub Lagos submitted vendor application',
          timestamp: '15 minutes ago',
          status: 'warning'
        },
        {
          id: '3',
          type: 'whatsapp',
          title: 'WhatsApp Automation',
          description: 'Order confirmation sent to customer',
          timestamp: '23 minutes ago',
          status: 'success'
        },
        {
          id: '4',
          type: 'customer',
          title: 'New Customer Registration',
          description: 'Sarah Johnson registered via virtual tour',
          timestamp: '1 hour ago',
          status: 'info'
        },
        {
          id: '5',
          type: 'order',
          title: 'Order Status Update',
          description: 'Order #VRT-20240121-1230 marked as shipped',
          timestamp: '2 hours ago',
          status: 'success'
        }
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const QuickStatCard = ({ 
    title, 
    value, 
    subtitle, 
    icon: Icon, 
    color = 'blue',
    trend
  }: { 
    title: string; 
    value: string | number; 
    subtitle: string;
    icon: any; 
    color?: string;
    trend?: { value: number; label: string };
  }) => (
    <Card className="hover:shadow-lg transition-all duration-300">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <p className="text-sm text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            <p className="text-xs text-muted-foreground">{subtitle}</p>
            {trend && (
              <div className="flex items-center gap-1 mt-1">
                <TrendingUp className="w-3 h-3 text-green-600" />
                <span className="text-xs text-green-600 font-medium">
                  +{trend.value}% {trend.label}
                </span>
              </div>
            )}
          </div>
          <div className={cn(
            "w-12 h-12 rounded-lg flex items-center justify-center",
            color === 'blue' && 'bg-blue-100 text-blue-600',
            color === 'green' && 'bg-green-100 text-green-600',
            color === 'purple' && 'bg-purple-100 text-purple-600',
            color === 'orange' && 'bg-orange-100 text-orange-600',
            color === 'red' && 'bg-red-100 text-red-600'
          )}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const ActivityItem = ({ activity }: { activity: RecentActivity }) => {
    const getStatusIcon = () => {
      switch (activity.status) {
        case 'success': return <CheckCircle className="w-4 h-4 text-green-600" />;
        case 'warning': return <AlertCircle className="w-4 h-4 text-yellow-600" />;
        case 'error': return <AlertCircle className="w-4 h-4 text-red-600" />;
        default: return <Clock className="w-4 h-4 text-blue-600" />;
      }
    };

    const getTypeIcon = () => {
      switch (activity.type) {
        case 'order': return <ShoppingCart className="w-4 h-4" />;
        case 'vendor': return <Package className="w-4 h-4" />;
        case 'customer': return <Users className="w-4 h-4" />;
        case 'whatsapp': return <MessageCircle className="w-4 h-4" />;
        default: return <Clock className="w-4 h-4" />;
      }
    };

    return (
      <div className="flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
        <div className="flex items-center gap-2">
          {getTypeIcon()}
          {getStatusIcon()}
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-sm line-clamp-1">{activity.title}</h4>
          <p className="text-xs text-muted-foreground line-clamp-1">{activity.description}</p>
          <p className="text-xs text-muted-foreground mt-1">{activity.timestamp}</p>
        </div>
      </div>
    );
  };

  const QuickActionCard = ({ 
    title, 
    description, 
    icon: Icon, 
    action, 
    color = 'blue' 
  }: {
    title: string;
    description: string;
    icon: any;
    action: () => void;
    color?: string;
  }) => (
    <Card className="hover:shadow-lg transition-all duration-300 cursor-pointer" onClick={action}>
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          <div className={cn(
            "w-10 h-10 rounded-lg flex items-center justify-center",
            color === 'blue' && 'bg-blue-100 text-blue-600',
            color === 'green' && 'bg-green-100 text-green-600',
            color === 'purple' && 'bg-purple-100 text-purple-600',
            color === 'orange' && 'bg-orange-100 text-orange-600'
          )}>
            <Icon className="w-5 h-5" />
          </div>
          <div className="flex-1">
            <h4 className="font-medium text-sm">{title}</h4>
            <p className="text-xs text-muted-foreground">{description}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (isLoading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!stats) return null;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">Commerce Dashboard</h1>
          <p className="text-muted-foreground">Manage your e-commerce platform and monitor performance</p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={loadDashboardData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export Report
          </Button>
          <Button>
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="vendors">Vendors</TabsTrigger>
          <TabsTrigger value="whatsapp">WhatsApp</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <QuickStatCard
              title="Today's Revenue"
              value={`₦${stats.revenue.today.toLocaleString()}`}
              subtitle={`₦${stats.revenue.month.toLocaleString()} this month`}
              icon={DollarSign}
              color="green"
              trend={{ value: stats.revenue.growth, label: 'vs last month' }}
            />
            <QuickStatCard
              title="Orders Today"
              value={stats.orders.total_today}
              subtitle={`${stats.orders.pending} pending approval`}
              icon={ShoppingCart}
              color="blue"
            />
            <QuickStatCard
              title="Active Vendors"
              value={stats.vendors.active}
              subtitle={`${stats.vendors.pending_approval} awaiting approval`}
              icon={Package}
              color="purple"
            />
            <QuickStatCard
              title="WhatsApp Messages"
              value={stats.whatsapp.messages_sent_today}
              subtitle={`${stats.whatsapp.response_rate}% response rate`}
              icon={MessageCircle}
              color="green"
            />
          </div>

          {/* Quick Actions & Recent Activity */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common administrative tasks</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <QuickActionCard
                  title="Approve Vendors"
                  description={`${stats.vendors.pending_approval} vendors awaiting approval`}
                  icon={Users}
                  action={() => setActiveTab('vendors')}
                  color="orange"
                />
                <QuickActionCard
                  title="Process Orders"
                  description={`${stats.orders.pending} orders need attention`}
                  icon={ShoppingCart}
                  action={() => setActiveTab('orders')}
                  color="blue"
                />
                <QuickActionCard
                  title="WhatsApp Settings"
                  description={`${stats.whatsapp.automation_rules_active} automation rules active`}
                  icon={MessageCircle}
                  action={() => setActiveTab('whatsapp')}
                  color="green"
                />
                <QuickActionCard
                  title="View Analytics"
                  description="Detailed performance insights"
                  icon={BarChart3}
                  action={() => setActiveTab('analytics')}
                  color="purple"
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
                <CardDescription>Latest platform activities</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {recentActivity.map((activity) => (
                  <ActivityItem key={activity.id} activity={activity} />
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Order Status Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Order Status Overview</CardTitle>
              <CardDescription>Current order pipeline status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 sm:grid-cols-4 gap-4">
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{stats.orders.pending}</div>
                  <div className="text-sm text-muted-foreground">Pending</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{stats.orders.processing}</div>
                  <div className="text-sm text-muted-foreground">Processing</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{stats.orders.completed}</div>
                  <div className="text-sm text-muted-foreground">Completed</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {stats.orders.pending + stats.orders.processing + stats.orders.completed}
                  </div>
                  <div className="text-sm text-muted-foreground">Total</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics">
          <CommerceAnalyticsDashboard />
        </TabsContent>

        {/* Orders Tab */}
        <TabsContent value="orders">
          <OrderManagement userType="admin" />
        </TabsContent>

        {/* Vendors Tab */}
        <TabsContent value="vendors">
          <AdminVendorManagement />
        </TabsContent>

        {/* WhatsApp Tab */}
        <TabsContent value="whatsapp">
          <WhatsAppAutomationManager />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminCommerceHub;
