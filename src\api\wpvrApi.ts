import axios from 'axios';

const WPVR_API_BASE = import.meta.env.VITE_WPVR_API_BASE || 'https://yourwpdomain.com/wp-json/wpvr/v1';
const WP_JWT_TOKEN = import.meta.env.VITE_WP_JWT_TOKEN;

const wpvrAPI = axios.create({
  baseURL: WPVR_API_BASE,
  headers: WP_JWT_TOKEN ? { Authorization: `Bearer ${WP_JWT_TOKEN}` } : {},
});

export const fetchWPVRTours = async () => {
  const res = await wpvrAPI.get('/tours');
  return res.data;
};

export const fetchWPVRVendors = async () => {
  const res = await wpvrAPI.get('/vendors');
  return res.data;
};
