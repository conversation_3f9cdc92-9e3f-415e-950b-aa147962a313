import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveAdminCardProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * A standardized, mobile-first responsive card for admin dashboard use.
 * - Full width on mobile, max-w on desktop
 * - Responsive padding and gap
 * - Rounded, shadow, border
 */
const ResponsiveAdminCard = ({ children, className }: ResponsiveAdminCardProps) => (
  <div
    className={cn(
      'w-full max-w-full sm:max-w-xl md:max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto rounded-xl border bg-white dark:bg-gray-950 shadow-sm p-3 sm:p-4 md:p-6 flex flex-col gap-2',
      className
    )}
  >
    {children}
  </div>
);

export default ResponsiveAdminCard;
