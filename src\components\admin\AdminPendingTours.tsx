
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle } from 'lucide-react';
import EnhancedTourCard from '@/components/EnhancedTourCard';
import { getTourCardSettings } from '@/config/tourCardSettings';
import { Tour } from '@/lib/supabase';

interface AdminPendingToursProps {
  pendingTours: Tour[];
  onApproveTour: (tour: Tour) => void;
  onRejectTour: (tour: Tour) => void;
}

const AdminPendingTours = ({ pendingTours, onApproveTour, onRejectTour }: AdminPendingToursProps) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Tours Pending Approval</h2>
        <Badge variant="secondary" className="bg-orange-100 text-orange-800">
          {pendingTours.length} pending
        </Badge>
      </div>
      
      {pendingTours.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <CheckCircle className="w-16 h-16 mx-auto mb-4 text-green-400" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">All caught up!</h3>
            <p className="text-gray-600">No tours pending approval</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pendingTours.map((tour) => {
            const settings = getTourCardSettings('adminPending');
            return (
              <div key={tour.id} className="relative">
                <EnhancedTourCard
                  tour={tour}
                  showActions={settings.showActions}
                  showEmbedded={settings.showEmbedded}
                  autoLoad={settings.autoLoad}
                  className="hover-lift"
                />
                <div className="absolute top-2 left-2 right-2 flex gap-2 z-40">
                  <Button
                    size="sm"
                    onClick={() => onApproveTour(tour)}
                    className="bg-green-600 hover:bg-green-700 flex-1"
                  >
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Approve
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => onRejectTour(tour)}
                    className="flex-1"
                  >
                    <XCircle className="w-4 h-4 mr-1" />
                    Reject
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default AdminPendingTours;
