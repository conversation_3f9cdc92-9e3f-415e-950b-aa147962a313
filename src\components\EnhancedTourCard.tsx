import { useState, useEffect, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  ExternalLink, 
  MapPin, 
  Eye, 
  Edit3,
  Share2,
  MoreHorizontal,
  User,
  Maximize2,
  Loader2
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Tour } from '@/lib/supabase';
import { Link } from 'react-router-dom';
import { createTourUrl } from '@/lib/slugUtils';
import { getCleanEmbedUrl, getCleanIframeProps } from '@/utils/cleanTourEmbedding';
import { getOptimizedAnimationDuration, debounce } from '@/utils/performanceOptimization';
import { toast } from 'sonner';
import OptimizedImage from '@/components/ui/optimized-image';
import '@/styles/clean-tours.css';

// Helper function to get CSS class for height
const getHeightClass = (height?: string): string => {
  if (!height) return 'tour-card-height-300';

  // Extract numeric value from height string (e.g., "250px" -> "250")
  const numericHeight = parseInt(height.replace(/\D/g, ''), 10);

  // Map to predefined CSS classes
  if (numericHeight <= 200) return 'tour-card-height-200';
  if (numericHeight <= 250) return 'tour-card-height-250';
  if (numericHeight <= 300) return 'tour-card-height-300';
  if (numericHeight <= 350) return 'tour-card-height-350';
  if (numericHeight <= 400) return 'tour-card-height-400';

  // Fallback to default
  return 'tour-card-height-300';
};

interface EnhancedTourCardProps {
  tour: Tour;
  showActions?: boolean;
  showEmbedded?: boolean;
  autoLoad?: boolean;
  height?: string;
  showControls?: boolean;
  className?: string;
  isAdminPreview?: boolean;
  hideOverlay?: boolean;
  onEdit?: (tour: Tour) => void;
  onDelete?: (tour: Tour) => void;
}

const EnhancedTourCard = ({
  tour,
  showActions = false,
  showEmbedded = true,
  autoLoad = false,
  height = "264px",
  showControls = true,
  className = "",
  isAdminPreview = false,
  hideOverlay = false,
  onEdit,
  onDelete
}: EnhancedTourCardProps) => {
  const [isEmbedLoaded, setIsEmbedLoaded] = useState(autoLoad || hideOverlay);
  const [isEmbedLoading, setIsEmbedLoading] = useState(false);
  const [embedError, setEmbedError] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [showOverlay, setShowOverlay] = useState(!hideOverlay && !autoLoad);
  const [isInView, setIsInView] = useState(false);
  const [shouldLoadIframe, setShouldLoadIframe] = useState(autoLoad || hideOverlay);
  const cardRef = useRef<HTMLDivElement>(null);

  // Optimized hover handlers with debouncing
  const debouncedSetHovered = debounce((hovered: boolean) => {
    setIsHovered(hovered);
  }, 50);

  // Lightning-fast Intersection Observer for optimal loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            // Lightning-fast loading: Load immediately when in view
            if (!shouldLoadIframe) {
              setShouldLoadIframe(true);
            }
          }
        });
      },
      {
        rootMargin: '200px', // Start loading 200px before card is visible for lightning-fast experience
        threshold: 0.05 // Lower threshold for faster triggering
      }
    );

    if (cardRef.current) {
      observer.observe(cardRef.current);
    }

    return () => {
      if (cardRef.current) {
        observer.unobserve(cardRef.current);
      }
    };
  }, [shouldLoadIframe]);

  const handleLoadEmbed = () => {
    setIsEmbedLoading(true);
    setIsEmbedLoaded(true);
    setEmbedError(false);
  };

  const handleEmbedLoad = () => {
    setIsEmbedLoading(false);
  };

  const handleEmbedError = () => {
    setIsEmbedLoading(false);
    setEmbedError(true);
  };

  const handleShare = async () => {
    const tourUrl = tour.slug ? `/tour/${tour.slug}` : createTourUrl(tour.title);
    const url = `${window.location.origin}${tourUrl}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: tour.title,
          text: tour.description || `Check out this virtual tour: ${tour.title}`,
          url: url,
        });
      } catch (err) {
        navigator.clipboard.writeText(url);
        alert('Tour link copied to clipboard!');
      }
    } else {
      navigator.clipboard.writeText(url);
      alert('Tour link copied to clipboard!');
    }
  };

  const tourUrl = tour.slug ? `/tour/${tour.slug}` : createTourUrl(tour.title);

  // VRT branding only (hide source platforms)
  const vrtBranding = {
    name: 'VRT',
    fullName: 'Virtual Real Tour',
    color: 'bg-blue-600 text-white'
  };

  return (
    <Card ref={cardRef} className={`overflow-hidden hover:shadow-lg transition-shadow w-full max-w-full card-hover-glow ${className}`}>
      {/* Embedded Tour Preview */}
      {showEmbedded && tour.embed_url && (
        <div
          className={`relative ${getHeightClass(height)}`}
          onMouseEnter={() => debouncedSetHovered(true)}
          onMouseLeave={() => debouncedSetHovered(false)}
        >
          {/* Conditional iframe loading for performance */}
          {shouldLoadIframe && tour.embed_url ? (
            <iframe
              src={getCleanEmbedUrl(tour.embed_url)}
              className={`w-full h-full border-0 clean-tour-iframe tour-card-iframe ${showOverlay ? 'with-overlay' : 'without-overlay'}`}
              title={tour.title}
              {...getCleanIframeProps()}
            />
          ) : (
            // Placeholder while not loaded
            <div className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
              <div className="text-center text-gray-500">
                <div className="w-16 h-16 mx-auto mb-2 bg-gray-300 rounded-full flex items-center justify-center">
                  <Play className="w-6 h-6 text-gray-600" />
                </div>
                <p className="text-sm font-medium">Virtual Tour</p>
                <p className="text-xs">Click to load</p>
              </div>
            </div>
          )}

          {/* Glass Morphism Overlay */}
          {showOverlay && (
            <div
              className="absolute inset-0 cursor-pointer transition-all duration-300 tour-card-overlay"
              onClick={() => {
                setShouldLoadIframe(true);
                setShowOverlay(false);
                setIsEmbedLoaded(true);
              }}
            >
              {/* Minimal Center Play Button - Only Visible on Hover */}
              {isHovered && (
                <div className="absolute inset-0 flex items-center justify-center z-10">
                  <div className={`
                    w-12 h-12 bg-white bg-opacity-95 backdrop-blur-sm rounded-full
                    flex items-center justify-center shadow-sm transition-all duration-200
                    hover:scale-105 hover:bg-opacity-100
                  `}>
                    <Play className="w-4 h-4 text-gray-800 ml-0.5" />
                  </div>
                </div>
              )}

              {/* Minimal VRT Badge - Bottom Right */}
              <div className="absolute bottom-2 right-2 z-10">
                <div className="bg-black bg-opacity-60 backdrop-blur-sm text-white text-xs px-2 py-1 rounded">
                  VRT
                </div>
              </div>
            </div>
          )}

          {/* Floating Controls - Always available when not showing overlay */}
          {!showOverlay && !embedError && showControls && (
            <div className="absolute top-2 right-2 z-30 flex gap-1">
              {isAdminPreview && (
                <Button
                  onClick={() => window.open(tourUrl, '_blank')}
                  variant="secondary"
                  size="sm"
                  className="bg-black bg-opacity-50 backdrop-blur-sm hover:bg-opacity-70 text-white border-0 h-8 w-8 p-0"
                  title="Open in new tab"
                >
                  <ExternalLink className="w-3 h-3" />
                </Button>
              )}
              <Button
                asChild
                variant="secondary"
                size="sm"
                className="bg-black bg-opacity-50 backdrop-blur-sm hover:bg-opacity-70 text-white border-0 h-8 w-8 p-0"
                title="Fullscreen"
              >
                <Link to={tourUrl}>
                  <Maximize2 className="w-3 h-3" />
                </Link>
              </Button>
            </div>
          )}
        </div>
      )}

      {/* Tour Information */}
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0 overflow-hidden">
              <h3 className="font-semibold text-lg leading-tight truncate break-words">
                {tour.title}
              </h3>
              {tour.location && (
                <div className="flex items-center text-sm text-gray-500 mt-1 min-w-0">
                  <MapPin className="w-3 h-3 mr-1 flex-shrink-0" />
                  <span className="truncate break-words">{tour.location}</span>
                </div>
              )}
            </div>
            
            {showActions && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {onEdit && (
                    <DropdownMenuItem onClick={() => onEdit(tour)}>
                      <Edit3 className="w-4 h-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                  )}
                  <DropdownMenuItem onClick={handleShare}>
                    <Share2 className="w-4 h-4 mr-2" />
                    Share
                  </DropdownMenuItem>
                  {onDelete && (
                    <DropdownMenuItem 
                      onClick={() => onDelete(tour)}
                      className="text-red-600"
                    >
                      Delete
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>

          {/* Description */}
          {tour.description && (
            <p className="text-sm text-gray-600 line-clamp-2 break-words overflow-hidden">
              {tour.description}
            </p>
          )}

          {/* Badges */}
          <div className="flex items-center gap-2 flex-wrap overflow-hidden">
            <Badge variant="outline" className="truncate">{tour.category}</Badge>
            <Badge variant="secondary" className="truncate">{tour.status}</Badge>
            {tour.business_type && (
              <Badge variant="outline" className="text-xs truncate">
                {tour.business_type}
              </Badge>
            )}
          </div>

          {/* Actions */}
          <div className="flex gap-2 pt-2">
            <Button asChild size="sm" className="flex-1 bg-blue-600 hover:bg-blue-700">
              <Link to={tourUrl}>
                <Eye className="w-4 h-4 mr-2" />
                View Tour
              </Link>
            </Button>
            {showActions && onEdit && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => onEdit(tour)}
                className="flex-1"
              >
                <Edit3 className="w-4 h-4 mr-2" />
                Edit
              </Button>
            )}
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between text-xs text-gray-400 pt-1 border-t">
            <span>Created {new Date(tour.created_at).toLocaleDateString()}</span>
            {tour.views > 0 && (
              <div className="flex items-center">
                <Eye className="w-3 h-3 mr-1" />
                <span>{tour.views} views</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default EnhancedTourCard;
