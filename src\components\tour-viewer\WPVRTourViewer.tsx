/**
 * WPVR Tour Viewer Component
 * Integrates WordPress WPVR plugin with React frontend
 * Handles hotspot clicks and triggers WooCommerce product overlays
 */

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft, 
  Share2, 
  Info, 
  MapPin, 
  Volume2, 
  VolumeX, 
  Maximize,
  Loader2,
  ShoppingCart,
  ExternalLink
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

// Import our commerce components
import ProductOverlay from '@/components/commerce/ProductOverlay';
import { useShoppingCart } from '@/hooks/useShoppingCart';
import { wooCommerceService } from '@/services/commerce/WooCommerceService';

interface WPVRTourViewerProps {
  tourSlug: string;
  wpvrBaseUrl: string; // e.g., "https://admin.virtualrealtour.ng"
  onBack?: () => void;
  onShare?: () => void;
  className?: string;
  autoplay?: boolean;
  showControls?: boolean;
}

interface WooCommerceProduct {
  id: number;
  name: string;
  price: string;
  regular_price: string;
  sale_price: string;
  description: string;
  short_description: string;
  images: Array<{
    id: number;
    src: string;
    alt: string;
  }>;
  meta_data: Array<{
    key: string;
    value: string;
  }>;
  vendor_whatsapp?: string;
}

const WPVRTourViewer: React.FC<WPVRTourViewerProps> = ({
  tourSlug,
  wpvrBaseUrl,
  onBack,
  onShare,
  className = '',
  autoplay = false,
  showControls = true
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isMuted, setIsMuted] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<WooCommerceProduct | null>(null);
  const [isProductLoading, setIsProductLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { addItem } = useShoppingCart();

  // Construct WPVR iframe URL
  const wpvrUrl = `${wpvrBaseUrl}/wp-content/plugins/wpvr/public/shortcode.php?id=${tourSlug}&autoplay=${autoplay ? 1 : 0}&muted=${isMuted ? 1 : 0}`;

  // Listen for postMessage from WPVR iframe
  useEffect(() => {
    const handleMessage = async (event: MessageEvent) => {
      // Verify origin for security
      if (!event.origin.includes(new URL(wpvrBaseUrl).hostname)) {
        return;
      }

      try {
        const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
        
        if (data.type === 'wpvr_hotspot_click' && data.productId) {
          await handleHotspotClick(data.productId);
        }
      } catch (error) {
        console.error('Error parsing message from WPVR:', error);
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [wpvrBaseUrl]);

  // Handle hotspot click - fetch product from WooCommerce
  const handleHotspotClick = useCallback(async (productId: string | number) => {
    setIsProductLoading(true);
    setError(null);

    try {
      const product = await wooCommerceService.getProduct(productId);
      
      // Extract WhatsApp number from meta data
      const whatsappMeta = product.meta_data?.find(meta => 
        meta.key === 'vendor_whatsapp' || meta.key === '_vendor_whatsapp'
      );
      
      if (whatsappMeta) {
        product.vendor_whatsapp = whatsappMeta.value;
      }

      setSelectedProduct(product);
      toast.success('Product loaded successfully');
    } catch (error) {
      console.error('Error fetching product:', error);
      setError('Failed to load product information');
      toast.error('Failed to load product');
    } finally {
      setIsProductLoading(false);
    }
  }, []);

  // Handle iframe load
  const handleIframeLoad = useCallback(() => {
    setIsLoading(false);
    toast.success('Virtual tour loaded');
  }, []);

  // Handle iframe error
  const handleIframeError = useCallback(() => {
    setIsLoading(false);
    setError('Failed to load virtual tour');
    toast.error('Failed to load tour');
  }, []);

  // Toggle fullscreen
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      iframeRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // Handle share
  const handleShare = useCallback(() => {
    if (navigator.share) {
      navigator.share({
        title: 'Virtual Tour',
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard');
    }
    onShare?.();
  }, [onShare]);

  return (
    <div className={cn('relative w-full h-screen bg-black', className)}>
      {/* Loading Overlay */}
      <AnimatePresence>
        {isLoading && (
          <motion.div
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 z-50 flex items-center justify-center bg-black"
          >
            <div className="text-center text-white">
              <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
              <p>Loading virtual tour...</p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error State */}
      {error && (
        <div className="absolute inset-0 z-40 flex items-center justify-center bg-black bg-opacity-75">
          <Card className="max-w-md mx-4">
            <CardContent className="p-6 text-center">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={() => window.location.reload()}>
                Retry
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Controls Overlay */}
      {showControls && !isFullscreen && (
        <div className="absolute top-4 left-4 right-4 z-30 flex justify-between items-center">
          <div className="flex gap-2">
            {onBack && (
              <Button
                variant="secondary"
                size="sm"
                onClick={onBack}
                className="bg-black/20 backdrop-blur-sm border-white/10 text-white hover:bg-black/40"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
            )}
          </div>

          <div className="flex gap-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setIsMuted(!isMuted)}
              className="bg-black/20 backdrop-blur-sm border-white/10 text-white hover:bg-black/40"
            >
              {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
            </Button>
            
            <Button
              variant="secondary"
              size="sm"
              onClick={handleShare}
              className="bg-black/20 backdrop-blur-sm border-white/10 text-white hover:bg-black/40"
            >
              <Share2 className="w-4 h-4" />
            </Button>
            
            <Button
              variant="secondary"
              size="sm"
              onClick={toggleFullscreen}
              className="bg-black/20 backdrop-blur-sm border-white/10 text-white hover:bg-black/40"
            >
              <Maximize className="w-4 h-4" />
            </Button>
          </div>
        </div>
      )}

      {/* WPVR Iframe */}
      <iframe
        ref={iframeRef}
        src={wpvrUrl}
        className="w-full h-full border-0"
        allowFullScreen
        title="Virtual Tour"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        allow="vr; xr; accelerometer; gyroscope; autoplay; fullscreen; camera; microphone; geolocation;"
        sandbox="allow-scripts allow-same-origin allow-presentation allow-orientation-lock allow-pointer-lock allow-forms"
      />

      {/* Product Overlay */}
      <ProductOverlay
        product={selectedProduct}
        isOpen={!!selectedProduct}
        isLoading={isProductLoading}
        onClose={() => setSelectedProduct(null)}
        onAddToCart={(product) => {
          addItem({
            id: product.id.toString(),
            name: product.name,
            price: parseFloat(product.price),
            image: product.images[0]?.src || '',
            quantity: 1,
            vendor_whatsapp: product.vendor_whatsapp
          });
          toast.success('Added to cart');
        }}
      />
    </div>
  );
};

export default WPVRTourViewer;
