
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Upload } from 'lucide-react';

interface FileUploadZoneProps {
  onFileUpload: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const FileUploadZone = ({ onFileUpload }: FileUploadZoneProps) => {
  return (
    <div className="space-y-4">
      <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 md:p-8 text-center hover:border-primary/50 transition-colors">
        <Upload className="w-8 h-8 md:w-12 md:h-12 text-muted-foreground mx-auto mb-4" />
        <p className="text-foreground mb-2 text-sm md:text-base">Drag and drop your 360° images here</p>
        <p className="text-xs md:text-sm text-muted-foreground mb-4">Supports: JPG, PNG, JPEG (Max 100MB per file)</p>
        <input
          type="file"
          multiple
          accept="image/*"
          onChange={onFileUpload}
          className="hidden"
          id="file-upload"
        />
        <Button asChild variant="outline" className="w-full sm:w-auto">
          <label htmlFor="file-upload" className="cursor-pointer">
            Choose Files
          </label>
        </Button>
      </div>
      <p className="text-xs text-muted-foreground">
        You can also create a tour without uploading files initially and add media later.
      </p>
    </div>
  );
};

export default FileUploadZone;
