/**
 * Custom Tour Creation Service
 * Uses our existing Photo Sphere Viewer and Supabase backend
 * Always available, no external dependencies
 */

import { 
  TourCreationService, 
  TourConfig, 
  SceneConfig, 
  HotspotConfig, 
  CreatedTour, 
  TourScene, 
  TourHotspot,
  TourCreationError,
  EmbedOptions,
  EmbedCallbacks,
  TourAnalytics
} from './types';
import { supabase } from '@/lib/supabase';
import { generateSlug } from '@/lib/slugUtils';

export class CustomTourService implements TourCreationService {
  readonly platform = 'custom' as const;

  get isConfigured(): boolean {
    // Always available since it uses our own infrastructure
    return true;
  }

  async createTour(config: TourConfig): Promise<CreatedTour> {
    try {
      const slug = generateSlug(config.title);
      
      // Create tour in our Supabase database
      const { data: tour, error } = await supabase
        .from('tours')
        .insert({
          title: config.title,
          description: config.description,
          category: config.category,
          location: config.location,
          business_name: config.businessInfo?.name,
          business_type: config.businessInfo?.type,
          contact_phone: config.businessInfo?.phone,
          contact_email: config.businessInfo?.email,
          website: config.businessInfo?.website,
          business_hours: config.businessInfo?.hours,
          slug,
          creation_method: 'native',
          tour_platform: 'custom',
          tour_config: {
            ...config.settings,
            businessInfo: config.businessInfo
          },
          status: 'draft'
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: tour.id,
        title: tour.title,
        description: tour.description,
        embedUrl: `${window.location.origin}/tour/${tour.slug}`,
        editUrl: `${window.location.origin}/admin/tours/${tour.id}/edit`,
        previewUrl: `${window.location.origin}/tour/${tour.slug}?preview=true`,
        scenes: [],
        platform: 'custom',
        platformSpecific: {
          customData: {
            supabaseId: tour.id,
            slug: tour.slug
          }
        },
        settings: config.settings,
        createdAt: new Date(tour.created_at),
        updatedAt: new Date(tour.updated_at),
      };
    } catch (error) {
      throw new TourCreationError(
        'CREATE_FAILED',
        'Failed to create tour in custom service',
        error,
        'custom'
      );
    }
  }

  async updateTour(tourId: string, config: Partial<TourConfig>): Promise<CreatedTour> {
    try {
      const { data: tour, error } = await supabase
        .from('tours')
        .update({
          title: config.title,
          description: config.description,
          category: config.category,
          location: config.location,
          business_name: config.businessInfo?.name,
          business_type: config.businessInfo?.type,
          contact_phone: config.businessInfo?.phone,
          contact_email: config.businessInfo?.email,
          website: config.businessInfo?.website,
          business_hours: config.businessInfo?.hours,
          tour_config: {
            ...config.settings,
            businessInfo: config.businessInfo
          }
        })
        .eq('id', tourId)
        .select()
        .single();

      if (error) throw error;

      return this.mapSupabaseTourToCreatedTour(tour);
    } catch (error) {
      throw new TourCreationError(
        'UPDATE_FAILED',
        'Failed to update tour in custom service',
        error,
        'custom'
      );
    }
  }

  async deleteTour(tourId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('tours')
        .delete()
        .eq('id', tourId);

      if (error) throw error;
    } catch (error) {
      throw new TourCreationError(
        'DELETE_FAILED',
        'Failed to delete tour in custom service',
        error,
        'custom'
      );
    }
  }

  async getTour(tourId: string): Promise<CreatedTour> {
    try {
      const { data: tour, error } = await supabase
        .from('tours')
        .select('*')
        .eq('id', tourId)
        .single();

      if (error) throw error;

      return this.mapSupabaseTourToCreatedTour(tour);
    } catch (error) {
      throw new TourCreationError(
        'GET_FAILED',
        'Failed to get tour from custom service',
        error,
        'custom'
      );
    }
  }

  async listTours(): Promise<CreatedTour[]> {
    try {
      const { data: tours, error } = await supabase
        .from('tours')
        .select('*')
        .eq('tour_platform', 'custom')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return tours.map(tour => this.mapSupabaseTourToCreatedTour(tour));
    } catch (error) {
      throw new TourCreationError(
        'LIST_FAILED',
        'Failed to list tours from custom service',
        error,
        'custom'
      );
    }
  }

  async addScene(tourId: string, scene: SceneConfig): Promise<TourScene> {
    try {
      let imageUrl = scene.imageUrl;

      // Upload image if file is provided
      if (scene.imageFile && !imageUrl) {
        imageUrl = await this.uploadImage(scene.imageFile);
      }

      const { data: sceneData, error } = await supabase
        .from('scenes')
        .insert({
          tour_id: tourId,
          name: scene.name,
          description: scene.description,
          image_url: imageUrl,
          order_index: scene.orderIndex,
          settings: scene.settings
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: sceneData.id,
        name: sceneData.name,
        description: sceneData.description,
        imageUrl: sceneData.image_url,
        orderIndex: sceneData.order_index,
        hotspots: [],
        platformSpecific: {
          customData: {
            supabaseId: sceneData.id
          }
        }
      };
    } catch (error) {
      throw new TourCreationError(
        'SCENE_ADD_FAILED',
        'Failed to add scene to custom tour',
        error,
        'custom'
      );
    }
  }

  async updateScene(tourId: string, sceneId: string, scene: Partial<SceneConfig>): Promise<TourScene> {
    try {
      const { data: sceneData, error } = await supabase
        .from('scenes')
        .update({
          name: scene.name,
          description: scene.description,
          order_index: scene.orderIndex,
          settings: scene.settings
        })
        .eq('id', sceneId)
        .eq('tour_id', tourId)
        .select()
        .single();

      if (error) throw error;

      return {
        id: sceneData.id,
        name: sceneData.name,
        description: sceneData.description,
        imageUrl: sceneData.image_url,
        orderIndex: sceneData.order_index,
        hotspots: [],
        platformSpecific: {
          customData: {
            supabaseId: sceneData.id
          }
        }
      };
    } catch (error) {
      throw new TourCreationError(
        'SCENE_UPDATE_FAILED',
        'Failed to update scene in custom service',
        error,
        'custom'
      );
    }
  }

  async deleteScene(tourId: string, sceneId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('scenes')
        .delete()
        .eq('id', sceneId)
        .eq('tour_id', tourId);

      if (error) throw error;
    } catch (error) {
      throw new TourCreationError(
        'SCENE_DELETE_FAILED',
        'Failed to delete scene from custom service',
        error,
        'custom'
      );
    }
  }

  async reorderScenes(tourId: string, sceneIds: string[]): Promise<void> {
    try {
      // Update order_index for each scene
      for (let i = 0; i < sceneIds.length; i++) {
        const { error } = await supabase
          .from('scenes')
          .update({ order_index: i })
          .eq('id', sceneIds[i])
          .eq('tour_id', tourId);

        if (error) throw error;
      }
    } catch (error) {
      throw new TourCreationError(
        'SCENE_REORDER_FAILED',
        'Failed to reorder scenes in custom service',
        error,
        'custom'
      );
    }
  }

  async addHotspot(tourId: string, sceneId: string, hotspot: HotspotConfig): Promise<TourHotspot> {
    try {
      const { data: hotspotData, error } = await supabase
        .from('hotspots')
        .insert({
          scene_id: sceneId,
          type: hotspot.type,
          label: hotspot.label,
          content: hotspot.content,
          position_x: hotspot.position.x,
          position_y: hotspot.position.y,
          position_z: hotspot.position.z,
          style: hotspot.style,
          actions: hotspot.actions,
          target_scene_id: hotspot.targetSceneId,
          product_id: hotspot.productId,
          vendor_id: hotspot.vendorId,
          link_url: hotspot.linkUrl,
          whatsapp_phone: hotspot.whatsappPhone,
          whatsapp_message: hotspot.whatsappMessage,
          media_url: hotspot.mediaUrl
        })
        .select()
        .single();

      if (error) throw error;

      return {
        id: hotspotData.id,
        type: hotspotData.type,
        label: hotspotData.label,
        content: hotspotData.content,
        position: {
          x: hotspotData.position_x,
          y: hotspotData.position_y,
          z: hotspotData.position_z
        },
        style: hotspotData.style,
        actions: hotspotData.actions,
        platformSpecific: {
          customData: {
            supabaseId: hotspotData.id
          }
        }
      };
    } catch (error) {
      throw new TourCreationError(
        'HOTSPOT_ADD_FAILED',
        'Failed to add hotspot to custom scene',
        error,
        'custom'
      );
    }
  }

  async updateHotspot(tourId: string, sceneId: string, hotspotId: string, hotspot: Partial<HotspotConfig>): Promise<TourHotspot> {
    // Implementation similar to addHotspot but with update
    throw new TourCreationError('NOT_IMPLEMENTED', 'Update hotspot not yet implemented', null, 'custom');
  }

  async deleteHotspot(tourId: string, sceneId: string, hotspotId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('hotspots')
        .delete()
        .eq('id', hotspotId);

      if (error) throw error;
    } catch (error) {
      throw new TourCreationError(
        'HOTSPOT_DELETE_FAILED',
        'Failed to delete hotspot from custom service',
        error,
        'custom'
      );
    }
  }

  async publishTour(tourId: string): Promise<CreatedTour> {
    try {
      const { data: tour, error } = await supabase
        .from('tours')
        .update({ status: 'published' })
        .eq('id', tourId)
        .select()
        .single();

      if (error) throw error;

      return this.mapSupabaseTourToCreatedTour(tour);
    } catch (error) {
      throw new TourCreationError(
        'PUBLISH_FAILED',
        'Failed to publish tour in custom service',
        error,
        'custom'
      );
    }
  }

  async unpublishTour(tourId: string): Promise<CreatedTour> {
    try {
      const { data: tour, error } = await supabase
        .from('tours')
        .update({ status: 'draft' })
        .eq('id', tourId)
        .select()
        .single();

      if (error) throw error;

      return this.mapSupabaseTourToCreatedTour(tour);
    } catch (error) {
      throw new TourCreationError(
        'UNPUBLISH_FAILED',
        'Failed to unpublish tour in custom service',
        error,
        'custom'
      );
    }
  }

  async getAnalytics(tourId: string, dateRange?: { start: Date; end: Date }): Promise<TourAnalytics> {
    try {
      let query = supabase
        .from('tour_analytics')
        .select('*')
        .eq('tour_id', tourId);

      if (dateRange) {
        query = query
          .gte('viewed_at', dateRange.start.toISOString())
          .lte('viewed_at', dateRange.end.toISOString());
      }

      const { data: analytics, error } = await query;

      if (error) throw error;

      // Process analytics data
      const views = analytics?.length || 0;
      const uniqueViews = new Set(analytics?.map(a => a.user_id)).size || 0;

      return {
        views,
        uniqueViews,
        averageTimeSpent: 0, // Calculate from analytics data
        hotspotInteractions: {},
        sceneViews: {},
        conversionEvents: []
      };
    } catch (error) {
      throw new TourCreationError(
        'ANALYTICS_FAILED',
        'Failed to get analytics from custom service',
        error,
        'custom'
      );
    }
  }

  getEmbedCode(tourId: string, options: EmbedOptions = {}): string {
    const width = options.width || '100%';
    const height = options.height || '400px';
    const allowFullscreen = options.allowFullscreen !== false;

    return `<iframe 
      src="${window.location.origin}/tour/${tourId}" 
      width="${width}" 
      height="${height}" 
      frameborder="0" 
      ${allowFullscreen ? 'allowfullscreen' : ''}
      ${options.customCSS ? `style="${options.customCSS}"` : ''}
    ></iframe>`;
  }

  setupEmbedAPI(tourId: string, callbacks?: EmbedCallbacks): void {
    // Custom embed API setup using postMessage
    if (typeof window !== 'undefined') {
      window.addEventListener('message', (event) => {
        if (event.origin !== window.location.origin) return;
        
        const { type, data } = event.data;
        
        switch (type) {
          case 'hotspot_click':
            callbacks?.onHotspotClick?.(data);
            break;
          case 'scene_change':
            callbacks?.onSceneChange?.(data);
            break;
          case 'tour_complete':
            callbacks?.onTourComplete?.();
            break;
          case 'error':
            callbacks?.onError?.(data.message);
            break;
        }
      });
    }
  }

  private async uploadImage(file: File): Promise<string> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      
      const { error: uploadError } = await supabase.storage
        .from('tour-media')
        .upload(fileName, file);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('tour-media')
        .getPublicUrl(fileName);

      return publicUrl;
    } catch (error) {
      throw new TourCreationError(
        'UPLOAD_FAILED',
        'Failed to upload image to custom service',
        error,
        'custom'
      );
    }
  }

  private mapSupabaseTourToCreatedTour(tour: any): CreatedTour {
    return {
      id: tour.id,
      title: tour.title,
      description: tour.description,
      embedUrl: `${window.location.origin}/tour/${tour.slug}`,
      editUrl: `${window.location.origin}/admin/tours/${tour.id}/edit`,
      previewUrl: `${window.location.origin}/tour/${tour.slug}?preview=true`,
      scenes: [], // Load separately if needed
      platform: 'custom',
      platformSpecific: {
        customData: {
          supabaseId: tour.id,
          slug: tour.slug
        }
      },
      settings: tour.tour_config,
      createdAt: new Date(tour.created_at),
      updatedAt: new Date(tour.updated_at),
    };
  }
}
