/**
 * CommonNinja Widget Editor - Direct Widget Embedding
 * Primary tour creation method using CommonNinja widgets with API as fallback
 */

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  ExternalLink, 
  Copy, 
  Check, 
  AlertCircle,
  Play,
  Settings,
  Save,
  Sparkles,
  Code,
  Globe,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';

interface CommonNinjaWidgetEditorProps {
  tourId: string;
  tourData: {
    title: string;
    description: string;
    category: string;
    location: string;
    business_name?: string;
    business_phone?: string;
    business_email?: string;
    business_whatsapp?: string;
    business_address?: string;
    business_website?: string;
  };
  onSave?: (tourData: any) => void;
  onPublish?: (tourData: any) => void;
  onClose?: () => void;
}

const CommonNinjaWidgetEditor = ({
  tourId,
  tourData,
  onSave,
  onPublish,
  onClose
}: CommonNinjaWidgetEditorProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [widgetEmbedCode, setWidgetEmbedCode] = useState('');
  const [customWidgetId, setCustomWidgetId] = useState('');
  const [useApiMode, setUseApiMode] = useState(false);
  const [isCopied, setIsCopied] = useState(false);
  const widgetContainerRef = useRef<HTMLDivElement>(null);

  // Load saved embed code on mount
  useEffect(() => {
    const savedEmbedCode = localStorage.getItem(`tour-${tourId}-widget-embed`);
    const savedWidgetId = localStorage.getItem(`tour-${tourId}-widget-id`);
    
    if (savedEmbedCode) {
      setWidgetEmbedCode(savedEmbedCode);
    }
    if (savedWidgetId) {
      setCustomWidgetId(savedWidgetId);
    }

    // Check if API mode should be used as fallback
    const apiMode = localStorage.getItem(`tour-${tourId}-api-mode`) === 'true';
    setUseApiMode(apiMode);

    setIsLoading(false);
  }, [tourId]);

  // Render widget when embed code changes
  useEffect(() => {
    if (widgetEmbedCode && widgetContainerRef.current) {
      renderWidget();
    }
  }, [widgetEmbedCode]);

  const renderWidget = () => {
    if (!widgetContainerRef.current || !widgetEmbedCode) return;

    try {
      // Clear previous content
      widgetContainerRef.current.innerHTML = '';
      
      // Create a wrapper div for the widget
      const wrapper = document.createElement('div');
      wrapper.className = 'w-full h-full min-h-[600px]';
      wrapper.innerHTML = widgetEmbedCode;
      
      widgetContainerRef.current.appendChild(wrapper);
      
      toast.success('Widget loaded successfully!');
    } catch (error) {
      console.error('Error rendering widget:', error);
      toast.error('Failed to load widget. Please check the embed code.');
    }
  };

  const handleEmbedCodeChange = (code: string) => {
    setWidgetEmbedCode(code);
    
    // Save to localStorage
    localStorage.setItem(`tour-${tourId}-widget-embed`, code);
    
    // Extract widget ID if possible
    const widgetIdMatch = code.match(/widget\/([a-zA-Z0-9-_]+)/);
    if (widgetIdMatch) {
      const extractedId = widgetIdMatch[1];
      setCustomWidgetId(extractedId);
      localStorage.setItem(`tour-${tourId}-widget-id`, extractedId);
    }
  };

  const handleWidgetIdChange = (id: string) => {
    setCustomWidgetId(id);
    localStorage.setItem(`tour-${tourId}-widget-id`, id);
    
    // Generate embed code from widget ID
    if (id.trim()) {
      const generatedEmbed = `<iframe src="https://widget.commoninja.com/widget/${id}" width="100%" height="600" frameborder="0" allowfullscreen title="VirtualRealTour - ${tourData.title}"></iframe>`;
      setWidgetEmbedCode(generatedEmbed);
      localStorage.setItem(`tour-${tourId}-widget-embed`, generatedEmbed);
    }
  };

  const copyEmbedCode = async () => {
    if (!widgetEmbedCode) return;
    
    try {
      await navigator.clipboard.writeText(widgetEmbedCode);
      setIsCopied(true);
      toast.success('Embed code copied to clipboard!');
      setTimeout(() => setIsCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy embed code');
    }
  };

  const handleSave = async () => {
    try {
      const tourSaveData = {
        id: tourId,
        embed_url: widgetEmbedCode,
        commonninja_widget_id: customWidgetId,
        tour_platform: 'commonninja',
        creation_method: 'widget',
        status: 'draft',
        updated_at: new Date().toISOString()
      };

      onSave?.(tourSaveData);
      toast.success('Tour saved successfully!');
    } catch (error) {
      console.error('Error saving tour:', error);
      toast.error('Failed to save tour');
    }
  };

  const handlePublish = async () => {
    if (!widgetEmbedCode) {
      toast.error('Please add a widget embed code before publishing');
      return;
    }

    try {
      const tourPublishData = {
        id: tourId,
        embed_url: widgetEmbedCode,
        commonninja_widget_id: customWidgetId,
        tour_platform: 'commonninja',
        creation_method: 'widget',
        status: 'published',
        published_at: new Date().toISOString()
      };

      onPublish?.(tourPublishData);
      toast.success('Tour published successfully!');
    } catch (error) {
      console.error('Error publishing tour:', error);
      toast.error('Failed to publish tour');
    }
  };

  const switchToApiMode = () => {
    setUseApiMode(true);
    localStorage.setItem(`tour-${tourId}-api-mode`, 'true');
    toast.info('Switched to API mode. Refresh to use API integration.');
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground">Loading widget editor...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-3">
              <Sparkles className="h-5 w-5 text-primary" />
              <div>
                <CardTitle className="text-lg">{tourData.title}</CardTitle>
                <p className="text-sm text-muted-foreground">CommonNinja Widget Integration</p>
              </div>
            </div>
            <div className="flex items-center gap-2 flex-wrap">
              <Badge variant="outline">Widget Mode</Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={switchToApiMode}
                title="Switch to API mode as fallback"
                className="shrink-0"
              >
                <Code className="h-4 w-4 mr-2" />
                API Mode
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="flex-1 flex flex-col lg:grid lg:grid-cols-3 gap-4">
        {/* Widget Configuration */}
        <Card className="w-full lg:col-span-1 order-2 lg:order-1">
          <CardHeader>
            <CardTitle className="text-base">Widget Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Tabs defaultValue="embed" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="embed">Embed Code</TabsTrigger>
                <TabsTrigger value="id">Widget ID</TabsTrigger>
              </TabsList>
              
              <TabsContent value="embed" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="embedCode">CommonNinja Embed Code</Label>
                  <Textarea
                    id="embedCode"
                    placeholder="Paste your CommonNinja widget embed code here..."
                    value={widgetEmbedCode}
                    onChange={(e) => handleEmbedCodeChange(e.target.value)}
                    rows={6}
                    className="font-mono text-xs"
                  />
                </div>
                
                {widgetEmbedCode && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyEmbedCode}
                    className="w-full"
                  >
                    {isCopied ? (
                      <Check className="h-4 w-4 mr-2" />
                    ) : (
                      <Copy className="h-4 w-4 mr-2" />
                    )}
                    {isCopied ? 'Copied!' : 'Copy Embed Code'}
                  </Button>
                )}
              </TabsContent>
              
              <TabsContent value="id" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="widgetId">Widget ID</Label>
                  <Input
                    id="widgetId"
                    placeholder="Enter CommonNinja widget ID..."
                    value={customWidgetId}
                    onChange={(e) => handleWidgetIdChange(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    The widget ID from your CommonNinja dashboard
                  </p>
                </div>
              </TabsContent>
            </Tabs>

            <Separator />

            <div className="space-y-2">
              <Button
                onClick={() => window.open('https://www.commoninja.com/virtual-tour/editor', '_blank')}
                className="w-full"
                variant="outline"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Create Widget
              </Button>
              
              <div className="flex gap-2">
                <Button onClick={handleSave} size="sm" variant="outline" className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </Button>
                <Button onClick={handlePublish} size="sm" className="flex-1">
                  <Globe className="h-4 w-4 mr-2" />
                  Publish
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Widget Preview */}
        <Card className="w-full lg:col-span-2 order-1 lg:order-2">
          <CardHeader>
            <CardTitle className="text-base">Widget Preview</CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            {widgetEmbedCode ? (
              <div
                ref={widgetContainerRef}
                className="w-full h-full min-h-[600px] bg-gray-50 rounded-lg"
              />
            ) : (
              <div className="h-[600px] flex items-center justify-center bg-gray-50 rounded-lg">
                <div className="text-center max-w-md p-8">
                  <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No Widget Configured</h3>
                  <p className="text-muted-foreground mb-4">
                    Add a CommonNinja widget embed code or widget ID to see the preview.
                  </p>
                  <Button
                    onClick={() => window.open('https://www.commoninja.com/virtual-tour/editor', '_blank')}
                    variant="outline"
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Create Widget
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CommonNinjaWidgetEditor;
