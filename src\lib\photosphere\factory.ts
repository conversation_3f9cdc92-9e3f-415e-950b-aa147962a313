/**
 * Photo Sphere Viewer Factory
 * Production-ready factory for creating PSV instances with plugins
 */

import { Viewer } from '@photo-sphere-viewer/core';
import { VirtualTourPlugin } from '@photo-sphere-viewer/virtual-tour-plugin';
import { MarkersPlugin } from '@photo-sphere-viewer/markers-plugin';
import { AutorotatePlugin } from '@photo-sphere-viewer/autorotate-plugin';
import { GalleryPlugin } from '@photo-sphere-viewer/gallery-plugin';
import { SettingsPlugin } from '@photo-sphere-viewer/settings-plugin';

import type {
  PSVConfig,
  PSVInstance,
  TourScene,
  VirtualTourConfig,
  GalleryConfig,
  SettingsConfig,
  PSVPlugins
} from './types';

import {
  PSVErrorBoundary,
  PSVMemoryManager,
  PSVPerformanceMonitor,
  createPSVConfig,
  setupWebGLErrorHandling,
  createErrorHandler
} from './utils';

import { virtualTourService } from './virtualTourService';
import { MarkerService, type MarkerServiceOptions } from './markerService';
import { EnterpriseFeatures, type EnterpriseSettings } from './enterpriseFeatures';

export interface PSVFactoryOptions {
  // Core configuration
  container: HTMLElement | string;
  panorama: string;

  // Plugin configurations
  virtualTour?: VirtualTourConfig;
  gallery?: GalleryConfig;
  settings?: SettingsConfig;
  markers?: MarkerServiceOptions;
  enterprise?: Partial<EnterpriseSettings>;

  // Feature flags
  enableAutorotate?: boolean;
  enableMarkers?: boolean;
  enableVirtualTour?: boolean;
  enableGallery?: boolean;
  enableSettings?: boolean;

  // Performance options
  enablePerformanceMonitoring?: boolean;
  enableProgressiveLoading?: boolean;

  // Event handlers
  onReady?: (instance: PSVInstance) => void;
  onError?: (error: any) => void;
  onSceneChange?: (sceneId: string) => void;
  onMarkerClick?: (markerId: string, markerData: any) => void;

  // Custom configuration overrides
  customConfig?: Partial<PSVConfig>;
}

/**
 * Main factory class for creating PSV instances
 */
export class PSVFactory {
  private static errorBoundary = new PSVErrorBoundary();
  
  /**
   * Create a complete PSV instance with plugins
   */
  static async create(options: PSVFactoryOptions): Promise<PSVInstance> {
    const monitor = options.enablePerformanceMonitoring ? new PSVPerformanceMonitor() : null;
    const errorHandler = createErrorHandler(options.onError);
    
    try {
      monitor?.startInit();
      
      // Create base configuration
      const config = createPSVConfig(
        options.container,
        options.panorama,
        options.customConfig
      );
      
      // Configure plugins
      const plugins: Array<[any, any?]> = [];
      
      // Virtual Tour Plugin
      if (options.enableVirtualTour && options.virtualTour) {
        plugins.push([VirtualTourPlugin, {
          ...options.virtualTour,
          dataMode: options.virtualTour.dataMode || 'client',
          renderMode: options.virtualTour.renderMode || 'markers',
          transition: {
            duration: 1500,
            loader: true,
            blur: false,
            ...options.virtualTour.transition
          }
        }]);
      }
      
      // Markers Plugin
      if (options.enableMarkers) {
        plugins.push([MarkersPlugin, {
          markers: []
        }]);
      }
      
      // Autorotate Plugin
      if (options.enableAutorotate) {
        plugins.push([AutorotatePlugin, {
          autostartDelay: 2000,
          autorotateSpeed: '2rpm',
          autorotateIdle: true
        }]);
      }
      
      // Gallery Plugin
      if (options.enableGallery && options.gallery) {
        plugins.push([GalleryPlugin, {
          ...options.gallery,
          visibleOnLoad: options.gallery.visibleOnLoad ?? false,
          hideOnClick: options.gallery.hideOnClick ?? true
        }]);
      }
      
      // Settings Plugin
      if (options.enableSettings) {
        plugins.push([SettingsPlugin, {
          persist: true,
          ...options.settings
        }]);
      }
      
      // Add plugins to config
      config.plugins = plugins;
      
      // Initialize viewer with error boundary
      const viewer = await this.errorBoundary.initializeWithRetry(config);
      
      monitor?.recordInit();
      
      // Setup WebGL error handling
      setupWebGLErrorHandling(viewer);
      
      // Get plugin instances
      const pluginInstances: PSVPlugins = {
        virtualTour: options.enableVirtualTour ? viewer.getPlugin(VirtualTourPlugin) : undefined,
        markers: options.enableMarkers ? viewer.getPlugin(MarkersPlugin) : undefined,
        autorotate: options.enableAutorotate ? viewer.getPlugin(AutorotatePlugin) : undefined,
        gallery: options.enableGallery ? viewer.getPlugin(GalleryPlugin) : undefined,
        settings: options.enableSettings ? viewer.getPlugin(SettingsPlugin) : undefined
      };

      // Initialize marker service if markers are enabled
      let markerService: MarkerService | undefined;
      if (options.enableMarkers && pluginInstances.markers) {
        markerService = new MarkerService({
          whatsappNumber: '2349077776066', // VirtualRealTour WhatsApp
          onProductClick: (product) => {
            const message = product.whatsappMessage ||
              `Hi! I'm interested in ${product.name} (${product.currency || '₦'}${product.price.toLocaleString()}) from your virtual tour.`;

            const whatsappUrl = `https://wa.me/${product.vendorPhone || '2349077776066'}?text=${encodeURIComponent(message)}`;
            window.open(whatsappUrl, '_blank');
          },
          onNavigationClick: (targetSceneId) => {
            if (options.onSceneChange) {
              options.onSceneChange(targetSceneId);
            }
          },
          onMarkerClick: (data) => {
            if (options.onMarkerClick) {
              options.onMarkerClick(data.marker.id, data.marker.data);
            }
          },
          ...options.markers
        });

        markerService.initialize(viewer, pluginInstances.markers);
      }

      // Initialize enterprise features
      let enterpriseFeatures: EnterpriseFeatures | undefined;
      if (options.enterprise) {
        enterpriseFeatures = new EnterpriseFeatures(options.enterprise);
      }
      
      // Setup event listeners
      this.setupEventListeners(viewer, pluginInstances, options);
      
      // Create instance wrapper
      const instance: PSVInstance = {
        viewer,
        plugins: pluginInstances,
        config,
        markerService,
        enterpriseFeatures,
        destroy: () => {
          if (markerService) {
            markerService.destroy();
          }
          if (enterpriseFeatures) {
            enterpriseFeatures.destroy();
          }
          PSVMemoryManager.cleanup(instance);
        },
        refresh: () => viewer.refresh(),
        resize: () => viewer.resize()
      };
      
      // Register for memory management
      PSVMemoryManager.register(instance);
      
      // Wait for ready event
      await new Promise<void>((resolve) => {
        viewer.addEventListener('ready', () => {
          monitor?.recordLoad();

          // Initialize enterprise features after viewer is ready
          if (enterpriseFeatures) {
            enterpriseFeatures.initialize(instance);
          }

          resolve();
        }, { once: true });
      });

      // Call ready callback
      if (options.onReady) {
        options.onReady(instance);
      }
      
      // Log performance metrics
      if (monitor) {
        monitor.logMetrics();
      }
      
      return instance;
      
    } catch (error) {
      monitor?.recordError();
      errorHandler(error, { options });
      throw error;
    }
  }
  
  /**
   * Create a simple viewer without virtual tour functionality
   */
  static async createSimple(
    container: HTMLElement | string,
    panorama: string,
    options: Partial<PSVFactoryOptions> = {}
  ): Promise<PSVInstance> {
    return this.create({
      container,
      panorama,
      enableMarkers: true,
      enableAutorotate: true,
      enableSettings: true,
      ...options
    });
  }
  
  /**
   * Create a virtual tour viewer with server-side node management
   */
  static async createVirtualTour(
    container: HTMLElement | string,
    scenes: TourScene[],
    options: Partial<PSVFactoryOptions> = {}
  ): Promise<PSVInstance> {
    if (scenes.length === 0) {
      throw new Error('At least one scene is required for virtual tour');
    }

    const virtualTourConfig: VirtualTourConfig = {
      nodes: scenes,
      startNodeId: scenes[0].id,
      preload: (node, link) => {
        // Use virtual tour service for intelligent preloading
        return virtualTourService.shouldPreloadNode(node, link);
      },
      transition: {
        duration: 1500,
        loader: true,
        blur: false
      },
      linksOnCompass: true,
      renderMode: 'markers',
      dataMode: 'server',
      getNode: async (nodeId: string) => {
        // Server-side node loading for dynamic tours
        const node = await virtualTourService.getNode(nodeId);
        if (!node) {
          throw new Error(`Node ${nodeId} not found`);
        }
        return node;
      },
      ...options.virtualTour
    };

    return this.create({
      container,
      panorama: scenes[0].panorama,
      enableVirtualTour: true,
      enableMarkers: true,
      enableAutorotate: true,
      enableGallery: true,
      enableSettings: true,
      virtualTour: virtualTourConfig,
      gallery: {
        items: scenes.map(scene => ({
          id: scene.id,
          name: scene.name,
          panorama: scene.panorama,
          thumbnail: scene.thumbnail
        })),
        visibleOnLoad: false,
        hideOnClick: true
      },
      ...options
    });
  }

  /**
   * Create a virtual tour from database tour ID
   */
  static async createVirtualTourFromId(
    container: HTMLElement | string,
    tourId: string,
    options: Partial<PSVFactoryOptions> = {}
  ): Promise<PSVInstance> {
    const tourData = await virtualTourService.loadTour(tourId);

    if (!tourData) {
      throw new Error(`Tour ${tourId} not found`);
    }

    return this.createVirtualTour(container, tourData.nodes, {
      ...options,
      virtualTour: {
        startNodeId: tourData.startNodeId,
        ...tourData.settings,
        ...options.virtualTour
      }
    });
  }
  
  /**
   * Setup event listeners for viewer and plugins
   */
  private static setupEventListeners(
    viewer: Viewer,
    plugins: PSVPlugins,
    options: PSVFactoryOptions
  ): void {
    // Core viewer events
    viewer.addEventListener('ready', () => {
      console.log('PSV: Viewer ready');
    });
    
    viewer.addEventListener('panorama-error', (error) => {
      console.error('PSV: Panorama load error:', error);
      if (options.onError) {
        options.onError(error);
      }
    });
    
    // Virtual tour events
    if (plugins.virtualTour && options.onSceneChange) {
      plugins.virtualTour.addEventListener('node-changed', (e) => {
        options.onSceneChange!(e.nodeId);
      });
    }
    
    // Marker events
    if (plugins.markers && options.onMarkerClick) {
      plugins.markers.addEventListener('select-marker', (e) => {
        options.onMarkerClick!(e.marker.id, e.marker.data);
      });
    }
    
    // Autorotate events
    if (plugins.autorotate) {
      plugins.autorotate.addEventListener('autorotate', (e) => {
        console.log('PSV: Autorotate', e.enabled ? 'started' : 'stopped');
      });
    }
    
    // Gallery events
    if (plugins.gallery) {
      plugins.gallery.addEventListener('show', () => {
        console.log('PSV: Gallery opened');
      });
      
      plugins.gallery.addEventListener('hide', () => {
        console.log('PSV: Gallery closed');
      });
    }
  }
  
  /**
   * Cleanup all PSV instances
   */
  static cleanup(): void {
    PSVMemoryManager.cleanupAll();
  }
}

// Export convenience functions
export const createSimpleViewer = PSVFactory.createSimple;
export const createVirtualTour = PSVFactory.createVirtualTour;
export const createViewer = PSVFactory.create;
