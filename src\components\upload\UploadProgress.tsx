
import { CheckCircle, Circle } from 'lucide-react'

interface UploadProgressProps {
  currentStep: number
  totalSteps?: number
}

const UploadProgress = ({ currentStep, totalSteps = 4 }: UploadProgressProps) => {
  const steps = [
    { number: 1, title: 'Basic Info', description: 'Tour details' },
    { number: 2, title: 'Category', description: 'Tour type & location' },
    { number: 3, title: 'Review', description: 'Confirm details' },
    { number: 4, title: 'Content', description: 'Upload or embed' }
  ]

  return (
    <div className="mb-8">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.number} className="flex items-center">
            <div className="flex flex-col items-center">
              <div className={`
                w-10 h-10 rounded-full flex items-center justify-center transition-colors
                ${currentStep > step.number 
                  ? 'bg-green-500 text-white' 
                  : currentStep === step.number 
                    ? 'bg-blue-500 text-white' 
                    : 'bg-gray-200 text-gray-400'
                }
              `}>
                {currentStep > step.number ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <span className="text-sm font-medium">{step.number}</span>
                )}
              </div>
              <div className="mt-2 text-center">
                <p className={`text-sm font-medium ${
                  currentStep >= step.number ? 'text-gray-900' : 'text-gray-400'
                }`}>
                  {step.title}
                </p>
                <p className="text-xs text-gray-500">{step.description}</p>
              </div>
            </div>
            {index < steps.length - 1 && (
              <div className={`flex-1 h-0.5 mx-4 ${
                currentStep > step.number ? 'bg-green-500' : 'bg-gray-200'
              }`} />
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

export default UploadProgress
