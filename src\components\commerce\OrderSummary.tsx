/**
 * OrderSummary Component
 * Comprehensive order details display with status tracking
 * Mobile-first responsive design with vendor breakdown
 */

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Package, 
  MapPin, 
  Phone, 
  Mail, 
  MessageCircle, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Truck, 
  CreditCard,
  User,
  Calendar,
  ExternalLink,
  Copy,
  Eye
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

export interface OrderItem {
  id: string;
  product: {
    id: string;
    title: string;
    price: number;
    images: string[];
  };
  vendor: {
    id: string;
    name: string;
    whatsapp_number?: string;
  };
  quantity: number;
  price: number;
}

export interface Order {
  id: string;
  order_number: string;
  customer_name: string;
  customer_phone: string;
  customer_email?: string;
  customer_address?: string;
  total_amount: number;
  status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  payment_method: string;
  payment_status: string;
  tour_context?: {
    tourId: string;
    tourTitle: string;
    sceneId: string;
  };
  created_at: string;
  updated_at: string;
  items: OrderItem[];
}

interface OrderSummaryProps {
  order: Order;
  variant?: 'full' | 'compact' | 'card';
  showCustomerInfo?: boolean;
  showVendorBreakdown?: boolean;
  showActions?: boolean;
  onContactVendor?: (vendorId: string, orderId: string) => void;
  onViewProduct?: (productId: string) => void;
  onTrackOrder?: (orderNumber: string) => void;
  className?: string;
}

const OrderSummary = ({
  order,
  variant = 'full',
  showCustomerInfo = true,
  showVendorBreakdown = true,
  showActions = true,
  onContactVendor,
  onViewProduct,
  onTrackOrder,
  className
}: OrderSummaryProps) => {
  const [expandedVendor, setExpandedVendor] = useState<string | null>(null);

  // Group items by vendor
  const vendorGroups = order.items.reduce((groups, item) => {
    const vendorId = item.vendor.id;
    if (!groups[vendorId]) {
      groups[vendorId] = {
        vendor: item.vendor,
        items: [],
        total: 0
      };
    }
    groups[vendorId].items.push(item);
    groups[vendorId].total += item.price * item.quantity;
    return groups;
  }, {} as Record<string, { vendor: OrderItem['vendor'], items: OrderItem[], total: number }>);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'confirmed': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'processing': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'shipped': return 'bg-indigo-100 text-indigo-800 border-indigo-200';
      case 'delivered': return 'bg-green-100 text-green-800 border-green-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      case 'refunded': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="w-4 h-4" />;
      case 'confirmed': return <CheckCircle className="w-4 h-4" />;
      case 'processing': return <Package className="w-4 h-4" />;
      case 'shipped': return <Truck className="w-4 h-4" />;
      case 'delivered': return <CheckCircle className="w-4 h-4" />;
      case 'cancelled': return <XCircle className="w-4 h-4" />;
      case 'refunded': return <CreditCard className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const copyOrderNumber = () => {
    navigator.clipboard.writeText(order.order_number);
    toast.success('Order number copied to clipboard');
  };

  const handleContactVendor = (vendorId: string) => {
    if (onContactVendor) {
      onContactVendor(vendorId, order.id);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (variant === 'card') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={cn("w-full max-w-sm", className)}
      >
        <Card className="h-full hover:shadow-lg transition-all duration-300">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">{order.order_number}</CardTitle>
              <Badge className={getStatusColor(order.status)} variant="outline">
                {getStatusIcon(order.status)}
                <span className="ml-1 capitalize">{order.status}</span>
              </Badge>
            </div>
            <CardDescription>
              {formatDate(order.created_at)}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Total Amount</span>
                <span className="font-semibold">₦{order.total_amount.toLocaleString()}</span>
              </div>
              
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Items</span>
                <span>{order.items.length} product{order.items.length !== 1 ? 's' : ''}</span>
              </div>

              {order.tour_context && (
                <div className="text-xs text-blue-600 bg-blue-50 p-2 rounded">
                  From: {order.tour_context.tourTitle}
                </div>
              )}

              {showActions && (
                <div className="flex gap-2 pt-2">
                  <Button variant="outline" size="sm" className="flex-1">
                    <Eye className="w-4 h-4 mr-1" />
                    View
                  </Button>
                  {onTrackOrder && (
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => onTrackOrder(order.order_number)}
                    >
                      <Truck className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn("w-full space-y-6", className)}
    >
      {/* Order Header */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <div className="flex items-center gap-3 mb-2">
                <CardTitle className="text-2xl">Order {order.order_number}</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={copyOrderNumber}
                  className="p-1 h-auto"
                >
                  <Copy className="w-4 h-4" />
                </Button>
              </div>
              <CardDescription className="flex items-center gap-4">
                <span className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  {formatDate(order.created_at)}
                </span>
                <span className="flex items-center gap-1">
                  <CreditCard className="w-4 h-4" />
                  {order.payment_method}
                </span>
              </CardDescription>
            </div>
            
            <div className="flex flex-col sm:items-end gap-2">
              <Badge className={getStatusColor(order.status)} variant="outline">
                {getStatusIcon(order.status)}
                <span className="ml-2 capitalize">{order.status}</span>
              </Badge>
              <div className="text-right">
                <div className="text-2xl font-bold">₦{order.total_amount.toLocaleString()}</div>
                <div className="text-sm text-muted-foreground">Total Amount</div>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Customer Information */}
      {showCustomerInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="w-5 h-5" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="font-medium">{order.customer_name}</div>
                <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                  <Phone className="w-4 h-4" />
                  {order.customer_phone}
                </div>
                {order.customer_email && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                    <Mail className="w-4 h-4" />
                    {order.customer_email}
                  </div>
                )}
              </div>
              {order.customer_address && (
                <div>
                  <div className="font-medium mb-1">Delivery Address</div>
                  <div className="flex items-start gap-2 text-sm text-muted-foreground">
                    <MapPin className="w-4 h-4 mt-0.5" />
                    {order.customer_address}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tour Context */}
      {order.tour_context && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <ExternalLink className="w-5 h-5 text-blue-600" />
              </div>
              <div>
                <div className="font-medium">Discovered in Virtual Tour</div>
                <div className="text-sm text-muted-foreground">{order.tour_context.tourTitle}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Vendor Breakdown */}
      {showVendorBreakdown && (
        <Card>
          <CardHeader>
            <CardTitle>Order Items by Vendor</CardTitle>
            <CardDescription>
              {Object.keys(vendorGroups).length} vendor{Object.keys(vendorGroups).length !== 1 ? 's' : ''}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {Object.entries(vendorGroups).map(([vendorId, group]) => (
              <div key={vendorId} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <Package className="w-4 h-4" />
                    </div>
                    <div>
                      <div className="font-medium">{group.vendor.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {group.items.length} item{group.items.length !== 1 ? 's' : ''}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">₦{group.total.toLocaleString()}</div>
                    {group.vendor.whatsapp_number && showActions && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleContactVendor(vendorId)}
                        className="mt-1"
                      >
                        <MessageCircle className="w-3 h-3 mr-1" />
                        Contact
                      </Button>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  {group.items.map((item) => (
                    <div key={item.id} className="flex items-center gap-3 p-2 bg-gray-50 rounded">
                      <div className="w-12 h-12 bg-gray-200 rounded overflow-hidden">
                        {item.product.images[0] ? (
                          <img
                            src={item.product.images[0]}
                            alt={item.product.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Package className="w-4 h-4 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm line-clamp-1">
                          {item.product.title}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {item.quantity}x ₦{item.product.price.toLocaleString()}
                        </div>
                      </div>
                      <div className="font-semibold text-sm">
                        ₦{(item.price * item.quantity).toLocaleString()}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Order Actions */}
      {showActions && (
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-wrap gap-3">
              {onTrackOrder && (
                <Button 
                  variant="outline"
                  onClick={() => onTrackOrder(order.order_number)}
                >
                  <Truck className="w-4 h-4 mr-2" />
                  Track Order
                </Button>
              )}
              <Button variant="outline">
                <MessageCircle className="w-4 h-4 mr-2" />
                Contact Support
              </Button>
              <Button variant="outline">
                <Copy className="w-4 h-4 mr-2" />
                Share Order
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </motion.div>
  );
};

export default OrderSummary;
