/**
 * Test Setup
 * Global test configuration and mocks
 */

import { expect, afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers);

// Cleanup after each test case
afterEach(() => {
  cleanup();
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
  root: null,
  rootMargin: '',
  thresholds: []
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}));

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  }))
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: vi.fn()
});

// Mock HTMLElement.scrollIntoView
Object.defineProperty(HTMLElement.prototype, 'scrollIntoView', {
  writable: true,
  value: vi.fn()
});

// Mock HTMLCanvasElement.getContext
Object.defineProperty(HTMLCanvasElement.prototype, 'getContext', {
  writable: true,
  value: vi.fn().mockImplementation((contextType) => {
    if (contextType === '2d') {
      return {
        fillRect: vi.fn(),
        clearRect: vi.fn(),
        getImageData: vi.fn(() => ({
          data: new Array(4)
        })),
        putImageData: vi.fn(),
        createImageData: vi.fn(() => []),
        setTransform: vi.fn(),
        drawImage: vi.fn(),
        save: vi.fn(),
        fillText: vi.fn(),
        restore: vi.fn(),
        beginPath: vi.fn(),
        moveTo: vi.fn(),
        lineTo: vi.fn(),
        closePath: vi.fn(),
        stroke: vi.fn(),
        translate: vi.fn(),
        scale: vi.fn(),
        rotate: vi.fn(),
        arc: vi.fn(),
        fill: vi.fn(),
        measureText: vi.fn(() => ({ width: 0 })),
        transform: vi.fn(),
        rect: vi.fn(),
        clip: vi.fn()
      };
    }
    if (contextType === 'webgl' || contextType === 'experimental-webgl') {
      return {
        getParameter: vi.fn((param) => {
          switch (param) {
            case 'VENDOR': return 'Test Vendor';
            case 'RENDERER': return 'Test Renderer';
            case 'VERSION': return 'WebGL 2.0';
            case 'MAX_TEXTURE_SIZE': return 4096;
            default: return null;
          }
        }),
        createShader: vi.fn(),
        shaderSource: vi.fn(),
        compileShader: vi.fn(),
        createProgram: vi.fn(),
        attachShader: vi.fn(),
        linkProgram: vi.fn(),
        useProgram: vi.fn(),
        createBuffer: vi.fn(),
        bindBuffer: vi.fn(),
        bufferData: vi.fn(),
        createTexture: vi.fn(),
        bindTexture: vi.fn(),
        texImage2D: vi.fn(),
        texParameteri: vi.fn(),
        drawArrays: vi.fn(),
        clear: vi.fn(),
        clearColor: vi.fn(),
        enable: vi.fn(),
        disable: vi.fn(),
        viewport: vi.fn()
      };
    }
    return null;
  })
});

// Mock URL.createObjectURL and revokeObjectURL
Object.defineProperty(URL, 'createObjectURL', {
  writable: true,
  value: vi.fn(() => 'blob:mock-url')
});

Object.defineProperty(URL, 'revokeObjectURL', {
  writable: true,
  value: vi.fn()
});

// Mock FileReader
global.FileReader = vi.fn().mockImplementation(() => ({
  readAsDataURL: vi.fn(function(this: any) {
    this.result = 'data:image/jpeg;base64,mock-base64-data';
    if (this.onload) {
      this.onload({ target: { result: this.result } });
    }
  }),
  readAsArrayBuffer: vi.fn(function(this: any) {
    this.result = new ArrayBuffer(8);
    if (this.onload) {
      this.onload({ target: { result: this.result } });
    }
  }),
  onload: null,
  onerror: null,
  result: null
})) as any;

// Mock Image constructor
global.Image = vi.fn().mockImplementation(() => ({
  onload: null,
  onerror: null,
  src: '',
  width: 1024,
  height: 512,
  crossOrigin: null,
  addEventListener: vi.fn(),
  removeEventListener: vi.fn()
})) as any;

// Mock performance.memory
Object.defineProperty(performance, 'memory', {
  writable: true,
  value: {
    usedJSHeapSize: 50 * 1024 * 1024, // 50MB
    totalJSHeapSize: 100 * 1024 * 1024, // 100MB
    jsHeapSizeLimit: 2 * 1024 * 1024 * 1024 // 2GB
  }
});

// Mock navigator.userAgent
Object.defineProperty(navigator, 'userAgent', {
  writable: true,
  value: 'Mozilla/5.0 (Test Environment) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
});

// Mock window.gc (garbage collection)
Object.defineProperty(window, 'gc', {
  writable: true,
  value: vi.fn()
});

// Mock crypto.randomUUID
Object.defineProperty(crypto, 'randomUUID', {
  writable: true,
  value: vi.fn(() => 'mock-uuid-' + Math.random().toString(36).substr(2, 9))
});

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn()
};

Object.defineProperty(window, 'localStorage', {
  writable: true,
  value: localStorageMock
});

// Mock sessionStorage
Object.defineProperty(window, 'sessionStorage', {
  writable: true,
  value: localStorageMock
});

// Mock fetch
global.fetch = vi.fn();

// Mock console methods to reduce noise in tests
const originalConsole = { ...console };
console.warn = vi.fn();
console.error = vi.fn();

// Restore console in specific tests if needed
export const restoreConsole = () => {
  console.warn = originalConsole.warn;
  console.error = originalConsole.error;
};

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn((cb) => {
  setTimeout(cb, 16);
  return 1;
});

global.cancelAnimationFrame = vi.fn();

// Mock getComputedStyle
Object.defineProperty(window, 'getComputedStyle', {
  writable: true,
  value: vi.fn().mockImplementation(() => ({
    getPropertyValue: vi.fn(() => ''),
    width: '1024px',
    height: '768px'
  }))
});

// Mock Element.getBoundingClientRect
Object.defineProperty(Element.prototype, 'getBoundingClientRect', {
  writable: true,
  value: vi.fn(() => ({
    width: 1024,
    height: 768,
    top: 0,
    left: 0,
    bottom: 768,
    right: 1024,
    x: 0,
    y: 0,
    toJSON: vi.fn()
  }))
});

// Mock DOMRect
global.DOMRect = vi.fn().mockImplementation((x = 0, y = 0, width = 0, height = 0) => ({
  x,
  y,
  width,
  height,
  top: y,
  left: x,
  bottom: y + height,
  right: x + width,
  toJSON: vi.fn()
}));

// Mock MutationObserver
global.MutationObserver = vi.fn().mockImplementation((callback) => ({
  observe: vi.fn(),
  disconnect: vi.fn(),
  takeRecords: vi.fn(() => [])
}));

// Mock PointerEvent
global.PointerEvent = vi.fn().mockImplementation((type, options = {}) => ({
  type,
  ...options,
  preventDefault: vi.fn(),
  stopPropagation: vi.fn()
})) as any;

// Mock Touch and TouchEvent for mobile testing
global.Touch = vi.fn().mockImplementation((options = {}) => ({
  identifier: 0,
  target: null,
  clientX: 0,
  clientY: 0,
  pageX: 0,
  pageY: 0,
  screenX: 0,
  screenY: 0,
  radiusX: 0,
  radiusY: 0,
  rotationAngle: 0,
  force: 1,
  ...options
})) as any;

global.TouchEvent = vi.fn().mockImplementation((type, options = {}) => ({
  type,
  touches: [],
  targetTouches: [],
  changedTouches: [],
  ...options,
  preventDefault: vi.fn(),
  stopPropagation: vi.fn()
})) as any;

// Setup test data attributes
document.body.setAttribute('data-testid', 'test-body');

// Global test utilities
export const createTestContainer = (id = 'test-container') => {
  const container = document.createElement('div');
  container.id = id;
  container.setAttribute('data-testid', id);
  document.body.appendChild(container);
  return container;
};

export const cleanupTestContainer = (container: HTMLElement) => {
  if (container.parentNode) {
    container.parentNode.removeChild(container);
  }
};

// Error boundary for tests
export const TestErrorBoundary = ({ children }: { children: React.ReactNode }) => {
  try {
    return <>{children}</>;
  } catch (error) {
    console.error('Test Error Boundary caught:', error);
    return <div data-testid="error-boundary">Test Error</div>;
  }
};
