
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import TourAssignmentItem from './TourAssignmentItem';

interface FeaturedTourAssignment {
  id: string;
  tour_id: string;
  section_type: string;
  display_order: number;
  is_active: boolean;
  tours: {
    title: string;
    thumbnail_url: string;
    category: string;
  };
}

interface FeaturedSectionCardProps {
  sectionType: string;
  assignments: FeaturedTourAssignment[];
}

const FeaturedSectionCard = ({ sectionType, assignments }: FeaturedSectionCardProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span className="capitalize">{sectionType} Section</span>
          <Badge variant="secondary">{assignments.length} tours</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {assignments.map((assignment) => (
            <TourAssignmentItem key={assignment.id} assignment={assignment} />
          ))}
          {assignments.length === 0 && (
            <p className="text-gray-500 text-center py-4">
              No tours assigned to this section
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default FeaturedSectionCard;
