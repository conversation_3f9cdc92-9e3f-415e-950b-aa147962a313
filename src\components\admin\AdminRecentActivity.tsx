
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Users } from 'lucide-react';
import { Tour, Profile } from '@/lib/supabase';

interface AdminRecentActivityProps {
  tours: Tour[];
  users: Profile[];
}

const AdminRecentActivity = ({ tours, users }: AdminRecentActivityProps) => {
  const recentTours = tours.slice(0, 5);
  const recentUsers = users.slice(0, 5);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Recent Tours</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentTours.map((tour) => (
              <div key={tour.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                    <MapPin className="w-5 h-5" />
                  </div>
                  <div>
                    <p className="font-medium">{tour.title}</p>
                    <p className="text-sm text-muted-foreground">{tour.category}</p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge variant={tour.status === 'published' ? 'default' : 'secondary'}>
                    {tour.status}
                  </Badge>
                  <p className="text-xs text-muted-foreground mt-1">{tour.views} views</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Users</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentUsers.map((user) => (
              <div key={user.id} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center">
                    <Users className="w-5 h-5" />
                  </div>
                  <div>
                    <p className="font-medium">{user.full_name || 'Unknown User'}</p>
                    <p className="text-sm text-muted-foreground">{user.email}</p>
                  </div>
                </div>
                <div className="text-right">
                  <Badge variant={user.role === 'admin' ? 'default' : 'secondary'}>
                    {user.role}
                  </Badge>
                  <p className="text-xs text-muted-foreground mt-1">
                    {new Date(user.created_at).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminRecentActivity;
