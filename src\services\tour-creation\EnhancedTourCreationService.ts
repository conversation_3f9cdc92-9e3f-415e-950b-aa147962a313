/**
 * Enhanced Tour Creation Service
 * Integrates CommonNinja widgets with our tour creation workflow
 */

import { CommonNinjaService } from './commonninja.service';
import { CustomTourService } from './custom.service';
import { CommonNinjaWidgetService, ImageHotspotConfig, CatalogConfig, BookingConfig } from '../widgets/CommonNinjaWidgetService';
import { supabase } from '@/lib/supabase';
import { generateSlug } from '@/lib/slugUtils';

export interface EnhancedTourConfig {
  // Basic Tour Info
  title: string;
  description: string;
  category: string;
  location: string;
  
  // Business Info
  businessName: string;
  businessType: string;
  contactPhone: string;
  contactEmail: string;
  website: string;
  businessHours: string;
  
  // Tour Content
  uploadMethod: 'images' | 'embed' | 'commonninja';
  embedUrl?: string;
  embedType?: 'iframe' | 'link' | 'custom';
  images?: File[];
  
  // Commerce Settings
  enableCommerce: boolean;
  vendorId?: string;
  whatsappNumber?: string;
  commissionRate?: number;
  
  // Widget Integration
  enableProductHotspots: boolean;
  enableCatalog: boolean;
  enableBooking: boolean;
  
  // Advanced Settings
  status: 'draft' | 'published';
  featured: boolean;
  allowReviews: boolean;
}

export interface TourCreationResult {
  tourId: string;
  embedUrl: string;
  editUrl?: string;
  widgets: {
    virtualTour?: string;
    productHotspots?: string;
    catalog?: string;
    booking?: string;
  };
  success: boolean;
  message: string;
}

export class EnhancedTourCreationService {
  private commonNinjaService: CommonNinjaService;
  private customTourService: CustomTourService;
  private widgetService: CommonNinjaWidgetService;

  constructor() {
    this.commonNinjaService = new CommonNinjaService();
    this.customTourService = new CustomTourService();
    this.widgetService = new CommonNinjaWidgetService();
  }

  /**
   * Create a complete tour with ecommerce integration
   */
  async createEnhancedTour(config: EnhancedTourConfig): Promise<TourCreationResult> {
    try {
      // Step 1: Create the main tour
      const tour = await this.createMainTour(config);
      
      // Step 2: Create associated widgets if enabled
      const widgets: TourCreationResult['widgets'] = {};
      
      if (config.enableCommerce) {
        // Create product catalog widget
        if (config.enableCatalog) {
          widgets.catalog = await this.createCatalogWidget(tour.id, config);
        }
        
        // Create product hotspot widget for the main tour image
        if (config.enableProductHotspots) {
          widgets.productHotspots = await this.createProductHotspotWidget(tour.id, config);
        }
      }
      
      // Create booking widget if enabled
      if (config.enableBooking) {
        widgets.booking = await this.createBookingWidget(tour.id, config);
      }
      
      // Step 3: Update tour with widget references
      await this.updateTourWithWidgets(tour.id, widgets);
      
      return {
        tourId: tour.id,
        embedUrl: tour.embedUrl,
        editUrl: tour.editUrl,
        widgets,
        success: true,
        message: 'Tour created successfully with all integrations!'
      };
      
    } catch (error) {
      console.error('Enhanced tour creation failed:', error);
      return {
        tourId: '',
        embedUrl: '',
        widgets: {},
        success: false,
        message: error instanceof Error ? error.message : 'Failed to create tour'
      };
    }
  }

  private async createMainTour(config: EnhancedTourConfig) {
    try {
      if (config.uploadMethod === 'commonninja' || config.uploadMethod === 'images') {
        // Use CommonNinja for virtual tour creation
        return await this.commonNinjaService.createTour({
          title: config.title,
          description: config.description,
          category: config.category,
          location: config.location,
          businessInfo: {
            name: config.businessName,
            type: config.businessType,
            phone: config.contactPhone,
            email: config.contactEmail,
            website: config.website,
            hours: config.businessHours,
          },
          settings: {
            autoRotate: true,
            showControls: true,
            enableVR: true,
            customBranding: true,
          },
        });
      } else {
        // Use custom service for embed tours
        return await this.customTourService.createTour({
          title: config.title,
          description: config.description,
          category: config.category,
          location: config.location,
          businessInfo: {
            name: config.businessName,
            type: config.businessType,
            phone: config.contactPhone,
            email: config.contactEmail,
            website: config.website,
            hours: config.businessHours,
          },
        });
      }
    } catch (error) {
      console.error('Main tour creation failed:', error);
      throw new Error(`Failed to create tour: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async createCatalogWidget(tourId: string, config: EnhancedTourConfig): Promise<string> {
    // Fetch products for this vendor/tour
    const products = await this.getVendorProducts(config.vendorId);
    
    const catalogConfig: CatalogConfig = {
      title: `${config.businessName} - Product Catalog`,
      description: `Browse products available in ${config.title}`,
      products: products.map(product => ({
        id: product.id,
        name: product.title,
        description: product.description,
        price: product.price,
        imageUrl: product.image_url,
        category: product.category,
        vendor: config.businessName,
      })),
      settings: {
        layout: 'grid',
        columns: 3,
        showPrices: true,
        enableFilters: true,
        enableSearch: true,
      },
    };

    const widget = await this.widgetService.createCatalog(catalogConfig);
    return widget.id;
  }

  private async createProductHotspotWidget(tourId: string, config: EnhancedTourConfig): Promise<string> {
    // Get the main tour image (first scene)
    const tourData = await this.commonNinjaService.getTour(tourId);
    const mainImageUrl = tourData.scenes[0]?.imageUrl || '';

    const hotspotConfig: ImageHotspotConfig = {
      title: `${config.title} - Product Hotspots`,
      description: 'Interactive product placement within the tour',
      imageUrl: mainImageUrl,
      hotspots: [
        // Default hotspots - will be customized by admin later
        {
          x: 30,
          y: 40,
          label: 'Featured Product',
          content: 'Click to view product details',
          type: 'product',
          action: {
            productId: 'placeholder',
          },
        },
      ],
      settings: {
        showLabels: true,
        hotspotStyle: 'modern',
        animation: 'pulse',
      },
    };

    const widget = await this.widgetService.createImageHotspot(hotspotConfig);
    return widget.id;
  }

  private async createBookingWidget(tourId: string, config: EnhancedTourConfig): Promise<string> {
    const bookingConfig: BookingConfig = {
      title: `Book Appointment - ${config.businessName}`,
      description: `Schedule a visit or consultation for ${config.title}`,
      businessInfo: {
        name: config.businessName,
        phone: config.contactPhone,
        email: config.contactEmail,
      },
      services: [
        {
          id: 'consultation',
          name: 'Property Consultation',
          description: 'Personal consultation and tour',
          duration: 60,
          price: 0,
        },
        {
          id: 'viewing',
          name: 'Property Viewing',
          description: 'Scheduled property viewing',
          duration: 30,
          price: 0,
        },
      ],
      settings: {
        timeSlots: ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00'],
        workingDays: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        advanceBookingDays: 30,
      },
    };

    const widget = await this.widgetService.createBooking(bookingConfig);
    return widget.id;
  }

  private async updateTourWithWidgets(tourId: string, widgets: TourCreationResult['widgets']) {
    // Update tour record in Supabase with widget references
    const { error } = await supabase
      .from('tours')
      .update({
        widget_config: {
          commonninja_widgets: widgets,
        },
        updated_at: new Date().toISOString(),
      })
      .eq('id', tourId);

    if (error) {
      console.error('Failed to update tour with widgets:', error);
    }
  }

  private async getVendorProducts(vendorId?: string) {
    if (!vendorId) return [];

    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .eq('vendor_id', vendorId)
      .eq('status', 'active')
      .limit(20);

    if (error) {
      console.error('Failed to fetch vendor products:', error);
      return [];
    }

    return products || [];
  }

  /**
   * Upload images for tour creation
   */
  async uploadTourImages(files: File[]): Promise<string[]> {
    const uploadPromises = files.map(file => this.commonNinjaService.uploadImage(file));
    return Promise.all(uploadPromises);
  }

  /**
   * Add scenes to existing tour
   */
  async addScenesToTour(tourId: string, imageUrls: string[]) {
    const scenePromises = imageUrls.map((imageUrl, index) => 
      this.commonNinjaService.addScene(tourId, {
        name: `Scene ${index + 1}`,
        description: `360° view ${index + 1}`,
        imageUrl,
        orderIndex: index,
      })
    );

    return Promise.all(scenePromises);
  }
}
