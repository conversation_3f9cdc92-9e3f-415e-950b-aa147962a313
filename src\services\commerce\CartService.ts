/**
 * Cart Service
 * Manages shopping cart functionality with local storage and database sync
 * Supports both authenticated and anonymous users
 */

import { supabase } from '@/lib/supabase';
import type { CartItem, CustomerInfo } from '@/components/commerce/ShoppingCart';

export interface CartState {
  items: CartItem[];
  sessionId: string;
  totalPrice: number;
  itemCount: number;
  vendorGroups: Map<string, CartItem[]>;
}

export class CartService {
  private sessionId: string;
  private storageKey = 'vrt-cart';

  constructor() {
    this.sessionId = this.getOrCreateSessionId();
  }

  /**
   * Get current cart state
   */
  getCartState(): CartState {
    const items = this.getCartItems();
    return this.calculateCartState(items);
  }

  /**
   * Add item to cart
   */
  async addToCart(
    productId: string, 
    quantity: number = 1, 
    tourContext?: {
      tourId: string;
      tourTitle: string;
      sceneId: string;
    }
  ): Promise<CartState> {
    try {
      // Get product details
      const { data: productData, error } = await supabase
        .from('products')
        .select(`
          *,
          vendor:vendors(id, name, whatsapp_number)
        `)
        .eq('id', productId)
        .single();

      if (error) throw error;

      const items = this.getCartItems();
      const existingItemIndex = items.findIndex(item => 
        item.product.id === productId && 
        JSON.stringify(item.tourContext) === JSON.stringify(tourContext)
      );

      if (existingItemIndex >= 0) {
        // Update existing item quantity
        items[existingItemIndex].quantity += quantity;
      } else {
        // Add new item
        const newItem: CartItem = {
          id: this.generateCartItemId(),
          product: {
            id: productData.id,
            title: productData.title,
            price: productData.price,
            images: productData.images || [],
            vendor: {
              id: productData.vendor.id,
              name: productData.vendor.name,
              whatsapp_number: productData.vendor.whatsapp_number
            }
          },
          quantity,
          tourContext
        };
        items.push(newItem);
      }

      this.saveCartItems(items);
      await this.syncToDatabase(items);

      return this.calculateCartState(items);
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw new Error('Failed to add item to cart');
    }
  }

  /**
   * Update item quantity
   */
  async updateQuantity(itemId: string, quantity: number): Promise<CartState> {
    try {
      const items = this.getCartItems();
      const itemIndex = items.findIndex(item => item.id === itemId);

      if (itemIndex >= 0) {
        if (quantity <= 0) {
          items.splice(itemIndex, 1);
        } else {
          items[itemIndex].quantity = quantity;
        }

        this.saveCartItems(items);
        await this.syncToDatabase(items);
      }

      return this.calculateCartState(items);
    } catch (error) {
      console.error('Error updating cart quantity:', error);
      throw new Error('Failed to update cart');
    }
  }

  /**
   * Remove item from cart
   */
  async removeItem(itemId: string): Promise<CartState> {
    try {
      const items = this.getCartItems();
      const filteredItems = items.filter(item => item.id !== itemId);

      this.saveCartItems(filteredItems);
      await this.syncToDatabase(filteredItems);

      return this.calculateCartState(filteredItems);
    } catch (error) {
      console.error('Error removing cart item:', error);
      throw new Error('Failed to remove item from cart');
    }
  }

  /**
   * Clear entire cart
   */
  async clearCart(): Promise<CartState> {
    try {
      this.saveCartItems([]);
      await this.syncToDatabase([]);
      return this.calculateCartState([]);
    } catch (error) {
      console.error('Error clearing cart:', error);
      throw new Error('Failed to clear cart');
    }
  }

  /**
   * Checkout cart and create order
   */
  async checkout(customerInfo: CustomerInfo): Promise<string> {
    try {
      const items = this.getCartItems();
      if (items.length === 0) {
        throw new Error('Cart is empty');
      }

      // Generate order number
      const { data: orderNumber } = await supabase.rpc('generate_order_number');
      
      const totalAmount = items.reduce(
        (sum, item) => sum + (item.product.price * item.quantity), 
        0
      );

      // Create order
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          order_number: orderNumber,
          customer_phone: customerInfo.phone,
          customer_email: customerInfo.email,
          customer_name: customerInfo.name,
          customer_address: customerInfo.address,
          total_amount: totalAmount,
          payment_method: 'whatsapp',
          tour_context: items[0]?.tourContext || null,
          vendor_orders: this.groupItemsByVendor(items)
        })
        .select()
        .single();

      if (orderError) throw orderError;

      // Create order items
      for (const item of items) {
        const { error: itemError } = await supabase
          .from('order_items')
          .insert({
            order_id: order.id,
            product_id: item.product.id,
            vendor_id: item.product.vendor.id,
            quantity: item.quantity,
            price: item.product.price,
            vendor_commission: item.product.price * item.quantity * 0.10 // 10% commission
          });

        if (itemError) throw itemError;
      }

      // Clear cart after successful order
      await this.clearCart();

      return order.order_number;
    } catch (error) {
      console.error('Error during checkout:', error);
      throw new Error('Failed to process checkout');
    }
  }

  /**
   * Sync cart to database for authenticated users
   */
  private async syncToDatabase(items: CartItem[]): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        // Clear existing cart items for this user/session
        await supabase
          .from('cart_items')
          .delete()
          .or(`session_id.eq.${this.sessionId},user_id.eq.${user.id}`);

        // Insert new cart items
        if (items.length > 0) {
          const cartItemsData = items.map(item => ({
            session_id: this.sessionId,
            user_id: user.id,
            product_id: item.product.id,
            quantity: item.quantity,
            tour_context: item.tourContext || null
          }));

          await supabase.from('cart_items').insert(cartItemsData);
        }
      }
    } catch (error) {
      // Sync failure shouldn't break cart functionality
      console.warn('Failed to sync cart to database:', error);
    }
  }

  /**
   * Load cart from database for authenticated users
   */
  async loadFromDatabase(): Promise<CartState> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      if (user) {
        const { data: cartItems, error } = await supabase
          .from('cart_items')
          .select(`
            *,
            product:products(
              *,
              vendor:vendors(id, name, whatsapp_number)
            )
          `)
          .eq('user_id', user.id);

        if (error) throw error;

        const items: CartItem[] = cartItems.map(item => ({
          id: item.id,
          product: {
            id: item.product.id,
            title: item.product.title,
            price: item.product.price,
            images: item.product.images || [],
            vendor: {
              id: item.product.vendor.id,
              name: item.product.vendor.name,
              whatsapp_number: item.product.vendor.whatsapp_number
            }
          },
          quantity: item.quantity,
          tourContext: item.tour_context
        }));

        this.saveCartItems(items);
        return this.calculateCartState(items);
      }

      return this.getCartState();
    } catch (error) {
      console.error('Error loading cart from database:', error);
      return this.getCartState();
    }
  }

  /**
   * Get cart items from local storage
   */
  private getCartItems(): CartItem[] {
    try {
      const stored = localStorage.getItem(this.storageKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Error reading cart from storage:', error);
      return [];
    }
  }

  /**
   * Save cart items to local storage
   */
  private saveCartItems(items: CartItem[]): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(items));
    } catch (error) {
      console.error('Error saving cart to storage:', error);
    }
  }

  /**
   * Calculate cart state from items
   */
  private calculateCartState(items: CartItem[]): CartState {
    const totalPrice = items.reduce(
      (sum, item) => sum + (item.product.price * item.quantity), 
      0
    );
    const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
    const vendorGroups = this.groupItemsByVendorMap(items);

    return {
      items,
      sessionId: this.sessionId,
      totalPrice,
      itemCount,
      vendorGroups
    };
  }

  /**
   * Group items by vendor (for order processing)
   */
  private groupItemsByVendor(items: CartItem[]): Record<string, any> {
    return items.reduce((groups, item) => {
      const vendorId = item.product.vendor.id;
      if (!groups[vendorId]) {
        groups[vendorId] = {
          vendor: item.product.vendor,
          items: [],
          total: 0
        };
      }
      groups[vendorId].items.push(item);
      groups[vendorId].total += item.product.price * item.quantity;
      return groups;
    }, {} as Record<string, any>);
  }

  /**
   * Group items by vendor (for UI display)
   */
  private groupItemsByVendorMap(items: CartItem[]): Map<string, CartItem[]> {
    return items.reduce((groups, item) => {
      const vendorId = item.product.vendor.id;
      if (!groups.has(vendorId)) {
        groups.set(vendorId, []);
      }
      groups.get(vendorId)!.push(item);
      return groups;
    }, new Map<string, CartItem[]>());
  }

  /**
   * Get or create session ID
   */
  private getOrCreateSessionId(): string {
    const sessionKey = 'vrt-session-id';
    let sessionId = localStorage.getItem(sessionKey);
    
    if (!sessionId) {
      sessionId = 'sess_' + Math.random().toString(36).substr(2, 16) + Date.now().toString(36);
      localStorage.setItem(sessionKey, sessionId);
    }
    
    return sessionId;
  }

  /**
   * Generate unique cart item ID
   */
  private generateCartItemId(): string {
    return 'cart_' + Math.random().toString(36).substr(2, 16) + Date.now().toString(36);
  }
}

export const cartService = new CartService();
