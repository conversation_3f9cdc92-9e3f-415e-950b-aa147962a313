/**
 * Virtual Tour Hook
 * React hook for managing virtual tour state and interactions
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { virtualTourService, type VirtualTourData, type VirtualTourNode } from '@/lib/photosphere/virtualTourService';
import type { PSVInstance } from '@/lib/photosphere/types';

export interface UseVirtualTourOptions {
  tourId: string;
  autoLoad?: boolean;
  enableRealTimeSync?: boolean;
  onNodeChange?: (nodeId: string, node: VirtualTourNode) => void;
  onTourLoad?: (tour: VirtualTourData) => void;
  onError?: (error: Error) => void;
}

export interface UseVirtualTourReturn {
  // Tour data
  tour: VirtualTourData | null;
  currentNode: VirtualTourNode | null;
  currentNodeId: string | null;
  
  // Loading states
  isLoading: boolean;
  isLoadingNode: boolean;
  error: string | null;
  
  // Actions
  loadTour: () => Promise<void>;
  navigateToNode: (nodeId: string) => Promise<void>;
  refreshTour: () => Promise<void>;
  
  // PSV integration
  attachToPSV: (psvInstance: PSVInstance) => void;
  detachFromPSV: () => void;
  
  // Real-time collaboration
  updateNode: (nodeId: string, updates: Partial<VirtualTourNode>) => void;
  
  // Utility
  getConnectedNodes: (nodeId?: string) => VirtualTourNode[];
  preloadConnectedNodes: (nodeId?: string) => Promise<void>;
}

export function useVirtualTour(options: UseVirtualTourOptions): UseVirtualTourReturn {
  const {
    tourId,
    autoLoad = true,
    enableRealTimeSync = false,
    onNodeChange,
    onTourLoad,
    onError
  } = options;

  // State
  const [tour, setTour] = useState<VirtualTourData | null>(null);
  const [currentNodeId, setCurrentNodeId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingNode, setIsLoadingNode] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Refs
  const psvInstanceRef = useRef<PSVInstance | null>(null);
  const realTimeSyncRef = useRef<any>(null);

  // Computed values
  const currentNode = tour?.nodes.find(node => node.id === currentNodeId) || null;

  /**
   * Load tour data from server
   */
  const loadTour = useCallback(async () => {
    if (!tourId) return;

    try {
      setIsLoading(true);
      setError(null);

      const tourData = await virtualTourService.loadTour(tourId);
      
      if (!tourData) {
        throw new Error('Tour not found or failed to load');
      }

      setTour(tourData);
      setCurrentNodeId(tourData.startNodeId);
      
      onTourLoad?.(tourData);
      toast.success('Virtual tour loaded successfully');
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load tour';
      setError(errorMessage);
      onError?.(err instanceof Error ? err : new Error(errorMessage));
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [tourId, onTourLoad, onError]);

  /**
   * Navigate to a specific node
   */
  const navigateToNode = useCallback(async (nodeId: string) => {
    if (!tour) return;

    try {
      setIsLoadingNode(true);
      
      // Check if node exists in current tour
      const targetNode = tour.nodes.find(node => node.id === nodeId);
      if (!targetNode) {
        // Try to load node from server (for dynamic loading)
        const serverNode = await virtualTourService.getNode(nodeId);
        if (!serverNode) {
          throw new Error('Node not found');
        }
        
        // Add node to tour if it's not already there
        setTour(prev => prev ? {
          ...prev,
          nodes: [...prev.nodes, serverNode]
        } : null);
      }

      setCurrentNodeId(nodeId);
      
      // Update PSV if attached
      if (psvInstanceRef.current?.plugins.virtualTour) {
        try {
          await psvInstanceRef.current.plugins.virtualTour.setCurrentNode(nodeId);
        } catch (psvError) {
          console.warn('Failed to update PSV node:', psvError);
        }
      }

      const node = targetNode || await virtualTourService.getNode(nodeId);
      if (node) {
        onNodeChange?.(nodeId, node);
      }
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to navigate to node';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoadingNode(false);
    }
  }, [tour, onNodeChange]);

  /**
   * Refresh tour data
   */
  const refreshTour = useCallback(async () => {
    virtualTourService.clearCache();
    await loadTour();
  }, [loadTour]);

  /**
   * Attach to PSV instance for integration
   */
  const attachToPSV = useCallback((psvInstance: PSVInstance) => {
    psvInstanceRef.current = psvInstance;
    
    // Listen for PSV virtual tour events
    if (psvInstance.plugins.virtualTour) {
      psvInstance.plugins.virtualTour.addEventListener('node-changed', (e: any) => {
        setCurrentNodeId(e.nodeId);
        const node = tour?.nodes.find(n => n.id === e.nodeId);
        if (node) {
          onNodeChange?.(e.nodeId, node);
        }
      });
    }
  }, [tour, onNodeChange]);

  /**
   * Detach from PSV instance
   */
  const detachFromPSV = useCallback(() => {
    if (psvInstanceRef.current?.plugins.virtualTour) {
      psvInstanceRef.current.plugins.virtualTour.removeAllListeners();
    }
    psvInstanceRef.current = null;
  }, []);

  /**
   * Update node data (for real-time collaboration)
   */
  const updateNode = useCallback((nodeId: string, updates: Partial<VirtualTourNode>) => {
    // Update local state
    setTour(prev => {
      if (!prev) return prev;
      
      return {
        ...prev,
        nodes: prev.nodes.map(node => 
          node.id === nodeId ? { ...node, ...updates } : node
        )
      };
    });

    // Update cache
    virtualTourService.updateNodeInCache(nodeId, updates);

    // Update PSV if attached and it's the current node
    if (nodeId === currentNodeId && psvInstanceRef.current?.plugins.virtualTour) {
      try {
        // Refresh the current node in PSV
        psvInstanceRef.current.plugins.virtualTour.refresh();
      } catch (error) {
        console.warn('Failed to refresh PSV after node update:', error);
      }
    }
  }, [currentNodeId]);

  /**
   * Get nodes connected to a specific node
   */
  const getConnectedNodes = useCallback((nodeId?: string): VirtualTourNode[] => {
    if (!tour) return [];
    
    const targetNodeId = nodeId || currentNodeId;
    if (!targetNodeId) return [];
    
    const targetNode = tour.nodes.find(node => node.id === targetNodeId);
    if (!targetNode) return [];
    
    const connectedNodeIds = targetNode.links.map(link => link.nodeId);
    return tour.nodes.filter(node => connectedNodeIds.includes(node.id));
  }, [tour, currentNodeId]);

  /**
   * Preload connected nodes for better performance
   */
  const preloadConnectedNodes = useCallback(async (nodeId?: string) => {
    const connectedNodes = getConnectedNodes(nodeId);
    
    // Preload panorama images
    const preloadPromises = connectedNodes.map(node => {
      return new Promise<void>((resolve) => {
        const img = new Image();
        img.onload = () => resolve();
        img.onerror = () => resolve(); // Don't fail on preload errors
        img.crossOrigin = 'anonymous';
        img.src = node.panorama;
      });
    });
    
    await Promise.allSettled(preloadPromises);
  }, [getConnectedNodes]);

  // Auto-load tour on mount
  useEffect(() => {
    if (autoLoad && tourId) {
      loadTour();
    }
  }, [autoLoad, tourId, loadTour]);

  // Setup real-time sync if enabled
  useEffect(() => {
    if (!enableRealTimeSync || !tourId) return;

    // TODO: Implement real-time sync with Supabase subscriptions
    // This would listen for changes to tour/scene data and update accordingly
    
    return () => {
      if (realTimeSyncRef.current) {
        realTimeSyncRef.current.unsubscribe();
      }
    };
  }, [enableRealTimeSync, tourId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      detachFromPSV();
    };
  }, [detachFromPSV]);

  return {
    // Tour data
    tour,
    currentNode,
    currentNodeId,
    
    // Loading states
    isLoading,
    isLoadingNode,
    error,
    
    // Actions
    loadTour,
    navigateToNode,
    refreshTour,
    
    // PSV integration
    attachToPSV,
    detachFromPSV,
    
    // Real-time collaboration
    updateNode,
    
    // Utility
    getConnectedNodes,
    preloadConnectedNodes
  };
}
