/**
 * Tour Data Synchronization Service
 * Ensures consistent and persistent data across all platforms:
 * - VirtualRealTour Database (Supabase)
 * - CommonNinja Platform (Underground)
 * - Custom Tour Editor
 * - Local Storage (Browser)
 */

import { supabase } from '@/lib/supabase';
import { virtualRealTourService } from '../tour-creation/virtualrealtour-service';

interface TourData {
  id: string;
  title: string;
  description: string;
  category: string;
  location: string;
  status: 'draft' | 'published' | 'archived';
  creation_method: 'images' | 'upload' | 'url';
  tour_platform: 'commonninja' | 'custom' | 'hybrid';
  
  // CommonNinja specific data
  commonninja_widget_id?: string;
  commonninja_embed_code?: string;
  commonninja_edit_url?: string;
  
  // Custom editor specific data
  custom_scenes?: TourScene[];
  custom_settings?: TourSettings;
  
  // Shared metadata
  business_info?: BusinessInfo;
  analytics_data?: AnalyticsData;
  
  // Sync metadata
  last_synced_at?: string;
  sync_status: 'synced' | 'pending' | 'error';
  sync_errors?: string[];
  
  created_at: string;
  updated_at: string;
}

interface TourScene {
  id: string;
  name: string;
  panorama: string;
  thumbnail?: string;
  initialView: { yaw: number; pitch: number; zoom: number };
  hotspots: TourHotspot[];
  audio?: string;
  description?: string;
}

interface TourHotspot {
  id: string;
  type: 'navigation' | 'product' | 'info' | 'link' | 'media';
  position: { yaw: number; pitch: number };
  title: string;
  content: string;
  targetSceneId?: string;
  productData?: any;
  linkUrl?: string;
  mediaUrl?: string;
  style?: any;
}

interface TourSettings {
  autoRotate: boolean;
  showControls: boolean;
  initialScene: string;
  theme: 'light' | 'dark';
  branding: boolean;
}

interface BusinessInfo {
  name: string;
  phone: string;
  email: string;
  whatsapp: string;
  address?: string;
  website?: string;
}

interface AnalyticsData {
  views: number;
  interactions: number;
  conversions: number;
  averageViewTime: number;
  lastViewed?: string;
}

class TourDataSyncService {
  private syncQueue: Map<string, TourData> = new Map();
  private syncInProgress = false;

  /**
   * Create a new tour with data sync across all platforms
   */
  async createTour(tourData: Partial<TourData>): Promise<TourData> {
    try {
      // Step 1: Create in our database first
      const newTour: TourData = {
        id: `tour_${Date.now()}`,
        title: tourData.title || 'Untitled Tour',
        description: tourData.description || '',
        category: tourData.category || 'general',
        location: tourData.location || '',
        status: 'draft',
        creation_method: tourData.creation_method || 'images',
        tour_platform: tourData.tour_platform || 'commonninja',
        sync_status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        ...tourData
      };

      // Step 2: Save to Supabase
      const { data, error } = await supabase
        .from('tours')
        .insert([newTour])
        .select()
        .single();

      if (error) throw error;

      // Step 3: Sync to other platforms
      await this.syncTourToAllPlatforms(data);

      return data;
    } catch (error) {
      console.error('Failed to create tour:', error);
      throw new Error('Failed to create tour with data sync');
    }
  }

  /**
   * Update tour data with sync across all platforms
   */
  async updateTour(tourId: string, updates: Partial<TourData>): Promise<TourData> {
    try {
      // Step 1: Update in our database
      const { data, error } = await supabase
        .from('tours')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
          sync_status: 'pending'
        })
        .eq('id', tourId)
        .select()
        .single();

      if (error) throw error;

      // Step 2: Add to sync queue
      this.addToSyncQueue(data);

      // Step 3: Process sync queue
      this.processSyncQueue();

      return data;
    } catch (error) {
      console.error('Failed to update tour:', error);
      throw new Error('Failed to update tour with data sync');
    }
  }

  /**
   * Sync tour data to all platforms
   */
  async syncTourToAllPlatforms(tourData: TourData): Promise<void> {
    const syncErrors: string[] = [];

    try {
      // Sync to CommonNinja (if using CommonNinja platform)
      if (tourData.tour_platform === 'commonninja' || tourData.tour_platform === 'hybrid') {
        await this.syncToCommonNinja(tourData);
      }

      // Sync to Custom Editor (if using custom platform)
      if (tourData.tour_platform === 'custom' || tourData.tour_platform === 'hybrid') {
        await this.syncToCustomEditor(tourData);
      }

      // Sync to Local Storage
      await this.syncToLocalStorage(tourData);

      // Update sync status
      await this.updateSyncStatus(tourData.id, 'synced');

    } catch (error) {
      console.error('Sync error:', error);
      syncErrors.push(error instanceof Error ? error.message : 'Unknown sync error');
      await this.updateSyncStatus(tourData.id, 'error', syncErrors);
    }
  }

  /**
   * Sync data to CommonNinja platform
   */
  private async syncToCommonNinja(tourData: TourData): Promise<void> {
    try {
      if (tourData.commonninja_widget_id) {
        // Update existing CommonNinja widget
        await virtualRealTourService.updateTour(tourData.commonninja_widget_id, {
          title: tourData.title,
          description: tourData.description,
          category: tourData.category,
          location: tourData.location
        });
      } else {
        // Create new CommonNinja widget
        const widget = await virtualRealTourService.createTour({
          title: tourData.title,
          description: tourData.description,
          category: tourData.category,
          location: tourData.location,
          businessInfo: tourData.business_info
        });

        // Update our database with CommonNinja widget info
        await supabase
          .from('tours')
          .update({
            commonninja_widget_id: widget.id,
            commonninja_embed_code: widget.embedCode,
            commonninja_edit_url: widget.editUrl
          })
          .eq('id', tourData.id);
      }
    } catch (error) {
      console.error('CommonNinja sync error:', error);
      throw new Error('Failed to sync to CommonNinja platform');
    }
  }

  /**
   * Sync data to Custom Editor
   */
  private async syncToCustomEditor(tourData: TourData): Promise<void> {
    try {
      // Save custom editor data to our database
      if (tourData.custom_scenes || tourData.custom_settings) {
        await supabase
          .from('tour_custom_data')
          .upsert({
            tour_id: tourData.id,
            scenes: tourData.custom_scenes || [],
            settings: tourData.custom_settings || {},
            updated_at: new Date().toISOString()
          });
      }
    } catch (error) {
      console.error('Custom editor sync error:', error);
      throw new Error('Failed to sync to custom editor');
    }
  }

  /**
   * Sync data to Local Storage for offline access
   */
  private async syncToLocalStorage(tourData: TourData): Promise<void> {
    try {
      const localData = {
        ...tourData,
        lastSyncedAt: new Date().toISOString()
      };

      localStorage.setItem(`tour_${tourData.id}`, JSON.stringify(localData));
      
      // Update local tour list
      const tourList = JSON.parse(localStorage.getItem('tour_list') || '[]');
      const existingIndex = tourList.findIndex((t: any) => t.id === tourData.id);
      
      if (existingIndex >= 0) {
        tourList[existingIndex] = { id: tourData.id, title: tourData.title, lastSynced: new Date().toISOString() };
      } else {
        tourList.push({ id: tourData.id, title: tourData.title, lastSynced: new Date().toISOString() });
      }
      
      localStorage.setItem('tour_list', JSON.stringify(tourList));
    } catch (error) {
      console.error('Local storage sync error:', error);
      throw new Error('Failed to sync to local storage');
    }
  }

  /**
   * Get tour data with sync status
   */
  async getTourWithSyncStatus(tourId: string): Promise<TourData | null> {
    try {
      const { data, error } = await supabase
        .from('tours')
        .select('*')
        .eq('id', tourId)
        .single();

      if (error) throw error;

      // Check if data needs syncing
      if (data.sync_status === 'pending') {
        this.addToSyncQueue(data);
        this.processSyncQueue();
      }

      return data;
    } catch (error) {
      console.error('Failed to get tour:', error);
      return null;
    }
  }

  /**
   * Sync data from CommonNinja back to our platform
   */
  async syncFromCommonNinja(tourId: string, embedCode: string): Promise<void> {
    try {
      // Process embed code and extract data
      const processedEmbedCode = this.processEmbedCode(embedCode);
      
      // Update our database
      await supabase
        .from('tours')
        .update({
          commonninja_embed_code: processedEmbedCode,
          updated_at: new Date().toISOString(),
          sync_status: 'synced',
          last_synced_at: new Date().toISOString()
        })
        .eq('id', tourId);

      // Sync to local storage
      const tourData = await this.getTourWithSyncStatus(tourId);
      if (tourData) {
        await this.syncToLocalStorage(tourData);
      }
    } catch (error) {
      console.error('Failed to sync from CommonNinja:', error);
      throw new Error('Failed to sync data from CommonNinja');
    }
  }

  /**
   * Process embed code to ensure our branding
   */
  private processEmbedCode(embedCode: string): string {
    return embedCode
      .replace(/commoninja\.com/g, 'virtualrealtour.ng')
      .replace(/Common Ninja/g, 'VirtualRealTour')
      .replace(/commoninja/g, 'virtualrealtour')
      .replace(/title="[^"]*"/g, 'title="VirtualRealTour - Professional 360° Virtual Tour"');
  }

  /**
   * Add tour to sync queue
   */
  private addToSyncQueue(tourData: TourData): void {
    this.syncQueue.set(tourData.id, tourData);
  }

  /**
   * Process sync queue
   */
  private async processSyncQueue(): Promise<void> {
    if (this.syncInProgress || this.syncQueue.size === 0) return;

    this.syncInProgress = true;

    try {
      for (const [tourId, tourData] of this.syncQueue) {
        await this.syncTourToAllPlatforms(tourData);
        this.syncQueue.delete(tourId);
      }
    } catch (error) {
      console.error('Sync queue processing error:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  /**
   * Update sync status in database
   */
  private async updateSyncStatus(
    tourId: string, 
    status: 'synced' | 'pending' | 'error', 
    errors?: string[]
  ): Promise<void> {
    await supabase
      .from('tours')
      .update({
        sync_status: status,
        sync_errors: errors || null,
        last_synced_at: new Date().toISOString()
      })
      .eq('id', tourId);
  }

  /**
   * Get sync statistics
   */
  async getSyncStatistics(): Promise<any> {
    const { data, error } = await supabase
      .from('tours')
      .select('sync_status')
      .not('sync_status', 'is', null);

    if (error) return null;

    const stats = data.reduce((acc, tour) => {
      acc[tour.sync_status] = (acc[tour.sync_status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: data.length,
      synced: stats.synced || 0,
      pending: stats.pending || 0,
      errors: stats.error || 0,
      queueSize: this.syncQueue.size
    };
  }

  /**
   * Force sync all pending tours
   */
  async forceSyncAll(): Promise<void> {
    const { data, error } = await supabase
      .from('tours')
      .select('*')
      .eq('sync_status', 'pending');

    if (error || !data) return;

    for (const tour of data) {
      this.addToSyncQueue(tour);
    }

    await this.processSyncQueue();
  }
}

// Export singleton instance
export const tourDataSyncService = new TourDataSyncService();
export default TourDataSyncService;
