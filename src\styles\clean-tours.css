/**
 * Clean Tour Embedding Styles
 * Hide busy controls and create minimal tour displays
 */

/* Clean iframe styling - Aggressive UI hiding and sound prevention */
.clean-tour-iframe {
  border: none !important;
  outline: none !important;
  background: transparent !important;
  overflow: hidden !important;
  /* Prevent autoplay and sound */
  autoplay: false !important;
  muted: true !important;
}

/* Hide common tour platform UI elements */
.clean-tour-iframe::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
  background: transparent;
}

/* Aggressive CSS to hide tour platform controls */
.clean-tour-iframe {
  /* Hide scrollbars */
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.clean-tour-iframe::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Hide common tour platform UI elements via CSS injection */
.clean-tour-iframe::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

/* Panoee specific hiding */
.clean-tour-iframe[src*="panoee.com"] {
  /* Additional styling for Panoee tours */
  filter: contrast(1.1) brightness(1.05);
}

/* TourMKR specific hiding */
.clean-tour-iframe[src*="tourmkr.com"] {
  /* Additional styling for TourMKR tours */
  filter: contrast(1.05) brightness(1.02);
}

/* MassInteract specific hiding */
.clean-tour-iframe[src*="massinteract.com"] {
  /* Additional styling for MassInteract tours */
  filter: contrast(1.1) brightness(1.03);
}

/* Generic tour platform hiding */
.clean-tour-iframe {
  /* Ensure smooth rendering */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Hide scrollbars in tour iframes */
.clean-tour-iframe::-webkit-scrollbar {
  display: none;
}

.clean-tour-iframe {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Responsive tour container */
.tour-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
  background: #f8fafc;
}

/* Loading state for tours */
.tour-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #64748b;
  font-size: 14px;
}

/* Error state for tours */
.tour-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #ef4444;
  font-size: 14px;
}

/* Glass morphism overlay styles */
.glass-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(0.5px);
  -webkit-backdrop-filter: blur(0.5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  z-index: 10;
}

.glass-overlay:hover {
  background: rgba(255, 255, 255, 0.02);
}

/* VRT branding overlay */
.vrt-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  color: #374151;
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 20;
}

/* Play button styling */
.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 64px;
  height: 64px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 20;
}

.play-button:hover {
  transform: translate(-50%, -50%) scale(1.1);
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.play-button svg {
  width: 24px;
  height: 24px;
  color: #374151;
  margin-left: 2px;
}

/* Tour info overlay */
.tour-info {
  position: absolute;
  bottom: 12px;
  left: 12px;
  right: 12px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 20;
}

.tour-category {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  color: #374151;
  font-size: 11px;
  font-weight: 500;
  padding: 3px 6px;
  border-radius: 3px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.tour-views {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  color: #374151;
  font-size: 11px;
  font-weight: 500;
  padding: 3px 6px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* Demo tour specific styles - Enhanced control hiding */
.demo-tour-iframe {
  border: none !important;
  outline: none !important;
  background: transparent !important;
  overflow: hidden !important;

  /* Initial blur effect for loading only */
  filter: blur(2px);
  transition: filter 0.3s ease-out;
  opacity: 0.8;

  /* Enhanced pointer events control */
  pointer-events: auto !important;
}

.demo-tour-iframe.loaded {
  filter: blur(0px);
  opacity: 1;
}

/* Hide scrollbars */
.demo-tour-iframe::-webkit-scrollbar {
  display: none !important;
  width: 0 !important;
  height: 0 !important;
}

.demo-tour-iframe {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Enhanced CSS overlay to disable source controls */
.demo-tour-iframe::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 999999;
  background: transparent;
}

/* Platform-specific aggressive control hiding */
.demo-tour-iframe[src*="panoee.com"] {
  /* Additional Panoee-specific hiding */
  filter: contrast(1.1) brightness(1.05) blur(0px);
}

.demo-tour-iframe[src*="tourmkr.com"] {
  /* Additional TourMKR-specific hiding */
  filter: contrast(1.05) brightness(1.02) blur(0px);
}

.demo-tour-iframe[src*="massinteract.com"] {
  /* Additional MassInteract-specific hiding */
  filter: contrast(1.1) brightness(1.03) blur(0px);
}

/* Demo tour sizing adjustments - keep centered */

.demo-tour-mobile-height {
  /* Desktop: uses aspect-video (16:9) - original good size */
}

@media (max-width: 768px) {
  .demo-tour-mobile-height {
    aspect-ratio: unset !important; /* Remove aspect-video on mobile */
    height: 435px; /* Mobile: 290px + 50% = 435px */
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .play-button {
    width: 56px;
    height: 56px;
  }

  .play-button svg {
    width: 20px;
    height: 20px;
  }

  .vrt-badge,
  .tour-category,
  .tour-views {
    font-size: 10px;
    padding: 2px 4px;
  }
}
