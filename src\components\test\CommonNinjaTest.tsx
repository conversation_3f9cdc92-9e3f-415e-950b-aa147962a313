/**
 * Test Component for CommonNinja Integration
 * This component tests the CommonNinja API and widget functionality
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  XCircle, 
  Loader2, 
  ExternalLink,
  Settings,
  Sparkles
} from 'lucide-react';
import { commonNinjaService } from '@/services/commonNinjaService';

const CommonNinjaTest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<{
    apiConnection: 'success' | 'error' | 'pending';
    widgetCreation: 'success' | 'error' | 'pending';
    widgetList: any[];
    error?: string;
  }>({
    apiConnection: 'pending',
    widgetCreation: 'pending',
    widgetList: []
  });

  const runTests = async () => {
    setIsLoading(true);
    setTestResults({
      apiConnection: 'pending',
      widgetCreation: 'pending',
      widgetList: []
    });

    try {
      // Test 1: API Connection
      console.log('Testing CommonNinja API connection...');
      const widgets = await commonNinjaService.getWidgets();
      
      setTestResults(prev => ({
        ...prev,
        apiConnection: 'success',
        widgetList: widgets
      }));

      // Test 2: Widget Creation
      console.log('Testing widget creation...');
      const testWidget = await commonNinjaService.createWidget({
        name: `Test Tour ${Date.now()}`,
        type: 'virtual_tour',
        config: {
          title: 'Test Virtual Tour',
          description: 'This is a test tour created by the integration',
          enableHotspots: true,
          enableEcommerce: true,
          enableVR: true
        }
      });

      setTestResults(prev => ({
        ...prev,
        widgetCreation: 'success',
        widgetList: [testWidget, ...prev.widgetList]
      }));

    } catch (error: any) {
      console.error('CommonNinja test failed:', error);
      setTestResults(prev => ({
        ...prev,
        apiConnection: 'error',
        widgetCreation: 'error',
        error: error.message || 'Unknown error occurred'
      }));
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: 'success' | 'error' | 'pending') => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'pending':
        return <div className="h-5 w-5 rounded-full bg-gray-300" />;
    }
  };

  const getStatusBadge = (status: 'success' | 'error' | 'pending') => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">Success</Badge>;
      case 'error':
        return <Badge variant="destructive">Failed</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            CommonNinja Integration Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-muted-foreground">
            Test the CommonNinja API integration and widget functionality.
          </p>
          
          <Button 
            onClick={runTests} 
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Running Tests...
              </>
            ) : (
              <>
                <Settings className="h-4 w-4 mr-2" />
                Run Integration Tests
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* API Connection Test */}
          <div className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center gap-3">
              {getStatusIcon(testResults.apiConnection)}
              <div>
                <h4 className="font-medium">API Connection</h4>
                <p className="text-sm text-muted-foreground">
                  Test connection to CommonNinja API
                </p>
              </div>
            </div>
            {getStatusBadge(testResults.apiConnection)}
          </div>

          {/* Widget Creation Test */}
          <div className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center gap-3">
              {getStatusIcon(testResults.widgetCreation)}
              <div>
                <h4 className="font-medium">Widget Creation</h4>
                <p className="text-sm text-muted-foreground">
                  Test creating a virtual tour widget
                </p>
              </div>
            </div>
            {getStatusBadge(testResults.widgetCreation)}
          </div>

          {/* Error Display */}
          {testResults.error && (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>
                <strong>Error:</strong> {testResults.error}
              </AlertDescription>
            </Alert>
          )}

          {/* Widget List */}
          {testResults.widgetList.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">Available Widgets ({testResults.widgetList.length})</h4>
              <div className="space-y-2 max-h-60 overflow-y-auto">
                {testResults.widgetList.map((widget, index) => (
                  <div key={widget.id || index} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="font-medium">{widget.title || widget.name}</h5>
                        <p className="text-sm text-muted-foreground">
                          ID: {widget.id} | Type: {widget.type}
                        </p>
                      </div>
                      {widget.url && (
                        <Button variant="outline" size="sm" asChild>
                          <a href={widget.url} target="_blank" rel="noopener noreferrer">
                            <ExternalLink className="h-4 w-4" />
                          </a>
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Environment Info */}
      <Card>
        <CardHeader>
          <CardTitle>Environment Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>API Key:</strong> {import.meta.env.VITE_COMMONNINJA_API_KEY ? 'Configured' : 'Missing'}
            </div>
            <div>
              <strong>Environment:</strong> {import.meta.env.MODE}
            </div>
            <div>
              <strong>Base URL:</strong> https://api.commoninja.com
            </div>
            <div>
              <strong>Widget Type:</strong> virtual_tour
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CommonNinjaTest;
