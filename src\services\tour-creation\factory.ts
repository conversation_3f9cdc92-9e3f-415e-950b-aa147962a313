/**
 * Tour Creation Service Factory
 * Provides abstraction layer for multiple tour creation platforms
 */

import { TourCreationService, TourCreationError } from './types';
import { CommonNinjaService } from './commonninja.service';
import { CloudPanoService } from './cloudpano.service';
import { CustomTourService } from './custom.service';
import { getDefaultPlatform, getRecommendedPlatform } from '@/config/tourPlatformConfig';

export type TourPlatform = 'commonninja' | 'cloudpano' | 'custom' | 'auto';

export class TourCreationServiceFactory {
  private static commonNinjaService: CommonNinjaService | null = null;
  private static cloudPanoService: CloudPanoService | null = null;
  private static customService: CustomTourService | null = null;

  /**
   * Get tour creation service for specified platform
   */
  static getService(platform: TourPlatform = 'auto'): TourCreationService {
    switch (platform) {
      case 'commonninja':
        return this.getCommonNinjaService();

      case 'cloudpano':
        return this.getCloudPanoService();

      case 'custom':
        return this.getCustomService();

      case 'auto':
        // Auto mode now defaults to CommonNinja
        return this.getCommonNinjaService();

      default:
        throw new TourCreationError(
          'INVALID_PLATFORM',
          `Unsupported platform: ${platform}`,
          null,
          platform
        );
    }
  }



  /**
   * Get CommonNinja service instance
   */
  private static getCommonNinjaService(): CommonNinjaService {
    if (!this.commonNinjaService) {
      this.commonNinjaService = new CommonNinjaService();
    }
    return this.commonNinjaService;
  }

  /**
   * Get CloudPano service instance
   */
  private static getCloudPanoService(): CloudPanoService {
    if (!this.cloudPanoService) {
      this.cloudPanoService = new CloudPanoService();
    }
    return this.cloudPanoService;
  }

  /**
   * Auto-select best available service
   */
  private static getAutoService(): TourCreationService {
    const commonNinja = this.getCommonNinjaService();
    const custom = this.getCustomService();

    // Prefer CommonNinja if configured (reliable and accessible)
    if (commonNinja.isConfigured) {
      return commonNinja;
    }

    // Fallback to custom implementation (always available)
    return custom;
  }

  /**
   * Get custom tour creation service (always available)
   */
  private static getCustomService(): CustomTourService {
    if (!this.customService) {
      this.customService = new CustomTourService();
    }
    return this.customService;
  }

  /**
   * Check which platforms are configured
   */
  static getAvailablePlatforms(): Array<{ platform: TourPlatform; configured: boolean; name: string }> {
    return [
      {
        platform: 'commonninja',
        configured: this.getCommonNinjaService().isConfigured,
        name: 'CommonNinja'
      },
      {
        platform: 'custom',
        configured: this.getCustomService().isConfigured,
        name: 'Custom (Photo Sphere Viewer)'
      }
    ];
  }

  /**
   * Get recommended platform based on configuration and features
   */
  static getRecommendedPlatform(isAdmin: boolean = false): TourPlatform {
    // Use centralized configuration for platform recommendation
    return getRecommendedPlatform(isAdmin);
  }

  /**
   * Validate platform configuration
   */
  static async validatePlatform(platform: TourPlatform): Promise<{
    valid: boolean;
    platform: TourPlatform;
    error?: string;
  }> {
    try {
      const service = this.getService(platform);
      
      if (!service.isConfigured) {
        return {
          valid: false,
          platform,
          error: `${service.platform} is not configured. Please check your API keys.`
        };
      }

      // Try to make a simple API call to validate
      try {
        await service.listTours();
        return {
          valid: true,
          platform: service.platform as TourPlatform
        };
      } catch (error) {
        return {
          valid: false,
          platform,
          error: `${service.platform} API validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        };
      }
    } catch (error) {
      return {
        valid: false,
        platform,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Get platform capabilities
   */
  static getPlatformCapabilities(platform: TourPlatform) {
    const capabilities = {
      commonninja: {
        nativeEcommerce: false,
        advancedHotspots: true,
        analytics: true,
        customBranding: true,
        apiAccess: true,
        whatsappIntegration: false,
        vrSupport: true,
        autoRotate: true,
        sceneTransitions: true,
        hotspotTypes: ['navigation', 'info', 'link', 'custom'],
        maxScenes: 50,
        maxHotspotsPerScene: 20,
        imageFormats: ['jpg', 'jpeg', 'png'],
        maxImageSize: 20 * 1024 * 1024, // 20MB
      },
      custom: {
        nativeEcommerce: true,
        advancedHotspots: true,
        analytics: true,
        customBranding: true,
        apiAccess: true,
        whatsappIntegration: true,
        vrSupport: true,
        autoRotate: true,
        sceneTransitions: true,
        hotspotTypes: ['navigation', 'info', 'product', 'whatsapp', 'link', 'audio', 'video', 'custom'],
        maxScenes: 1000,
        maxHotspotsPerScene: 100,
        imageFormats: ['jpg', 'jpeg', 'png', 'webp', 'avif'],
        maxImageSize: 100 * 1024 * 1024, // 100MB
      }
    };

    if (platform === 'auto') {
      const recommended = this.getRecommendedPlatform();
      return capabilities[recommended === 'auto' ? 'custom' : recommended];
    }

    return capabilities[platform] || capabilities.custom;
  }

  /**
   * Reset service instances (useful for testing)
   */
  static reset(): void {
    this.commonNinjaService = null;
    this.customService = null;
  }
}

/**
 * Convenience function to get tour creation service
 */
export function getTourCreationService(platform: TourPlatform = 'auto'): TourCreationService {
  return TourCreationServiceFactory.getService(platform);
}

/**
 * Hook for React components to get tour creation service
 */
export function useTourCreationService(platform: TourPlatform = 'auto') {
  const service = getTourCreationService(platform);
  const capabilities = TourCreationServiceFactory.getPlatformCapabilities(platform);
  const availablePlatforms = TourCreationServiceFactory.getAvailablePlatforms();

  return {
    service,
    capabilities,
    availablePlatforms,
    platform: service.platform,
    isConfigured: service.isConfigured,
    validatePlatform: (p: TourPlatform) => TourCreationServiceFactory.validatePlatform(p),
    getRecommendedPlatform: () => TourCreationServiceFactory.getRecommendedPlatform(),
  };
}

/**
 * Environment configuration helper
 */
export function getEnvironmentConfig() {
  return {
    commonninja: {
      apiKey: process.env.NEXT_PUBLIC_COMMONNINJA_API_KEY,
      configured: !!process.env.NEXT_PUBLIC_COMMONNINJA_API_KEY,
    },
    custom: {
      enabled: process.env.NEXT_PUBLIC_ENABLE_CUSTOM_TOUR_CREATION !== 'false',
      configured: true, // Always available
    },
  };
}

/**
 * Development helper to check configuration
 */
export function checkConfiguration() {
  const config = getEnvironmentConfig();
  const platforms = TourCreationServiceFactory.getAvailablePlatforms();
  
  console.log('🔧 Tour Creation Service Configuration:');
  console.log('Environment Variables:', config);
  console.log('Available Platforms:', platforms);
  console.log('Recommended Platform:', TourCreationServiceFactory.getRecommendedPlatform());
  
  return {
    config,
    platforms,
    recommended: TourCreationServiceFactory.getRecommendedPlatform(),
  };
}
