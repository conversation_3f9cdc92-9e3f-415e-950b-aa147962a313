/**
 * VirtualRealTour Theme Configuration
 * 
 * This file contains all theme-related utilities and configurations.
 * Change theme colors here to update the entire application.
 */

export type ThemeColor = 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'emerald';

export interface ThemeConfig {
  name: string;
  primary: string;
  primaryHover: string;
  primaryLight: string;
  primaryBorder: string;
  primaryForeground: string;
}

/**
 * Available theme configurations
 * Add new themes here to extend color options
 */
export const themeConfigs: Record<ThemeColor, ThemeConfig> = {
  blue: {
    name: 'Blue',
    primary: '210 100% 50%',        // Blue-600
    primaryHover: '210 100% 45%',   // Blue-700
    primaryLight: '210 100% 97%',   // Blue-50
    primaryBorder: '210 100% 85%',  // Blue-200
    primaryForeground: '0 0% 100%', // White
  },
  green: {
    name: 'Green',
    primary: '142 76% 36%',         // Green-600
    primaryHover: '142 76% 31%',    // Green-700
    primaryLight: '142 76% 97%',    // Green-50
    primaryBorder: '142 76% 85%',   // Green-200
    primaryForeground: '0 0% 100%', // White
  },
  purple: {
    name: '<PERSON>',
    primary: '262 83% 58%',         // Purple-600
    primaryHover: '262 83% 53%',    // Purple-700
    primaryLight: '262 83% 97%',    // Purple-50
    primaryBorder: '262 83% 85%',   // Purple-200
    primaryForeground: '0 0% 100%', // White
  },
  orange: {
    name: 'Orange',
    primary: '25 95% 53%',          // Orange-600
    primaryHover: '25 95% 48%',     // Orange-700
    primaryLight: '25 95% 97%',     // Orange-50
    primaryBorder: '25 95% 85%',    // Orange-200
    primaryForeground: '0 0% 100%', // White
  },
  red: {
    name: 'Red',
    primary: '0 84% 60%',           // Red-600
    primaryHover: '0 84% 55%',      // Red-700
    primaryLight: '0 84% 97%',      // Red-50
    primaryBorder: '0 84% 85%',     // Red-200
    primaryForeground: '0 0% 100%', // White
  },
  emerald: {
    name: 'Emerald',
    primary: '160 84% 39%',         // Emerald-600
    primaryHover: '160 84% 34%',    // Emerald-700
    primaryLight: '160 84% 97%',    // Emerald-50
    primaryBorder: '160 84% 85%',   // Emerald-200
    primaryForeground: '0 0% 100%', // White
  },
};

/**
 * Current active theme - Change this to switch the entire app theme
 */
export const CURRENT_THEME: ThemeColor = 'blue';

/**
 * Apply theme to the document root
 */
export function applyTheme(theme: ThemeColor = CURRENT_THEME) {
  const config = themeConfigs[theme];
  const root = document.documentElement;

  // Update CSS custom properties
  root.style.setProperty('--theme-primary', config.primary);
  root.style.setProperty('--theme-primary-hover', config.primaryHover);
  root.style.setProperty('--theme-primary-light', config.primaryLight);
  root.style.setProperty('--theme-primary-border', config.primaryBorder);
  root.style.setProperty('--theme-primary-foreground', config.primaryForeground);

  // Also update the main primary color for shadcn components
  root.style.setProperty('--primary', config.primary);
  root.style.setProperty('--primary-foreground', config.primaryForeground);
}

/**
 * Theme utility classes for consistent styling
 */
export const themeClasses = {
  // Primary colors
  primary: 'bg-theme-primary text-theme-primary-foreground',
  primaryHover: 'hover:bg-theme-primary-hover',
  primaryLight: 'bg-theme-primary-light',
  primaryBorder: 'border-theme-primary-border',
  primaryText: 'text-theme-primary',

  // Button variants
  button: {
    primary: 'bg-theme-primary text-theme-primary-foreground hover:bg-theme-primary-hover',
    outline: 'border border-theme-primary-border text-theme-primary hover:bg-theme-primary-light',
    ghost: 'text-theme-primary hover:bg-theme-primary-light',
  },

  // Card variants
  card: {
    primary: 'bg-theme-primary-light border-theme-primary-border',
    highlight: 'border-theme-primary border-2',
  },

  // Icon colors
  icon: {
    primary: 'text-theme-primary',
    success: 'text-theme-success',
    warning: 'text-theme-warning',
    info: 'text-theme-info',
  },

  // Badge variants
  badge: {
    primary: 'bg-theme-primary text-theme-primary-foreground',
    success: 'bg-theme-success text-white',
    warning: 'bg-theme-warning text-white',
    info: 'bg-theme-info text-white',
  },
};

/**
 * Initialize theme on app load
 */
export function initializeTheme() {
  applyTheme(CURRENT_THEME);
}
