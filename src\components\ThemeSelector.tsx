/**
 * Theme Selector Component
 * 
 * This component allows switching between different theme colors.
 * Can be used in admin panels or settings pages for theme customization.
 */

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useTheme } from '@/components/ThemeProvider';
import { themeConfigs, type ThemeColor, CURRENT_THEME } from '@/lib/theme';

export function ThemeSelector() {
  const { setThemeColor } = useTheme();
  const [selectedTheme, setSelectedTheme] = useState<ThemeColor>(CURRENT_THEME);

  const handleThemeChange = (theme: ThemeColor) => {
    setSelectedTheme(theme);
    setThemeColor(theme);
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🎨 Theme Color Selector
          <Badge variant="outline" className="text-xs">
            Current: {themeConfigs[selectedTheme].name}
          </Badge>
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Choose a color scheme for the entire application
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {Object.entries(themeConfigs).map(([key, config]) => {
            const themeKey = key as ThemeColor;
            const isSelected = selectedTheme === themeKey;
            
            return (
              <Button
                key={key}
                variant={isSelected ? "default" : "outline"}
                className={`h-auto p-4 flex flex-col items-center gap-2 ${
                  isSelected ? 'ring-2 ring-offset-2 ring-primary' : ''
                }`}
                onClick={() => handleThemeChange(themeKey)}
              >
                <div 
                  className="w-8 h-8 rounded-full border-2 border-white shadow-sm"
                  style={{ 
                    backgroundColor: `hsl(${config.primary})` 
                  }}
                />
                <span className="text-sm font-medium">{config.name}</span>
              </Button>
            );
          })}
        </div>
        
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">Preview</h4>
          <div className="flex gap-2 flex-wrap">
            <div className="bg-theme-primary text-theme-primary-foreground px-3 py-1 rounded text-sm">
              Primary Button
            </div>
            <div className="bg-theme-primary-light border border-theme-primary-border px-3 py-1 rounded text-sm">
              Light Background
            </div>
            <div className="text-theme-primary px-3 py-1 rounded text-sm font-medium">
              Primary Text
            </div>
          </div>
        </div>

        <div className="mt-4 text-xs text-muted-foreground">
          <p>💡 <strong>Tip:</strong> Changes apply instantly across the entire application.</p>
          <p>🔧 <strong>Developer:</strong> Update CURRENT_THEME in src/lib/theme.ts to set the default theme.</p>
        </div>
      </CardContent>
    </Card>
  );
}

export default ThemeSelector;
