import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  RefreshCw, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Loader2, 
  Download,
  ExternalLink,
  Clock,
  Globe,
  TrendingUp,
  AlertCircle
} from 'lucide-react';
import { brokenLinkChecker, type TourLinkReport, type LinkCheckResult } from '@/utils/brokenLinkChecker';
import { toast } from 'sonner';

const BrokenLinkChecker: React.FC = () => {
  const [reports, setReports] = useState<TourLinkReport[]>([]);
  const [isChecking, setIsChecking] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentCheck, setCurrentCheck] = useState('');
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  useEffect(() => {
    // Load any cached results on component mount
    const summary = brokenLinkChecker.getSummary();
    if (summary.total > 0) {
      setLastChecked(new Date());
    }
  }, []);

  const handleCheckAllTours = async () => {
    setIsChecking(true);
    setProgress(0);
    setCurrentCheck('Initializing link check...');

    try {
      const tourReports = await brokenLinkChecker.checkAllTours();
      setReports(tourReports);
      setLastChecked(new Date());
      
      const summary = getSummaryStats(tourReports);
      toast.success(`Link check complete: Checked ${summary.total} tours. Found ${summary.broken} broken links and ${summary.warnings} warnings.`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Link check failed');
    } finally {
      setIsChecking(false);
      setProgress(0);
      setCurrentCheck('');
    }
  };

  const getSummaryStats = (tourReports: TourLinkReport[]) => {
    const stats = {
      total: tourReports.length,
      healthy: tourReports.filter(r => r.overallStatus === 'healthy').length,
      warnings: tourReports.filter(r => r.overallStatus === 'issues').length,
      broken: tourReports.filter(r => r.overallStatus === 'broken').length
    };
    return stats;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'issues':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'broken':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Loader2 className="h-4 w-4 animate-spin" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'issues':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'broken':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const exportResults = () => {
    const exportData = brokenLinkChecker.exportResults();
    const blob = new Blob([exportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `link-check-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const summary = getSummaryStats(reports);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Link Health Monitor</h2>
          <p className="text-muted-foreground">
            Monitor and validate all tour links and hotspot URLs
          </p>
        </div>
        <div className="flex gap-2">
          {reports.length > 0 && (
            <Button variant="outline" onClick={exportResults}>
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          )}
          <Button onClick={handleCheckAllTours} disabled={isChecking}>
            {isChecking ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            {isChecking ? 'Checking...' : 'Check All Links'}
          </Button>
        </div>
      </div>

      {/* Progress */}
      {isChecking && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="font-medium">Checking tour links...</span>
              </div>
              {progress > 0 && (
                <Progress value={progress} className="w-full" />
              )}
              {currentCheck && (
                <p className="text-sm text-muted-foreground">{currentCheck}</p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Summary Stats */}
      {reports.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4 text-blue-500" />
                <div>
                  <p className="text-sm font-medium">Total Tours</p>
                  <p className="text-2xl font-bold">{summary.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <div>
                  <p className="text-sm font-medium">Healthy</p>
                  <p className="text-2xl font-bold text-green-600">{summary.healthy}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-yellow-500" />
                <div>
                  <p className="text-sm font-medium">Warnings</p>
                  <p className="text-2xl font-bold text-yellow-600">{summary.warnings}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-500" />
                <div>
                  <p className="text-sm font-medium">Broken</p>
                  <p className="text-2xl font-bold text-red-600">{summary.broken}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Last Checked Info */}
      {lastChecked && (
        <Alert>
          <Clock className="h-4 w-4" />
          <AlertDescription>
            Last checked: {lastChecked.toLocaleString()}
          </AlertDescription>
        </Alert>
      )}

      {/* Tour Reports */}
      {reports.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Tour Link Reports
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-96">
              <div className="space-y-4">
                {reports.map((report) => (
                  <Card key={report.tourId} className="p-4">
                    <div className="space-y-3">
                      {/* Tour Header */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(report.overallStatus)}
                          <h4 className="font-medium">{report.tourTitle}</h4>
                          <Badge className={getStatusColor(report.overallStatus)}>
                            {report.overallStatus}
                          </Badge>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {report.lastChecked.toLocaleString()}
                        </div>
                      </div>

                      {/* Embed URL Status */}
                      {report.embedUrl && (
                        <div className="space-y-2">
                          <div className="flex items-center gap-2">
                            <Globe className="h-3 w-3" />
                            <span className="text-sm font-medium">Embed URL</span>
                            {getStatusIcon(report.embedUrl.status)}
                          </div>
                          
                          {report.embedUrl.error && (
                            <Alert variant="destructive">
                              <AlertCircle className="h-4 w-4" />
                              <AlertDescription className="text-xs">
                                {report.embedUrl.error}
                              </AlertDescription>
                            </Alert>
                          )}
                          
                          {report.embedUrl.warnings.length > 0 && (
                            <div className="space-y-1">
                              {report.embedUrl.warnings.map((warning, idx) => (
                                <div key={idx} className="text-xs text-yellow-600 flex items-center gap-1">
                                  <AlertTriangle className="h-3 w-3" />
                                  {warning}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}

                      {/* Hotspot Links */}
                      {report.hotspotLinks.length > 0 && (
                        <div className="space-y-2">
                          <Separator />
                          <div className="flex items-center gap-2">
                            <ExternalLink className="h-3 w-3" />
                            <span className="text-sm font-medium">
                              Hotspot Links ({report.hotspotLinks.length})
                            </span>
                          </div>
                          
                          <div className="space-y-1">
                            {report.hotspotLinks.map((link, idx) => (
                              <div key={idx} className="flex items-center gap-2 text-xs">
                                {getStatusIcon(link.status)}
                                <span className="truncate flex-1">{link.url}</span>
                                {link.responseTime && (
                                  <span className="text-muted-foreground">
                                    {link.responseTime}ms
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </Card>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {reports.length === 0 && !isChecking && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <Globe className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h3 className="font-medium">No Link Checks Yet</h3>
                <p className="text-sm text-muted-foreground">
                  Click "Check All Links" to start monitoring your tour links
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default BrokenLinkChecker;
