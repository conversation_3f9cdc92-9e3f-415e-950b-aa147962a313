-- Add CloudPano support to tours table
-- Migration: 20250627000001_add_cloudpano_support.sql

-- Update tour_platform enum to include cloudpano
ALTER TABLE public.tours 
DROP CONSTRAINT IF EXISTS tours_tour_platform_check;

ALTER TABLE public.tours 
ADD CONSTRAINT tours_tour_platform_check 
CHECK (tour_platform IN ('commonninja', 'cloudpano', 'custom'));

-- Add CloudPano specific columns
ALTER TABLE public.tours 
ADD COLUMN IF NOT EXISTS cloudpano_tour_id TEXT,
ADD COLUMN IF NOT EXISTS cloudpano_embed_url TEXT,
ADD COLUMN IF NOT EXISTS cloudpano_edit_url TEXT,
ADD COLUMN IF NOT EXISTS cloudpano_thumbnail_url TEXT,
ADD COLUMN IF NOT EXISTS cloudpano_config JSONB DEFAULT '{}'::jsonb,
ADD COLUMN IF NOT EXISTS approval_status TEXT CHECK (approval_status IN ('draft', 'pending_approval', 'approved', 'rejected')) DEFAULT 'draft',
ADD COLUMN IF NOT EXISTS approval_notes TEXT,
ADD COLUMN IF NOT EXISTS approved_by UUID REFERENCES public.profiles(id),
ADD COLUMN IF NOT EXISTS approved_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS submitted_for_approval_at TIMESTAMP WITH TIME ZONE;

-- Create CloudPano tours table for tracking sync status
CREATE TABLE IF NOT EXISTS public.cloudpano_tours (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tour_id UUID REFERENCES public.tours(id) ON DELETE CASCADE,
    cloudpano_tour_id TEXT NOT NULL UNIQUE,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    thumbnail_url TEXT,
    embed_url TEXT,
    edit_url TEXT,
    viewer_url TEXT,
    sync_status TEXT CHECK (sync_status IN ('syncing', 'synced', 'error')) DEFAULT 'syncing',
    sync_error TEXT,
    last_synced_at TIMESTAMP WITH TIME ZONE,
    cloudpano_created_at TIMESTAMP WITH TIME ZONE,
    cloudpano_updated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Add indexes for CloudPano tours
CREATE INDEX IF NOT EXISTS idx_cloudpano_tours_tour_id ON public.cloudpano_tours(tour_id);
CREATE INDEX IF NOT EXISTS idx_cloudpano_tours_cloudpano_tour_id ON public.cloudpano_tours(cloudpano_tour_id);
CREATE INDEX IF NOT EXISTS idx_cloudpano_tours_user_id ON public.cloudpano_tours(user_id);
CREATE INDEX IF NOT EXISTS idx_cloudpano_tours_sync_status ON public.cloudpano_tours(sync_status);

-- Add indexes for new tour columns
CREATE INDEX IF NOT EXISTS idx_tours_cloudpano_tour_id ON public.tours(cloudpano_tour_id);
CREATE INDEX IF NOT EXISTS idx_tours_approval_status ON public.tours(approval_status);
CREATE INDEX IF NOT EXISTS idx_tours_approved_by ON public.tours(approved_by);

-- Enable RLS on cloudpano_tours table
ALTER TABLE public.cloudpano_tours ENABLE ROW LEVEL SECURITY;

-- RLS policies for cloudpano_tours
CREATE POLICY "Users can view their own CloudPano tours" ON public.cloudpano_tours
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own CloudPano tours" ON public.cloudpano_tours
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own CloudPano tours" ON public.cloudpano_tours
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Admins can view all CloudPano tours" ON public.cloudpano_tours
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Update tours RLS policies to handle approval workflow
CREATE POLICY "Admins can approve tours" ON public.tours
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_cloudpano_tours_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for cloudpano_tours updated_at
CREATE TRIGGER update_cloudpano_tours_updated_at
    BEFORE UPDATE ON public.cloudpano_tours
    FOR EACH ROW
    EXECUTE FUNCTION update_cloudpano_tours_updated_at();

-- Add comments for documentation
COMMENT ON TABLE public.cloudpano_tours IS 'Tracks CloudPano tours and their sync status with our platform';
COMMENT ON COLUMN public.tours.cloudpano_tour_id IS 'CloudPano tour ID for direct integration';
COMMENT ON COLUMN public.tours.cloudpano_embed_url IS 'CloudPano embed URL for public viewing';
COMMENT ON COLUMN public.tours.cloudpano_edit_url IS 'CloudPano edit URL for tour modification';
COMMENT ON COLUMN public.tours.cloudpano_thumbnail_url IS 'CloudPano generated thumbnail URL';
COMMENT ON COLUMN public.tours.cloudpano_config IS 'CloudPano specific configuration and metadata';
COMMENT ON COLUMN public.tours.approval_status IS 'Tour approval status for admin workflow';
COMMENT ON COLUMN public.tours.approval_notes IS 'Admin notes for approval/rejection';
COMMENT ON COLUMN public.tours.approved_by IS 'Admin who approved the tour';
COMMENT ON COLUMN public.tours.approved_at IS 'Timestamp when tour was approved';
COMMENT ON COLUMN public.tours.submitted_for_approval_at IS 'Timestamp when tour was submitted for approval';
