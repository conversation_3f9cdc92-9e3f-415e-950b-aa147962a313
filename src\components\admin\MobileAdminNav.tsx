/**
 * Mobile Admin Navigation Component
 * Enhanced mobile navigation for admin interface
 * Provides touch-optimized navigation without breaking existing functionality
 */

import { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import {
  Menu,
  X,
  Home,
  Users,
  Building2,
  Target,
  FileText,
  ShoppingCart,
  Settings,
  BarChart3,
  Wrench,
  Eye,
  UserCheck,
  Globe,
  ChevronRight
} from 'lucide-react';
import { useResponsiveDesign } from '@/hooks/useResponsiveDesign';

interface MobileAdminNavProps {
  isOpen: boolean;
  onClose: () => void;
}

const MobileAdminNav = ({ isOpen, onClose }: MobileAdminNavProps) => {
  const location = useLocation();
  const { getTouchTargetSize, isMobile } = useResponsiveDesign();

  const navigationItems = [
    {
      title: 'Overview',
      items: [
        { name: 'Dashboard', href: '/admin', icon: Home, badge: null },
        { name: 'Analytics', href: '/admin/analytics', icon: BarChart3, badge: 'New' }
      ]
    },
    {
      title: 'Management',
      items: [
        { name: 'Users', href: '/admin/users', icon: Users, badge: null },
        { name: 'Tours', href: '/admin/tours', icon: Building2, badge: null },
        { name: 'Vendors', href: '/admin/vendors', icon: UserCheck, badge: null }
      ]
    },
    {
      title: 'Content & Design',
      items: [
        { name: 'Overlay Studio', href: '/admin/overlays', icon: Target, badge: 'Enhanced' },
        { name: 'Pages', href: '/admin/pages', icon: FileText, badge: null },
        { name: 'Commerce', href: '/admin/commerce', icon: ShoppingCart, badge: null }
      ]
    },
    {
      title: 'System',
      items: [
        { name: 'Integrations', href: '/admin/integrations', icon: Settings, badge: null },
        { name: 'Tools', href: '/admin/tools', icon: Wrench, badge: null }
      ]
    },
    {
      title: 'Quick Actions',
      items: [
        { name: 'User Dashboard', href: '/dashboard', icon: Eye, badge: null },
        { name: 'Vendor Dashboard', href: '/vendor-dashboard', icon: Building2, badge: null },
        { name: 'View Website', href: '/', icon: Globe, badge: null }
      ]
    }
  ];

  const isActiveRoute = (href: string) => {
    if (href === '/admin') {
      return location.pathname === '/admin';
    }
    return location.pathname.startsWith(href);
  };

  return (
    <div className="lg:hidden">
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="left" className="w-80 p-0">
          <div className="flex flex-col h-full">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                  <Building2 className="w-4 h-4 text-primary-foreground" />
                </div>
                <div>
                  <h2 className="font-semibold text-sm">VirtualRealTour</h2>
                  <p className="text-xs text-muted-foreground">Admin Panel</p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className={getTouchTargetSize()}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Navigation */}
            <ScrollArea className="flex-1 px-4">
              <div className="space-y-6 py-4">
                {navigationItems.map((section) => (
                  <div key={section.title}>
                    <h3 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-3">
                      {section.title}
                    </h3>
                    <div className="space-y-1">
                      {section.items.map((item) => {
                        const Icon = item.icon;
                        const isActive = isActiveRoute(item.href);
                        
                        return (
                          <Link
                            key={item.name}
                            to={item.href}
                            onClick={onClose}
                            className={`
                              flex items-center justify-between w-full p-3 rounded-lg text-left
                              transition-all duration-200
                              ${getTouchTargetSize()}
                              ${isActive 
                                ? 'bg-primary text-primary-foreground shadow-sm' 
                                : 'hover:bg-muted text-muted-foreground hover:text-foreground'
                              }
                            `}
                          >
                            <div className="flex items-center gap-3">
                              <Icon className="w-4 h-4" />
                              <span className="font-medium text-sm">{item.name}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              {item.badge && (
                                <Badge 
                                  variant={isActive ? "secondary" : "outline"} 
                                  className="text-xs px-1.5 py-0"
                                >
                                  {item.badge}
                                </Badge>
                              )}
                              <ChevronRight className="w-3 h-3" />
                            </div>
                          </Link>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>

            {/* Footer */}
            <div className="p-4 border-t">
              <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
                <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                  <UserCheck className="w-4 h-4 text-primary-foreground" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-sm">Admin User</p>
                  <p className="text-xs text-muted-foreground truncate"><EMAIL></p>
                </div>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  );
};

// Mobile Navigation Trigger Component
export const MobileAdminNavTrigger = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { isMobile, getTouchTargetSize } = useResponsiveDesign();

  if (!isMobile) return null;

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(true)}
        className={`lg:hidden ${getTouchTargetSize()}`}
      >
        <Menu className="w-5 h-5" />
        <span className="sr-only">Open navigation menu</span>
      </Button>
      
      <MobileAdminNav isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </>
  );
};

export default MobileAdminNav;
