/**
 * Commerce Service
 * Main service that orchestrates all e-commerce functionality
 * Integrates products, orders, vendors, and WhatsApp communication
 */

import { supabase } from '@/lib/supabase';
import { productService } from './ProductService';
import { cartService } from './CartService';
import { whatsappService } from './WhatsAppService';
import type { Product } from '@/components/commerce/ProductCard';
import type { Vendor } from '@/components/commerce/VendorProfile';
import type { Order } from '@/components/commerce/OrderSummary';
import type { CustomerInfo } from '@/components/commerce/ShoppingCart';

export interface VendorRegistration {
  name: string;
  email: string;
  phone?: string;
  whatsapp_number?: string;
  business_address?: string;
  business_description?: string;
  logo_url?: string;
}

export interface VendorStats {
  total_products: number;
  active_products: number;
  draft_products: number;
  total_orders: number;
  pending_orders: number;
  completed_orders: number;
  total_revenue: number;
  monthly_revenue: number;
  commission_paid: number;
  rating: number;
  response_time: string;
}

export class CommerceService {
  /**
   * Vendor Management
   */
  async registerVendor(vendorData: VendorRegistration): Promise<Vendor> {
    try {
      const { data, error } = await supabase
        .from('vendors')
        .insert({
          name: vendorData.name,
          email: vendorData.email,
          phone: vendorData.phone,
          whatsapp_number: vendorData.whatsapp_number,
          business_address: vendorData.business_address,
          business_description: vendorData.business_description,
          logo_url: vendorData.logo_url,
          status: 'pending'
        })
        .select()
        .single();

      if (error) throw error;

      // Send notification to admin about new vendor registration
      await this.notifyAdminNewVendor(data.id);

      return data;
    } catch (error) {
      console.error('Error registering vendor:', error);
      throw new Error('Failed to register vendor');
    }
  }

  async approveVendor(vendorId: string, commissionRate: number = 0.10): Promise<Vendor> {
    try {
      const { data, error } = await supabase
        .from('vendors')
        .update({ 
          status: 'approved',
          commission_rate: commissionRate 
        })
        .eq('id', vendorId)
        .select()
        .single();

      if (error) throw error;

      // Send welcome message to vendor
      await this.sendVendorWelcomeMessage(data);

      return data;
    } catch (error) {
      console.error('Error approving vendor:', error);
      throw new Error('Failed to approve vendor');
    }
  }

  async getVendor(vendorId: string): Promise<Vendor | null> {
    try {
      const { data, error } = await supabase
        .from('vendors')
        .select('*')
        .eq('id', vendorId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error fetching vendor:', error);
      throw new Error('Failed to fetch vendor');
    }
  }

  async getVendors(status?: string): Promise<Vendor[]> {
    try {
      let query = supabase.from('vendors').select('*');
      
      if (status) {
        query = query.eq('status', status);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching vendors:', error);
      throw new Error('Failed to fetch vendors');
    }
  }

  async getVendorStats(vendorId: string): Promise<VendorStats> {
    try {
      // Get product stats
      const { data: productStats } = await supabase
        .from('products')
        .select('status')
        .eq('vendor_id', vendorId);

      // Get order stats
      const { data: orderStats } = await supabase
        .from('order_items')
        .select(`
          quantity,
          price,
          vendor_commission,
          order:orders(status, created_at)
        `)
        .eq('vendor_id', vendorId);

      // Calculate stats
      const total_products = productStats?.length || 0;
      const active_products = productStats?.filter(p => p.status === 'active').length || 0;
      const draft_products = productStats?.filter(p => p.status === 'draft').length || 0;

      const total_orders = orderStats?.length || 0;
      const pending_orders = orderStats?.filter(o => 
        ['pending', 'confirmed', 'processing'].includes(o.order.status)
      ).length || 0;
      const completed_orders = orderStats?.filter(o => 
        ['delivered', 'shipped'].includes(o.order.status)
      ).length || 0;

      const total_revenue = orderStats?.reduce((sum, item) => 
        sum + (item.price * item.quantity), 0
      ) || 0;

      const currentMonth = new Date().getMonth();
      const monthly_revenue = orderStats?.filter(item => 
        new Date(item.order.created_at).getMonth() === currentMonth
      ).reduce((sum, item) => sum + (item.price * item.quantity), 0) || 0;

      const commission_paid = orderStats?.reduce((sum, item) => 
        sum + item.vendor_commission, 0
      ) || 0;

      return {
        total_products,
        active_products,
        draft_products,
        total_orders,
        pending_orders,
        completed_orders,
        total_revenue,
        monthly_revenue,
        commission_paid,
        rating: 4.5, // TODO: Implement rating system
        response_time: '< 2 hours' // TODO: Calculate actual response time
      };
    } catch (error) {
      console.error('Error fetching vendor stats:', error);
      throw new Error('Failed to fetch vendor stats');
    }
  }

  /**
   * Order Management
   */
  async getOrder(orderId: string): Promise<Order | null> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          items:order_items(
            *,
            product:products(*),
            vendor:vendors(id, name, whatsapp_number)
          )
        `)
        .eq('id', orderId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        throw error;
      }

      return this.mapSupabaseOrderToOrder(data);
    } catch (error) {
      console.error('Error fetching order:', error);
      throw new Error('Failed to fetch order');
    }
  }

  async getOrders(filters: {
    vendorId?: string;
    customerId?: string;
    status?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<Order[]> {
    try {
      let query = supabase
        .from('orders')
        .select(`
          *,
          items:order_items(
            *,
            product:products(*),
            vendor:vendors(id, name, whatsapp_number)
          )
        `)
        .order('created_at', { ascending: false });

      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      if (filters.offset) {
        query = query.range(filters.offset, filters.offset + (filters.limit || 20) - 1);
      }

      const { data, error } = await query;

      if (error) throw error;

      let orders = data.map(order => this.mapSupabaseOrderToOrder(order));

      // Filter by vendor if specified
      if (filters.vendorId) {
        orders = orders.filter(order => 
          order.items.some(item => item.vendor.id === filters.vendorId)
        );
      }

      return orders;
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw new Error('Failed to fetch orders');
    }
  }

  async updateOrderStatus(orderId: string, status: string): Promise<Order> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
        .select(`
          *,
          items:order_items(
            *,
            product:products(*),
            vendor:vendors(id, name, whatsapp_number)
          )
        `)
        .single();

      if (error) throw error;

      const order = this.mapSupabaseOrderToOrder(data);

      // Send status update notification
      await this.sendOrderStatusUpdate(order);

      return order;
    } catch (error) {
      console.error('Error updating order status:', error);
      throw new Error('Failed to update order status');
    }
  }

  /**
   * Complete checkout process
   */
  async processCheckout(customerInfo: CustomerInfo): Promise<string> {
    try {
      // Create order through cart service
      const orderNumber = await cartService.checkout(customerInfo);

      // Get the created order
      const { data: order } = await supabase
        .from('orders')
        .select(`
          *,
          items:order_items(
            *,
            product:products(*),
            vendor:vendors(id, name, whatsapp_number)
          )
        `)
        .eq('order_number', orderNumber)
        .single();

      if (order) {
        const mappedOrder = this.mapSupabaseOrderToOrder(order);
        
        // Send notifications
        await this.sendOrderNotifications(mappedOrder);
      }

      return orderNumber;
    } catch (error) {
      console.error('Error processing checkout:', error);
      throw new Error('Failed to process checkout');
    }
  }

  /**
   * Send order notifications to all parties
   */
  private async sendOrderNotifications(order: Order): Promise<void> {
    try {
      // Send confirmation to customer
      await whatsappService.sendOrderConfirmation(order, order.items);

      // Group items by vendor and send notifications
      const vendorGroups = order.items.reduce((groups, item) => {
        const vendorId = item.vendor.id;
        if (!groups[vendorId]) {
          groups[vendorId] = {
            vendor: item.vendor,
            items: []
          };
        }
        groups[vendorId].items.push(item);
        return groups;
      }, {} as Record<string, { vendor: any, items: any[] }>);

      // Send notification to each vendor
      for (const group of Object.values(vendorGroups)) {
        if (group.vendor.whatsapp_number) {
          await whatsappService.sendOrderToVendor(
            order, 
            group.items, 
            group.vendor
          );
        }
      }
    } catch (error) {
      console.error('Error sending order notifications:', error);
    }
  }

  /**
   * Send order status update notifications
   */
  private async sendOrderStatusUpdate(order: Order): Promise<void> {
    try {
      // TODO: Implement status update notifications
      console.log(`Order ${order.order_number} status updated to ${order.status}`);
    } catch (error) {
      console.error('Error sending status update:', error);
    }
  }

  /**
   * Send vendor welcome message
   */
  private async sendVendorWelcomeMessage(vendor: Vendor): Promise<void> {
    try {
      if (vendor.whatsapp_number) {
        const message = `🎉 Welcome to VirtualRealTour! 

Your vendor account has been approved. You can now:
• Add products to your catalog
• Receive orders from customers
• Manage your business through our platform

Start adding your first product today!

Best regards,
VirtualRealTour Team`;

        // TODO: Send WhatsApp message to vendor
        console.log(`Welcome message sent to vendor: ${vendor.name}`);
      }
    } catch (error) {
      console.error('Error sending vendor welcome message:', error);
    }
  }

  /**
   * Notify admin about new vendor registration
   */
  private async notifyAdminNewVendor(vendorId: string): Promise<void> {
    try {
      // TODO: Implement admin notification system
      console.log(`New vendor registration: ${vendorId}`);
    } catch (error) {
      console.error('Error notifying admin:', error);
    }
  }

  /**
   * Map Supabase order data to Order interface
   */
  private mapSupabaseOrderToOrder(data: any): Order {
    return {
      id: data.id,
      order_number: data.order_number,
      customer_name: data.customer_name,
      customer_phone: data.customer_phone,
      customer_email: data.customer_email,
      customer_address: data.customer_address,
      total_amount: data.total_amount,
      status: data.status,
      payment_method: data.payment_method,
      payment_status: data.payment_status,
      tour_context: data.tour_context,
      created_at: data.created_at,
      updated_at: data.updated_at,
      items: data.items.map((item: any) => ({
        id: item.id,
        product: {
          id: item.product.id,
          title: item.product.title,
          price: item.product.price,
          images: item.product.images || []
        },
        vendor: {
          id: item.vendor.id,
          name: item.vendor.name,
          whatsapp_number: item.vendor.whatsapp_number
        },
        quantity: item.quantity,
        price: item.price
      }))
    };
  }
}

export const commerceService = new CommerceService();
