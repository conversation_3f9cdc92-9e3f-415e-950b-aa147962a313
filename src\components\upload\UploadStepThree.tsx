
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { categories } from '@/lib/constants';
import { FormData } from '@/hooks/useUploadForm';

interface UploadStepThreeProps {
  formData: FormData;
}

const UploadStepThree = ({ formData }: UploadStepThreeProps) => {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Review Your Tour</h3>
        <p className="text-sm text-muted-foreground mt-1">
          Please review your tour details before submission
        </p>
      </div>

      <Card>
        <CardContent className="p-6 space-y-4">
          <div className="space-y-2">
            <Label className="font-medium">Title</Label>
            <p className="text-foreground">{formData.title}</p>
          </div>

          {formData.description && (
            <div className="space-y-2">
              <Label className="font-medium">Description</Label>
              <p className="text-foreground">{formData.description}</p>
            </div>
          )}

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="font-medium">Category</Label>
              <p className="text-foreground">
                {categories.find(cat => cat.value === formData.category)?.label}
              </p>
            </div>
            {formData.location && (
              <div className="space-y-2">
                <Label className="font-medium">Location</Label>
                <p className="text-foreground">{formData.location}</p>
              </div>
            )}
          </div>

          <div className="space-y-2">
            <Label className="font-medium">Media Files</Label>
            <p className="text-foreground">
              {formData.files.length} file{formData.files.length !== 1 ? 's' : ''} uploaded
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="bg-primary/5 border border-primary/20 rounded-lg p-4">
        <p className="text-sm text-primary">
          <strong>Next steps:</strong> Your tour will be submitted for review. Once approved,
          it will be published and you can add interactive hotspots and customize the experience
          in your dashboard.
        </p>
      </div>
    </div>
  );
};

export default UploadStepThree;
