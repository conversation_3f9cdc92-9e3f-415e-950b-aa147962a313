/**
 * Performance Monitoring Utilities
 * Real-time performance tracking for VirtualRealTour
 */

export interface PerformanceMetrics {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  bundleSize: number;
  imageLoadTime: number;
  psvInitTime: number;
}

export interface PerformanceThresholds {
  loadTime: number;
  renderTime: number;
  memoryUsage: number;
  imageLoadTime: number;
}

/**
 * Performance monitor class
 */
export class PerformanceMonitor {
  private metrics: Partial<PerformanceMetrics> = {};
  private observers: PerformanceObserver[] = [];
  private startTime: number = performance.now();
  
  private thresholds: PerformanceThresholds = {
    loadTime: 3000, // 3 seconds
    renderTime: 100, // 100ms
    memoryUsage: 50 * 1024 * 1024, // 50MB
    imageLoadTime: 2000 // 2 seconds
  };

  constructor() {
    this.initializeObservers();
    this.trackPageLoad();
  }

  /**
   * Initialize performance observers
   */
  private initializeObservers() {
    // Navigation timing
    if ('PerformanceObserver' in window) {
      const navObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            this.metrics.loadTime = navEntry.loadEventEnd - navEntry.navigationStart;
          }
        });
      });

      navObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navObserver);

      // Resource timing
      const resourceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          if (entry.name.includes('.jpg') || entry.name.includes('.png') || entry.name.includes('.webp')) {
            this.metrics.imageLoadTime = entry.duration;
          }
        });
      });

      resourceObserver.observe({ entryTypes: ['resource'] });
      this.observers.push(resourceObserver);
    }
  }

  /**
   * Track page load performance
   */
  private trackPageLoad() {
    window.addEventListener('load', () => {
      setTimeout(() => {
        this.collectMetrics();
      }, 100);
    });
  }

  /**
   * Collect current performance metrics
   */
  collectMetrics(): PerformanceMetrics {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    
    this.metrics = {
      loadTime: navigation ? navigation.loadEventEnd - navigation.navigationStart : 0,
      renderTime: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
      memoryUsage: this.getMemoryUsage(),
      bundleSize: this.getBundleSize(),
      imageLoadTime: this.metrics.imageLoadTime || 0,
      psvInitTime: this.metrics.psvInitTime || 0
    };

    return this.metrics as PerformanceMetrics;
  }

  /**
   * Get memory usage
   */
  private getMemoryUsage(): number {
    if ('memory' in performance) {
      return (performance as any).memory.usedJSHeapSize;
    }
    return 0;
  }

  /**
   * Estimate bundle size
   */
  private getBundleSize(): number {
    const resources = performance.getEntriesByType('resource');
    return resources
      .filter(resource => resource.name.includes('.js'))
      .reduce((total, resource) => total + (resource.transferSize || 0), 0);
  }

  /**
   * Track PSV initialization time
   */
  trackPSVInit(startTime: number) {
    this.metrics.psvInitTime = performance.now() - startTime;
  }

  /**
   * Check if metrics exceed thresholds
   */
  checkThresholds(): { metric: string; value: number; threshold: number }[] {
    const violations: { metric: string; value: number; threshold: number }[] = [];
    
    Object.entries(this.thresholds).forEach(([key, threshold]) => {
      const value = this.metrics[key as keyof PerformanceMetrics];
      if (value && value > threshold) {
        violations.push({ metric: key, value, threshold });
      }
    });

    return violations;
  }

  /**
   * Log performance metrics
   */
  logMetrics() {
    const metrics = this.collectMetrics();
    const violations = this.checkThresholds();

    console.group('🚀 Performance Metrics');
    console.log('Load Time:', `${metrics.loadTime.toFixed(2)}ms`);
    console.log('Render Time:', `${metrics.renderTime.toFixed(2)}ms`);
    console.log('Memory Usage:', `${(metrics.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
    console.log('Bundle Size:', `${(metrics.bundleSize / 1024).toFixed(2)}KB`);
    console.log('Image Load Time:', `${metrics.imageLoadTime.toFixed(2)}ms`);
    console.log('PSV Init Time:', `${metrics.psvInitTime.toFixed(2)}ms`);
    
    if (violations.length > 0) {
      console.warn('⚠️ Performance Violations:', violations);
    }
    
    console.groupEnd();
  }

  /**
   * Get Core Web Vitals
   */
  getCoreWebVitals(): Promise<{
    lcp: number;
    fid: number;
    cls: number;
  }> {
    return new Promise((resolve) => {
      const vitals = { lcp: 0, fid: 0, cls: 0 };
      let resolveCount = 0;

      // Largest Contentful Paint
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        vitals.lcp = lastEntry.startTime;
        resolveCount++;
        if (resolveCount === 3) resolve(vitals);
      }).observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          vitals.fid = entry.processingStart - entry.startTime;
        });
        resolveCount++;
        if (resolveCount === 3) resolve(vitals);
      }).observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift
      new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            vitals.cls += entry.value;
          }
        });
        resolveCount++;
        if (resolveCount === 3) resolve(vitals);
      }).observe({ entryTypes: ['layout-shift'] });

      // Fallback timeout
      setTimeout(() => resolve(vitals), 5000);
    });
  }

  /**
   * Cleanup observers
   */
  destroy() {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
  }
}

/**
 * Bundle analyzer utility
 */
export class BundleAnalyzer {
  static analyzeChunks() {
    const resources = performance.getEntriesByType('resource');
    const jsResources = resources.filter(r => r.name.includes('.js'));
    
    const analysis = jsResources.map(resource => ({
      name: resource.name.split('/').pop(),
      size: resource.transferSize || 0,
      loadTime: resource.duration,
      cached: resource.transferSize === 0
    }));

    console.table(analysis);
    return analysis;
  }

  static getUnusedCode() {
    if ('coverage' in window) {
      // This would require DevTools coverage API
      console.log('Code coverage analysis requires DevTools');
    }
  }
}

/**
 * Memory leak detector
 */
export class MemoryLeakDetector {
  private initialMemory: number = 0;
  private checkInterval: number = 0;

  start() {
    if ('memory' in performance) {
      this.initialMemory = (performance as any).memory.usedJSHeapSize;
      
      this.checkInterval = window.setInterval(() => {
        const currentMemory = (performance as any).memory.usedJSHeapSize;
        const growth = currentMemory - this.initialMemory;
        
        if (growth > 10 * 1024 * 1024) { // 10MB growth
          console.warn('🚨 Potential memory leak detected:', {
            initial: `${(this.initialMemory / 1024 / 1024).toFixed(2)}MB`,
            current: `${(currentMemory / 1024 / 1024).toFixed(2)}MB`,
            growth: `${(growth / 1024 / 1024).toFixed(2)}MB`
          });
        }
      }, 30000); // Check every 30 seconds
    }
  }

  stop() {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }
  }
}

// Global performance monitor
export const performanceMonitor = new PerformanceMonitor();

// Initialize memory leak detection in development
if (process.env.NODE_ENV === 'development') {
  const memoryDetector = new MemoryLeakDetector();
  memoryDetector.start();
}
