
import { Card, CardContent } from '@/components/ui/card'
import { Star } from 'lucide-react'

const TestimonialsSection = () => {
  const testimonials = [
    {
      name: "<PERSON>",
      title: "Real Estate Director",
      company: "Luxury Properties Lagos",
      content: "VirtualRealTour transformed how we showcase properties. Client inquiries increased by 280% within 3 months.",
      rating: 5
    },
    {
      name: "<PERSON>",
      title: "Hotel Manager",
      company: "Four Points Sheraton Lagos",
      content: "The virtual tours significantly increased our online bookings. Guests arrive knowing exactly what to expect.",
      rating: 5
    },
    {
      name: "Dr. <PERSON><PERSON>",
      title: "University Director",
      company: "University of Ibadan",
      content: "Perfect for virtual campus tours. International students can explore our facilities from anywhere in the world.",
      rating: 5
    }
  ]

  return (
    <section className="bg-background section-gradient-subtle">
      <div className="container py-16 lg:py-24">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            What Our Clients Say
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Trusted by leading organizations across Nigeria
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="card-hover-glow">
              <CardContent className="p-6">
                <div className="flex text-primary mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 fill-current" />
                  ))}
                </div>
                
                <blockquote className="text-muted-foreground mb-6 leading-relaxed">
                  "{testimonial.content}"
                </blockquote>
                
                <div className="border-t border-border pt-4">
                  <h4 className="font-semibold text-foreground">{testimonial.name}</h4>
                  <p className="text-sm text-muted-foreground">{testimonial.title}</p>
                  <p className="text-sm text-primary font-medium">{testimonial.company}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

export default TestimonialsSection
