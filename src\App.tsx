import { Toaster } from "@/components/ui/toaster";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ThemeProvider } from "@/components/ThemeProvider";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { AuthProvider } from "@/hooks/useAuth";
import { AuthGuard } from "@/components/AuthGuard";
import FloatingWhatsAppWidget from "@/components/commerce/FloatingWhatsAppWidget";
import { ErrorBoundary } from "react-error-boundary";

// Error fallback component
const ErrorFallback = ({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) => {
  console.error('🚨 Application Error:', error);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center">
        <div className="text-red-500 text-6xl mb-4">⚠️</div>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Something went wrong</h1>
        <p className="text-gray-600 mb-4">
          We're sorry, but something unexpected happened. Please try refreshing the page.
        </p>
        <div className="space-y-2">
          <button
            type="button"
            onClick={resetErrorBoundary}
            className="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Try again
          </button>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="w-full bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
          >
            Refresh page
          </button>
        </div>
        {process.env.NODE_ENV === 'development' && (
          <details className="mt-4 text-left">
            <summary className="cursor-pointer text-sm text-gray-500">Error details</summary>
            <pre className="mt-2 text-xs text-red-600 bg-red-50 p-2 rounded overflow-auto">
              {error.message}
            </pre>
          </details>
        )}
      </div>
    </div>
  );
};

// Public Pages
import Index from "./pages/Index";
import Welcome from "./pages/Welcome";
import Services from "./pages/Services";
import Showcase from "./pages/Showcase";
import About from "./pages/About";
import Contact from "./pages/Contact";
import Auth from "./pages/Auth";
import TourView from "./pages/TourView";
import TourEmbed from "./pages/TourEmbed";
import WPVRTourPage from "./pages/WPVRTourPage";
import WooCommerceTest from "./pages/WooCommerceTest";
import UserTourEditor from "./pages/UserTourEditor";
import NotFound from "./pages/NotFound";
import OrderTracking from "./pages/OrderTracking";

// User Pages
import Dashboard from "./pages/Dashboard";

// Vendor Pages
import VendorDashboardUnified from "./pages/VendorDashboardUnified";
import VendorProducts from "./pages/VendorProducts";

// Admin Pages - Unified System
import AdminDashboard from "./pages/AdminDashboard";
import AdminPages from "./pages/admin/AdminPages";
import AdminToursUnified from "./pages/admin/AdminToursUnified";
import AdminOverlays from "./pages/admin/AdminOverlays";
import AdminVendorsUnified from "./pages/admin/AdminVendorsUnified";
import AdminCommerceUnified from "./pages/admin/AdminCommerceUnified";
import AdminIntegrations from "./pages/admin/AdminIntegrations";
import AdminTools from "./pages/admin/AdminTools";
import AdminUsers from "./pages/admin/AdminUsers";
import AdminWordPress from "./pages/admin/AdminWordPress";

// Keep essential admin pages
import AdminProfile from "./pages/admin/AdminProfile";
import AdminTourEditor from "./pages/admin/AdminTourEditor";

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // 5 minutes
      retry: 1,
    },
  },
});

const App = () => {
  console.log('🚀 App component rendering...');

  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('🚨 App Error Boundary caught error:', error, errorInfo);
      }}
    >
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme="light" storageKey="vrt-ui-theme">
          <AuthProvider>
            <TooltipProvider>
              <Toaster />
              <Sonner />
              <BrowserRouter>
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={<Index />} />
                <Route path="/welcome" element={<Welcome />} />
                <Route path="/services" element={<Services />} />
                <Route path="/showcase" element={<Showcase />} />
                <Route path="/about" element={<About />} />
                <Route path="/contact" element={<Contact />} />
                <Route path="/auth" element={<Auth />} />

                {/* Tour Routes */}
                <Route path="/tours" element={<TourView />} />
                <Route path="/tours/:id" element={<TourView />} />
                <Route path="/tour/:id" element={<TourView />} />
                <Route path="/embed/:id" element={<TourEmbed />} />
                <Route path="/wpvr/:id" element={<WPVRTourPage />} />

                {/* User Routes */}
                <Route path="/dashboard" element={
                  <AuthGuard>
                    <Dashboard />
                  </AuthGuard>
                } />
                <Route path="/user-tour-editor" element={
                  <AuthGuard>
                    <UserTourEditor />
                  </AuthGuard>
                } />

                {/* Vendor Routes */}
                <Route path="/vendor" element={
                  <AuthGuard>
                    <VendorDashboardUnified />
                  </AuthGuard>
                } />
                <Route path="/vendor-dashboard" element={
                  <AuthGuard>
                    <VendorDashboardUnified />
                  </AuthGuard>
                } />
                <Route path="/vendor/products" element={
                  <AuthGuard>
                    <VendorProducts />
                  </AuthGuard>
                } />

                {/* Admin Routes - Unified System */}
                <Route path="/admin" element={
                  <AuthGuard requireAdmin>
                    <AdminDashboard />
                  </AuthGuard>
                } />
                <Route path="/admin/pages" element={
                  <AuthGuard requireAdmin>
                    <AdminPages />
                  </AuthGuard>
                } />
                <Route path="/admin/tours" element={
                  <AuthGuard requireAdmin>
                    <AdminToursUnified />
                  </AuthGuard>
                } />
                <Route path="/admin/overlays" element={
                  <AuthGuard requireAdmin>
                    <AdminOverlays />
                  </AuthGuard>
                } />
                <Route path="/admin/vendors" element={
                  <AuthGuard requireAdmin>
                    <AdminVendorsUnified />
                  </AuthGuard>
                } />
                <Route path="/admin/commerce" element={
                  <AuthGuard requireAdmin>
                    <AdminCommerceUnified />
                  </AuthGuard>
                } />
                <Route path="/admin/users" element={
                  <AuthGuard requireAdmin>
                    <AdminUsers />
                  </AuthGuard>
                } />
                <Route path="/admin/integrations" element={
                  <AuthGuard requireAdmin>
                    <AdminIntegrations />
                  </AuthGuard>
                } />
                <Route path="/admin/tools" element={
                  <AuthGuard requireAdmin>
                    <AdminTools />
                  </AuthGuard>
                } />
                <Route path="/admin/wordpress" element={
                  <AuthGuard requireAdmin>
                    <AdminWordPress />
                  </AuthGuard>
                } />

                {/* Essential Admin Routes */}
                <Route path="/admin/tours/:tourId/edit" element={
                  <AuthGuard requireAdmin>
                    <AdminTourEditor />
                  </AuthGuard>
                } />
                <Route path="/admin/profile" element={
                  <AuthGuard requireAdmin>
                    <AdminProfile />
                  </AuthGuard>
                } />

                {/* Test Routes */}
                <Route path="/woocommerce-test" element={<WooCommerceTest />} />
                <Route path="/order-tracking" element={<OrderTracking />} />

                {/* 404 Route */}
                <Route path="*" element={<NotFound />} />
              </Routes>

              {/* Floating Components - Available on all pages */}
              <FloatingWhatsAppWidget />
            </BrowserRouter>
          </TooltipProvider>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;