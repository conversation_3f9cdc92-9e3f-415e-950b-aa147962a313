/**
 * Streamlined Tour Creation Modal for Users
 * Simplified, mobile-first tour creation experience
 */

import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Upload, 
  FileImage, 
  MapPin, 
  Building, 
  Phone, 
  Mail, 
  Globe,
  ArrowRight,
  Check,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { createTourSlug } from '@/lib/slugUtils';
import CategorySelect from '@/components/upload/CategorySelect';

interface StreamlinedTourCreationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

interface FormData {
  title: string;
  description: string;
  category: string;
  location: string;
  businessName: string;
  businessType: string;
  contactPhone: string;
  contactEmail: string;
  website: string;
  businessHours: string;
  files: File[];
}

const StreamlinedTourCreationModal = ({
  open,
  onOpenChange,
  onSuccess
}: StreamlinedTourCreationModalProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { user } = useAuth();

  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    category: '',
    location: '',
    businessName: '',
    businessType: '',
    contactPhone: '',
    contactEmail: user?.email || '',
    website: '',
    businessHours: '',
    files: []
  });

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      category: '',
      location: '',
      businessName: '',
      businessType: '',
      contactPhone: '',
      contactEmail: user?.email || '',
      website: '',
      businessHours: '',
      files: []
    });
    setCurrentStep(1);
  };

  const handleClose = () => {
    onOpenChange(false);
    resetForm();
  };

  const handleNext = () => {
    if (currentStep === 1) {
      if (!formData.title || !formData.category || !formData.location) {
        toast.error('Please fill in all required fields');
        return;
      }
    }
    setCurrentStep(prev => prev + 1);
  };

  const handleBack = () => {
    setCurrentStep(prev => prev - 1);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setFormData(prev => ({ ...prev, files }));
    toast.success(`${files.length} image(s) selected`);
  };

  const handleSubmit = async () => {
    if (!user?.id) {
      toast.error('Please log in to create a tour');
      return;
    }

    setIsSubmitting(true);

    try {
      const slug = createTourSlug(formData.title);

      // Create tour in database
      const { data: tour, error } = await supabase
        .from('tours')
        .insert({
          title: formData.title,
          description: formData.description,
          category: formData.category,
          location: formData.location,
          business_name: formData.businessName,
          business_type: formData.businessType,
          contact_phone: formData.contactPhone,
          contact_email: formData.contactEmail,
          website: formData.website,
          business_hours: formData.businessHours,
          slug,
          user_id: user.id,
          creation_method: 'upload', // Use valid enum value for image uploads
          tour_platform: 'commonninja', // Users always use CommonNinja
          status: 'draft',
          views: 0,
          featured: false
        })
        .select()
        .single();

      if (error) throw error;

      // Upload files if any
      if (formData.files.length > 0) {
        for (let i = 0; i < formData.files.length; i++) {
          const file = formData.files[i];
          const fileExt = file.name.split('.').pop();
          const fileName = `${user.id}/${tour.id}/${Date.now()}-${i}.${fileExt}`;

          const { error: uploadError } = await supabase.storage
            .from('tour-media')
            .upload(fileName, file);

          if (uploadError) {
            console.error('Upload error:', uploadError);
            continue;
          }

          // Set first image as thumbnail
          if (i === 0) {
            const { data: { publicUrl } } = supabase.storage
              .from('tour-media')
              .getPublicUrl(fileName);

            await supabase
              .from('tours')
              .update({ thumbnail_url: publicUrl })
              .eq('id', tour.id);
          }
        }
      }

      toast.success('Tour created successfully! It will be reviewed by our team.');
      onSuccess?.();
      handleClose();

    } catch (error) {
      console.error('Error creating tour:', error);
      toast.error('Failed to create tour. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const canProceed = () => {
    if (currentStep === 1) {
      return formData.title && formData.category && formData.location;
    }
    if (currentStep === 2) {
      return true; // Business info is optional
    }
    if (currentStep === 3) {
      return formData.files.length > 0;
    }
    return true;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[95vw] max-w-2xl max-h-[95vh] overflow-y-auto sm:w-full">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Create Virtual Tour</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Step {currentStep} of 3 - {currentStep === 1 ? 'Basic Information' : currentStep === 2 ? 'Business Details' : 'Upload Images'}
          </p>
        </DialogHeader>

        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2 mb-6">
          <div
            className={`bg-blue-600 h-2 rounded-full transition-all duration-300 ${
              currentStep === 1 ? 'w-1/3' : currentStep === 2 ? 'w-2/3' : 'w-full'
            }`}
          />
        </div>

        <div className="space-y-6">
          {/* Step 1: Basic Information */}
          {currentStep === 1 && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="title">Tour Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter tour title"
                  className="mt-1"
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe your virtual tour"
                  rows={3}
                  className="mt-1"
                />
              </div>

              <div>
                <Label>Category *</Label>
                <CategorySelect
                  value={formData.category}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                />
              </div>

              <div>
                <Label htmlFor="location">Location *</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                  placeholder="City, State, Country"
                  className="mt-1"
                />
              </div>
            </div>
          )}

          {/* Step 2: Business Information */}
          {currentStep === 2 && (
            <div className="space-y-4">
              <div className="text-center mb-4">
                <Building className="w-12 h-12 mx-auto mb-2 text-blue-500" />
                <h3 className="text-lg font-semibold">Business Information</h3>
                <p className="text-sm text-muted-foreground">Optional - Add business details for better visibility</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="businessName">Business Name</Label>
                  <Input
                    id="businessName"
                    value={formData.businessName}
                    onChange={(e) => setFormData(prev => ({ ...prev, businessName: e.target.value }))}
                    placeholder="Your business name"
                  />
                </div>

                <div>
                  <Label htmlFor="businessType">Business Type</Label>
                  <Input
                    id="businessType"
                    value={formData.businessType}
                    onChange={(e) => setFormData(prev => ({ ...prev, businessType: e.target.value }))}
                    placeholder="e.g., Restaurant, Hotel, Museum"
                  />
                </div>

                <div>
                  <Label htmlFor="contactPhone">Phone</Label>
                  <Input
                    id="contactPhone"
                    value={formData.contactPhone}
                    onChange={(e) => setFormData(prev => ({ ...prev, contactPhone: e.target.value }))}
                    placeholder="+234 xxx xxx xxxx"
                  />
                </div>

                <div>
                  <Label htmlFor="contactEmail">Email</Label>
                  <Input
                    id="contactEmail"
                    type="email"
                    value={formData.contactEmail}
                    onChange={(e) => setFormData(prev => ({ ...prev, contactEmail: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    value={formData.website}
                    onChange={(e) => setFormData(prev => ({ ...prev, website: e.target.value }))}
                    placeholder="https://yourbusiness.com"
                  />
                </div>

                <div>
                  <Label htmlFor="businessHours">Business Hours</Label>
                  <Input
                    id="businessHours"
                    value={formData.businessHours}
                    onChange={(e) => setFormData(prev => ({ ...prev, businessHours: e.target.value }))}
                    placeholder="Mon-Fri 9AM-5PM"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Upload Images */}
          {currentStep === 3 && (
            <div className="space-y-4">
              <div className="text-center mb-4">
                <FileImage className="w-12 h-12 mx-auto mb-2 text-blue-500" />
                <h3 className="text-lg font-semibold">Upload 360° Images</h3>
                <p className="text-sm text-muted-foreground">Upload panoramic images to create your virtual tour</p>
              </div>

              <Card className="border-dashed border-2 border-gray-300 hover:border-blue-400 transition-colors">
                <CardContent className="p-8 text-center">
                  <input
                    type="file"
                    accept="image/*"
                    multiple
                    onChange={handleFileUpload}
                    className="hidden"
                    id="file-upload"
                  />
                  <label htmlFor="file-upload" className="cursor-pointer">
                    <Upload className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                    <p className="text-lg font-medium mb-2">Click to upload images</p>
                    <p className="text-sm text-muted-foreground">
                      Select multiple 360° panoramic images (JPG, PNG)
                    </p>
                  </label>
                </CardContent>
              </Card>

              {formData.files.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">{formData.files.length} file(s) selected:</p>
                  {formData.files.map((file, index) => (
                    <div key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Check className="w-4 h-4 text-green-500" />
                      {file.name}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-between pt-6 border-t">
          <Button
            variant="outline"
            onClick={currentStep === 1 ? handleClose : handleBack}
            disabled={isSubmitting}
          >
            {currentStep === 1 ? 'Cancel' : 'Back'}
          </Button>

          <Button
            onClick={currentStep === 3 ? handleSubmit : handleNext}
            disabled={!canProceed() || isSubmitting}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Creating...
              </>
            ) : currentStep === 3 ? (
              <>
                <Upload className="w-4 h-4 mr-2" />
                Create Tour
              </>
            ) : (
              <>
                Next
                <ArrowRight className="w-4 h-4 ml-2" />
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default StreamlinedTourCreationModal;
