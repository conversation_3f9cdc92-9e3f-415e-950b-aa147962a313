/**
 * Markers Hook
 * React hook for managing PSV markers and hotspots
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { toast } from 'sonner';
import type { MarkerService, ProductData } from '@/lib/photosphere/markerService';
import type { TourMarker, PSVInstance } from '@/lib/photosphere/types';

export interface UseMarkersOptions {
  onMarkerClick?: (marker: TourMarker) => void;
  onProductClick?: (product: ProductData) => void;
  onNavigationClick?: (targetSceneId: string) => void;
  onInfoClick?: (title: string, content: string) => void;
  onLinkClick?: (url: string) => void;
  onMediaClick?: (mediaUrl: string, mediaType: string) => void;
  whatsappNumber?: string;
}

export interface UseMarkersReturn {
  // Marker management
  markers: TourMarker[];
  addMarker: (marker: TourMarker) => void;
  removeMarker: (markerId: string) => void;
  updateMarker: (markerId: string, updates: Partial<TourMarker>) => void;
  clearMarkers: () => void;
  setMarkers: (markers: TourMarker[]) => void;
  
  // Marker queries
  getMarker: (markerId: string) => TourMarker | null;
  getMarkersByType: (type: string) => TourMarker[];
  getProductMarkers: () => TourMarker[];
  getNavigationMarkers: () => TourMarker[];
  
  // PSV integration
  attachToPSV: (psvInstance: PSVInstance) => void;
  detachFromPSV: () => void;
  
  // Marker creation helpers
  createProductMarker: (position: { yaw: number; pitch: number }, product: ProductData) => TourMarker;
  createNavigationMarker: (position: { yaw: number; pitch: number }, targetSceneId: string, title?: string) => TourMarker;
  createInfoMarker: (position: { yaw: number; pitch: number }, title: string, content: string) => TourMarker;
  createLinkMarker: (position: { yaw: number; pitch: number }, url: string, title?: string) => TourMarker;
  createMediaMarker: (position: { yaw: number; pitch: number }, mediaUrl: string, title?: string, mediaType?: string) => TourMarker;
  
  // State
  isAttached: boolean;
  markerCount: number;
}

export function useMarkers(options: UseMarkersOptions = {}): UseMarkersReturn {
  const [markers, setMarkersState] = useState<TourMarker[]>([]);
  const [isAttached, setIsAttached] = useState(false);
  
  const markerServiceRef = useRef<MarkerService | null>(null);
  const psvInstanceRef = useRef<PSVInstance | null>(null);

  /**
   * Add a marker
   */
  const addMarker = useCallback((marker: TourMarker) => {
    setMarkersState(prev => {
      // Check if marker already exists
      if (prev.find(m => m.id === marker.id)) {
        console.warn(`Marker with ID ${marker.id} already exists`);
        return prev;
      }
      
      const newMarkers = [...prev, marker];
      
      // Add to PSV if attached
      if (markerServiceRef.current) {
        markerServiceRef.current.addMarker(marker);
      }
      
      return newMarkers;
    });
  }, []);

  /**
   * Remove a marker
   */
  const removeMarker = useCallback((markerId: string) => {
    setMarkersState(prev => {
      const newMarkers = prev.filter(m => m.id !== markerId);
      
      // Remove from PSV if attached
      if (markerServiceRef.current) {
        markerServiceRef.current.removeMarker(markerId);
      }
      
      return newMarkers;
    });
  }, []);

  /**
   * Update a marker
   */
  const updateMarker = useCallback((markerId: string, updates: Partial<TourMarker>) => {
    setMarkersState(prev => {
      const newMarkers = prev.map(marker => 
        marker.id === markerId ? { ...marker, ...updates } : marker
      );
      
      // Update in PSV if attached
      if (markerServiceRef.current) {
        markerServiceRef.current.updateMarker(markerId, updates);
      }
      
      return newMarkers;
    });
  }, []);

  /**
   * Clear all markers
   */
  const clearMarkers = useCallback(() => {
    setMarkersState([]);
    
    // Clear from PSV if attached
    if (markerServiceRef.current) {
      markerServiceRef.current.clearMarkers();
    }
  }, []);

  /**
   * Set markers (replace all)
   */
  const setMarkers = useCallback((newMarkers: TourMarker[]) => {
    setMarkersState(newMarkers);
    
    // Update PSV if attached
    if (markerServiceRef.current) {
      markerServiceRef.current.addMarkers(newMarkers);
    }
  }, []);

  /**
   * Get marker by ID
   */
  const getMarker = useCallback((markerId: string): TourMarker | null => {
    return markers.find(m => m.id === markerId) || null;
  }, [markers]);

  /**
   * Get markers by type
   */
  const getMarkersByType = useCallback((type: string): TourMarker[] => {
    return markers.filter(m => m.type === type);
  }, [markers]);

  /**
   * Get product markers
   */
  const getProductMarkers = useCallback((): TourMarker[] => {
    return getMarkersByType('product');
  }, [getMarkersByType]);

  /**
   * Get navigation markers
   */
  const getNavigationMarkers = useCallback((): TourMarker[] => {
    return getMarkersByType('navigation');
  }, [getMarkersByType]);

  /**
   * Attach to PSV instance
   */
  const attachToPSV = useCallback((psvInstance: PSVInstance) => {
    if (!psvInstance.markerService) {
      console.warn('PSV instance does not have marker service');
      return;
    }

    psvInstanceRef.current = psvInstance;
    markerServiceRef.current = psvInstance.markerService;
    
    // Add existing markers to PSV
    if (markers.length > 0) {
      markerServiceRef.current.addMarkers(markers);
    }
    
    setIsAttached(true);
  }, [markers]);

  /**
   * Detach from PSV instance
   */
  const detachFromPSV = useCallback(() => {
    if (markerServiceRef.current) {
      markerServiceRef.current.clearMarkers();
    }
    
    markerServiceRef.current = null;
    psvInstanceRef.current = null;
    setIsAttached(false);
  }, []);

  /**
   * Create product marker
   */
  const createProductMarker = useCallback((
    position: { yaw: number; pitch: number },
    product: ProductData
  ): TourMarker => {
    return {
      id: `product-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'product',
      position: {
        yaw: `${position.yaw}deg`,
        pitch: `${position.pitch}deg`
      },
      title: product.name,
      content: product.description,
      productData: product,
      onClick: (marker, viewer) => {
        if (options.onProductClick) {
          options.onProductClick(product);
        }
      }
    };
  }, [options.onProductClick]);

  /**
   * Create navigation marker
   */
  const createNavigationMarker = useCallback((
    position: { yaw: number; pitch: number },
    targetSceneId: string,
    title?: string
  ): TourMarker => {
    return {
      id: `navigation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'navigation',
      position: {
        yaw: `${position.yaw}deg`,
        pitch: `${position.pitch}deg`
      },
      title: title || 'Go to scene',
      content: `Navigate to ${title || 'another scene'}`,
      targetSceneId,
      onClick: (marker, viewer) => {
        if (options.onNavigationClick) {
          options.onNavigationClick(targetSceneId);
        }
      }
    };
  }, [options.onNavigationClick]);

  /**
   * Create info marker
   */
  const createInfoMarker = useCallback((
    position: { yaw: number; pitch: number },
    title: string,
    content: string
  ): TourMarker => {
    return {
      id: `info-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'info',
      position: {
        yaw: `${position.yaw}deg`,
        pitch: `${position.pitch}deg`
      },
      title,
      content,
      onClick: (marker, viewer) => {
        if (options.onInfoClick) {
          options.onInfoClick(title, content);
        }
      }
    };
  }, [options.onInfoClick]);

  /**
   * Create link marker
   */
  const createLinkMarker = useCallback((
    position: { yaw: number; pitch: number },
    url: string,
    title?: string
  ): TourMarker => {
    return {
      id: `link-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'link',
      position: {
        yaw: `${position.yaw}deg`,
        pitch: `${position.pitch}deg`
      },
      title: title || 'External link',
      content: `Visit ${url}`,
      linkUrl: url,
      onClick: (marker, viewer) => {
        if (options.onLinkClick) {
          options.onLinkClick(url);
        }
      }
    };
  }, [options.onLinkClick]);

  /**
   * Create media marker
   */
  const createMediaMarker = useCallback((
    position: { yaw: number; pitch: number },
    mediaUrl: string,
    title?: string,
    mediaType?: string
  ): TourMarker => {
    return {
      id: `media-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: 'media',
      position: {
        yaw: `${position.yaw}deg`,
        pitch: `${position.pitch}deg`
      },
      title: title || 'Media',
      content: `View ${mediaType || 'media'}`,
      mediaUrl,
      mediaType,
      onClick: (marker, viewer) => {
        if (options.onMediaClick) {
          options.onMediaClick(mediaUrl, mediaType || 'unknown');
        }
      }
    };
  }, [options.onMediaClick]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      detachFromPSV();
    };
  }, [detachFromPSV]);

  return {
    // Marker management
    markers,
    addMarker,
    removeMarker,
    updateMarker,
    clearMarkers,
    setMarkers,
    
    // Marker queries
    getMarker,
    getMarkersByType,
    getProductMarkers,
    getNavigationMarkers,
    
    // PSV integration
    attachToPSV,
    detachFromPSV,
    
    // Marker creation helpers
    createProductMarker,
    createNavigationMarker,
    createInfoMarker,
    createLinkMarker,
    createMediaMarker,
    
    // State
    isAttached,
    markerCount: markers.length
  };
}
