
import { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, Link, Upload } from 'lucide-react';
import { FormData } from '@/hooks/useUploadForm';

interface UploadStepFourProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
  uploadMethod?: 'images' | 'embed' | 'video';
}

const UploadStepFour = ({ formData, setFormData, uploadMethod = 'images' }: UploadStepFourProps) => {
  const [localUploadMethod, setLocalUploadMethod] = useState<'files' | 'embed'>(
    uploadMethod === 'embed' ? 'embed' : 'files'
  );

  const handleEmbedChange = (field: keyof FormData, value: string) => {
    setFormData({
      ...formData,
      [field]: value
    } as FormData);
  };

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold">Tour Content</h3>
        <p className="text-sm text-muted-foreground mt-1">
          Choose how you want to add your virtual tour content
        </p>
      </div>

      {/* Upload Method Selection - Only show if not predetermined */}
      {uploadMethod === 'images' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card
            className={`cursor-pointer transition-colors ${
              localUploadMethod === 'files' ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
            }`}
            onClick={() => setLocalUploadMethod('files')}
          >
            <CardContent className="p-4 text-center">
              <Upload className="w-8 h-8 mx-auto mb-2 text-primary" />
              <h4 className="font-medium">Upload Files</h4>
              <p className="text-sm text-muted-foreground">Upload 360° images or videos</p>
            </CardContent>
          </Card>

          <Card
            className={`cursor-pointer transition-colors ${
              localUploadMethod === 'embed' ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
            }`}
            onClick={() => setLocalUploadMethod('embed')}
          >
            <CardContent className="p-4 text-center">
              <Link className="w-8 h-8 mx-auto mb-2 text-primary" />
              <h4 className="font-medium">Embed Tour</h4>
              <p className="text-sm text-muted-foreground">Add existing tour link or embed code</p>
            </CardContent>
          </Card>
        </div>
      )}

      {(localUploadMethod === 'files' || uploadMethod === 'images') && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Upload className="w-5 h-5 mr-2" />
              File Upload
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="file-upload">360° Images/Videos</Label>
              <input
                id="file-upload"
                type="file"
                multiple
                accept="image/*,video/*"
                onChange={(e) => {
                  const files = Array.from(e.target.files || []);
                  setFormData({ ...formData, files });
                }}
                className="block w-full text-sm text-muted-foreground file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary/10 file:text-primary hover:file:bg-primary/20"
                aria-describedby="file-upload-help"
                title="Upload 360° images or videos for your virtual tour"
                aria-label="Upload 360° images or videos for your virtual tour"
              />
              <p id="file-upload-help" className="text-xs text-muted-foreground">
                Upload 360° images or videos. Supported formats: JPG, PNG, WebP, MP4, WebM
              </p>
            </div>

            {formData.files.length > 0 && (
              <div className="space-y-2">
                <Label>Selected Files ({formData.files.length})</Label>
                <div className="space-y-2">
                  {formData.files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                      <div className="flex items-center">
                        <FileText className="w-4 h-4 mr-2 text-muted-foreground" />
                        <span className="text-sm">{file.name}</span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          const newFiles = formData.files.filter((_, i) => i !== index);
                          setFormData({ ...formData, files: newFiles });
                        }}
                      >
                        Remove
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {(localUploadMethod === 'embed' || uploadMethod === 'embed') && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Link className="w-5 h-5 mr-2" />
              Embed External Tour
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="embedType">Embed Type</Label>
              <Select
                value={formData.embedType}
                onValueChange={(value) => handleEmbedChange('embedType', value)}
              >
                <SelectTrigger className="h-10">
                  <SelectValue placeholder="Select embed type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="link">Direct Link</SelectItem>
                  <SelectItem value="iframe">Iframe Embed</SelectItem>
                  <SelectItem value="custom">Custom Code</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="embedUrl">
                {formData.embedType === 'link' ? 'Tour URL' :
                 formData.embedType === 'iframe' ? 'Iframe URL' : 'Embed Code'}
              </Label>
              {formData.embedType === 'custom' ? (
                <Textarea
                  id="embedUrl"
                  value={formData.embedUrl}
                  onChange={(e) => handleEmbedChange('embedUrl', e.target.value)}
                  placeholder="Paste your custom embed code here..."
                  rows={4}
                  className="min-h-[100px]"
                />
              ) : (
                <Input
                  id="embedUrl"
                  type="url"
                  value={formData.embedUrl}
                  onChange={(e) => handleEmbedChange('embedUrl', e.target.value)}
                  placeholder={
                    formData.embedType === 'link'
                      ? "https://example.com/tour/123"
                      : "https://example.com/embed/tour/123"
                  }
                  className="h-10"
                />
              )}
            </div>

            {formData.embedUrl && (
              <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-sm text-green-700">
                  ✓ {formData.embedType === 'link' ? 'Tour link' : 'Embed code'} added successfully
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default UploadStepFour;
