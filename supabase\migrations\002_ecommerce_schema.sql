-- E-commerce Schema Extension for VirtualRealTour
-- Vendor lock-free e-commerce functionality

-- Create vendor status enum
CREATE TYPE vendor_status AS ENUM ('pending', 'approved', 'suspended', 'rejected');

-- Create product status enum  
CREATE TYPE product_status AS ENUM ('draft', 'active', 'inactive', 'out_of_stock');

-- Create order status enum
CREATE TYPE order_status AS ENUM ('pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded');

-- Create payment method enum
CREATE TYPE payment_method AS ENUM ('whatsapp', 'bank_transfer', 'cash_on_delivery', 'mobile_money');

-- Vendors table
CREATE TABLE vendors (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    phone TEXT,
    whatsapp_number TEXT,
    business_address TEXT,
    business_description TEXT,
    logo_url TEXT,
    commission_rate DECIMAL(3,2) DEFAULT 0.10 CHECK (commission_rate >= 0 AND commission_rate <= 1),
    status vendor_status DEFAULT 'pending',
    bank_details JSONB,
    verification_documents JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Products table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID REFERENCES vendors(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    compare_at_price DECIMAL(10,2) CHECK (compare_at_price >= price),
    sku TEXT,
    inventory_quantity INTEGER DEFAULT 0 CHECK (inventory_quantity >= 0),
    images JSONB DEFAULT '[]'::jsonb,
    status product_status DEFAULT 'draft',
    category TEXT,
    tags TEXT[],
    weight DECIMAL(8,2),
    dimensions JSONB, -- {length, width, height}
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Tour-Product Integration (Hotspots)
CREATE TABLE tour_product_hotspots (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tour_id UUID REFERENCES tours(id) ON DELETE CASCADE NOT NULL,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE NOT NULL,
    scene_id TEXT NOT NULL,
    position JSONB NOT NULL, -- {x: 0.5, y: 0.3, z: 0.2}
    hotspot_style JSONB DEFAULT '{}'::jsonb,
    display_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Shopping Cart (Session-based)
CREATE TABLE cart_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id TEXT NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    tour_context JSONB, -- stores tour_id, scene_id for context
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Orders table
CREATE TABLE orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_number TEXT UNIQUE NOT NULL,
    customer_phone TEXT NOT NULL,
    customer_email TEXT,
    customer_name TEXT NOT NULL,
    customer_address TEXT,
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
    status order_status DEFAULT 'pending',
    payment_method payment_method DEFAULT 'whatsapp',
    payment_status TEXT DEFAULT 'pending',
    tour_context JSONB, -- stores tour information for context
    vendor_orders JSONB DEFAULT '{}'::jsonb, -- splits by vendor
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Order Items table
CREATE TABLE order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE NOT NULL,
    product_id UUID REFERENCES products(id) ON DELETE RESTRICT NOT NULL,
    vendor_id UUID REFERENCES vendors(id) ON DELETE RESTRICT NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    price DECIMAL(10,2) NOT NULL CHECK (price >= 0),
    vendor_commission DECIMAL(10,2) NOT NULL CHECK (vendor_commission >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Vendor Commission Tracking
CREATE TABLE vendor_commissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    vendor_id UUID REFERENCES vendors(id) ON DELETE CASCADE NOT NULL,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE NOT NULL,
    commission_amount DECIMAL(10,2) NOT NULL CHECK (commission_amount >= 0),
    commission_rate DECIMAL(3,2) NOT NULL,
    status TEXT DEFAULT 'pending', -- pending, paid, cancelled
    paid_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Create indexes for performance
CREATE INDEX idx_vendors_status ON vendors(status);
CREATE INDEX idx_vendors_email ON vendors(email);
CREATE INDEX idx_products_vendor_id ON products(vendor_id);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_products_category ON products(category);
CREATE INDEX idx_tour_product_hotspots_tour_id ON tour_product_hotspots(tour_id);
CREATE INDEX idx_tour_product_hotspots_product_id ON tour_product_hotspots(product_id);
CREATE INDEX idx_cart_items_session_id ON cart_items(session_id);
CREATE INDEX idx_cart_items_user_id ON cart_items(user_id);
CREATE INDEX idx_orders_customer_phone ON orders(customer_phone);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);
CREATE INDEX idx_order_items_vendor_id ON order_items(vendor_id);
CREATE INDEX idx_vendor_commissions_vendor_id ON vendor_commissions(vendor_id);

-- Create sequence for order numbers
CREATE SEQUENCE order_sequence START 1000;

-- Function to generate order numbers
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
BEGIN
    RETURN 'VRT-' || TO_CHAR(NOW(), 'YYYYMMDD') || '-' || 
           LPAD(NEXTVAL('order_sequence')::TEXT, 4, '0');
END;
$$ LANGUAGE plpgsql;

-- Function to calculate vendor commission
CREATE OR REPLACE FUNCTION calculate_vendor_commission(
    order_id UUID
) RETURNS JSON AS $$
DECLARE
    commission_data JSON;
BEGIN
    SELECT json_agg(
        json_build_object(
            'vendor_id', oi.vendor_id,
            'total_sales', SUM(oi.price * oi.quantity),
            'commission_amount', SUM(oi.vendor_commission),
            'items_count', COUNT(oi.id)
        )
    )
    INTO commission_data
    FROM order_items oi
    WHERE oi.order_id = calculate_vendor_commission.order_id
    GROUP BY oi.vendor_id;
    
    RETURN commission_data;
END;
$$ LANGUAGE plpgsql;

-- Function to update product inventory
CREATE OR REPLACE FUNCTION update_product_inventory()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Decrease inventory when order item is created
        UPDATE products 
        SET inventory_quantity = inventory_quantity - NEW.quantity,
            updated_at = NOW()
        WHERE id = NEW.product_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Increase inventory when order item is deleted (cancelled)
        UPDATE products 
        SET inventory_quantity = inventory_quantity + OLD.quantity,
            updated_at = NOW()
        WHERE id = OLD.product_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update inventory
CREATE TRIGGER trigger_update_inventory
    AFTER INSERT OR DELETE ON order_items
    FOR EACH ROW EXECUTE FUNCTION update_product_inventory();

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at timestamps
CREATE TRIGGER update_vendors_updated_at BEFORE UPDATE ON vendors
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cart_items_updated_at BEFORE UPDATE ON cart_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE vendors ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE tour_product_hotspots ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE vendor_commissions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for vendors
CREATE POLICY "Vendors can view their own data" ON vendors
    FOR SELECT USING (auth.uid() IN (
        SELECT id FROM profiles WHERE email = vendors.email
    ));

CREATE POLICY "Admins can view all vendors" ON vendors
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for products
CREATE POLICY "Anyone can view active products" ON products
    FOR SELECT USING (status = 'active');

CREATE POLICY "Vendors can manage their own products" ON products
    FOR ALL USING (
        vendor_id IN (
            SELECT v.id FROM vendors v, profiles p 
            WHERE v.email = p.email AND p.id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all products" ON products
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for cart items
CREATE POLICY "Users can manage their own cart" ON cart_items
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Session-based cart access" ON cart_items
    FOR ALL USING (user_id IS NULL); -- Allow anonymous cart access

-- RLS Policies for orders
CREATE POLICY "Users can view their own orders" ON orders
    FOR SELECT USING (
        customer_email IN (
            SELECT email FROM profiles WHERE id = auth.uid()
        )
    );

CREATE POLICY "Vendors can view orders containing their products" ON orders
    FOR SELECT USING (
        id IN (
            SELECT DISTINCT oi.order_id 
            FROM order_items oi, vendors v, profiles p
            WHERE oi.vendor_id = v.id 
            AND v.email = p.email 
            AND p.id = auth.uid()
        )
    );

CREATE POLICY "Admins can manage all orders" ON orders
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
