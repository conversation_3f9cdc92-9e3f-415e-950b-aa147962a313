/**
 * Mobile-First Responsive Design System
 * Critical mobile optimizations and responsive utilities
 */

/* ===== MOBILE-FIRST BASE STYLES ===== */

/* Prevent horizontal overflow on all elements */
* {
  box-sizing: border-box;
}

html {
  /* Prevent zoom on input focus on iOS */
  -webkit-text-size-adjust: 100%;
  /* Smooth scrolling */
  scroll-behavior: smooth;
}

body {
  /* Prevent horizontal overflow */
  overflow-x: hidden;
  width: 100%;
  max-width: 100vw;
  /* Optimize font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Improve touch scrolling on iOS */
  -webkit-overflow-scrolling: touch;
}

/* ===== TOUCH-FRIENDLY INTERACTIONS ===== */

/* Minimum touch target size (44px x 44px) */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Enhanced touch feedback */
.touch-feedback {
  transition: transform 0.15s ease-out;
}

.touch-feedback:active {
  transform: scale(0.98);
}

/* Remove tap highlight on mobile */
.no-tap-highlight {
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* ===== RESPONSIVE CONTAINERS ===== */

/* Prevent content overflow */
.container-safe {
  width: 100%;
  max-width: 100vw;
  overflow-x: hidden;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container-safe {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .container-safe {
    padding-left: 3rem;
    padding-right: 3rem;
  }
}

/* ===== RESPONSIVE TEXT AND IMAGES ===== */

/* Responsive images */
img, video, iframe {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Prevent text overflow */
.text-safe {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: 100%;
}

/* Responsive text sizing */
.text-responsive-sm {
  font-size: 0.875rem; /* 14px */
}

.text-responsive-base {
  font-size: 0.875rem; /* 14px mobile */
}

.text-responsive-lg {
  font-size: 1rem; /* 16px mobile */
}

.text-responsive-xl {
  font-size: 1.125rem; /* 18px mobile */
}

.text-responsive-2xl {
  font-size: 1.25rem; /* 20px mobile */
}

@media (min-width: 640px) {
  .text-responsive-base {
    font-size: 1rem; /* 16px tablet+ */
  }
  
  .text-responsive-lg {
    font-size: 1.125rem; /* 18px tablet+ */
  }
  
  .text-responsive-xl {
    font-size: 1.25rem; /* 20px tablet+ */
  }
  
  .text-responsive-2xl {
    font-size: 1.5rem; /* 24px tablet+ */
  }
}

/* ===== MOBILE FORM OPTIMIZATIONS ===== */

/* Larger form inputs on mobile */
input, textarea, select, button {
  font-size: 16px; /* Prevent zoom on iOS */
  min-height: 44px; /* Touch-friendly */
}

@media (min-width: 640px) {
  input, textarea, select, button {
    font-size: 14px;
    min-height: 40px;
  }
}

/* Form field spacing */
.form-field-mobile {
  margin-bottom: 1rem;
}

@media (min-width: 640px) {
  .form-field-mobile {
    margin-bottom: 1.5rem;
  }
}

/* ===== MOBILE NAVIGATION ===== */

/* Mobile-first navigation */
.nav-mobile {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
}

@media (min-width: 1024px) {
  .nav-mobile {
    display: none;
  }
}

/* ===== RESPONSIVE GRID SYSTEMS ===== */

/* Mobile-first grid */
.grid-mobile-1 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

.grid-mobile-2 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .grid-mobile-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

.grid-mobile-3 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .grid-mobile-3 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .grid-mobile-3 {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

/* ===== MOBILE CARD LAYOUTS ===== */

/* Mobile-optimized cards */
.card-mobile {
  background: white;
  border-radius: 0.75rem;
  padding: 1rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
  width: 100%;
  overflow: hidden;
}

@media (min-width: 640px) {
  .card-mobile {
    padding: 1.5rem;
    margin-bottom: 1.5rem;
  }
}

/* ===== MOBILE MODAL AND OVERLAY ===== */

/* Full-screen modals on mobile */
.modal-mobile {
  position: fixed;
  inset: 0;
  z-index: 50;
  background: white;
  overflow-y: auto;
}

@media (min-width: 640px) {
  .modal-mobile {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 500px;
    height: auto;
    max-height: 90vh;
    border-radius: 0.75rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }
}

/* ===== MOBILE TABLE RESPONSIVENESS ===== */

/* Stack table cells on mobile */
.table-mobile {
  width: 100%;
  border-collapse: collapse;
}

.table-mobile thead {
  display: none;
}

.table-mobile tbody tr {
  display: block;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  padding: 1rem;
}

.table-mobile tbody td {
  display: block;
  text-align: left;
  padding: 0.5rem 0;
  border: none;
}

.table-mobile tbody td:before {
  content: attr(data-label) ": ";
  font-weight: 600;
  color: #374151;
}

@media (min-width: 768px) {
  .table-mobile thead {
    display: table-header-group;
  }
  
  .table-mobile tbody tr {
    display: table-row;
    border: none;
    margin-bottom: 0;
    padding: 0;
  }
  
  .table-mobile tbody td {
    display: table-cell;
    text-align: left;
    padding: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .table-mobile tbody td:before {
    content: none;
  }
}

/* ===== MOBILE SPACING UTILITIES ===== */

/* Mobile-first spacing */
.space-mobile-sm > * + * {
  margin-top: 0.5rem;
}

.space-mobile-md > * + * {
  margin-top: 1rem;
}

.space-mobile-lg > * + * {
  margin-top: 1.5rem;
}

@media (min-width: 640px) {
  .space-mobile-sm > * + * {
    margin-top: 0.75rem;
  }
  
  .space-mobile-md > * + * {
    margin-top: 1.5rem;
  }
  
  .space-mobile-lg > * + * {
    margin-top: 2rem;
  }
}

/* ===== MOBILE PERFORMANCE OPTIMIZATIONS ===== */

/* Optimize animations for mobile */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* GPU acceleration for smooth animations */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* ===== MOBILE ACCESSIBILITY ===== */

/* Focus styles for keyboard navigation */
.focus-mobile:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Screen reader only content */
.sr-only-mobile {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== MOBILE PRINT STYLES ===== */

@media print {
  .no-print {
    display: none !important;
  }
  
  .print-full-width {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}
