
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Check, Camera, Globe, Headset, Settings, Smartphone, Users, Zap, ArrowRight } from 'lucide-react'
import { Link } from 'react-router-dom'

const EnhancedServicesSection = () => {
  const services = [
    {
      title: "360° Photography & Production",
      description: "Professional capture and production of high-quality immersive content",
      icon: Camera,
      features: [
        "High-resolution 360° photography",
        "4K 360° video recording", 
        "Professional lighting & HDR",
        "Same-day turnaround",
        "Drone aerial 360° capture"
      ],
      badge: "Most Popular",
      gradient: "from-blue-500 to-purple-600"
    },
    {
      title: "Interactive Virtual Tours",
      description: "Complete virtual tour development with advanced interactive elements",
      icon: Globe,
      features: [
        "Interactive hotspot creation",
        "Seamless navigation system",
        "Custom branding integration",
        "WhatsApp contact integration",
        "Advanced analytics dashboard",
        "Multi-language support"
      ],
      badge: "Enterprise",
      gradient: "from-emerald-500 to-teal-600"
    },
    {
      title: "VR & AR Experiences",
      description: "Next-generation VR and AR-ready immersive experiences",
      icon: Headset,
      features: [
        "VR headset compatibility",
        "AR overlay integration",
        "Immersive audio experiences",
        "Multi-platform deployment",
        "Custom interaction design"
      ],
      badge: "Premium",
      gradient: "from-purple-500 to-pink-600"
    }
  ]

  const industries = [
    {
      title: "Real Estate",
      description: "Property showcases and virtual walkthroughs",
      icon: Settings,
      color: "bg-blue-100 text-blue-700"
    },
    {
      title: "Education",
      description: "Campus tours and learning experiences",
      icon: Users,
      color: "bg-emerald-100 text-emerald-700"
    },
    {
      title: "Hospitality",
      description: "Hotels, restaurants, and venues",
      icon: Smartphone,
      color: "bg-purple-100 text-purple-700"
    },
    {
      title: "Commercial",
      description: "Business spaces and retail",
      icon: Zap,
      color: "bg-orange-100 text-orange-700"
    }
  ]

  return (
    <section className="py-24 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-20">
          <Badge variant="secondary" className="mb-4 px-4 py-2 text-sm">
            Professional Services
          </Badge>
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            World-Class Virtual Tour Solutions
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            From capture to deployment, we provide end-to-end virtual tour services 
            that meet international standards and exceed client expectations.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
          {services.map((service, index) => (
            <Card key={index} className="group hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 border-0 bg-white relative overflow-hidden">
              <div className={`absolute inset-0 bg-gradient-to-br ${service.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>
              
              {service.badge && (
                <div className="absolute top-4 right-4">
                  <Badge className="bg-gradient-to-r from-emerald-500 to-emerald-600 text-white border-0">
                    {service.badge}
                  </Badge>
                </div>
              )}

              <CardHeader className="pb-4">
                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-br ${service.gradient} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <service.icon className="w-8 h-8 text-white" />
                </div>
                <CardTitle className="text-xl font-bold text-gray-900">{service.title}</CardTitle>
                <CardDescription className="text-gray-600 text-base">{service.description}</CardDescription>
              </CardHeader>

              <CardContent className="space-y-6">
                <ul className="space-y-3">
                  {service.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start">
                      <Check className="w-5 h-5 text-emerald-500 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
                
                <Button className="w-full bg-gradient-to-r from-gray-800 to-gray-900 hover:from-gray-900 hover:to-black" asChild>
                  <Link to="/contact">
                    Get Quote
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Industries Section */}
        <div className="bg-white rounded-3xl p-8 md:p-12 shadow-xl">
          <div className="text-center mb-12">
            <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Industries We Serve
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Trusted by leading businesses across multiple sectors
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            {industries.map((industry, index) => (
              <div key={index} className="text-center group hover:scale-105 transition-transform duration-300">
                <div className={`w-16 h-16 rounded-2xl ${industry.color} flex items-center justify-center mx-auto mb-4`}>
                  <industry.icon className="w-8 h-8" />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">{industry.title}</h4>
                <p className="text-sm text-gray-600">{industry.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default EnhancedServicesSection
