/**
 * Tour Platform Configuration
 * Centralized configuration for tour creation platforms
 */

export type TourPlatform = 'commonninja' | 'custom' | 'auto';

export interface PlatformConfig {
  platform: TourPlatform;
  name: string;
  description: string;
  isDefault: boolean;
  adminOnly: boolean;
  features: {
    ecommerce: boolean;
    hotspots: boolean;
    analytics: boolean;
    customBranding: boolean;
    whatsappIntegration: boolean;
    vrSupport: boolean;
  };
}

/**
 * Platform configurations
 */
export const PLATFORM_CONFIGS: Record<TourPlatform, PlatformConfig> = {
  commonninja: {
    platform: 'commonninja',
    name: 'Default Tour Creator',
    description: 'Professional virtual tour creation with e-commerce integration',
    isDefault: true,
    adminOnly: false,
    features: {
      ecommerce: true,
      hotspots: true,
      analytics: true,
      customBranding: true,
      whatsappIntegration: true,
      vrSupport: true,
    }
  },
  custom: {
    platform: 'custom',
    name: 'Advanced Tour Creator',
    description: 'Custom Photo Sphere Viewer with advanced features (Admin Only)',
    isDefault: false,
    adminOnly: true, // Only admins can access this
    features: {
      ecommerce: true,
      hotspots: true,
      analytics: true,
      customBranding: true,
      whatsappIntegration: true,
      vrSupport: true,
    }
  },
  auto: {
    platform: 'auto',
    name: 'Auto Select',
    description: 'Automatically select the best platform',
    isDefault: false,
    adminOnly: true,
    features: {
      ecommerce: true,
      hotspots: true,
      analytics: true,
      customBranding: true,
      whatsappIntegration: true,
      vrSupport: true,
    }
  }
};

/**
 * Get the default platform for tour creation
 */
export function getDefaultPlatform(): TourPlatform {
  return 'commonninja';
}

/**
 * Get available platforms for a user role
 */
export function getAvailablePlatforms(isAdmin: boolean = false): PlatformConfig[] {
  return Object.values(PLATFORM_CONFIGS).filter(config => 
    !config.adminOnly || isAdmin
  );
}

/**
 * Get platform configuration
 */
export function getPlatformConfig(platform: TourPlatform): PlatformConfig {
  return PLATFORM_CONFIGS[platform];
}

/**
 * Check if a platform is available for a user
 */
export function isPlatformAvailable(platform: TourPlatform, isAdmin: boolean = false): boolean {
  const config = getPlatformConfig(platform);
  return !config.adminOnly || isAdmin;
}

/**
 * Get the recommended platform for users
 * Users always get CommonNinja, admins can choose
 */
export function getRecommendedPlatform(isAdmin: boolean = false): TourPlatform {
  if (isAdmin) {
    // Admins can choose, but CommonNinja is still recommended
    return 'commonninja';
  }
  
  // Users always get CommonNinja
  return 'commonninja';
}

/**
 * Tour creation method configuration
 */
export interface CreationMethod {
  id: 'images' | 'embed' | 'video';
  title: string;
  description: string;
  icon: string;
  recommended?: boolean;
  comingSoon?: boolean;
  adminOnly?: boolean;
}

export const CREATION_METHODS: CreationMethod[] = [
  {
    id: 'images',
    title: '360° Images',
    description: 'Upload panoramic images to create your virtual tour with e-commerce',
    icon: 'FileImage',
    recommended: true,
    adminOnly: false
  },
  {
    id: 'embed',
    title: 'Embed External Tour',
    description: 'Embed tours from Matterport, Kuula, or other platforms',
    icon: 'Link2',
    adminOnly: true // Only admins can embed external tours
  },
  {
    id: 'video',
    title: '360° Video',
    description: 'Upload 360-degree video content',
    icon: 'Video',
    comingSoon: true,
    adminOnly: true
  }
];

/**
 * Get available creation methods for a user role
 */
export function getAvailableCreationMethods(isAdmin: boolean = false): CreationMethod[] {
  return CREATION_METHODS.filter(method => 
    !method.adminOnly || isAdmin
  );
}

/**
 * Default tour settings
 */
export const DEFAULT_TOUR_SETTINGS = {
  platform: getDefaultPlatform(),
  creationMethod: 'images' as const,
  status: 'draft' as const,
  featured: false,
  views: 0,
  autoRotate: true,
  showControls: true,
  enableVR: true,
  customBranding: true,
  ecommerceEnabled: true,
  whatsappIntegration: true
};

/**
 * User role permissions
 */
export const USER_PERMISSIONS = {
  user: {
    canCreateTours: true,
    canPublishDirectly: false, // Users need admin approval
    canAccessCustomEditor: false,
    canEmbedExternalTours: false,
    canManageVendors: false,
    availablePlatforms: ['commonninja'] as TourPlatform[]
  },
  vendor: {
    canCreateTours: true,
    canPublishDirectly: false, // Vendors need admin approval
    canAccessCustomEditor: false,
    canEmbedExternalTours: false,
    canManageVendors: false,
    availablePlatforms: ['commonninja'] as TourPlatform[]
  },
  admin: {
    canCreateTours: true,
    canPublishDirectly: true,
    canAccessCustomEditor: true,
    canEmbedExternalTours: true,
    canManageVendors: true,
    availablePlatforms: ['commonninja', 'custom', 'auto'] as TourPlatform[]
  }
};

/**
 * Get user permissions based on role
 */
export function getUserPermissions(userRole: 'user' | 'vendor' | 'admin' = 'user') {
  return USER_PERMISSIONS[userRole];
}
