# WordPress + WooCommerce Setup for VirtualRealTour

## 🎯 Quick Setup Guide

### Step 1: Install WordPress Plugin

1. Copy the file `wordpress-plugin/virtualtour-react-bridge.php` to your WordPress:
   ```
   wp-content/plugins/virtualtour-react-bridge/virtualtour-react-bridge.php
   ```

2. Activate the plugin in WordPress Admin → Plugins

3. Verify installation by visiting:
   ```
   http://localhost:10090/wp-json/virtualtour/v1/ping
   ```
   
   Expected response:
   ```json
   {
     "status": "connected",
     "timestamp": "2025-06-30 10:00:00",
     "wordpress_version": "6.x",
     "woocommerce_active": true,
     "wpvr_active": false
   }
   ```

### Step 2: Configure CORS (Already Done by Plugin)

The plugin automatically enables CORS for these domains:
- `http://localhost:3000` (React dev server)
- `http://localhost:3001` 
- `http://localhost:5173` (Vite dev server)
- `https://virtualrealtour.ng` (Production)
- `https://staging.virtualrealtour.ng` (Staging)
- `https://tour-nigeria-vista.vercel.app` (Vercel)

### Step 3: Test Connection

1. Start your WordPress server: `http://localhost:10090`
2. Start React app: `bun dev`
3. Visit: `http://localhost:3000/woocommerce-test`
4. Click "Test Connection"

## 🔧 Environment Configuration

Your `.env.local` is already configured with:

```env
# WordPress + WooCommerce Configuration
VITE_WORDPRESS_URL=http://localhost:10090
VITE_WOO_CONSUMER_KEY=ck_405631cb8df6b2245d0695f814f05e47ca6befbd
VITE_WOO_CONSUMER_SECRET=cs_51c2556e9ad57d2ef40c82f6e3bc266e99103f98

# WordPress Application Password Authentication
VITE_WP_APP_PASSWORD=JiQN tXNq ieO1 hRDv 6gjd dtQj
VITE_WP_USERNAME=iwalk-wp-application-pass

# Authentication Method
VITE_WOO_AUTH_METHOD=app-password
```

## 🧪 Testing Checklist

| Test | URL | Expected Result |
|------|-----|----------------|
| WordPress Ping | `/wp-json/virtualtour/v1/ping` | ✅ `{"status": "connected"}` |
| WooCommerce Test | `/wp-json/virtualtour/v1/woo-test` | ✅ Products list |
| WPVR Test | `/wp-json/virtualtour/v1/wpvr-test` | ✅ Tours list (if WPVR active) |
| React Connection | `http://localhost:3000/woocommerce-test` | ✅ Green status |
| Product Fetch | Test with product ID | ✅ Product overlay |

## 🚨 Troubleshooting

### CORS Errors
- Ensure plugin is activated
- Check WordPress admin → Settings → VRT Bridge
- Verify your React app URL is in allowed origins

### Authentication Errors
- Verify Application Password is correct
- Check WooCommerce API keys are valid
- Try switching auth method in `.env.local`

### Connection Refused
- Ensure WordPress is running on `http://localhost:10090`
- Check Local by Flywheel is started
- Verify no firewall blocking

## 🎨 Virtual Tour Integration

Once connected, you can:

1. **Add Product Hotspots**: Use product IDs in tour hotspots
2. **Test Overlay**: Click hotspots to see product overlays
3. **WhatsApp Checkout**: Test checkout flow with vendor WhatsApp

## 📁 File Structure

```
wordpress-plugin/
└── virtualtour-react-bridge.php    # WordPress plugin

src/
├── services/commerce/
│   └── WooCommerceService.ts        # API service
├── components/tour-overlay/
│   └── VirtualTourOverlay.tsx       # Hotspot overlay system
├── components/commerce/
│   └── ProductOverlay.tsx           # Product lightbox
└── pages/
    └── WooCommerceTest.tsx          # Test page
```

## 🚀 Next Steps

1. **Install Plugin** → Activate in WordPress
2. **Test Connection** → Visit test page
3. **Add Products** → Create test products in WooCommerce
4. **Map Hotspots** → Connect product IDs to tour hotspots
5. **Test Overlay** → Click hotspots to see products

Your Virtual Tour Overlay Commerce System is ready! 🎉
