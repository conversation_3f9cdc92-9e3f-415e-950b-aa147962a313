/**
 * Admin Tour Editor Page
 * Full-screen tour creation and editing interface with CommonNinja integration
 */

import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Save, Eye, Share2, Settings } from 'lucide-react';
import { supabase, Tour } from '@/lib/supabase';
import { toast } from 'sonner';
import CommonNinjaTourEditor from '@/components/tour-editor/CommonNinjaTourEditor';
import CommonNinjaWidgetIntegration from '@/components/tour-editor/CommonNinjaWidgetIntegration';
import CommonNinjaWidgetEditor from '@/components/tour-editor/CommonNinjaWidgetEditor';
import CloudPanoTourEditor from '@/components/tour-editor/CloudPanoTourEditor';
import BasicTourEditor from '@/components/tour-editor/BasicTourEditor';
import VirtualRealTourEditor from '@/components/tour-editor/VirtualRealTourEditor';
import IntegratedTourEditor from '@/components/tour-editor/IntegratedTourEditor';
import SyncStatusIndicator from '@/components/ui/SyncStatusIndicator';
import { TourCreationServiceFactory } from '@/services/tour-creation/factory';

const AdminTourEditor = () => {
  const { tourId } = useParams<{ tourId: string }>();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const [commonNinjaWidgetId, setCommonNinjaWidgetId] = useState<string>('');
  const [editorType, setEditorType] = useState<'widget' | 'cloudpano' | 'commonninja' | 'integrated' | 'custom'>('widget');

  // Fetch tour data
  const { data: tour, isLoading, error: tourError } = useQuery({
    queryKey: ['tour', tourId],
    queryFn: async () => {
      if (!tourId) throw new Error('Tour ID is required');

      console.log('Fetching tour data for ID:', tourId);

      const { data, error } = await supabase
        .from('tours')
        .select('*')
        .eq('id', tourId)
        .single();

      if (error) {
        console.error('Error fetching tour:', error);
        throw error;
      }

      console.log('Tour data fetched:', data);

      // Determine the appropriate editor based on tour data
      if (data.commonninja_embed_code || data.commonninja_widget_id) {
        setEditorType('commonninja');
        setCommonNinjaWidgetId(data.commonninja_widget_id || '');
      } else if (data.custom_scenes && data.custom_scenes.length > 0) {
        setEditorType('integrated');
      } else {
        // Default to custom editor for new tours with 360° images
        setEditorType('custom');
      }

      return data as Tour;
    },
    enabled: !!tourId,
  });

  // Initialize tour editor based on upload method
  useEffect(() => {
    const initializeTourEditor = async () => {
      if (!tour || !tourId) return;

      try {
        // For new tours without platform set, default to CommonNinja
        if (!tour.tour_platform) {
          // Default to CommonNinja for all new tours
          const { error } = await supabase
            .from('tours')
            .update({
              tour_platform: 'commonninja',
              updated_at: new Date().toISOString()
            })
            .eq('id', tourId);

          if (error) throw error;
          setEditorType('commonninja');
          return;
        }

        // Initialize CommonNinja if needed
        if (tour.tour_platform === 'commonninja' && !tour.commonninja_widget_id) {
          const service = TourCreationServiceFactory.getService('commonninja');

          const createdTour = await service.createTour({
            title: tour.title,
            description: tour.description || '',
            category: tour.category,
            location: tour.location || '',
            businessInfo: {
              name: tour.business_name || '',
              type: tour.business_type || '',
              phone: tour.contact_phone || '',
              email: tour.contact_email || '',
              website: tour.website || '',
              hours: tour.business_hours || ''
            }
          });

          // Update tour with CommonNinja widget ID
          const { error } = await supabase
            .from('tours')
            .update({
              commonninja_widget_id: createdTour.id,
              embed_url: createdTour.embedUrl
            })
            .eq('id', tourId);

          if (error) throw error;

          setCommonNinjaWidgetId(createdTour.id);
          setEditorType('commonninja');
          toast.success('Tour editor initialized successfully!');
        }

      } catch (error) {
        console.error('Failed to initialize tour editor:', error);
        toast.error('Failed to initialize tour editor');
      }
    };

    initializeTourEditor();
  }, [tour, tourId]);

  // Save tour mutation
  const saveTourMutation = useMutation({
    mutationFn: async (tourData: any) => {
      if (!tourId) throw new Error('Tour ID is required');

      const { error } = await supabase
        .from('tours')
        .update({
          embed_url: tourData.embedUrl,
          status: 'draft',
          updated_at: new Date().toISOString()
        })
        .eq('id', tourId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tour', tourId] });
      toast.success('Tour saved successfully!');
    },
    onError: (error) => {
      toast.error(`Failed to save tour: ${error.message}`);
    },
  });

  // Publish tour mutation
  const publishTourMutation = useMutation({
    mutationFn: async (tourData: any) => {
      if (!tourId) throw new Error('Tour ID is required');

      const { error } = await supabase
        .from('tours')
        .update({
          embed_url: tourData.embedUrl,
          status: 'published',
          updated_at: new Date().toISOString()
        })
        .eq('id', tourId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tour', tourId] });
      toast.success('Tour published successfully!');
    },
    onError: (error) => {
      toast.error(`Failed to publish tour: ${error.message}`);
    },
  });

  const handleSave = (tourData: any) => {
    saveTourMutation.mutate(tourData);
  };

  const handlePublish = (tourData: any) => {
    publishTourMutation.mutate(tourData);
  };

  const handleClose = () => {
    navigate('/admin/tours');
  };

  const handlePreview = () => {
    if (tour?.slug) {
      window.open(`/tour/${tour.slug}`, '_blank');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent mx-auto mb-4" />
          <p className="text-muted-foreground">Loading tour editor...</p>
        </div>
      </div>
    );
  }

  if (tourError) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Error Loading Tour</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Failed to load tour: {tourError.message}
            </p>
            <Button onClick={() => navigate('/admin/tours')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Tours
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!tour) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Tour Not Found</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              The requested tour could not be found. Tour ID: {tourId}
            </p>
            <Button onClick={() => navigate('/admin/tours')}>
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Tours
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Compact Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50">
        <div className="container mx-auto px-4 py-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button variant="ghost" size="sm" onClick={handleClose}>
                <ArrowLeft className="w-4 h-4 mr-1" />
                Back
              </Button>
              <div className="h-4 w-px bg-border" />
              <div className="flex items-center gap-3">
                <h1 className="text-lg font-semibold">{tour.title}</h1>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Badge variant={tour.status === 'published' ? 'default' : 'secondary'} className="text-xs">
                    {tour.status}
                  </Badge>
                  <span>{tour.category}</span>
                  <span>•</span>
                  <span>{tour.location}</span>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <SyncStatusIndicator tourId={tour.id} />
              <Button variant="outline" size="sm" onClick={handlePreview}>
                <Eye className="w-4 h-4 mr-1" />
                Preview
              </Button>
              <Button variant="outline" size="sm" onClick={handleSave}>
                <Save className="w-4 h-4 mr-1" />
                Save
              </Button>
              <Button size="sm" onClick={handlePublish}>
                <Share2 className="w-4 h-4 mr-1" />
                Publish
              </Button>
              {/* Admin-only editor switching - Users don't see this */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  const nextEditor = editorType === 'widget' ? 'cloudpano' :
                                   editorType === 'cloudpano' ? 'commonninja' :
                                   editorType === 'commonninja' ? 'integrated' :
                                   editorType === 'integrated' ? 'custom' : 'widget';
                  setEditorType(nextEditor);
                  toast.info(`Switched to ${nextEditor} editor`);
                }}
                className="opacity-50 hover:opacity-100"
                title="Admin: Switch Editor Type (CommonNinja Widget → CloudPano → CommonNinja API → Integrated → Custom)"
              >
                <Settings className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Full-Screen Editor Content */}
      <div className="h-[calc(100vh-80px)]">
        {tour.creation_method === 'images' ? (
          editorType === 'widget' ? (
            <CommonNinjaWidgetEditor
              tourId={tour.id}
              tourData={{
                title: tour.title,
                description: tour.description || '',
                category: tour.category,
                location: tour.location || '',
                business_name: tour.business_name || '',
                business_phone: tour.contact_phone || '',
                business_email: tour.contact_email || '',
                business_whatsapp: tour.contact_phone || '',
                business_address: tour.location || '',
                business_website: tour.website || ''
              }}
              onSave={handleSave}
              onPublish={handlePublish}
              onClose={handleClose}
            />
          ) : editorType === 'cloudpano' ? (
            <CloudPanoTourEditor
              tourId={tour.id}
              tourData={{
                title: tour.title,
                description: tour.description || '',
                category: tour.category,
                location: tour.location || '',
                business_name: tour.business_name || '',
                business_phone: tour.contact_phone || '',
                business_email: tour.contact_email || '',
                business_whatsapp: tour.contact_phone || '',
                business_address: tour.location || '',
                business_website: tour.website || ''
              }}
              onSave={handleSave}
              onPublish={handlePublish}
              onClose={handleClose}
              isAdmin={true}
            />
          ) : editorType === 'commonninja' ? (
            <CommonNinjaWidgetIntegration
              tourId={tour.id}
              tourData={{
                title: tour.title,
                description: tour.description || '',
                category: tour.category,
                location: tour.location || '',
                business_name: tour.business_name || '',
                business_phone: tour.contact_phone || '',
                business_email: tour.contact_email || '',
                business_whatsapp: tour.contact_phone || '',
                business_address: tour.location || '',
                business_website: tour.website || ''
              }}
              onSave={handleSave}
              onPublish={handlePublish}
              onClose={handleClose}
            />
          ) : editorType === 'integrated' ? (
            <IntegratedTourEditor
              tourId={tour.id}
              tourData={{
                title: tour.title,
                description: tour.description || '',
                category: tour.category,
                location: tour.location || ''
              }}
              onSave={handleSave}
              onPublish={handlePublish}
              onClose={handleClose}
            />
          ) : (
            <VirtualRealTourEditor
              tourId={tour.id}
              tourData={{
                title: tour.title,
                description: tour.description || '',
                category: tour.category,
                location: tour.location || ''
              }}
              onSave={handleSave}
              onPublish={handlePublish}
              onClose={handleClose}
            />
          )
        ) : (
          <Card className="h-full">
            <CardContent className="flex items-center justify-center h-full">
              <div className="text-center">
                <p className="text-muted-foreground mb-4">
                  This tour type doesn't support the visual editor.
                </p>
                <Button onClick={handleClose}>
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Tours
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default AdminTourEditor;
