/**
 * Admin Mobile Optimization Demo
 * Showcase of mobile-first responsive improvements
 */

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  CheckCircle, 
  AlertTriangle,
  Eye,
  Settings,
  Target,
  Menu,
  Touch,
  Zap,
  Shield
} from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';
import TourWrapper from '@/components/tours/TourWrapper';
import { useResponsiveDesign } from '@/hooks/useResponsiveDesign';
import { 
  ResponsiveContainer, 
  ResponsiveGrid, 
  ResponsiveStack, 
  ResponsiveText,
  ResponsiveButtonGroup 
} from '@/components/ui/ResponsiveContainer';

const AdminMobileDemo = () => {
  const [selectedDemo, setSelectedDemo] = useState('overview');
  const { 
    isMobile, 
    isTablet, 
    isDesktop, 
    width, 
    height, 
    orientation, 
    isTouch,
    getTouchTargetSize 
  } = useResponsiveDesign();

  const mobileImprovements = [
    {
      category: 'Navigation',
      improvements: [
        'Touch-optimized admin navigation',
        'Mobile-friendly sidebar collapse',
        'Gesture-based interactions',
        'Thumb-zone optimization'
      ],
      status: 'complete'
    },
    {
      category: 'Tour Viewer',
      improvements: [
        'Responsive tour embedding',
        'Mobile control panels',
        'Touch-friendly hotspots',
        'Adaptive overlay sizing'
      ],
      status: 'complete'
    },
    {
      category: 'Admin Interface',
      improvements: [
        'Mobile-responsive forms',
        'Touch-target optimization',
        'Overflow prevention',
        'Adaptive grid layouts'
      ],
      status: 'complete'
    },
    {
      category: 'Performance',
      improvements: [
        'Lazy loading optimization',
        'Touch gesture support',
        'Reduced motion support',
        'Battery-conscious animations'
      ],
      status: 'complete'
    }
  ];

  const deviceInfo = {
    type: isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop',
    width: width,
    height: height,
    orientation: orientation,
    isTouch: isTouch,
    pixelRatio: window.devicePixelRatio || 1
  };

  return (
    <AdminLayout>
      <ResponsiveContainer>
        <div className="space-y-6">
          {/* Header */}
          <ResponsiveStack direction="responsive" justify="between" align="center">
            <div>
              <ResponsiveText 
                size={{ mobile: 'text-xl', desktop: 'text-3xl' }}
                weight="bold"
              >
                Mobile Optimization Demo
              </ResponsiveText>
              <ResponsiveText 
                size={{ mobile: 'text-sm', desktop: 'text-base' }}
                color="muted"
              >
                Mobile-first responsive improvements for VirtualRealTour
              </ResponsiveText>
            </div>
            
            <ResponsiveButtonGroup orientation="responsive">
              <Button variant="outline" size={isMobile ? "sm" : "default"}>
                <Eye className="w-4 h-4 mr-2" />
                {isMobile ? 'View' : 'View Original'}
              </Button>
              <Button size={isMobile ? "sm" : "default"}>
                <Settings className="w-4 h-4 mr-2" />
                {isMobile ? 'Config' : 'Configure'}
              </Button>
            </ResponsiveButtonGroup>
          </ResponsiveStack>

          {/* Device Info Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {isMobile ? <Smartphone className="w-5 h-5" /> : 
                 isTablet ? <Tablet className="w-5 h-5" /> : 
                 <Monitor className="w-5 h-5" />}
                Current Device Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveGrid cols={{ mobile: 2, tablet: 3, desktop: 6 }} gap="md">
                <div className="text-center">
                  <ResponsiveText size={{ mobile: 'text-lg', desktop: 'text-xl' }} weight="bold">
                    {deviceInfo.type}
                  </ResponsiveText>
                  <ResponsiveText size={{ mobile: 'text-xs', desktop: 'text-sm' }} color="muted">
                    Device Type
                  </ResponsiveText>
                </div>
                
                <div className="text-center">
                  <ResponsiveText size={{ mobile: 'text-lg', desktop: 'text-xl' }} weight="bold">
                    {deviceInfo.width}px
                  </ResponsiveText>
                  <ResponsiveText size={{ mobile: 'text-xs', desktop: 'text-sm' }} color="muted">
                    Width
                  </ResponsiveText>
                </div>
                
                <div className="text-center">
                  <ResponsiveText size={{ mobile: 'text-lg', desktop: 'text-xl' }} weight="bold">
                    {deviceInfo.height}px
                  </ResponsiveText>
                  <ResponsiveText size={{ mobile: 'text-xs', desktop: 'text-sm' }} color="muted">
                    Height
                  </ResponsiveText>
                </div>
                
                <div className="text-center">
                  <ResponsiveText size={{ mobile: 'text-lg', desktop: 'text-xl' }} weight="bold">
                    {deviceInfo.orientation}
                  </ResponsiveText>
                  <ResponsiveText size={{ mobile: 'text-xs', desktop: 'text-sm' }} color="muted">
                    Orientation
                  </ResponsiveText>
                </div>
                
                <div className="text-center">
                  <Badge variant={deviceInfo.isTouch ? "default" : "secondary"}>
                    {deviceInfo.isTouch ? <Touch className="w-3 h-3 mr-1" /> : null}
                    {deviceInfo.isTouch ? 'Touch' : 'Mouse'}
                  </Badge>
                  <ResponsiveText size={{ mobile: 'text-xs', desktop: 'text-sm' }} color="muted">
                    Input Method
                  </ResponsiveText>
                </div>
                
                <div className="text-center">
                  <ResponsiveText size={{ mobile: 'text-lg', desktop: 'text-xl' }} weight="bold">
                    {deviceInfo.pixelRatio}x
                  </ResponsiveText>
                  <ResponsiveText size={{ mobile: 'text-xs', desktop: 'text-sm' }} color="muted">
                    Pixel Ratio
                  </ResponsiveText>
                </div>
              </ResponsiveGrid>
            </CardContent>
          </Card>

          {/* Mobile Improvements Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Mobile Optimization Status</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveGrid cols={{ mobile: 1, tablet: 2, desktop: 4 }} gap="md">
                {mobileImprovements.map((category) => (
                  <Card key={category.category} className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <ResponsiveText weight="semibold">
                        {category.category}
                      </ResponsiveText>
                      <Badge variant={category.status === 'complete' ? 'default' : 'secondary'}>
                        {category.status === 'complete' ? (
                          <CheckCircle className="w-3 h-3 mr-1" />
                        ) : (
                          <AlertTriangle className="w-3 h-3 mr-1" />
                        )}
                        {category.status}
                      </Badge>
                    </div>
                    
                    <div className="space-y-2">
                      {category.improvements.map((improvement, idx) => (
                        <div key={idx} className="flex items-center gap-2">
                          <CheckCircle className="w-3 h-3 text-green-500 flex-shrink-0" />
                          <ResponsiveText size={{ mobile: 'text-xs', desktop: 'text-sm' }}>
                            {improvement}
                          </ResponsiveText>
                        </div>
                      ))}
                    </div>
                  </Card>
                ))}
              </ResponsiveGrid>
            </CardContent>
          </Card>

          {/* Demo Tabs */}
          <Tabs value={selectedDemo} onValueChange={setSelectedDemo} className="space-y-6">
            <TabsList className={`
              grid w-full 
              ${isMobile ? 'grid-cols-2' : 'grid-cols-4'}
            `}>
              <TabsTrigger value="overview" className={getTouchTargetSize()}>
                <Target className="w-4 h-4 mr-2" />
                {isMobile ? 'Overview' : 'Overview'}
              </TabsTrigger>
              <TabsTrigger value="tour-wrapper" className={getTouchTargetSize()}>
                <Eye className="w-4 h-4 mr-2" />
                {isMobile ? 'Tours' : 'Tour Viewer'}
              </TabsTrigger>
              <TabsTrigger value="navigation" className={getTouchTargetSize()}>
                <Menu className="w-4 h-4 mr-2" />
                {isMobile ? 'Nav' : 'Navigation'}
              </TabsTrigger>
              <TabsTrigger value="performance" className={getTouchTargetSize()}>
                <Zap className="w-4 h-4 mr-2" />
                {isMobile ? 'Perf' : 'Performance'}
              </TabsTrigger>
            </TabsList>

            {/* Tour Wrapper Demo */}
            <TabsContent value="tour-wrapper" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Mobile-Responsive Tour Viewer</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <ResponsiveText color="muted">
                      This demo shows the enhanced tour viewer with mobile-optimized controls,
                      touch-friendly hotspots, and responsive overlay system.
                    </ResponsiveText>
                    
                    <div className="border rounded-lg overflow-hidden">
                      <TourWrapper
                        tourId="mobile-demo"
                        src="https://example.com/demo-tour"
                        title="Mobile-Optimized Tour"
                        hotspotTheme="glass"
                        productSync={true}
                        showControls={true}
                        allowFullscreen={true}
                        className={isMobile ? "h-64" : "h-96"}
                      />
                    </div>
                    
                    <ResponsiveGrid cols={{ mobile: 1, tablet: 2, desktop: 3 }} gap="md">
                      <Card className="p-4">
                        <ResponsiveText weight="semibold">Mobile Controls</ResponsiveText>
                        <ResponsiveText size={{ mobile: 'text-xs', desktop: 'text-sm' }} color="muted">
                          Touch-optimized control panel with larger targets
                        </ResponsiveText>
                      </Card>
                      <Card className="p-4">
                        <ResponsiveText weight="semibold">Responsive Hotspots</ResponsiveText>
                        <ResponsiveText size={{ mobile: 'text-xs', desktop: 'text-sm' }} color="muted">
                          Larger hotspots on mobile with touch feedback
                        </ResponsiveText>
                      </Card>
                      <Card className="p-4">
                        <ResponsiveText weight="semibold">Adaptive Layout</ResponsiveText>
                        <ResponsiveText size={{ mobile: 'text-xs', desktop: 'text-sm' }} color="muted">
                          Layout adapts to screen size and orientation
                        </ResponsiveText>
                      </Card>
                    </ResponsiveGrid>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Performance Demo */}
            <TabsContent value="performance" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="w-5 h-5" />
                    Performance Optimizations
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveGrid cols={{ mobile: 1, tablet: 2, desktop: 2 }} gap="md">
                    <div>
                      <ResponsiveText weight="semibold">Mobile Performance</ResponsiveText>
                      <div className="space-y-2 mt-3">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <ResponsiveText size={{ mobile: 'text-sm', desktop: 'text-sm' }}>
                            Touch gesture optimization
                          </ResponsiveText>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <ResponsiveText size={{ mobile: 'text-sm', desktop: 'text-sm' }}>
                            Reduced animation on low-power devices
                          </ResponsiveText>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <ResponsiveText size={{ mobile: 'text-sm', desktop: 'text-sm' }}>
                            Lazy loading for better performance
                          </ResponsiveText>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <ResponsiveText weight="semibold">Accessibility</ResponsiveText>
                      <div className="space-y-2 mt-3">
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <ResponsiveText size={{ mobile: 'text-sm', desktop: 'text-sm' }}>
                            44px minimum touch targets
                          </ResponsiveText>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <ResponsiveText size={{ mobile: 'text-sm', desktop: 'text-sm' }}>
                            Proper contrast ratios
                          </ResponsiveText>
                        </div>
                        <div className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <ResponsiveText size={{ mobile: 'text-sm', desktop: 'text-sm' }}>
                            Screen reader compatibility
                          </ResponsiveText>
                        </div>
                      </div>
                    </div>
                  </ResponsiveGrid>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </ResponsiveContainer>
    </AdminLayout>
  );
};

export default AdminMobileDemo;
