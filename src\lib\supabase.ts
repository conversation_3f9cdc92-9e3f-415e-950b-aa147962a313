
import { createClient } from '@supabase/supabase-js'
import { ensureHTTPS, getSecureWebSocketURL } from '@/utils/httpsEnforcement'

const supabaseUrl = ensureHTTPS('https://dnbjrfgfugpmyrconepx.supabase.co')
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRuYmpyZmdmdWdwbXlyY29uZXB4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkxNjY0ODUsImV4cCI6MjA2NDc0MjQ4NX0.Vo2qoT6Io3BZdaR4uWA7C3eZ19sg4y-zZYod73e3Bgs'

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  },
  global: {
    headers: {
      'X-Client-Info': 'virtualrealtour-web'
    }
  }
})

// Types for our database
export interface Profile {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  role: 'user' | 'admin'
  created_at: string
  updated_at: string
}

export interface Tour {
  id: string
  title: string
  description?: string
  category: 'property' | 'education' | 'hospitality' | 'tourism' | 'culture' | 'commercial' | 'healthcare' | 'government'
  location?: string
  business_name?: string
  business_type?: string
  contact_phone?: string
  contact_email?: string
  website?: string
  business_hours?: string
  thumbnail_url?: string
  status: 'draft' | 'processing' | 'published' | 'archived'
  views: number
  scenes_count: number
  user_id: string
  featured: boolean
  slug?: string
  embed_url?: string
  embed_type?: 'iframe' | 'link' | 'custom'
  created_at: string
  updated_at: string
  profiles?: Profile
  // New fields for enhanced tour creation
  creation_method?: 'upload' | 'embed' | 'native' | 'images'
  tour_platform?: 'commonninja' | 'cloudpano' | 'custom'
  platform_tour_id?: string
  platform_specific?: Record<string, unknown>
  tour_config?: Record<string, unknown>
  // CloudPano specific fields
  cloudpano_tour_id?: string
  cloudpano_embed_url?: string
  cloudpano_edit_url?: string
  cloudpano_thumbnail_url?: string
  cloudpano_config?: Record<string, unknown>
  approval_status?: 'draft' | 'pending_approval' | 'approved' | 'rejected'
  approval_notes?: string
  approved_by?: string
  approved_at?: string
  submitted_for_approval_at?: string
  // Backward compatibility aliases
  business_phone?: string
  business_email?: string
  business_whatsapp?: string
  business_address?: string
  business_website?: string
  // ProductHotspot overlay support
  hotspots?: ProductHotspotData[]
}

// For admin overlay: minimal ProductHotspot type for JSONB
export interface ProductHotspotData {
  id: string;
  x: number;
  y: number;
  label: string;
  product?: {
    id: number | string;
    name: string;
    description?: string;
    image?: string;
    url?: string;
  } | null;
}

export interface Scene {
  id: string
  tour_id: string
  name: string
  description?: string
  image_url: string
  order_index: number
  created_at: string
  hotspots?: Hotspot[]
}

export interface Hotspot {
  id: string
  scene_id: string
  type: 'navigation' | 'info' | 'whatsapp' | 'link' | 'audio' | 'video'
  label?: string
  content?: string
  position_x: number
  position_y: number
  position_z: number
  target_scene_id?: string
  link_url?: string
  whatsapp_phone?: string
  whatsapp_message?: string
  created_at: string
}

export interface TourAnalytics {
  id: string
  tour_id: string
  viewer_ip?: string
  viewer_location?: string
  viewed_at: string
  time_spent_seconds: number
}
