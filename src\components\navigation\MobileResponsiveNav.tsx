/**
 * Mobile-Responsive Navigation Component
 * Adaptive navigation that works seamlessly across all devices
 */

import { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON>et<PERSON>ontent, <PERSON>etTrigger, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Badge } from '@/components/ui/badge';
import { 
  Menu, 
  X, 
  Home,
  Search,
  User,
  Settings,
  MapPin,
  ShoppingCart,
  Bell,
  ChevronDown,
  ExternalLink
} from 'lucide-react';

interface NavItem {
  label: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  badge?: string | number;
  external?: boolean;
  children?: NavItem[];
}

interface MobileResponsiveNavProps {
  brand?: {
    name: string;
    logo?: string;
    href?: string;
  };
  items: NavItem[];
  actions?: React.ReactNode;
  user?: {
    name: string;
    avatar?: string;
    role?: string;
  };
  className?: string;
}

const MobileResponsiveNav = ({
  brand = { name: 'VirtualRealTour', href: '/' },
  items,
  actions,
  user,
  className
}: MobileResponsiveNavProps) => {
  const [isMobile, setIsMobile] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const location = useLocation();

  // Detect screen size
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Handle scroll for header effects
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Close mobile menu on route change
  useEffect(() => {
    setMobileMenuOpen(false);
  }, [location.pathname]);

  // Toggle expanded menu items
  const toggleExpanded = (label: string) => {
    setExpandedItems(prev => 
      prev.includes(label) 
        ? prev.filter(item => item !== label)
        : [...prev, label]
    );
  };

  // Check if nav item is active
  const isActive = (href: string) => {
    return location.pathname === href || location.pathname.startsWith(href + '/');
  };

  // Render navigation item
  const renderNavItem = (item: NavItem, isMobile = false) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.includes(item.label);
    const active = isActive(item.href);

    if (hasChildren && isMobile) {
      return (
        <div key={item.label} className="space-y-2">
          <button
            onClick={() => toggleExpanded(item.label)}
            className={cn(
              "flex items-center justify-between w-full p-3 rounded-lg text-left transition-colors",
              active ? "bg-primary text-primary-foreground" : "hover:bg-muted"
            )}
          >
            <div className="flex items-center gap-3">
              {item.icon && <item.icon className="w-5 h-5" />}
              <span className="font-medium">{item.label}</span>
              {item.badge && (
                <Badge variant="secondary" className="ml-auto">
                  {item.badge}
                </Badge>
              )}
            </div>
            <ChevronDown 
              className={cn(
                "w-4 h-4 transition-transform",
                isExpanded && "rotate-180"
              )} 
            />
          </button>
          
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="overflow-hidden"
              >
                <div className="pl-8 space-y-1">
                  {item.children?.map(child => (
                    <Link
                      key={child.href}
                      to={child.href}
                      className={cn(
                        "flex items-center gap-2 p-2 rounded-md text-sm transition-colors",
                        isActive(child.href) 
                          ? "bg-primary text-primary-foreground" 
                          : "hover:bg-muted"
                      )}
                    >
                      {child.icon && <child.icon className="w-4 h-4" />}
                      {child.label}
                      {child.external && <ExternalLink className="w-3 h-3 ml-auto" />}
                    </Link>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      );
    }

    return (
      <Link
        key={item.href}
        to={item.href}
        target={item.external ? '_blank' : undefined}
        rel={item.external ? 'noopener noreferrer' : undefined}
        className={cn(
          "flex items-center gap-3 p-3 rounded-lg transition-colors",
          isMobile ? "w-full" : "px-4 py-2",
          active 
            ? "bg-primary text-primary-foreground" 
            : "hover:bg-muted"
        )}
      >
        {item.icon && <item.icon className="w-5 h-5" />}
        <span className={cn("font-medium", !isMobile && "hidden lg:inline")}>
          {item.label}
        </span>
        {item.badge && (
          <Badge variant="secondary" className={cn(isMobile ? "ml-auto" : "")}>
            {item.badge}
          </Badge>
        )}
        {item.external && <ExternalLink className="w-4 h-4 ml-auto" />}
      </Link>
    );
  };

  return (
    <motion.nav
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={cn(
        "sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
        scrolled && "shadow-sm",
        className
      )}
    >
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          {/* Brand */}
          <Link 
            to={brand.href || '/'} 
            className="flex items-center gap-2 font-bold text-xl"
          >
            {brand.logo && (
              <img 
                src={brand.logo} 
                alt={brand.name} 
                className="w-8 h-8 object-contain"
              />
            )}
            <span className="hidden sm:inline">{brand.name}</span>
          </Link>

          {/* Desktop Navigation */}
          {!isMobile && (
            <div className="hidden lg:flex items-center gap-2">
              {items.map(item => renderNavItem(item, false))}
            </div>
          )}

          {/* Actions & Mobile Menu */}
          <div className="flex items-center gap-2">
            {/* Actions */}
            {actions && (
              <div className="hidden sm:flex items-center gap-2">
                {actions}
              </div>
            )}

            {/* User Menu */}
            {user && (
              <Button variant="ghost" size="sm" className="hidden sm:flex items-center gap-2">
                <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                  {user.avatar ? (
                    <img 
                      src={user.avatar} 
                      alt={user.name}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <User className="w-4 h-4" />
                  )}
                </div>
                <span className="hidden lg:inline">{user.name}</span>
              </Button>
            )}

            {/* Mobile Menu */}
            {isMobile && (
              <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="sm" className="p-2">
                    <Menu className="w-6 h-6" />
                    <span className="sr-only">Toggle menu</span>
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-80 p-0">
                  <div className="flex flex-col h-full">
                    {/* Mobile Header */}
                    <SheetHeader className="p-6 border-b">
                      <SheetTitle className="text-left">
                        {brand.name}
                      </SheetTitle>
                      {user && (
                        <div className="flex items-center gap-3 mt-4">
                          <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                            {user.avatar ? (
                              <img 
                                src={user.avatar} 
                                alt={user.name}
                                className="w-full h-full rounded-full object-cover"
                              />
                            ) : (
                              <User className="w-5 h-5" />
                            )}
                          </div>
                          <div>
                            <p className="font-medium">{user.name}</p>
                            {user.role && (
                              <p className="text-sm text-muted-foreground">{user.role}</p>
                            )}
                          </div>
                        </div>
                      )}
                    </SheetHeader>

                    {/* Mobile Navigation Items */}
                    <div className="flex-1 overflow-y-auto p-6">
                      <div className="space-y-2">
                        {items.map(item => renderNavItem(item, true))}
                      </div>
                    </div>

                    {/* Mobile Actions */}
                    {actions && (
                      <div className="border-t p-6">
                        <div className="space-y-2">
                          {actions}
                        </div>
                      </div>
                    )}
                  </div>
                </SheetContent>
              </Sheet>
            )}
          </div>
        </div>
      </div>
    </motion.nav>
  );
};

export default MobileResponsiveNav;
