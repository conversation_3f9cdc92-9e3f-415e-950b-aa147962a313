
/* Import all CSS files first, before Tailwind directives */
@import './styles/photosphere-markers.css';
@import './styles/mobile-responsive.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Modern shadcn/ui color system - mobile-first design */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.65rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;

    /* VRT Theme Variables with fallbacks */
    --theme-primary: 210 100% 50%;
    --theme-primary-hover: 210 100% 45%;
    --theme-primary-light: 210 100% 97%;
    --theme-primary-border: 210 100% 85%;
    --theme-primary-foreground: 0 0% 100%;

    /* Additional theme variables for comprehensive coverage */
    --theme-success: 142 76% 36%;
    --theme-success-light: 142 76% 96%;
    --theme-success-border: 142 76% 85%;
    --theme-warning: 38 92% 50%;
    --theme-warning-light: 38 92% 96%;
    --theme-warning-border: 38 92% 85%;
    --theme-info: 217 91% 60%;
    --theme-info-light: 217 91% 96%;
    --theme-info-border: 217 91% 85%;

    /* Enhanced Design System Variables */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --enhanced-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.04);
    --enhanced-shadow-md: 0 4px 16px rgba(0, 0, 0, 0.08);
    --enhanced-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.12);
    --enhanced-shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.16);

    /* Glass/Fluid Design System Variables */
    --glass-bg-primary: rgba(255, 255, 255, 0.15);
    --glass-bg-secondary: rgba(255, 255, 255, 0.1);
    --glass-bg-tertiary: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-border-strong: rgba(255, 255, 255, 0.3);
    --glass-shadow-sm: 0 4px 16px rgba(31, 38, 135, 0.1);
    --glass-shadow-md: 0 8px 32px rgba(31, 38, 135, 0.15);
    --glass-shadow-lg: 0 12px 40px rgba(31, 38, 135, 0.25);
    --glass-blur-sm: blur(10px);
    --glass-blur-md: blur(20px);
    --glass-blur-lg: blur(30px);
    --glass-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .dark {
    /* Dark theme - mobile optimized */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Dark theme glass variables */
    --glass-bg-primary: rgba(0, 0, 0, 0.25);
    --glass-bg-secondary: rgba(0, 0, 0, 0.15);
    --glass-bg-tertiary: rgba(0, 0, 0, 0.1);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-border-strong: rgba(255, 255, 255, 0.2);
    --glass-shadow-sm: 0 4px 16px rgba(0, 0, 0, 0.3);
    --glass-shadow-md: 0 8px 32px rgba(0, 0, 0, 0.4);
    --glass-shadow-lg: 0 12px 40px rgba(0, 0, 0, 0.5);
    --glass-text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply leading-tight;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  }

  /* Mobile-first responsive design */
  html, body {
    @apply overflow-x-hidden;
    scroll-behavior: smooth;
  }

  /* Ensure proper touch targets on mobile */
  button, [role="button"] {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Prevent zoom on iOS form inputs */
  input, textarea, select {
    font-size: max(16px, 1rem);
  }

  /* Theme utility classes for backward compatibility */
  .text-theme-primary {
    color: hsl(var(--theme-primary));
  }

  .bg-theme-primary {
    background-color: hsl(var(--theme-primary));
  }

  .bg-theme-primary-light {
    background-color: hsl(var(--theme-primary-light));
  }

  .border-theme-primary-border {
    border-color: hsl(var(--theme-primary-border));
  }

  .text-theme-primary-foreground {
    color: hsl(var(--theme-primary-foreground));
  }

  .hover\:bg-theme-primary-hover:hover {
    background-color: hsl(var(--theme-primary-hover));
  }
}

@layer utilities {
  /* Line clamp utility - keep this as it's useful */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

/* Modern shadcn/ui responsive patterns */
@layer components {
  /* Glass/Fluid Design System Components */
  .glass-panel {
    background: var(--glass-bg-primary);
    -webkit-backdrop-filter: var(--glass-blur-md);
    backdrop-filter: var(--glass-blur-md);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    box-shadow: var(--glass-shadow-md), inset 0 0 0 1px rgba(255, 255, 255, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glass-panel:hover {
    background: var(--glass-bg-secondary);
    box-shadow: var(--glass-shadow-lg), inset 0 0 0 1px rgba(255, 255, 255, 0.12);
    transform: translateY(-2px);
  }

  .glass-card {
    background: var(--glass-bg-secondary);
    -webkit-backdrop-filter: var(--glass-blur-sm);
    backdrop-filter: var(--glass-blur-sm);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    box-shadow: var(--glass-shadow-sm);
    padding: 1rem;
  }

  .glass-button {
    background: var(--glass-bg-primary);
    -webkit-backdrop-filter: var(--glass-blur-sm);
    backdrop-filter: var(--glass-blur-sm);
    border: 1px solid var(--glass-border-strong);
    border-radius: 8px;
    color: hsl(var(--foreground));
    text-shadow: var(--glass-text-shadow);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glass-button:hover {
    background: var(--glass-bg-secondary);
    border-color: var(--glass-border-strong);
    transform: translateY(-1px);
    box-shadow: var(--glass-shadow-md);
  }

  .glass-input {
    background: var(--glass-bg-tertiary);
    -webkit-backdrop-filter: var(--glass-blur-sm);
    backdrop-filter: var(--glass-blur-sm);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    color: hsl(var(--foreground));
    padding: 0.75rem;
    transition: all 0.2s ease;
  }

  .glass-input:focus {
    background: var(--glass-bg-secondary);
    border-color: var(--glass-border-strong);
    box-shadow: var(--glass-shadow-sm), 0 0 0 2px rgba(59, 130, 246, 0.1);
    outline: none;
  }

  .glass-textarea {
    @apply glass-input;
    min-height: 80px;
    resize: vertical;
  }

  .glass-select {
    @apply glass-input;
    cursor: pointer;
  }

  .glass-label {
    color: hsl(var(--foreground));
    font-weight: 500;
    text-shadow: var(--glass-text-shadow);
    margin-bottom: 0.5rem;
    display: block;
  }

  .glass-tooltip {
    background: var(--glass-bg-primary);
    -webkit-backdrop-filter: var(--glass-blur-md);
    backdrop-filter: var(--glass-blur-md);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    color: hsl(var(--foreground));
    text-shadow: var(--glass-text-shadow);
    box-shadow: var(--glass-shadow-md);
  }

  .glass-modal {
    background: var(--glass-bg-primary);
    -webkit-backdrop-filter: var(--glass-blur-lg);
    backdrop-filter: var(--glass-blur-lg);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--glass-shadow-lg);
  }

  .glass-navigation {
    background: var(--glass-bg-secondary);
    -webkit-backdrop-filter: var(--glass-blur-md);
    backdrop-filter: var(--glass-blur-md);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow-sm);
  }

  .glass-sidebar {
    background: var(--glass-bg-primary);
    -webkit-backdrop-filter: var(--glass-blur-lg);
    backdrop-filter: var(--glass-blur-lg);
    border-right: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow-md);
  }

  /* Radix UI component z-index management */
  [data-radix-popper-content-wrapper] {
    @apply z-50;
  }

  [data-radix-dialog-overlay] {
    @apply z-50;
  }

  [data-radix-dialog-content] {
    @apply z-50 w-full max-w-lg mx-auto;
  }

  /* Mobile-optimized dialog */
  @media (max-width: 768px) {
    [data-radix-dialog-content] {
      @apply w-[95vw] max-w-[95vw] max-h-[90vh] m-[2.5vh_auto];
    }
  }

  /* Ensure close button is always visible and properly positioned */
  [data-radix-dialog-content] button[data-radix-dialog-close] {
    @apply z-10;
  }

  /* Demo tour iframe optimization for clean display */
  .demo-tour-iframe {
    border: none !important;
    outline: none !important;
    background: transparent !important;
    pointer-events: auto;
    overflow: hidden;
  }

  /* Hide any potential source branding or popups in iframes */
  .demo-tour-iframe::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: -1;
  }

  /* Line clamp utilities already defined above - removed duplicates */

  /* Ensure modal content is scrollable on mobile */
  .modal-content-scrollable {
    @apply overflow-y-auto overscroll-contain;
    /* Modern smooth scrolling for all browsers */
    scroll-behavior: smooth;
  }

  /* Enhanced scrolling for touch devices */
  @supports (overscroll-behavior: contain) {
    .modal-content-scrollable {
      overscroll-behavior: contain;
    }
  }

  /* Responsive image handling */
  img, video, iframe {
    @apply max-w-full h-auto;
  }

  /* Lazy loading images with blur-up effect */
  img[loading="lazy"] {
    @apply opacity-0 transition-opacity duration-300;
    filter: blur(5px);
  }

  img[loading="lazy"].loaded {
    @apply opacity-100;
    filter: blur(0);
  }

  /* Progressive image loading */
  .progressive-image {
    position: relative;
    overflow: hidden;
  }

  .progressive-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transform: translateX(-100%);
    animation: shimmer 1.5s infinite;
  }

  @keyframes shimmer {
    100% {
      transform: translateX(100%);
    }
  }

  /* Optimized 360° image loading */
  .panorama-loading {
    background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    animation: panorama-loading 1s linear infinite;
  }

  @keyframes panorama-loading {
    0% { background-position: 0 0, 0 10px, 10px -10px, -10px 0px; }
    100% { background-position: 20px 20px, 20px 30px, 30px 10px, 10px 20px; }
  }

  /* Commerce-specific mobile optimizations */
  .shopping-cart-sheet {
    @apply w-full sm:max-w-lg;
  }

  @media (max-width: 640px) {
    .shopping-cart-sheet {
      @apply w-full max-w-full h-full;
    }
  }

  /* Product card responsive grid */
  .product-grid {
    @apply grid gap-4;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  @media (max-width: 640px) {
    .product-grid {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      @apply gap-3;
    }
  }

  /* Vendor dashboard mobile optimization */
  .vendor-stats-grid {
    @apply grid gap-4;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  @media (max-width: 640px) {
    .vendor-stats-grid {
      grid-template-columns: repeat(2, 1fr);
      @apply gap-3;
    }
  }

  /* Order tracking timeline mobile optimization */
  .order-timeline {
    @apply space-y-4;
  }

  @media (max-width: 640px) {
    .order-timeline {
      @apply space-y-3;
    }
  }

  /* WhatsApp widget mobile positioning */
  .whatsapp-widget {
    @apply fixed bottom-4 right-4 z-50;
  }

  @media (max-width: 640px) {
    .whatsapp-widget {
      @apply bottom-3 right-3;
    }
  }

  /* Ensure touch-friendly interactions */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* Mobile-optimized form inputs */
  .mobile-input {
    @apply text-base; /* Prevents zoom on iOS */
  }

  /* Responsive table wrapper */
  .table-responsive {
    @apply overflow-x-auto -mx-4 px-4;
  }

  @media (max-width: 640px) {
    .table-responsive {
      @apply -mx-2 px-2;
    }
  }

  /* Mobile-specific glass components */
  @media (max-width: 768px) {
    .glass-panel {
      border-radius: 12px;
      margin: 0.5rem;
    }

    .glass-sidebar {
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      width: 280px;
      z-index: 50;
      transform: translateX(-100%);
      transition: transform 0.3s ease;
    }

    .glass-sidebar.open {
      transform: translateX(0);
    }

    .glass-navigation {
      position: sticky;
      top: 0;
      z-index: 40;
      border-radius: 0;
      margin: 0;
    }

    .glass-modal {
      margin: 1rem;
      max-height: calc(100vh - 2rem);
      border-radius: 16px;
    }

    .glass-button {
      min-height: 44px;
      min-width: 44px;
      padding: 0.75rem 1rem;
    }

    .glass-input,
    .glass-textarea,
    .glass-select {
      font-size: 16px; /* Prevents zoom on iOS */
      min-height: 44px;
    }
  }

  /* Tour editor mobile layout */
  @media (max-width: 1024px) {
    .tour-editor-grid {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .tour-editor-sidebar {
      order: -1;
      max-height: 300px;
      overflow-y: auto;
    }

    .tour-editor-main {
      min-height: 400px;
    }

    .tour-editor-properties {
      max-height: 400px;
      overflow-y: auto;
    }
  }
}

/* Enhanced Design System Utilities */
@layer utilities {
  /* Glass Morphism Effects */
  .glass {
    background: var(--glass-bg);
    -webkit-backdrop-filter: blur(16px);
    backdrop-filter: blur(16px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .glass-card {
    @apply glass rounded-xl;
  }

  .glass-nav {
    @apply glass border-b border-white/10;
  }

  /* Enhanced Shadows */
  .shadow-enhanced-sm { box-shadow: var(--enhanced-shadow-sm); }
  .shadow-enhanced-md { box-shadow: var(--enhanced-shadow-md); }
  .shadow-enhanced-lg { box-shadow: var(--enhanced-shadow-lg); }
  .shadow-enhanced-xl { box-shadow: var(--enhanced-shadow-xl); }

  /* Smooth Transitions */
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .transition-bounce {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  /* Enhanced Hover Effects */
  .hover-lift {
    @apply transition-smooth;
  }
  .hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--enhanced-shadow-lg);
  }

  .hover-scale {
    @apply transition-smooth;
  }
  .hover-scale:hover {
    transform: scale(1.02);
  }

  /* Enhanced Button Hover Effects */
  .btn-glow:hover {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.4), 0 0 40px rgba(59, 130, 246, 0.2);
  }

  .btn-gradient-shift {
    background: linear-gradient(45deg, var(--primary), var(--primary));
    background-size: 200% 200%;
    transition: background-position 0.3s ease;
  }

  .btn-gradient-shift:hover {
    background-position: 100% 0;
  }

  /* Augment Code inspired button effects */
  .btn-augment-style {
    position: relative;
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary-foreground)) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
  }

  .btn-augment-style::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  .btn-augment-style:hover::before {
    left: 100%;
  }

  .btn-augment-style:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
  }

  /* Card Enhancement */
  .card-hover-glow {
    transition: all 0.3s ease;
  }

  .card-hover-glow:hover {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 0 0 1px rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
  }

  /* Augment Code inspired card effects */
  .card-augment-style {
    position: relative;
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
  }

  .card-augment-style::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .card-augment-style:hover::before {
    opacity: 1;
  }

  .card-augment-style:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(255, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  /* Typography Enhancements */
  .text-gradient {
    background: linear-gradient(135deg, var(--primary), var(--primary-foreground));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .heading-enhanced {
    letter-spacing: -0.025em;
    line-height: 1.1;
    font-weight: 700;
  }

  /* Subtle Section Backgrounds */
  .section-gradient-subtle {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.02) 50%,
      rgba(255, 255, 255, 0.05) 100%);
  }

  /* Enhanced Form Focus Effects */
  .input-focus-glow {
    transition: all 0.3s ease;
  }

  .input-focus-glow:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  }

  /* Interactive Micro-animations */
  .icon-hover {
    transition: all 0.2s ease;
  }

  .icon-hover:hover {
    transform: scale(1.1);
    color: var(--primary);
  }

  .button-press {
    transition: transform 0.1s ease;
  }

  .button-press:active {
    transform: scale(0.98);
  }

  .link-underline {
    position: relative;
    transition: color 0.2s ease;
  }

  .link-underline::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 1px;
    background: var(--primary);
    transition: width 0.3s ease;
  }

  .link-underline:hover::after {
    width: 100%;
  }

  /* Enhanced Mobile Experience */
  .touch-feedback {
    transition: all 0.1s ease;
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
  }

  .touch-feedback:active {
    transform: scale(0.97);
    background-color: rgba(59, 130, 246, 0.05);
  }

  /* Enhanced Image Blur Placeholder */
  .image-blur-placeholder {
    background-size: cover;
    background-position: center;
    filter: blur(10px);
    transform: scale(1.1);
  }

  .mobile-friendly {
    min-height: 44px;
    min-width: 44px;
    touch-action: manipulation;
  }

  /* Better mobile scrolling */
  .smooth-scroll {
    scroll-behavior: smooth;
  }

  /* Enhanced scroll performance */
  .scroll-optimized {
    will-change: transform;
    transform: translateZ(0);
  }

  /* Modern overscroll behavior with fallback */
  @supports (overscroll-behavior: contain) {
    .scroll-optimized {
      overscroll-behavior: contain;
    }
  }

  /* Intersection observer optimization */
  .lazy-load {
    content-visibility: auto;
    contain-intrinsic-size: 300px;
  }

  /* Tour Viewer Styles */
  .tour-viewer-iframe {
    height: calc(100vh - 4rem);
    border: none;
    background: #f8fafc;
  }

  .tour-card-iframe {
    border: none;
    background: transparent;
    transition: opacity 0.3s ease;
  }

  .tour-card-iframe.with-overlay {
    pointer-events: none;
    opacity: 0.95;
  }

  .tour-card-iframe.without-overlay {
    pointer-events: auto;
    opacity: 1;
  }

  /* Clean tour iframe for carousel and clean displays */
  .clean-tour-iframe {
    border: none !important;
    outline: none !important;
    background: transparent;
    pointer-events: auto;
    opacity: 1;
    transition: opacity 0.3s ease;
  }

  .clean-tour-iframe:focus {
    outline: none !important;
  }

  .tour-card-overlay {
    background: rgba(255, 255, 255, 0.02);
    -webkit-backdrop-filter: blur(0.2px);
    backdrop-filter: blur(0.2px);
    border: none;
    transition: background 0.3s ease;
  }

  .tour-card-overlay:hover {
    background: rgba(255, 255, 255, 0.01);
  }

  .tour-card-container {
    height: var(--tour-height, 300px);
  }

  /* Common tour card heights */
  .tour-card-height-200 { height: 200px; }
  .tour-card-height-250 { height: 250px; }
  .tour-card-height-300 { height: 300px; }
  .tour-card-height-350 { height: 350px; }
  .tour-card-height-400 { height: 400px; }

  /* Enhanced Scroll Animation Classes - More Obvious */
  .fade-in-up {
    opacity: 0;
    transform: translateY(60px);
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .fade-in-up.animate {
    opacity: 1;
    transform: translateY(0);
  }

  .fade-in-left {
    opacity: 0;
    transform: translateX(-60px);
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .fade-in-left.animate {
    opacity: 1;
    transform: translateX(0);
  }

  .fade-in-right {
    opacity: 0;
    transform: translateX(60px);
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94), transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .fade-in-right.animate {
    opacity: 1;
    transform: translateX(0);
  }

  .fade-in {
    opacity: 0;
    transition: opacity 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .fade-in.animate {
    opacity: 1;
  }

  /* New dramatic animation types */
  .slide-in-up {
    opacity: 0;
    transform: translateY(80px) scale(0.95);
    transition: opacity 0.9s cubic-bezier(0.16, 1, 0.3, 1), transform 0.9s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .slide-in-up.animate {
    opacity: 1;
    transform: translateY(0) scale(1);
  }

  .zoom-in {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.7s cubic-bezier(0.16, 1, 0.3, 1), transform 0.7s cubic-bezier(0.16, 1, 0.3, 1);
  }

  .zoom-in.animate {
    opacity: 1;
    transform: scale(1);
  }

  /* Safe animation classes that ensure visibility - subtle version */
  .safe-fade-in {
    opacity: 1;
    transform: translateY(8px);
    transition: transform 0.4s ease-out;
  }

  .safe-fade-in.animate {
    transform: translateY(0);
  }

  /* Augment Code inspired status indicators */
  .status-indicator {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.05);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }

  .status-indicator::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: pulse-glow 2s infinite;
  }

  @keyframes pulse-glow {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.7;
      transform: scale(1.1);
    }
  }

  .status-indicator.available {
    color: #10b981;
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
  }

  .status-indicator.busy {
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
  }

  .status-indicator.offline {
    color: #6b7280;
    background: rgba(107, 114, 128, 0.1);
    border-color: rgba(107, 114, 128, 0.2);
  }

  /* Enhanced gradient text effect */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, #60a5fa 50%, #a78bfa 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
}

/* Performance optimizations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

.gpu-accelerated {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Smooth scrolling for better UX */
html {
  scroll-behavior: smooth;
}

/* Optimize animations for 60fps */
.optimized-animation {
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
}

/* Accessibility and performance */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .status-indicator::before {
    animation: none !important;
  }

  .gradient-text {
    animation: none !important;
  }
}
