
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { AdminSidebar } from './AdminSidebar';
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb';
import { useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import ProfileDropdown from './ProfileDropdown';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const location = useLocation();

  const getBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs = [];

    if (pathSegments[0] === 'admin') {
      breadcrumbs.push({ label: 'Admin', href: '/admin' });

      if (pathSegments[1]) {
        const pageNames: Record<string, string> = {
          'tours': 'Tour Management',
          'featured': 'Featured Tours',
          'demo': 'Demo Tour',
          'users': 'User Management',
          'content': 'Content Management',
          'media': 'Media Library',
          'analytics': 'Analytics',
          'settings': 'Site Settings',
          'diagnostics': 'System Diagnostics'
        };

        breadcrumbs.push({
          label: pageNames[pathSegments[1]] || pathSegments[1],
          href: `/admin/${pathSegments[1]}`
        });
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = getBreadcrumbs();

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AdminSidebar />
        <div className="flex-1 flex flex-col min-w-0">
          {/* Mobile-First Topbar/Header */}
          <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex h-14 sm:h-16 items-center justify-between px-3 sm:px-4 md:px-6 lg:px-8">
              <div className="flex items-center gap-2 sm:gap-4 min-w-0 flex-1">
                <SidebarTrigger className="touch-target" />

                {/* Mobile: Show current page title */}
                <div className="block md:hidden min-w-0 flex-1">
                  <h1 className="text-sm font-semibold truncate">
                    {breadcrumbs.length > 0 ? breadcrumbs[breadcrumbs.length - 1].label : 'Admin'}
                  </h1>
                </div>

                {/* Desktop: Show full breadcrumbs */}
                <div className="hidden md:block min-w-0 flex-1">
                  <Breadcrumb>
                    <BreadcrumbList>
                      {breadcrumbs.map((breadcrumb, index) => (
                        <div key={breadcrumb.href} className="flex items-center">
                          {index > 0 && <BreadcrumbSeparator />}
                          <BreadcrumbItem>
                            {index === breadcrumbs.length - 1 ? (
                              <BreadcrumbPage className="text-sm">{breadcrumb.label}</BreadcrumbPage>
                            ) : (
                              <BreadcrumbLink href={breadcrumb.href} className="text-sm">{breadcrumb.label}</BreadcrumbLink>
                            )}
                          </BreadcrumbItem>
                        </div>
                      ))}
                    </BreadcrumbList>
                  </Breadcrumb>
                </div>
              </div>

              {/* Mobile-optimized profile section */}
              <div className="flex items-center gap-1 sm:gap-2">
                <ProfileDropdown />
              </div>
            </div>
          </header>
          {/* Mobile-First Main Content Area */}
          <main className="flex-1 w-full overflow-x-hidden">
            <div className="p-3 sm:p-4 md:p-6 lg:p-8 max-w-full">
              {children}
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default AdminLayout;
