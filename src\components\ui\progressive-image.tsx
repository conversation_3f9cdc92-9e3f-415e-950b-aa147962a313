// Progressive Image Loading Component
// Provides blur placeholders, format detection, and smooth loading transitions

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { FormatSupport, imageUrlBuilder } from '@/utils/imageOptimization';

interface ProgressiveImageProps {
  src: string;
  alt: string;
  placeholder?: string;
  className?: string;
  width?: number;
  height?: number;
  quality?: number;
  priority?: boolean;
  sizes?: string;
  onLoad?: () => void;
  onError?: () => void;
  fallback?: string;
  blurDataURL?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'none' | 'scale-down';
}

export const ProgressiveImage: React.FC<ProgressiveImageProps> = ({
  src,
  alt,
  placeholder,
  className,
  width,
  height,
  quality = 80,
  priority = false,
  sizes,
  onLoad,
  onError,
  fallback,
  blurDataURL,
  objectFit = 'cover'
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [isInView, setIsInView] = useState(priority);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority || isInView) return;

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observerRef.current?.disconnect();
        }
      },
      {
        rootMargin: '50px',
        threshold: 0.1
      }
    );

    if (imgRef.current) {
      observerRef.current.observe(imgRef.current);
    }

    return () => {
      observerRef.current?.disconnect();
    };
  }, [priority, isInView]);

  // Generate optimized image sources
  const generateSources = useCallback(async () => {
    if (!isInView) return;

    try {
      const supportsWebP = await FormatSupport.supportsWebP();
      const supportsAVIF = await FormatSupport.supportsAVIF();

      let optimizedSrc = src;

      // Use best available format
      if (supportsAVIF) {
        optimizedSrc = imageUrlBuilder.buildUrl(src, {
          width,
          height,
          quality,
          format: 'avif'
        });
      } else if (supportsWebP) {
        optimizedSrc = imageUrlBuilder.buildUrl(src, {
          width,
          height,
          quality,
          format: 'webp'
        });
      } else {
        optimizedSrc = imageUrlBuilder.buildUrl(src, {
          width,
          height,
          quality,
          format: 'jpeg'
        });
      }

      setCurrentSrc(optimizedSrc);
    } catch (error) {
      console.warn('Failed to generate optimized image source:', error);
      setCurrentSrc(src);
    }
  }, [src, width, height, quality, isInView]);

  useEffect(() => {
    generateSources();
  }, [generateSources]);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    setIsError(false);
    onLoad?.();
  }, [onLoad]);

  const handleError = useCallback(() => {
    setIsError(true);
    if (fallback && currentSrc !== fallback) {
      setCurrentSrc(fallback);
    } else {
      onError?.();
    }
  }, [currentSrc, fallback, onError]);

  // Generate responsive sources for picture element
  const generateResponsiveSources = () => {
    if (!sizes || !width) return null;

    const breakpoints = [
      { width: Math.round(width * 0.5), media: '(max-width: 640px)' },
      { width: Math.round(width * 0.75), media: '(max-width: 1024px)' },
      { width: width, media: '(min-width: 1025px)' }
    ];

    return breakpoints.map(({ width: bpWidth, media }) => (
      <source
        key={media}
        media={media}
        srcSet={`
          ${imageUrlBuilder.buildUrl(src, { width: bpWidth, format: 'avif' })} 1x,
          ${imageUrlBuilder.buildUrl(src, { width: bpWidth * 2, format: 'avif' })} 2x
        `}
        type="image/avif"
      />
    ));
  };

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    overflow: 'hidden',
    backgroundColor: '#f3f4f6',
    ...(width && height && { aspectRatio: `${width} / ${height}` })
  };

  const imageStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectFit,
    transition: 'opacity 0.3s ease-in-out',
    opacity: isLoaded ? 1 : 0
  };

  const placeholderStyle: React.CSSProperties = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    objectFit,
    filter: 'blur(10px)',
    transform: 'scale(1.1)',
    transition: 'opacity 0.3s ease-in-out',
    opacity: isLoaded ? 0 : 1
  };

  return (
    <div className={cn('relative', className)} style={containerStyle}>
      {/* Blur placeholder */}
      {(placeholder || blurDataURL) && (
        <img
          src={placeholder || blurDataURL}
          alt=""
          style={placeholderStyle}
          aria-hidden="true"
        />
      )}

      {/* Loading skeleton */}
      {!isLoaded && !placeholder && !blurDataURL && (
        <div
          className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse"
          style={{
            backgroundSize: '200% 100%',
            animation: 'shimmer 1.5s infinite'
          }}
        />
      )}

      {/* Main image with responsive sources */}
      {isInView && currentSrc && (
        <picture>
          {generateResponsiveSources()}
          
          {/* WebP sources */}
          <source
            srcSet={`
              ${imageUrlBuilder.buildUrl(src, { width, format: 'webp' })} 1x,
              ${imageUrlBuilder.buildUrl(src, { width: width ? width * 2 : undefined, format: 'webp' })} 2x
            `}
            type="image/webp"
          />
          
          {/* Fallback image */}
          <img
            ref={imgRef}
            src={currentSrc}
            alt={alt}
            style={imageStyle}
            onLoad={handleLoad}
            onError={handleError}
            loading={priority ? 'eager' : 'lazy'}
            decoding="async"
            sizes={sizes}
          />
        </picture>
      )}

      {/* Error state */}
      {isError && !fallback && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="text-center text-gray-500">
            <svg
              className="w-12 h-12 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <p className="text-sm">Image unavailable</p>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {!isLoaded && !isError && isInView && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin" />
        </div>
      )}
    </div>
  );
};

// Hook for progressive image loading
export const useProgressiveImage = (src: string, placeholder?: string) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(placeholder || '');

  useEffect(() => {
    if (!src) return;

    const img = new Image();
    
    img.onload = () => {
      setCurrentSrc(src);
      setIsLoaded(true);
    };
    
    img.onerror = () => {
      setIsLoaded(false);
    };
    
    img.src = src;
  }, [src]);

  return { src: currentSrc, isLoaded };
};

// Optimized image component for tour cards
export const TourImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
  priority?: boolean;
}> = ({ src, alt, className, priority = false }) => {
  return (
    <ProgressiveImage
      src={src}
      alt={alt}
      className={className}
      width={400}
      height={300}
      quality={85}
      priority={priority}
      sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
      objectFit="cover"
      fallback="/images/tour-placeholder.jpg"
    />
  );
};

// Hero image component with high priority loading
export const HeroImage: React.FC<{
  src: string;
  alt: string;
  className?: string;
}> = ({ src, alt, className }) => {
  return (
    <ProgressiveImage
      src={src}
      alt={alt}
      className={className}
      width={1920}
      height={1080}
      quality={90}
      priority={true}
      sizes="100vw"
      objectFit="cover"
    />
  );
};

// Add shimmer animation styles
const shimmerStyles = `
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`;

// Inject styles
if (typeof document !== 'undefined') {
  const styleSheet = document.createElement('style');
  styleSheet.textContent = shimmerStyles;
  document.head.appendChild(styleSheet);
}
