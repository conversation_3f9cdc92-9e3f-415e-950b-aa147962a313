
-- Create a table to track featured tour assignments for different sections
CREATE TABLE public.featured_tour_assignments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  tour_id UUID REFERENCES public.tours(id) ON DELETE CASCADE NOT NULL,
  section_type TEXT NOT NULL CHECK (section_type IN ('demo', 'featured', 'showcase')),
  display_order INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(tour_id, section_type)
);

-- Enable RLS for featured tour assignments
ALTER TABLE public.featured_tour_assignments ENABLE ROW LEVEL SECURITY;

-- Create policy for admins to manage featured tour assignments
CREATE POLICY "Admins can manage featured tour assignments"
ON public.featured_tour_assignments
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM public.profiles 
    WHERE profiles.id = auth.uid() 
    AND profiles.role = 'admin'
  )
);

-- Create policy for public to view active assignments
CREATE POLICY "Public can view active featured tour assignments"
ON public.featured_tour_assignments
FOR SELECT
USING (is_active = true);

-- Create storage bucket for tour media if it doesn't exist
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'tour-media',
  'tour-media', 
  true,
  52428800, -- 50MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'video/mp4', 'video/webm']
) ON CONFLICT (id) DO NOTHING;

-- Create storage policy for tour media uploads
CREATE POLICY "Authenticated users can upload tour media"
ON storage.objects
FOR INSERT
TO authenticated
WITH CHECK (bucket_id = 'tour-media');

-- Create storage policy for public access to tour media
CREATE POLICY "Public can view tour media"
ON storage.objects
FOR SELECT
TO public
USING (bucket_id = 'tour-media');

-- Create storage policy for users to update their own uploads
CREATE POLICY "Users can update their own tour media"
ON storage.objects
FOR UPDATE
TO authenticated
USING (bucket_id = 'tour-media' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Create storage policy for users to delete their own uploads
CREATE POLICY "Users can delete their own tour media"
ON storage.objects
FOR DELETE
TO authenticated
USING (bucket_id = 'tour-media' AND auth.uid()::text = (storage.foldername(name))[1]);

-- Add embed_url column to tours table for external tour links
ALTER TABLE public.tours ADD COLUMN IF NOT EXISTS embed_url TEXT;
ALTER TABLE public.tours ADD COLUMN IF NOT EXISTS embed_type TEXT CHECK (embed_type IN ('iframe', 'link', 'custom'));

-- Create function to get featured tours for a specific section
CREATE OR REPLACE FUNCTION public.get_featured_tours_for_section(section_name TEXT)
RETURNS TABLE (
  tour_id UUID,
  title TEXT,
  description TEXT,
  category TEXT,
  location TEXT,
  thumbnail_url TEXT,
  embed_url TEXT,
  embed_type TEXT,
  views INTEGER,
  scenes_count INTEGER,
  display_order INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    t.id,
    t.title,
    t.description,
    t.category::TEXT,
    t.location,
    t.thumbnail_url,
    t.embed_url,
    t.embed_type,
    t.views,
    t.scenes_count,
    fta.display_order
  FROM public.tours t
  INNER JOIN public.featured_tour_assignments fta ON t.id = fta.tour_id
  WHERE fta.section_type = section_name
    AND fta.is_active = true
    AND t.status = 'published'
  ORDER BY fta.display_order ASC, t.created_at DESC;
END;
$$;
