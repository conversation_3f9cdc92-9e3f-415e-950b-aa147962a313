import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Check, Camera, Upload, Settings, Share2, Eye, MapPin, Smartphone, Headset, Globe } from 'lucide-react';
import { Link } from 'react-router-dom';

const Services = () => {
  const services = [
    {
      title: "360° Photography & Videography",
      description: "Professional capture of immersive 360-degree content for your virtual tours.",
      icon: Camera,
      features: [
        "High-resolution 360° photography",
        "4K 360° video recording",
        "Professional lighting setup",
        "HDR processing for optimal quality",
        "Same-day turnaround available"
      ],
      popular: false
    },
    {
      title: "Virtual Tour Creation",
      description: "Complete virtual tour development with interactive hotspots and navigation.",
      icon: Eye,
      features: [
        "Interactive hotspot creation",
        "Seamless scene transitions",
        "Custom branding integration",
        "Mobile-optimized delivery",
        "WhatsApp contact integration",
        "Advanced analytics dashboard"
      ],
      popular: true
    },
    {
      title: "VR Experience Development",
      description: "Advanced VR-ready experiences for headsets and immersive viewing.",
      icon: Headset,
      features: [
        "VR headset compatibility",
        "Immersive audio integration",
        "Advanced interaction systems",
        "Multi-platform deployment",
        "Custom VR controllers support"
      ],
      popular: false
    }
  ];

  const industries = [
    {
      title: "Real Estate",
      description: "Property showcases and virtual walkthroughs",
      icon: MapPin,
      examples: ["Residential properties", "Commercial spaces", "Land developments", "Property management"]
    },
    {
      title: "Education",
      description: "Campus tours and educational experiences",
      icon: Globe,
      examples: ["University campuses", "School facilities", "Training centers", "Educational museums"]
    },
    {
      title: "Hospitality",
      description: "Hotels, restaurants, and event venues",
      icon: Smartphone,
      examples: ["Hotel rooms & facilities", "Restaurant interiors", "Event venues", "Resort amenities"]
    },
    {
      title: "Commercial",
      description: "Business spaces and retail environments",
      icon: Settings,
      examples: ["Office spaces", "Retail stores", "Showrooms", "Manufacturing facilities"]
    }
  ];

  const process = [
    {
      step: "1",
      title: "Consultation",
      description: "We discuss your needs, location, and objectives for the virtual tour."
    },
    {
      step: "2",
      title: "Site Visit & Capture",
      description: "Our team visits your location with professional 360° equipment for content capture."
    },
    {
      step: "3",
      title: "Post-Production",
      description: "We process, edit, and enhance your content with professional tools and techniques."
    },
    {
      step: "4",
      title: "Tour Development",
      description: "Interactive elements, hotspots, and navigation are added to create your virtual tour."
    },
    {
      step: "5",
      title: "Delivery & Hosting",
      description: "Your tour is deployed on our platform and made accessible across all devices."
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      {/* Enhanced Hero Section with Background */}
      <section className="relative pt-32 lg:pt-40 pb-20 overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.unsplash.com/photo-1497366216548-37526070297c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80"
            alt="Modern office spaces for virtual tour services"
            className="w-full h-full object-cover object-center"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-black/70"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center rounded-full bg-white/10 backdrop-blur-sm border border-white/20 px-4 py-2 text-sm font-medium text-white mb-6">
              🎯 Professional Services
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 drop-shadow-lg">
              Our Services
            </h1>
            <p className="text-xl md:text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed drop-shadow-md">
              Professional virtual tour services tailored for Nigerian businesses.
              From capture to delivery, we handle everything to showcase your space beautifully.
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 sm:px-6 py-8">

        {/* Enhanced Services Grid */}
        <div className="mb-20">
          <div className="text-center mb-16">
            <div className="inline-flex items-center rounded-full bg-theme-primary-light border border-theme-primary-border px-4 py-2 text-sm font-medium text-theme-primary mb-6">
              ⚡ Our Expertise
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              What We Offer
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Comprehensive virtual tour solutions designed to showcase your space with professional quality and cutting-edge technology.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className={`relative hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 hover:scale-[1.02] ${service.popular ? 'border-theme-primary border-2 shadow-lg' : 'border-gray-200'}`}>
                {service.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-gradient-to-r from-theme-primary to-theme-primary-hover text-theme-primary-foreground px-4 py-1.5 shadow-lg">Most Popular</Badge>
                  </div>
                )}
                <CardHeader className="text-center pb-3">
                  <div className="w-16 h-16 bg-theme-primary-light rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <service.icon className="w-8 h-8 text-theme-primary" />
                  </div>
                  <CardTitle className="text-xl font-bold text-gray-900">{service.title}</CardTitle>
                  <CardDescription className="text-gray-600">{service.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3 mb-6">
                    {service.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start">
                        <Check className="w-5 h-5 text-theme-primary mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Button className="w-full bg-theme-primary hover:bg-theme-primary-hover text-theme-primary-foreground shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105" asChild>
                    <Link to="/contact">Get Quote</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Enhanced Industries Section */}
        <section className="mb-20 bg-gradient-to-br from-gray-50 via-white to-gray-50 rounded-3xl p-8 lg:p-12">
          <div className="text-center mb-16">
            <div className="inline-flex items-center rounded-full bg-theme-primary-light border border-theme-primary-border px-4 py-2 text-sm font-medium text-theme-primary mb-6">
              🏢 Industry Focus
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Industries We Serve
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Specialized virtual tour solutions tailored for diverse industries across Nigeria.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {industries.map((industry, index) => (
              <Card key={index} className="hover:shadow-xl transition-all duration-300 hover:-translate-y-1 hover:scale-105 border-0 bg-white/80 backdrop-blur-sm">
                <CardContent className="p-6 text-center">
                  <div className="w-12 h-12 bg-theme-primary-light rounded-xl flex items-center justify-center mx-auto mb-4">
                    <industry.icon className="w-6 h-6 text-theme-primary" />
                  </div>
                  <h3 className="font-bold text-gray-900 mb-2">{industry.title}</h3>
                  <p className="text-sm text-gray-600 mb-4">{industry.description}</p>
                  <div className="space-y-1">
                    {industry.examples.map((example, idx) => (
                      <div key={idx} className="text-xs text-gray-500 bg-gray-50 rounded-full px-2 py-1 inline-block mr-1 mb-1">{example}</div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Process Section */}
        <div className="mb-20">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
            Our Process
          </h2>
          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {process.map((item, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="flex-shrink-0 w-12 h-12 bg-theme-primary text-theme-primary-foreground rounded-full flex items-center justify-center font-bold">
                    {item.step}
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{item.title}</h3>
                    <p className="text-gray-600">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Enhanced CTA Section */}
        <section className="relative overflow-hidden rounded-3xl">
          <div className="absolute inset-0 z-0">
            <img
              src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
              alt="Retail store ready for virtual tour services"
              className="w-full h-full object-cover object-center"
            />
            <div className="absolute inset-0 bg-gradient-to-br from-theme-primary/90 via-theme-primary/70 to-theme-primary/90"></div>
          </div>

          <div className="relative z-10 p-8 md:p-12 text-center">
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 drop-shadow-lg">
              Ready to Get Started?
            </h2>
            <p className="text-xl text-white/90 mb-8 max-w-2xl mx-auto drop-shadow-md">
              Contact us today for a free consultation and quote. Let's bring your space to life with immersive virtual tours.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="text-lg px-8 py-4 bg-white text-theme-primary hover:bg-gray-100 shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105" asChild>
                <Link to="/contact">Get Free Quote</Link>
              </Button>
              <Button size="lg" className="text-lg px-8 py-4 bg-transparent border-2 border-white text-white hover:bg-white hover:text-theme-primary shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105" asChild>
                <Link to="/showcase">View Sample Tours</Link>
              </Button>
            </div>
          </div>
        </section>
      </div>

      <Footer />
    </div>
  );
};

export default Services;
