/**
 * Lightning-Fast Performance Optimizations
 * Ultra-optimized utilities for maximum speed
 */

// Memoization cache with automatic cleanup
const memoCache = new Map<string, { value: any; timestamp: number; ttl: number }>();
const CACHE_CLEANUP_INTERVAL = 60000; // 1 minute

// Cleanup expired cache entries
setInterval(() => {
  const now = Date.now();
  for (const [key, entry] of memoCache.entries()) {
    if (now - entry.timestamp > entry.ttl) {
      memoCache.delete(key);
    }
  }
}, CACHE_CLEANUP_INTERVAL);

/**
 * Ultra-fast memoization with TTL
 */
export const fastMemo = <T extends (...args: any[]) => any>(
  fn: T,
  ttl: number = 300000 // 5 minutes default
): T => {
  return ((...args: Parameters<T>): ReturnType<T> => {
    const key = JSON.stringify(args);
    const cached = memoCache.get(key);
    
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.value;
    }
    
    const result = fn(...args);
    memoCache.set(key, { value: result, timestamp: Date.now(), ttl });
    return result;
  }) as T;
};

/**
 * Batch DOM operations for better performance
 */
export const batchDOMOperations = (operations: (() => void)[]) => {
  requestAnimationFrame(() => {
    operations.forEach(op => op());
  });
};

/**
 * Optimized event listener with automatic cleanup
 */
export const optimizedEventListener = (
  element: HTMLElement,
  event: string,
  handler: EventListener,
  options?: AddEventListenerOptions
) => {
  const optimizedHandler = fastMemo(handler, 100); // Cache for 100ms
  element.addEventListener(event, optimizedHandler, options);
  
  return () => {
    element.removeEventListener(event, optimizedHandler, options);
  };
};

/**
 * Preload critical resources
 */
export const preloadCriticalResources = (resources: string[]) => {
  resources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource;
    
    if (resource.includes('.css')) {
      link.as = 'style';
    } else if (resource.includes('.js')) {
      link.as = 'script';
    } else if (resource.match(/\.(jpg|jpeg|png|webp|avif)$/)) {
      link.as = 'image';
    } else {
      link.as = 'fetch';
      link.crossOrigin = 'anonymous';
    }
    
    document.head.appendChild(link);
  });
};

/**
 * Optimized image loading with WebP/AVIF support
 */
export const optimizedImageLoader = (src: string, options?: {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'avif' | 'auto';
}) => {
  return new Promise<HTMLImageElement>((resolve, reject) => {
    const img = new Image();
    
    // Add optimization parameters
    let optimizedSrc = src;
    if (options) {
      const url = new URL(src, window.location.origin);
      if (options.width) url.searchParams.set('w', options.width.toString());
      if (options.height) url.searchParams.set('h', options.height.toString());
      if (options.quality) url.searchParams.set('q', options.quality.toString());
      if (options.format) url.searchParams.set('fm', options.format);
      optimizedSrc = url.toString();
    }
    
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = optimizedSrc;
  });
};

/**
 * Virtual scrolling for large lists
 */
export class VirtualScroller {
  private container: HTMLElement;
  private itemHeight: number;
  private visibleCount: number;
  private startIndex = 0;
  private endIndex = 0;
  
  constructor(container: HTMLElement, itemHeight: number) {
    this.container = container;
    this.itemHeight = itemHeight;
    this.visibleCount = Math.ceil(container.clientHeight / itemHeight) + 2; // Buffer
  }
  
  updateVisibleRange(scrollTop: number) {
    this.startIndex = Math.floor(scrollTop / this.itemHeight);
    this.endIndex = Math.min(this.startIndex + this.visibleCount, this.getTotalItems());
  }
  
  getVisibleItems<T>(items: T[]): { items: T[]; startIndex: number; endIndex: number } {
    return {
      items: items.slice(this.startIndex, this.endIndex),
      startIndex: this.startIndex,
      endIndex: this.endIndex
    };
  }
  
  private getTotalItems(): number {
    return Math.floor(this.container.scrollHeight / this.itemHeight);
  }
}

/**
 * Optimized API request with caching and deduplication
 */
const requestCache = new Map<string, Promise<any>>();

export const optimizedFetch = async <T>(
  url: string,
  options?: RequestInit & { ttl?: number }
): Promise<T> => {
  const cacheKey = `${url}-${JSON.stringify(options)}`;
  
  // Return existing request if in progress
  if (requestCache.has(cacheKey)) {
    return requestCache.get(cacheKey);
  }
  
  const request = fetch(url, options)
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return response.json();
    })
    .finally(() => {
      // Remove from cache after completion
      setTimeout(() => {
        requestCache.delete(cacheKey);
      }, options?.ttl || 5000);
    });
  
  requestCache.set(cacheKey, request);
  return request;
};

/**
 * Optimized component state updates
 */
export const batchStateUpdates = (updates: (() => void)[]) => {
  // Use React's unstable_batchedUpdates if available
  if (typeof (window as any).React?.unstable_batchedUpdates === 'function') {
    (window as any).React.unstable_batchedUpdates(() => {
      updates.forEach(update => update());
    });
  } else {
    // Fallback to requestAnimationFrame
    requestAnimationFrame(() => {
      updates.forEach(update => update());
    });
  }
};

/**
 * Memory-efficient object pooling
 */
export class ObjectPool<T> {
  private pool: T[] = [];
  private createFn: () => T;
  private resetFn: (obj: T) => void;
  
  constructor(createFn: () => T, resetFn: (obj: T) => void, initialSize = 10) {
    this.createFn = createFn;
    this.resetFn = resetFn;
    
    // Pre-populate pool
    for (let i = 0; i < initialSize; i++) {
      this.pool.push(createFn());
    }
  }
  
  acquire(): T {
    return this.pool.pop() || this.createFn();
  }
  
  release(obj: T): void {
    this.resetFn(obj);
    this.pool.push(obj);
  }
}

/**
 * Optimized CSS-in-JS with caching
 */
const styleCache = new Map<string, HTMLStyleElement>();

export const injectOptimizedCSS = (css: string, id?: string) => {
  const cacheKey = id || css;
  
  if (styleCache.has(cacheKey)) {
    return styleCache.get(cacheKey)!;
  }
  
  const style = document.createElement('style');
  style.textContent = css;
  if (id) style.id = id;
  
  document.head.appendChild(style);
  styleCache.set(cacheKey, style);
  
  return style;
};

/**
 * Performance monitoring with automatic reporting
 */
export const performanceMonitor = {
  marks: new Map<string, number>(),
  
  start(name: string) {
    this.marks.set(name, performance.now());
    performance.mark(`${name}-start`);
  },
  
  end(name: string) {
    const startTime = this.marks.get(name);
    if (startTime) {
      const duration = performance.now() - startTime;
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);
      
      // Auto-report slow operations
      if (duration > 100) {
        console.warn(`⚠️ Slow operation: ${name} took ${duration.toFixed(2)}ms`);
      }
      
      this.marks.delete(name);
      return duration;
    }
    return 0;
  },
  
  getMetrics() {
    return performance.getEntriesByType('measure');
  }
};

/**
 * Optimized scroll handling
 */
export const optimizedScrollHandler = (
  callback: (scrollTop: number, scrollDirection: 'up' | 'down') => void,
  throttleMs = 16 // 60fps
) => {
  let lastScrollTop = 0;
  let ticking = false;
  
  return (event: Event) => {
    const scrollTop = (event.target as HTMLElement).scrollTop;
    
    if (!ticking) {
      requestAnimationFrame(() => {
        const direction = scrollTop > lastScrollTop ? 'down' : 'up';
        callback(scrollTop, direction);
        lastScrollTop = scrollTop;
        ticking = false;
      });
      ticking = true;
    }
  };
};
