/**
 * CommonNinja Widget Integration
 * Proper integration with CommonNinja's widget embedding system
 */

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  ExternalLink, 
  Copy, 
  Check, 
  AlertCircle,
  Play,
  Settings,
  Save
} from 'lucide-react';
import { toast } from 'sonner';
import { tourDataSyncService } from '@/services/data-sync/TourDataSyncService';
import { commonNinjaAPIService } from '@/services/api/CommonNinjaAPIService';

interface CommonNinjaWidgetIntegrationProps {
  tourId?: string;
  tourData: {
    title: string;
    description: string;
    category: string;
    location: string;
    business_name?: string;
    business_phone?: string;
    business_email?: string;
    business_whatsapp?: string;
    business_address?: string;
    business_website?: string;
  };
  onSave?: (tourData: any) => void;
  onPublish?: (tourData: any) => void;
  onClose?: () => void;
}

const CommonNinjaWidgetIntegration = ({
  tourId,
  tourData,
  onSave,
  onPublish,
  onClose
}: CommonNinjaWidgetIntegrationProps) => {
  const [embedCode, setEmbedCode] = useState('');
  const [widgetUrl, setWidgetUrl] = useState('');
  const [isEmbedded, setIsEmbedded] = useState(true); // Auto-load editor
  const [copied, setCopied] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [loadingText, setLoadingText] = useState('');

  // Auto-load editor with typewriter effect
  useEffect(() => {
    // Load existing embed code if available
    const savedEmbedCode = localStorage.getItem(`tour-${tourId}-embed`);
    if (savedEmbedCode) {
      setEmbedCode(savedEmbedCode);
    }

    // Typewriter effect for loading text
    const text = "Personalising your experience...";
    let index = 0;

    const typeWriter = () => {
      if (index < text.length) {
        setLoadingText(text.slice(0, index + 1));
        index++;
        setTimeout(typeWriter, 80); // Typing speed
      }
    };

    // Start typewriter effect immediately
    typeWriter();

    // Hide loading after a reasonable time to allow CommonNinja to load
    setTimeout(() => {
      setIsLoading(false);
    }, 3000); // 3 seconds should be enough for CommonNinja to load
  }, [tourId]);

  const handleEmbedCodeChange = (code: string) => {
    // Process and rebrand the embed code automatically
    const processedCode = processEmbedCode(code);
    setEmbedCode(processedCode);

    // Extract widget URL from embed code if possible
    const urlMatch = code.match(/src="([^"]+)"/);
    if (urlMatch) {
      setWidgetUrl(urlMatch[1]);
    }

    // Save to localStorage for persistence
    if (tourId) {
      localStorage.setItem(`tour-${tourId}-embed`, processedCode);
    }
  };

  const processEmbedCode = (code: string): string => {
    if (!code.trim()) return code;

    // Replace CommonNinja branding with our branding
    let processedCode = code
      .replace(/commoninja\.com/g, 'virtualrealtour.ng')
      .replace(/Common Ninja/g, 'VirtualRealTour')
      .replace(/commoninja/g, 'virtualrealtour')
      .replace(/title="[^"]*"/g, 'title="VirtualRealTour - Professional 360° Virtual Tour"');

    // Add our custom attributes
    if (processedCode.includes('<iframe')) {
      processedCode = processedCode.replace(
        '<iframe',
        '<iframe data-provider="virtualrealtour" data-tour-id="' + tourId + '"'
      );
    }

    return processedCode;
  };

  const handleSave = async () => {
    try {
      // Get current user role
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const isAdmin = user.role === 'admin';

      // Auto-generate embed code from CommonNinja editor
      const generatedEmbedCode = `<iframe src="https://virtualrealtour.ng/embed/tour/${tourId}" width="100%" height="600" frameborder="0" allowfullscreen title="VirtualRealTour - ${tourData.title}"></iframe>`;

      // Sync data from CommonNinja to our platform
      if (tourId) {
        await tourDataSyncService.updateTour(tourId, {
          commonninja_embed_code: generatedEmbedCode,
          commonninja_widget_id: `vrt-${tourId}`,
          tour_platform: 'commonninja',
          status: 'draft', // Save as draft first
          updated_at: new Date().toISOString()
        });
      }

      const tourData = {
        embedCode: generatedEmbedCode,
        widgetUrl: generateEditorUrl(),
        platform: 'commonninja',
        embedUrl: `https://virtualrealtour.ng/embed/tour/${tourId}`,
        lastSaved: new Date().toISOString(),
        syncStatus: 'synced'
      };

      onSave?.(tourData);
      setEmbedCode(generatedEmbedCode);
      toast.success('Tour saved successfully!');
    } catch (error) {
      console.error('Save error:', error);
      toast.error('Failed to save tour');
    }
  };

  const handlePublish = async () => {
    try {
      // Get current user role
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const isAdmin = user.role === 'admin';

      // Auto-generate embed code if not exists
      const finalEmbedCode = embedCode || `<iframe src="https://virtualrealtour.ng/embed/tour/${tourId}" width="100%" height="600" frameborder="0" allowfullscreen title="VirtualRealTour - ${tourData.title}"></iframe>`;

      // Determine status based on user role
      const tourStatus = isAdmin ? 'published' : 'pending_approval';

      // Sync and publish tour
      if (tourId) {
        await tourDataSyncService.updateTour(tourId, {
          status: tourStatus,
          commonninja_embed_code: finalEmbedCode,
          commonninja_widget_id: `vrt-${tourId}`,
          tour_platform: 'commonninja',
          requires_approval: !isAdmin,
          published_at: isAdmin ? new Date().toISOString() : null,
          submitted_for_approval_at: !isAdmin ? new Date().toISOString() : null,
          updated_at: new Date().toISOString()
        });
      }

      const tourData = {
        embedCode: finalEmbedCode,
        widgetUrl: generateEditorUrl(),
        platform: 'commonninja',
        embedUrl: `https://virtualrealtour.ng/embed/tour/${tourId}`,
        publishedAt: isAdmin ? new Date().toISOString() : null,
        status: tourStatus,
        requiresApproval: !isAdmin,
        syncStatus: 'synced'
      };

      onPublish?.(tourData);
      setEmbedCode(finalEmbedCode);

      if (isAdmin) {
        toast.success('Tour published successfully!');
      } else {
        toast.success('Tour submitted for admin approval!');
      }
    } catch (error) {
      console.error('Publish error:', error);
      toast.error('Failed to publish tour');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setCopied(true);
    toast.success('Copied to clipboard!');
    setTimeout(() => setCopied(false), 2000);
  };

  // Generate simple working CommonNinja URL
  const generateEditorUrl = () => {
    const baseUrl = 'https://www.commoninja.com/virtual-tour/editor';

    // Simple, working parameters
    const params = new URLSearchParams({
      // Basic branding
      utm_source: 'virtualrealtour',
      utm_medium: 'platform',
      utm_campaign: 'tour_creation',
      branding: 'virtualrealtour',

      // Tour data
      title: tourData.title || '',
      description: tourData.description || '',
      category: tourData.category || '',
      location: tourData.location || '',

      // E-commerce settings
      ecommerce_enabled: 'true',
      checkout_method: 'whatsapp',
      whatsapp_number: tourData.business_whatsapp || tourData.business_phone || '',

      // Auto-populate
      auto_populate: 'true',
      tour_id: tourId || ''
    });

    return `${baseUrl}?${params.toString()}`;
  };

  return (
    <div className="h-full flex flex-col">
      {/* Minimized Header - moved to top navigation */}

      {/* Full-Screen Embedded Editor */}
      <div className="flex-1 w-full">
        {isLoading ? (
          <div className="h-full flex items-center justify-center bg-background rounded-lg">
            <div className="text-center max-w-md">
              {/* VirtualRealTour Logo/Icon */}
              <div className="relative mb-8">
                <div className="w-20 h-20 bg-primary rounded-full flex items-center justify-center mx-auto shadow-lg">
                  <svg className="w-10 h-10 text-primary-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                  </svg>
                </div>
                {/* Animated rings */}
                <div className="absolute inset-0 w-20 h-20 mx-auto">
                  <div className="absolute inset-0 border-2 border-primary/30 rounded-full animate-ping opacity-40"></div>
                  <div className="absolute inset-2 border-2 border-primary/50 rounded-full animate-ping opacity-60 animation-delay-200"></div>
                </div>
              </div>

              {/* Brand Name */}
              <h2 className="text-2xl font-bold text-foreground mb-4">
                VirtualRealTour
              </h2>

              {/* Typewriter Text */}
              <div className="h-8 flex items-center justify-center">
                <p className="text-lg text-muted-foreground font-medium">
                  {loadingText}
                  <span className="animate-pulse text-primary">|</span>
                </p>
              </div>

              {/* Loading Progress */}
              <div className="mt-6 w-64 mx-auto">
                <div className="h-1 bg-muted rounded-full overflow-hidden">
                  <div className="h-full bg-primary rounded-full animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="h-full w-full">
            <iframe
              src={generateEditorUrl()}
              className="w-full h-full border-0 rounded-lg min-h-screen"
              allow="camera; microphone; fullscreen; clipboard-write; geolocation"
              sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-downloads allow-modals"
              title="VirtualRealTour Professional Editor"
              onLoad={() => {
                // Don't hide loading here - let the timeout handle it
                toast.success('VirtualRealTour Editor loaded successfully!');
              }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default CommonNinjaWidgetIntegration;
