
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Database, ExternalLink, CheckCircle } from 'lucide-react'

export const SupabaseBanner = () => {
  // Since we have the project configured, show a success message
  return (
    <Alert className="mb-6 bg-green-50 border-green-200">
      <CheckCircle className="h-4 w-4 text-green-600" />
      <AlertDescription className="flex items-center justify-between">
        <div>
          <strong className="text-green-800">Supabase Connected Successfully!</strong>
          <p className="text-sm text-green-700 mt-1">
            Your database is set up and ready. Authentication, tour management, and file storage are fully functional.
          </p>
        </div>
        <Button size="sm" variant="outline" asChild className="border-green-300 text-green-700 hover:bg-green-100">
          <a href="https://supabase.com/dashboard/project/dnbjrfgfugpmyrconepx" target="_blank" rel="noopener noreferrer">
            <Database className="w-4 h-4 mr-2" />
            View Dashboard
          </a>
        </Button>
      </AlertDescription>
    </Alert>
  )
}
