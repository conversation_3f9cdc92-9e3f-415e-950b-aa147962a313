/**
 * Admin Overlay Studio
 * Create and customize hotspots, icons, animations, and product overlays
 */

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { useResponsiveDesign } from '@/hooks/useResponsiveDesign';
import {
  Sparkles,
  Plus,
  Eye,
  Edit,
  Save,
  Download,
  Upload,
  Palette,
  Zap,
  Target,
  Image as ImageIcon,
  ShoppingCart,
  MessageCircle,
  ExternalLink,
  Play,
  Pause,
  Link
} from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';

const AdminOverlays = () => {
  const [activeTab, setActiveTab] = useState('hotspots');
  const [selectedIcon, setSelectedIcon] = useState('default');
  const [animationType, setAnimationType] = useState('pulse');
  const [overlayOpacity, setOverlayOpacity] = useState([80]);
  const [hotspotSize, setHotspotSize] = useState([24]);

  // Enhanced responsive design
  const {
    isMobile,
    isTablet,
    getTouchTargetSize,
    getHoverClasses,
    getGridColumns,
    getContainerClasses
  } = useResponsiveDesign();

  // Enhanced Hotspot Library (TourMkr Inspired)
  const iconLibrary = [
    {
      id: 'product',
      name: 'Product Card',
      icon: ShoppingCart,
      color: 'text-green-600',
      description: 'WooCommerce product with buy button',
      features: ['Price display', 'Add to cart', 'WhatsApp checkout']
    },
    {
      id: 'info',
      name: 'Information',
      icon: MessageCircle,
      color: 'text-blue-600',
      description: 'Rich text content with formatting',
      features: ['Rich editor', 'Images', 'Custom styling']
    },
    {
      id: 'video',
      name: 'Video Player',
      icon: Play,
      color: 'text-purple-600',
      description: 'Embedded video content',
      features: ['YouTube/Vimeo', 'Custom video', 'Controls']
    },
    {
      id: 'link',
      name: 'External Link',
      icon: ExternalLink,
      color: 'text-orange-600',
      description: 'Link to external websites',
      features: ['Custom URL', 'New tab', 'Click tracking']
    },
    {
      id: 'gallery',
      name: 'Image Gallery',
      icon: ImageIcon,
      color: 'text-pink-600',
      description: 'Photo gallery with lightbox',
      features: ['Multiple images', 'Lightbox', 'Captions']
    },
    {
      id: 'scene',
      name: 'Scene Jump',
      icon: Target,
      color: 'text-cyan-600',
      description: 'Navigate to another scene',
      features: ['Scene selection', 'Transitions', 'Preview']
    },
    {
      id: 'text',
      name: 'Text Overlay',
      icon: Edit,
      color: 'text-gray-600',
      description: 'Simple text content',
      features: ['Custom fonts', 'Colors', 'Animations']
    },
    {
      id: 'sparkle',
      name: 'Special Effect',
      icon: Sparkles,
      color: 'text-yellow-600',
      description: 'Animated special effects',
      features: ['Particle effects', 'Glow', 'Animations']
    }
  ];

  // Enhanced Animation Presets (TourMkr Style)
  const animationTypes = [
    {
      id: 'pulse',
      name: 'Pulse',
      description: 'Gentle pulsing effect',
      class: 'animate-pulse',
      intensity: 'subtle'
    },
    {
      id: 'bounce',
      name: 'Bounce',
      description: 'Bouncing animation',
      class: 'animate-bounce',
      intensity: 'medium'
    },
    {
      id: 'ping',
      name: 'Ping',
      description: 'Radar-like ping effect',
      class: 'animate-ping',
      intensity: 'attention'
    },
    {
      id: 'glow',
      name: 'Glow',
      description: 'Glowing effect',
      class: 'shadow-lg shadow-blue-500/50',
      intensity: 'subtle'
    },
    {
      id: 'scale',
      name: 'Scale Hover',
      description: 'Scale up on hover',
      class: 'hover:scale-110 transition-transform duration-300',
      intensity: 'interactive'
    },
    {
      id: 'fade',
      name: 'Fade Hover',
      description: 'Fade effect on hover',
      class: 'hover:opacity-100 opacity-70 transition-opacity',
      intensity: 'subtle'
    },
    {
      id: 'spin',
      name: 'Spin',
      description: 'Rotating animation',
      class: 'animate-spin',
      intensity: 'high'
    },
    {
      id: 'none',
      name: 'Static',
      description: 'No animation',
      class: '',
      intensity: 'none'
    }
  ];

  // Enhanced Popup Style Templates (TourMkr Inspired)
  const overlayTemplates = [
    {
      id: 'product-card',
      name: 'Product Card',
      description: 'Standard product display with image, title, price',
      preview: 'bg-white border rounded-lg p-4 shadow-lg',
      features: ['Product image', 'Title & price', 'Add to cart', 'WhatsApp buy'],
      size: 'medium'
    },
    {
      id: 'product-modal',
      name: 'Product Modal',
      description: 'Full-screen product details with gallery',
      preview: 'bg-white border rounded-lg p-6 shadow-xl',
      features: ['Image gallery', 'Full description', 'Variants', 'Reviews'],
      size: 'large'
    },
    {
      id: 'info-tooltip',
      name: 'Info Tooltip',
      description: 'Small information popup',
      preview: 'bg-black text-white rounded px-3 py-2 text-sm',
      features: ['Quick info', 'Minimal design', 'Auto-position'],
      size: 'small'
    },
    {
      id: 'glass-overlay',
      name: 'Glass Effect',
      description: 'Transparent glass-style overlay',
      preview: 'bg-white/20 backdrop-blur-md border border-white/30 rounded-lg p-4',
      features: ['Backdrop blur', 'Transparency', 'Modern look'],
      size: 'medium'
    },
    {
      id: 'video-player',
      name: 'Video Player',
      description: 'Embedded video with controls',
      preview: 'bg-black rounded-lg p-2 shadow-2xl',
      features: ['Video embed', 'Custom controls', 'Fullscreen'],
      size: 'large'
    },
    {
      id: 'image-gallery',
      name: 'Image Gallery',
      description: 'Photo gallery with lightbox',
      preview: 'bg-white rounded-lg p-3 shadow-lg border',
      features: ['Multiple images', 'Lightbox view', 'Navigation'],
      size: 'medium'
    },
    {
      id: 'contact-form',
      name: 'Contact Form',
      description: 'Lead capture form',
      preview: 'bg-gradient-to-br from-blue-50 to-white rounded-lg p-4 border',
      features: ['Custom fields', 'Validation', 'Email integration'],
      size: 'medium'
    },
    {
      id: 'minimal-card',
      name: 'Minimal Card',
      description: 'Clean minimal design',
      preview: 'bg-gray-50 border-l-4 border-blue-500 p-4 shadow-sm',
      features: ['Clean design', 'Accent border', 'Typography focus'],
      size: 'small'
    }
  ];

  return (
    <AdminLayout>
      <div className={`space-y-6 ${getContainerClasses()}`}>
        {/* Enhanced Mobile-Responsive Header */}
        <div className="flex flex-col gap-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <h1 className={`font-bold ${isMobile ? 'text-xl' : 'text-2xl md:text-3xl'}`}>
                Overlay Studio
              </h1>
              <p className={`text-muted-foreground ${isMobile ? 'text-sm' : ''}`}>
                Create and customize hotspots, icons, animations, and product overlays
              </p>
            </div>

            <div className={`flex gap-2 ${isMobile ? 'flex-col' : 'flex-row'}`}>
              <Button
                variant="outline"
                size={isMobile ? "sm" : "default"}
                className={getTouchTargetSize()}
              >
                <Download className="w-4 h-4 mr-2" />
                {isMobile ? 'Export' : 'Export Templates'}
              </Button>
              <Button
                size={isMobile ? "sm" : "default"}
                className={getTouchTargetSize()}
              >
                <Plus className="w-4 h-4 mr-2" />
                {isMobile ? 'Create' : 'Create Template'}
              </Button>
            </div>
          </div>

          {/* Mobile-specific quick stats */}
          {isMobile && (
            <div className="flex gap-2 overflow-x-auto pb-2">
              <Badge variant="secondary" className="whitespace-nowrap">8 Hotspot Types</Badge>
              <Badge variant="secondary" className="whitespace-nowrap">8 Animations</Badge>
              <Badge variant="secondary" className="whitespace-nowrap">8 Templates</Badge>
            </div>
          )}
        </div>

        {/* Enhanced Mobile-Responsive Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className={`
            grid w-full
            ${isMobile ? 'grid-cols-2' : 'grid-cols-2 lg:grid-cols-4'}
            ${isMobile ? 'gap-1' : 'gap-2'}
          `}>
            <TabsTrigger
              value="hotspots"
              className={`flex items-center gap-2 ${getTouchTargetSize()}`}
            >
              <Target className="w-4 h-4" />
              <span className={isMobile ? 'text-xs' : 'hidden sm:inline'}>
                {isMobile ? 'Spots' : 'Hotspots'}
              </span>
            </TabsTrigger>
            <TabsTrigger
              value="icons"
              className={`flex items-center gap-2 ${getTouchTargetSize()}`}
            >
              <Palette className="w-4 h-4" />
              <span className={isMobile ? 'text-xs' : 'hidden sm:inline'}>
                {isMobile ? 'Icons' : 'Icons'}
              </span>
            </TabsTrigger>
            <TabsTrigger
              value="animations"
              className={`flex items-center gap-2 ${getTouchTargetSize()}`}
            >
              <Zap className="w-4 h-4" />
              <span className={isMobile ? 'text-xs' : 'hidden sm:inline'}>
                {isMobile ? 'Anim' : 'Animations'}
              </span>
            </TabsTrigger>
            <TabsTrigger
              value="templates"
              className={`flex items-center gap-2 ${getTouchTargetSize()}`}
            >
              <Sparkles className="w-4 h-4" />
              <span className={isMobile ? 'text-xs' : 'hidden sm:inline'}>
                {isMobile ? 'Temp' : 'Templates'}
              </span>
            </TabsTrigger>
          </TabsList>

          {/* Enhanced Mobile-Responsive Hotspot Designer */}
          <TabsContent value="hotspots" className="space-y-6">
            <div className={`
              grid gap-6
              ${isMobile ? 'grid-cols-1' : 'grid-cols-1 lg:grid-cols-2'}
            `}>
              {/* Hotspot Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Hotspot Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Enhanced Mobile-Responsive Hotspot Type Selection */}
                  <div className="space-y-3">
                    <Label>Hotspot Type</Label>
                    <div className={`
                      grid gap-3
                      ${isMobile ? 'grid-cols-1' : 'grid-cols-2'}
                    `}>
                      {iconLibrary.map((icon) => {
                        const Icon = icon.icon;
                        return (
                          <button
                            key={icon.id}
                            type="button"
                            onClick={() => setSelectedIcon(icon.id)}
                            className={`
                              p-3 rounded-lg border-2 transition-all duration-200 text-left
                              ${getTouchTargetSize()}
                              ${getHoverClasses('hover:border-primary/50 hover:shadow-sm')}
                              ${isTouch ? 'active:scale-95' : ''}
                              ${selectedIcon === icon.id
                                ? 'border-primary bg-primary/10 shadow-md'
                                : 'border-border'
                              }
                            `}
                          >
                            <div className={`flex items-start gap-3 ${isMobile ? 'flex-col' : ''}`}>
                              <Icon className={`w-5 h-5 mt-0.5 ${icon.color} ${isMobile ? 'mx-auto' : ''}`} />
                              <div className={`flex-1 min-w-0 ${isMobile ? 'text-center' : ''}`}>
                                <p className={`font-medium ${isMobile ? 'text-sm' : 'text-sm'}`}>
                                  {icon.name}
                                </p>
                                <p className={`text-xs text-muted-foreground mt-1 ${isMobile ? 'line-clamp-1' : 'line-clamp-2'}`}>
                                  {icon.description}
                                </p>
                                {icon.features && !isMobile && (
                                  <div className="flex flex-wrap gap-1 mt-2">
                                    {icon.features.slice(0, 2).map((feature, idx) => (
                                      <Badge key={idx} variant="secondary" className="text-xs px-1 py-0">
                                        {feature}
                                      </Badge>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </div>
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Size Control */}
                  <div className="space-y-3">
                    <Label>Hotspot Size: {hotspotSize[0]}px</Label>
                    <Slider
                      value={hotspotSize}
                      onValueChange={setHotspotSize}
                      max={48}
                      min={16}
                      step={2}
                      className="w-full"
                    />
                  </div>

                  {/* Enhanced Animation Selection */}
                  <div className="space-y-3">
                    <Label>Animation Style</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {animationTypes.map((animation) => (
                        <button
                          key={animation.id}
                          type="button"
                          onClick={() => setAnimationType(animation.id)}
                          className={`p-3 rounded-lg border text-left transition-all duration-200 ${
                            animationType === animation.id
                              ? 'border-primary bg-primary/10 shadow-md'
                              : 'border-border hover:border-primary/50 hover:shadow-sm'
                          }`}
                        >
                          <div className="flex items-center justify-between mb-1">
                            <p className="font-medium text-sm">{animation.name}</p>
                            <Badge
                              variant={
                                animation.intensity === 'high' ? 'destructive' :
                                animation.intensity === 'attention' ? 'default' :
                                animation.intensity === 'medium' ? 'secondary' :
                                'outline'
                              }
                              className="text-xs px-1 py-0"
                            >
                              {animation.intensity}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground">{animation.description}</p>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Color Customization */}
                  <div className="space-y-3">
                    <Label>Hotspot Color</Label>
                    <div className="flex gap-2">
                      {['bg-blue-600', 'bg-green-600', 'bg-purple-600', 'bg-orange-600', 'bg-pink-600', 'bg-yellow-600'].map((color) => (
                        <button
                          key={color}
                          className={`w-8 h-8 rounded-full ${color} border-2 border-white shadow-md hover:scale-110 transition-transform`}
                        />
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Live Preview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="w-5 h-5" />
                    Live Preview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg relative overflow-hidden">
                    {/* Sample 360° background */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-50"></div>
                    
                    {/* Preview Hotspot */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      <div 
                        className={`
                          flex items-center justify-center rounded-full bg-blue-600 text-white shadow-lg cursor-pointer
                          ${animationType === 'pulse' ? 'animate-pulse' : ''}
                          ${animationType === 'bounce' ? 'animate-bounce' : ''}
                          ${animationType === 'glow' ? 'shadow-blue-400/50 shadow-lg' : ''}
                        `}
                        style={{ 
                          width: `${hotspotSize[0]}px`, 
                          height: `${hotspotSize[0]}px`,
                          animation: animationType === 'rotate' ? 'spin 2s linear infinite' : undefined
                        }}
                      >
                        {(() => {
                          const selectedIconData = iconLibrary.find(icon => icon.id === selectedIcon);
                          const Icon = selectedIconData?.icon || Target;
                          return <Icon className="w-4 h-4" />;
                        })()}
                      </div>
                    </div>

                    {/* Preview Controls */}
                    <div className="absolute bottom-4 left-4 right-4 flex justify-center gap-2">
                      <Button size="sm" variant="secondary">
                        <Play className="w-4 h-4 mr-1" />
                        Preview
                      </Button>
                      <Button size="sm" variant="secondary">
                        <Save className="w-4 h-4 mr-1" />
                        Save
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Icon Library */}
          <TabsContent value="icons" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="w-5 h-5" />
                    Icon Library
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-4 gap-4">
                    {iconLibrary.map((icon) => {
                      const Icon = icon.icon;
                      return (
                        <div key={icon.id} className="text-center p-4 border rounded-lg hover:shadow-md transition-shadow">
                          <Icon className={`w-8 h-8 mx-auto mb-2 ${icon.color}`} />
                          <p className="text-sm font-medium">{icon.name}</p>
                        </div>
                      );
                    })}
                  </div>
                  <Button className="w-full mt-4" variant="outline">
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Custom Icons
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Custom Icon Upload</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                    <Upload className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-sm text-muted-foreground mb-2">
                      Drag and drop SVG icons here, or click to browse
                    </p>
                    <Button variant="outline">Browse Files</Button>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="icon-name">Icon Name</Label>
                    <Input id="icon-name" placeholder="Enter icon name" />
                  </div>
                  <Button className="w-full">
                    <Plus className="w-4 h-4 mr-2" />
                    Add to Library
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Animation Studio */}
          <TabsContent value="animations" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  Animation Studio
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {animationTypes.map((animation) => (
                    <Card key={animation.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="pt-6 text-center">
                        <div className="w-16 h-16 bg-blue-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                          <Target className="w-8 h-8 text-white" />
                        </div>
                        <h3 className="font-semibold mb-2">{animation.name}</h3>
                        <p className="text-sm text-muted-foreground mb-4">{animation.description}</p>
                        <Button variant="outline" size="sm">
                          <Play className="w-4 h-4 mr-1" />
                          Preview
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Overlay Templates */}
          <TabsContent value="templates" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  Overlay Templates
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {overlayTemplates.map((template) => (
                    <Card key={template.id} className="cursor-pointer hover:shadow-lg transition-all duration-200 border-2 hover:border-primary group">
                      <CardContent className="pt-6">
                        {/* Enhanced Template Preview */}
                        <div className={`h-32 mb-4 ${template.preview} flex items-center justify-center relative overflow-hidden rounded-lg`}>
                          <div className="absolute top-2 right-2">
                            <Badge variant="outline" className="text-xs bg-white/90">
                              {template.size}
                            </Badge>
                          </div>
                          {template.id === 'product-card' && (
                            <div className="w-full h-full p-3">
                              <div className="bg-white rounded-lg shadow-lg p-3 h-full">
                                <div className="w-full h-16 bg-gray-200 rounded mb-2"></div>
                                <div className="space-y-1">
                                  <div className="h-3 bg-gray-300 rounded w-3/4"></div>
                                  <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                                </div>
                              </div>
                            </div>
                          )}
                          {template.id === 'product-modal' && (
                            <div className="w-full h-full p-2">
                              <div className="bg-white rounded-lg shadow-xl p-2 h-full border">
                                <div className="flex gap-2 h-full">
                                  <div className="w-1/2 bg-gray-200 rounded"></div>
                                  <div className="w-1/2 space-y-1">
                                    <div className="h-2 bg-gray-300 rounded"></div>
                                    <div className="h-2 bg-gray-200 rounded w-3/4"></div>
                                    <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                          {template.id === 'info-tooltip' && (
                            <div className="bg-black text-white rounded px-3 py-2 text-xs">
                              <p>Info tooltip</p>
                            </div>
                          )}
                          {template.id === 'glass-overlay' && (
                            <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-lg p-4 text-center">
                              <p className="text-xs text-white">Glass Effect</p>
                            </div>
                          )}
                        </div>

                        {/* Enhanced Template Info */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <h3 className="font-semibold">{template.name}</h3>
                            <Badge variant="secondary" className="text-xs">
                              {template.size}
                            </Badge>
                          </div>

                          <p className="text-sm text-muted-foreground">{template.description}</p>

                          {/* Feature Tags */}
                          {template.features && (
                            <div className="flex flex-wrap gap-1">
                              {template.features.map((feature, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs px-2 py-0">
                                  {feature}
                                </Badge>
                              ))}
                            </div>
                          )}

                          <div className="flex gap-2 pt-2">
                            <Button variant="outline" size="sm" className="flex-1">
                              <Eye className="w-4 h-4 mr-1" />
                              Preview
                            </Button>
                            <Button size="sm" className="flex-1">
                              <Edit className="w-4 h-4 mr-1" />
                              Use Template
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Template Customization */}
            <Card>
              <CardHeader>
                <CardTitle>Template Customization</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-semibold">Overlay Settings</h4>
                    <div className="space-y-3">
                      <div className="space-y-2">
                        <Label>Background Opacity: {overlayOpacity[0]}%</Label>
                        <Slider
                          value={overlayOpacity}
                          onValueChange={setOverlayOpacity}
                          max={100}
                          min={0}
                          step={5}
                          className="w-full"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Border Radius</Label>
                        <Slider
                          defaultValue={[8]}
                          max={24}
                          min={0}
                          step={2}
                          className="w-full"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Shadow Intensity</Label>
                        <Slider
                          defaultValue={[50]}
                          max={100}
                          min={0}
                          step={10}
                          className="w-full"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-semibold">Live Preview</h4>
                    <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-50"></div>

                      {/* Sample Overlay */}
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div
                          className="bg-white rounded-lg shadow-lg p-4 max-w-xs"
                          style={{ opacity: overlayOpacity[0] / 100 }}
                        >
                          <div className="w-full h-20 bg-gray-200 rounded mb-3"></div>
                          <h4 className="font-semibold mb-1">Sample Product</h4>
                          <p className="text-sm text-gray-600 mb-2">Product description here</p>
                          <div className="flex items-center justify-between">
                            <span className="font-bold text-green-600">₦25,000</span>
                            <Button size="sm">Add to Cart</Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Export Options */}
            <Card>
              <CardHeader>
                <CardTitle>Export & Integration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button variant="outline" className="h-20 flex-col">
                    <Download className="w-6 h-6 mb-2" />
                    Export CSS
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <Upload className="w-6 h-6 mb-2" />
                    Export JSON
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <Link className="w-6 h-6 mb-2" />
                    Generate Embed
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminOverlays;
