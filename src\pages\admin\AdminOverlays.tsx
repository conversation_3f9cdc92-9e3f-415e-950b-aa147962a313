/**
 * Admin Overlay Studio
 * Create and customize hotspots, icons, animations, and product overlays
 */

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import {
  Sparkles,
  Plus,
  Eye,
  Edit,
  Save,
  Download,
  Upload,
  Palette,
  Zap,
  Target,
  Image as ImageIcon,
  ShoppingCart,
  MessageCircle,
  ExternalLink,
  Play,
  Pause,
  Link
} from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';

const AdminOverlays = () => {
  const [activeTab, setActiveTab] = useState('hotspots');
  const [selectedIcon, setSelectedIcon] = useState('default');
  const [animationType, setAnimationType] = useState('pulse');
  const [overlayOpacity, setOverlayOpacity] = useState([80]);
  const [hotspotSize, setHotspotSize] = useState([24]);

  const iconLibrary = [
    { id: 'default', name: 'Default', icon: Target, color: 'text-blue-600' },
    { id: 'shopping', name: 'Shopping', icon: ShoppingCart, color: 'text-green-600' },
    { id: 'info', name: 'Information', icon: MessageCircle, color: 'text-purple-600' },
    { id: 'link', name: 'External Link', icon: ExternalLink, color: 'text-orange-600' },
    { id: 'image', name: 'Image', icon: ImageIcon, color: 'text-pink-600' },
    { id: 'sparkle', name: 'Sparkle', icon: Sparkles, color: 'text-yellow-600' }
  ];

  const animationTypes = [
    { id: 'pulse', name: 'Pulse', description: 'Gentle pulsing effect' },
    { id: 'bounce', name: 'Bounce', description: 'Bouncing animation' },
    { id: 'glow', name: 'Glow', description: 'Glowing effect' },
    { id: 'rotate', name: 'Rotate', description: 'Rotating animation' },
    { id: 'scale', name: 'Scale', description: 'Scaling in/out' },
    { id: 'none', name: 'None', description: 'No animation' }
  ];

  const overlayTemplates = [
    {
      id: 'product-card',
      name: 'Product Card',
      description: 'Standard product display with image, title, price',
      preview: 'bg-white border rounded-lg p-4 shadow-lg'
    },
    {
      id: 'product-modal',
      name: 'Product Modal',
      description: 'Full-screen product details with gallery',
      preview: 'bg-white border rounded-lg p-6 shadow-xl'
    },
    {
      id: 'info-tooltip',
      name: 'Info Tooltip',
      description: 'Small information popup',
      preview: 'bg-black text-white rounded px-3 py-2 text-sm'
    },
    {
      id: 'glass-overlay',
      name: 'Glass Overlay',
      description: 'Transparent glass-style overlay',
      preview: 'bg-white/20 backdrop-blur-md border border-white/30 rounded-lg p-4'
    }
  ];

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Overlay Studio</h1>
            <p className="text-muted-foreground">
              Create and customize hotspots, icons, animations, and product overlays
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export Templates
            </Button>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Create Template
            </Button>
          </div>
        </div>

        {/* Overlay Studio Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
            <TabsTrigger value="hotspots" className="flex items-center gap-2">
              <Target className="w-4 h-4" />
              <span className="hidden sm:inline">Hotspots</span>
            </TabsTrigger>
            <TabsTrigger value="icons" className="flex items-center gap-2">
              <Palette className="w-4 h-4" />
              <span className="hidden sm:inline">Icons</span>
            </TabsTrigger>
            <TabsTrigger value="animations" className="flex items-center gap-2">
              <Zap className="w-4 h-4" />
              <span className="hidden sm:inline">Animations</span>
            </TabsTrigger>
            <TabsTrigger value="templates" className="flex items-center gap-2">
              <Sparkles className="w-4 h-4" />
              <span className="hidden sm:inline">Templates</span>
            </TabsTrigger>
          </TabsList>

          {/* Hotspot Designer */}
          <TabsContent value="hotspots" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Hotspot Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="w-5 h-5" />
                    Hotspot Configuration
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Icon Selection */}
                  <div className="space-y-3">
                    <Label>Hotspot Icon</Label>
                    <div className="grid grid-cols-3 gap-2">
                      {iconLibrary.map((icon) => {
                        const Icon = icon.icon;
                        return (
                          <button
                            key={icon.id}
                            onClick={() => setSelectedIcon(icon.id)}
                            className={`p-3 rounded-lg border-2 transition-colors ${
                              selectedIcon === icon.id
                                ? 'border-primary bg-primary/10'
                                : 'border-border hover:border-primary/50'
                            }`}
                          >
                            <Icon className={`w-6 h-6 mx-auto ${icon.color}`} />
                            <p className="text-xs mt-1">{icon.name}</p>
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Size Control */}
                  <div className="space-y-3">
                    <Label>Hotspot Size: {hotspotSize[0]}px</Label>
                    <Slider
                      value={hotspotSize}
                      onValueChange={setHotspotSize}
                      max={48}
                      min={16}
                      step={2}
                      className="w-full"
                    />
                  </div>

                  {/* Animation Selection */}
                  <div className="space-y-3">
                    <Label>Animation</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {animationTypes.map((animation) => (
                        <button
                          key={animation.id}
                          onClick={() => setAnimationType(animation.id)}
                          className={`p-3 rounded-lg border text-left transition-colors ${
                            animationType === animation.id
                              ? 'border-primary bg-primary/10'
                              : 'border-border hover:border-primary/50'
                          }`}
                        >
                          <p className="font-medium text-sm">{animation.name}</p>
                          <p className="text-xs text-muted-foreground">{animation.description}</p>
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Color Customization */}
                  <div className="space-y-3">
                    <Label>Hotspot Color</Label>
                    <div className="flex gap-2">
                      {['bg-blue-600', 'bg-green-600', 'bg-purple-600', 'bg-orange-600', 'bg-pink-600', 'bg-yellow-600'].map((color) => (
                        <button
                          key={color}
                          className={`w-8 h-8 rounded-full ${color} border-2 border-white shadow-md hover:scale-110 transition-transform`}
                        />
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Live Preview */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Eye className="w-5 h-5" />
                    Live Preview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg relative overflow-hidden">
                    {/* Sample 360° background */}
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-50"></div>
                    
                    {/* Preview Hotspot */}
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      <div 
                        className={`
                          flex items-center justify-center rounded-full bg-blue-600 text-white shadow-lg cursor-pointer
                          ${animationType === 'pulse' ? 'animate-pulse' : ''}
                          ${animationType === 'bounce' ? 'animate-bounce' : ''}
                          ${animationType === 'glow' ? 'shadow-blue-400/50 shadow-lg' : ''}
                        `}
                        style={{ 
                          width: `${hotspotSize[0]}px`, 
                          height: `${hotspotSize[0]}px`,
                          animation: animationType === 'rotate' ? 'spin 2s linear infinite' : undefined
                        }}
                      >
                        {(() => {
                          const selectedIconData = iconLibrary.find(icon => icon.id === selectedIcon);
                          const Icon = selectedIconData?.icon || Target;
                          return <Icon className="w-4 h-4" />;
                        })()}
                      </div>
                    </div>

                    {/* Preview Controls */}
                    <div className="absolute bottom-4 left-4 right-4 flex justify-center gap-2">
                      <Button size="sm" variant="secondary">
                        <Play className="w-4 h-4 mr-1" />
                        Preview
                      </Button>
                      <Button size="sm" variant="secondary">
                        <Save className="w-4 h-4 mr-1" />
                        Save
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Icon Library */}
          <TabsContent value="icons" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Palette className="w-5 h-5" />
                    Icon Library
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-4 gap-4">
                    {iconLibrary.map((icon) => {
                      const Icon = icon.icon;
                      return (
                        <div key={icon.id} className="text-center p-4 border rounded-lg hover:shadow-md transition-shadow">
                          <Icon className={`w-8 h-8 mx-auto mb-2 ${icon.color}`} />
                          <p className="text-sm font-medium">{icon.name}</p>
                        </div>
                      );
                    })}
                  </div>
                  <Button className="w-full mt-4" variant="outline">
                    <Upload className="w-4 h-4 mr-2" />
                    Upload Custom Icons
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Custom Icon Upload</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                    <Upload className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-sm text-muted-foreground mb-2">
                      Drag and drop SVG icons here, or click to browse
                    </p>
                    <Button variant="outline">Browse Files</Button>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="icon-name">Icon Name</Label>
                    <Input id="icon-name" placeholder="Enter icon name" />
                  </div>
                  <Button className="w-full">
                    <Plus className="w-4 h-4 mr-2" />
                    Add to Library
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Animation Studio */}
          <TabsContent value="animations" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="w-5 h-5" />
                  Animation Studio
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {animationTypes.map((animation) => (
                    <Card key={animation.id} className="cursor-pointer hover:shadow-md transition-shadow">
                      <CardContent className="pt-6 text-center">
                        <div className="w-16 h-16 bg-blue-600 rounded-full mx-auto mb-4 flex items-center justify-center">
                          <Target className="w-8 h-8 text-white" />
                        </div>
                        <h3 className="font-semibold mb-2">{animation.name}</h3>
                        <p className="text-sm text-muted-foreground mb-4">{animation.description}</p>
                        <Button variant="outline" size="sm">
                          <Play className="w-4 h-4 mr-1" />
                          Preview
                        </Button>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Overlay Templates */}
          <TabsContent value="templates" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="w-5 h-5" />
                  Overlay Templates
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {overlayTemplates.map((template) => (
                    <Card key={template.id} className="cursor-pointer hover:shadow-md transition-shadow border-2 hover:border-primary">
                      <CardContent className="pt-6">
                        <div className={`h-32 mb-4 ${template.preview} flex items-center justify-center relative overflow-hidden`}>
                          {template.id === 'product-card' && (
                            <div className="w-full h-full p-3">
                              <div className="bg-white rounded-lg shadow-lg p-3 h-full">
                                <div className="w-full h-16 bg-gray-200 rounded mb-2"></div>
                                <div className="space-y-1">
                                  <div className="h-3 bg-gray-300 rounded w-3/4"></div>
                                  <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                                </div>
                              </div>
                            </div>
                          )}
                          {template.id === 'product-modal' && (
                            <div className="w-full h-full p-2">
                              <div className="bg-white rounded-lg shadow-xl p-2 h-full border">
                                <div className="flex gap-2 h-full">
                                  <div className="w-1/2 bg-gray-200 rounded"></div>
                                  <div className="w-1/2 space-y-1">
                                    <div className="h-2 bg-gray-300 rounded"></div>
                                    <div className="h-2 bg-gray-200 rounded w-3/4"></div>
                                    <div className="h-2 bg-gray-200 rounded w-1/2"></div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          )}
                          {template.id === 'info-tooltip' && (
                            <div className="bg-black text-white rounded px-3 py-2 text-xs">
                              <p>Info tooltip</p>
                            </div>
                          )}
                          {template.id === 'glass-overlay' && (
                            <div className="bg-white/20 backdrop-blur-md border border-white/30 rounded-lg p-4 text-center">
                              <p className="text-xs text-white">Glass Effect</p>
                            </div>
                          )}
                        </div>
                        <h3 className="font-semibold mb-2">{template.name}</h3>
                        <p className="text-sm text-muted-foreground mb-4">{template.description}</p>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm" className="flex-1">
                            <Eye className="w-4 h-4 mr-1" />
                            Preview
                          </Button>
                          <Button size="sm" className="flex-1">
                            <Edit className="w-4 h-4 mr-1" />
                            Customize
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Template Customization */}
            <Card>
              <CardHeader>
                <CardTitle>Template Customization</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-semibold">Overlay Settings</h4>
                    <div className="space-y-3">
                      <div className="space-y-2">
                        <Label>Background Opacity: {overlayOpacity[0]}%</Label>
                        <Slider
                          value={overlayOpacity}
                          onValueChange={setOverlayOpacity}
                          max={100}
                          min={0}
                          step={5}
                          className="w-full"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Border Radius</Label>
                        <Slider
                          defaultValue={[8]}
                          max={24}
                          min={0}
                          step={2}
                          className="w-full"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Shadow Intensity</Label>
                        <Slider
                          defaultValue={[50]}
                          max={100}
                          min={0}
                          step={10}
                          className="w-full"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-semibold">Live Preview</h4>
                    <div className="aspect-video bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-blue-200 via-purple-200 to-pink-200 opacity-50"></div>

                      {/* Sample Overlay */}
                      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                        <div
                          className="bg-white rounded-lg shadow-lg p-4 max-w-xs"
                          style={{ opacity: overlayOpacity[0] / 100 }}
                        >
                          <div className="w-full h-20 bg-gray-200 rounded mb-3"></div>
                          <h4 className="font-semibold mb-1">Sample Product</h4>
                          <p className="text-sm text-gray-600 mb-2">Product description here</p>
                          <div className="flex items-center justify-between">
                            <span className="font-bold text-green-600">₦25,000</span>
                            <Button size="sm">Add to Cart</Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Export Options */}
            <Card>
              <CardHeader>
                <CardTitle>Export & Integration</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button variant="outline" className="h-20 flex-col">
                    <Download className="w-6 h-6 mb-2" />
                    Export CSS
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <Upload className="w-6 h-6 mb-2" />
                    Export JSON
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <Link className="w-6 h-6 mb-2" />
                    Generate Embed
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminOverlays;
