
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MapPin } from 'lucide-react';
import EnhancedTourCard from '@/components/EnhancedTourCard';
import { getTourCardSettings } from '@/config/tourCardSettings';
import { Tour } from '@/lib/supabase';

interface AdminPublishedToursProps {
  publishedTours: Tour[];
  onDeleteTour: (tour: Tour) => void;
  onToggleFeatured: (tour: Tour) => void;
}

const AdminPublishedTours = ({ publishedTours, onDeleteTour, onToggleFeatured }: AdminPublishedToursProps) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Published Tours</h2>
        <Badge variant="secondary" className="bg-green-100 text-green-800">
          {publishedTours.length} published
        </Badge>
      </div>
      
      {publishedTours.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <MapPin className="w-16 h-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No published tours</h3>
            <p className="text-gray-600">Tours will appear here once approved</p>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {publishedTours.map((tour) => {
            const settings = getTourCardSettings('adminPublished');
            return (
              <div key={tour.id} className="relative">
                <EnhancedTourCard
                  tour={tour}
                  showEmbedded={settings.showEmbedded}
                  showActions={settings.showActions}
                  autoLoad={settings.autoLoad}
                  onDelete={onDeleteTour}
                  className="hover-lift"
                />
                <div className="absolute top-2 right-2 z-40">
                  <Button
                    size="sm"
                    variant={tour.featured ? "default" : "outline"}
                    onClick={() => onToggleFeatured(tour)}
                    className={tour.featured ? "bg-yellow-500 hover:bg-yellow-600" : ""}
                  >
                    {tour.featured ? 'Featured' : 'Feature'}
                  </Button>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export default AdminPublishedTours;
