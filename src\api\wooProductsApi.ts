import axios from 'axios';

const WOO_API_BASE = import.meta.env.VITE_WOO_API_BASE || 'https://yourwpdomain.com/wp-json/wc/v3';
const WOO_JWT_TOKEN = import.meta.env.VITE_WOO_JWT_TOKEN;

const wooAPI = axios.create({
  baseURL: WOO_API_BASE,
  headers: WOO_JWT_TOKEN ? { Authorization: `Bearer ${WOO_JWT_TOKEN}` } : {},
});

export const fetchWooProducts = async () => {
  const res = await wooAPI.get('/products');
  return res.data;
};
