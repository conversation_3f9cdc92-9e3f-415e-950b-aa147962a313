/**
 * Tour Embed Page
 * Secure tour embedding page that wraps external tours
 */

import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase, Tour } from '@/lib/supabase';
import { generateSecureEmbedHtml, TourEmbedConfig } from '@/api/embed/tourEmbedApi';
import { Loader2, AlertCircle, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';

const TourEmbed = () => {
  const { tourId } = useParams<{ tourId: string }>();
  const [searchParams] = useSearchParams();
  const [embedHtml, setEmbedHtml] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string>('');

  // Parse embed configuration from URL parameters
  const embedConfig: TourEmbedConfig = {
    tour_id: tourId || '',
    platform: (searchParams.get('platform') as any) || 'custom',
    autoplay: searchParams.get('autoplay') === 'true',
    controls: searchParams.get('controls') !== 'false', // Default to true
    branding: searchParams.get('branding') === 'true',
    muted: searchParams.get('muted') === 'true'
  };

  // Fetch tour data
  const { data: tour, isLoading: isTourLoading, error: tourError } = useQuery({
    queryKey: ['tour-embed', tourId],
    queryFn: async () => {
      if (!tourId) throw new Error('Tour ID is required');
      
      const { data, error } = await supabase
        .from('tours')
        .select('*')
        .eq('id', tourId)
        .eq('status', 'published')
        .single();

      if (error) throw error;
      return data as Tour;
    },
    enabled: !!tourId
  });

  // Generate secure embed HTML
  useEffect(() => {
    const generateEmbed = async () => {
      if (!tour || !tourId) return;

      setIsGenerating(true);
      setError('');

      try {
        const html = await generateSecureEmbedHtml(embedConfig);
        setEmbedHtml(html);
      } catch (err) {
        console.error('Error generating embed:', err);
        setError(err instanceof Error ? err.message : 'Failed to generate embed');
      } finally {
        setIsGenerating(false);
      }
    };

    generateEmbed();
  }, [tour, tourId]);

  // Loading state
  if (isTourLoading || isGenerating) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <Loader2 className="w-12 h-12 animate-spin mx-auto mb-4" />
          <p className="text-lg">Loading virtual tour...</p>
          <p className="text-sm text-gray-400 mt-2">
            {isTourLoading ? 'Fetching tour data...' : 'Generating secure embed...'}
          </p>
        </div>
      </div>
    );
  }

  // Error state
  if (tourError || error || !tour) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white max-w-md mx-auto p-6">
          <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold mb-4">Tour Not Available</h1>
          <p className="text-gray-300 mb-6">
            {error || tourError?.message || 'The requested tour could not be found or is not published.'}
          </p>
          <div className="space-y-3">
            <Button 
              onClick={() => window.location.reload()} 
              className="w-full"
            >
              Try Again
            </Button>
            <Button 
              variant="outline" 
              onClick={() => window.location.href = '/'}
              className="w-full"
            >
              <ExternalLink className="w-4 h-4 mr-2" />
              Browse Tours
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Render secure embed
  if (embedHtml) {
    return (
      <div className="w-full h-screen">
        <iframe
          srcDoc={embedHtml}
          className="w-full h-full border-0"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
          allowFullScreen
          sandbox="allow-scripts allow-same-origin allow-presentation"
          title={`${tour.title} - Virtual Tour`}
        />
      </div>
    );
  }

  // Fallback loading state
  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="text-center text-white">
        <Loader2 className="w-12 h-12 animate-spin mx-auto mb-4" />
        <p className="text-lg">Preparing virtual tour...</p>
      </div>
    </div>
  );
};

export default TourEmbed;
