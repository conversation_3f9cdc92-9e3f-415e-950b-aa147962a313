/**
 * SEO Optimization Utility
 * Advanced SEO optimization for VirtualRealTour e-commerce platform
 * Optimized for Nigerian market and e-commerce search visibility
 */

export interface SEOMetadata {
  title: string;
  description: string;
  keywords: string[];
  canonicalUrl?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogType?: string;
  twitterCard?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  structuredData?: any;
  alternateUrls?: { [key: string]: string };
  robots?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
}

export interface ProductSEO extends SEOMetadata {
  productName: string;
  productDescription: string;
  price: number;
  currency: string;
  availability: string;
  brand: string;
  category: string;
  images: string[];
  reviews?: {
    rating: number;
    reviewCount: number;
  };
}

export interface TourSEO extends SEOMetadata {
  tourName: string;
  tourDescription: string;
  location: string;
  tourType: string;
  duration?: string;
  price?: number;
  images: string[];
}

class SEOOptimizer {
  private baseUrl: string;
  private siteName: string;
  private defaultImage: string;

  constructor() {
    this.baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://virtualrealtour.com';
    this.siteName = 'VirtualRealTour - Nigerian E-commerce Platform';
    this.defaultImage = `${this.baseUrl}/images/og-default.jpg`;
  }

  /**
   * Generate SEO metadata for homepage
   */
  generateHomepageSEO(): SEOMetadata {
    return {
      title: 'VirtualRealTour - Nigeria\'s Premier 360° E-commerce Platform',
      description: 'Discover amazing Nigerian products through immersive 360° virtual tours. Shop electronics, fashion, beauty products and more with WhatsApp checkout. Experience the future of online shopping in Nigeria.',
      keywords: [
        'virtual tour',
        'Nigeria e-commerce',
        '360 degree shopping',
        'Nigerian products',
        'WhatsApp shopping',
        'online marketplace Nigeria',
        'virtual reality shopping',
        'immersive shopping experience',
        'Lagos online shopping',
        'Abuja e-commerce',
        'Nigerian vendors',
        'African marketplace'
      ],
      canonicalUrl: this.baseUrl,
      ogTitle: 'VirtualRealTour - Experience Shopping Like Never Before',
      ogDescription: 'Nigeria\'s first 360° virtual tour e-commerce platform. Discover products through immersive experiences and shop with WhatsApp.',
      ogImage: this.defaultImage,
      ogType: 'website',
      twitterCard: 'summary_large_image',
      twitterTitle: 'VirtualRealTour - 360° Shopping Experience',
      twitterDescription: 'Discover Nigerian products through immersive virtual tours',
      twitterImage: this.defaultImage,
      structuredData: this.generateWebsiteStructuredData(),
      robots: 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1'
    };
  }

  /**
   * Generate SEO metadata for shop page
   */
  generateShopSEO(): SEOMetadata {
    return {
      title: 'Shop Nigerian Products - VirtualRealTour Marketplace',
      description: 'Browse thousands of authentic Nigerian products from verified vendors. Electronics, fashion, beauty, home goods and more. Fast delivery across Nigeria with WhatsApp checkout.',
      keywords: [
        'Nigerian products',
        'online shopping Nigeria',
        'buy Nigerian goods',
        'Lagos marketplace',
        'Abuja shopping',
        'Nigerian electronics',
        'African fashion',
        'Nigerian beauty products',
        'home goods Nigeria',
        'verified vendors Nigeria'
      ],
      canonicalUrl: `${this.baseUrl}/shop`,
      ogTitle: 'Shop Authentic Nigerian Products Online',
      ogDescription: 'Discover thousands of products from verified Nigerian vendors with fast delivery and WhatsApp checkout.',
      ogImage: `${this.baseUrl}/images/og-shop.jpg`,
      ogType: 'website',
      structuredData: this.generateShopStructuredData()
    };
  }

  /**
   * Generate SEO metadata for product pages
   */
  generateProductSEO(product: any): ProductSEO {
    const productUrl = `${this.baseUrl}/product/${product.id}`;
    const productImage = product.images?.[0] || this.defaultImage;

    return {
      title: `${product.title} - Buy Online in Nigeria | VirtualRealTour`,
      description: `${product.description} Available for ₦${product.price.toLocaleString()} with fast delivery across Nigeria. Shop now with WhatsApp checkout.`,
      keywords: [
        product.title.toLowerCase(),
        product.category?.toLowerCase(),
        'buy online Nigeria',
        'Nigerian marketplace',
        'WhatsApp shopping',
        product.vendor?.name?.toLowerCase(),
        ...product.tags || []
      ],
      canonicalUrl: productUrl,
      ogTitle: product.title,
      ogDescription: product.description,
      ogImage: productImage,
      ogType: 'product',
      twitterCard: 'summary_large_image',
      twitterTitle: product.title,
      twitterDescription: product.description,
      twitterImage: productImage,
      structuredData: this.generateProductStructuredData(product),
      productName: product.title,
      productDescription: product.description,
      price: product.price,
      currency: 'NGN',
      availability: product.inventory_quantity > 0 ? 'InStock' : 'OutOfStock',
      brand: product.vendor?.name || 'VirtualRealTour',
      category: product.category,
      images: product.images || [this.defaultImage]
    };
  }

  /**
   * Generate SEO metadata for tour pages
   */
  generateTourSEO(tour: any): TourSEO {
    const tourUrl = `${this.baseUrl}/tour/${tour.slug}`;
    const tourImage = tour.thumbnail_url || this.defaultImage;

    return {
      title: `${tour.title} - 360° Virtual Tour | VirtualRealTour`,
      description: `Experience ${tour.title} through our immersive 360° virtual tour. Explore every detail and discover products within the tour. ${tour.description}`,
      keywords: [
        tour.title.toLowerCase(),
        '360 virtual tour',
        'immersive experience',
        'virtual reality',
        tour.location?.toLowerCase(),
        'Nigeria virtual tour',
        'interactive tour',
        'VR experience'
      ],
      canonicalUrl: tourUrl,
      ogTitle: `360° Virtual Tour: ${tour.title}`,
      ogDescription: tour.description,
      ogImage: tourImage,
      ogType: 'article',
      twitterCard: 'summary_large_image',
      structuredData: this.generateTourStructuredData(tour),
      tourName: tour.title,
      tourDescription: tour.description,
      location: tour.location || 'Nigeria',
      tourType: '360° Virtual Tour',
      images: [tourImage]
    };
  }

  /**
   * Generate website structured data
   */
  private generateWebsiteStructuredData(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'WebSite',
      name: this.siteName,
      url: this.baseUrl,
      description: 'Nigeria\'s premier 360° virtual tour e-commerce platform',
      potentialAction: {
        '@type': 'SearchAction',
        target: `${this.baseUrl}/shop?q={search_term_string}`,
        'query-input': 'required name=search_term_string'
      },
      sameAs: [
        'https://facebook.com/virtualrealtour',
        'https://twitter.com/virtualrealtour',
        'https://instagram.com/virtualrealtour',
        'https://linkedin.com/company/virtualrealtour'
      ]
    };
  }

  /**
   * Generate shop structured data
   */
  private generateShopStructuredData(): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'Store',
      name: 'VirtualRealTour Marketplace',
      description: 'Nigerian e-commerce marketplace with 360° virtual tours',
      url: `${this.baseUrl}/shop`,
      address: {
        '@type': 'PostalAddress',
        addressCountry: 'NG',
        addressRegion: 'Lagos',
        addressLocality: 'Lagos'
      },
      geo: {
        '@type': 'GeoCoordinates',
        latitude: 6.5244,
        longitude: 3.3792
      },
      currenciesAccepted: 'NGN',
      paymentAccepted: 'WhatsApp, Bank Transfer, Cash on Delivery',
      priceRange: '₦₦'
    };
  }

  /**
   * Generate product structured data
   */
  private generateProductStructuredData(product: any): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'Product',
      name: product.title,
      description: product.description,
      image: product.images || [this.defaultImage],
      brand: {
        '@type': 'Brand',
        name: product.vendor?.name || 'VirtualRealTour'
      },
      category: product.category,
      offers: {
        '@type': 'Offer',
        price: product.price,
        priceCurrency: 'NGN',
        availability: product.inventory_quantity > 0 
          ? 'https://schema.org/InStock' 
          : 'https://schema.org/OutOfStock',
        seller: {
          '@type': 'Organization',
          name: product.vendor?.name || 'VirtualRealTour'
        },
        url: `${this.baseUrl}/product/${product.id}`
      },
      aggregateRating: product.reviews ? {
        '@type': 'AggregateRating',
        ratingValue: product.reviews.rating,
        reviewCount: product.reviews.reviewCount
      } : undefined
    };
  }

  /**
   * Generate tour structured data
   */
  private generateTourStructuredData(tour: any): any {
    return {
      '@context': 'https://schema.org',
      '@type': 'TouristAttraction',
      name: tour.title,
      description: tour.description,
      image: tour.thumbnail_url,
      url: `${this.baseUrl}/tour/${tour.slug}`,
      address: tour.location ? {
        '@type': 'PostalAddress',
        addressLocality: tour.location,
        addressCountry: 'NG'
      } : undefined,
      touristType: 'Virtual Reality Experience',
      isAccessibleForFree: true
    };
  }

  /**
   * Apply SEO metadata to document head
   */
  applySEOMetadata(metadata: SEOMetadata): void {
    if (typeof document === 'undefined') return;

    // Update title
    document.title = metadata.title;

    // Update or create meta tags
    this.updateMetaTag('description', metadata.description);
    this.updateMetaTag('keywords', metadata.keywords.join(', '));
    this.updateMetaTag('robots', metadata.robots || 'index, follow');
    
    if (metadata.author) {
      this.updateMetaTag('author', metadata.author);
    }

    // Open Graph tags
    this.updateMetaTag('og:title', metadata.ogTitle || metadata.title, 'property');
    this.updateMetaTag('og:description', metadata.ogDescription || metadata.description, 'property');
    this.updateMetaTag('og:image', metadata.ogImage || this.defaultImage, 'property');
    this.updateMetaTag('og:type', metadata.ogType || 'website', 'property');
    this.updateMetaTag('og:url', metadata.canonicalUrl || window.location.href, 'property');
    this.updateMetaTag('og:site_name', this.siteName, 'property');

    // Twitter Card tags
    this.updateMetaTag('twitter:card', metadata.twitterCard || 'summary_large_image');
    this.updateMetaTag('twitter:title', metadata.twitterTitle || metadata.title);
    this.updateMetaTag('twitter:description', metadata.twitterDescription || metadata.description);
    this.updateMetaTag('twitter:image', metadata.twitterImage || metadata.ogImage || this.defaultImage);

    // Canonical URL
    if (metadata.canonicalUrl) {
      this.updateLinkTag('canonical', metadata.canonicalUrl);
    }

    // Structured data
    if (metadata.structuredData) {
      this.updateStructuredData(metadata.structuredData);
    }

    // Alternate URLs
    if (metadata.alternateUrls) {
      Object.entries(metadata.alternateUrls).forEach(([lang, url]) => {
        this.updateLinkTag('alternate', url, { hreflang: lang });
      });
    }
  }

  /**
   * Update or create meta tag
   */
  private updateMetaTag(name: string, content: string, attribute: string = 'name'): void {
    let meta = document.querySelector(`meta[${attribute}="${name}"]`) as HTMLMetaElement;
    
    if (!meta) {
      meta = document.createElement('meta');
      meta.setAttribute(attribute, name);
      document.head.appendChild(meta);
    }
    
    meta.content = content;
  }

  /**
   * Update or create link tag
   */
  private updateLinkTag(rel: string, href: string, attributes?: { [key: string]: string }): void {
    let link = document.querySelector(`link[rel="${rel}"]`) as HTMLLinkElement;
    
    if (!link) {
      link = document.createElement('link');
      link.rel = rel;
      document.head.appendChild(link);
    }
    
    link.href = href;
    
    if (attributes) {
      Object.entries(attributes).forEach(([key, value]) => {
        link.setAttribute(key, value);
      });
    }
  }

  /**
   * Update structured data
   */
  private updateStructuredData(data: any): void {
    let script = document.querySelector('script[type="application/ld+json"]') as HTMLScriptElement;
    
    if (!script) {
      script = document.createElement('script');
      script.type = 'application/ld+json';
      document.head.appendChild(script);
    }
    
    script.textContent = JSON.stringify(data);
  }

  /**
   * Generate sitemap data
   */
  generateSitemapData(): any {
    return {
      urls: [
        { loc: this.baseUrl, priority: 1.0, changefreq: 'daily' },
        { loc: `${this.baseUrl}/shop`, priority: 0.9, changefreq: 'daily' },
        { loc: `${this.baseUrl}/showcase`, priority: 0.8, changefreq: 'weekly' },
        { loc: `${this.baseUrl}/about`, priority: 0.6, changefreq: 'monthly' },
        { loc: `${this.baseUrl}/contact`, priority: 0.6, changefreq: 'monthly' },
        { loc: `${this.baseUrl}/services`, priority: 0.7, changefreq: 'monthly' }
      ]
    };
  }
}

// Global SEO optimizer instance
const seoOptimizer = new SEOOptimizer();

export default seoOptimizer;

// Export utility functions
export const applySEO = (metadata: SEOMetadata) => seoOptimizer.applySEOMetadata(metadata);
export const generateHomepageSEO = () => seoOptimizer.generateHomepageSEO();
export const generateShopSEO = () => seoOptimizer.generateShopSEO();
export const generateProductSEO = (product: any) => seoOptimizer.generateProductSEO(product);
export const generateTourSEO = (tour: any) => seoOptimizer.generateTourSEO(tour);

// Export for global use in development
if (typeof window !== 'undefined') {
  (window as any).seoOptimizer = seoOptimizer;
}
