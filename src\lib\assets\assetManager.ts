/**
 * Asset Management System
 * Comprehensive system for handling 360° images, validation, optimization, and CDN integration
 */

import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

export interface AssetMetadata {
  id: string;
  filename: string;
  originalName: string;
  size: number;
  width: number;
  height: number;
  aspectRatio: number;
  format: string;
  mimeType: string;
  uploadedAt: string;
  optimizedVersions?: {
    thumbnail: string;
    preview: string;
    compressed: string;
    original: string;
  };
  validation: {
    isValid: boolean;
    isPanorama: boolean;
    errors: string[];
    warnings: string[];
  };
  cdn?: {
    url: string;
    transformations: Record<string, string>;
  };
}

export interface UploadOptions {
  generateThumbnail?: boolean;
  generatePreview?: boolean;
  compressOriginal?: boolean;
  validatePanorama?: boolean;
  uploadToCDN?: boolean;
  quality?: number; // 0-100
  maxWidth?: number;
  maxHeight?: number;
  folder?: string;
}

export interface ValidationResult {
  isValid: boolean;
  isPanorama: boolean;
  errors: string[];
  warnings: string[];
  metadata: {
    width: number;
    height: number;
    aspectRatio: number;
    fileSize: number;
    format: string;
  };
}

/**
 * Asset Manager for 360° images and tour assets
 */
export class AssetManager {
  private static instance: AssetManager;
  private uploadQueue: Map<string, Promise<AssetMetadata>> = new Map();
  private cache: Map<string, AssetMetadata> = new Map();

  private constructor() {}

  static getInstance(): AssetManager {
    if (!AssetManager.instance) {
      AssetManager.instance = new AssetManager();
    }
    return AssetManager.instance;
  }

  /**
   * Upload and process a 360° image
   */
  async uploadAsset(
    file: File,
    options: UploadOptions = {}
  ): Promise<AssetMetadata> {
    const uploadId = `${file.name}-${Date.now()}`;
    
    // Check if already uploading
    if (this.uploadQueue.has(uploadId)) {
      return this.uploadQueue.get(uploadId)!;
    }

    const uploadPromise = this.processUpload(file, options);
    this.uploadQueue.set(uploadId, uploadPromise);

    try {
      const result = await uploadPromise;
      this.cache.set(result.id, result);
      return result;
    } finally {
      this.uploadQueue.delete(uploadId);
    }
  }

  /**
   * Process the actual upload
   */
  private async processUpload(
    file: File,
    options: UploadOptions
  ): Promise<AssetMetadata> {
    const {
      generateThumbnail = true,
      generatePreview = true,
      compressOriginal = true,
      validatePanorama = true,
      uploadToCDN = false,
      quality = 85,
      maxWidth = 8192,
      maxHeight = 4096,
      folder = 'panoramas'
    } = options;

    // Step 1: Validate the file
    const validation = await this.validateImage(file, validatePanorama);
    
    if (!validation.isValid) {
      throw new Error(`Invalid image: ${validation.errors.join(', ')}`);
    }

    // Step 2: Generate unique filename
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
    const uniqueFilename = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExtension}`;
    const filePath = `${folder}/${uniqueFilename}`;

    // Step 3: Process image (resize, compress, generate variants)
    const processedImages = await this.processImage(file, {
      generateThumbnail,
      generatePreview,
      compressOriginal,
      quality,
      maxWidth,
      maxHeight
    });

    // Step 4: Upload to Supabase Storage
    const uploadResults = await this.uploadToStorage(processedImages, filePath);

    // Step 5: Create asset metadata
    const assetMetadata: AssetMetadata = {
      id: uniqueFilename.replace(`.${fileExtension}`, ''),
      filename: uniqueFilename,
      originalName: file.name,
      size: file.size,
      width: validation.metadata.width,
      height: validation.metadata.height,
      aspectRatio: validation.metadata.aspectRatio,
      format: validation.metadata.format,
      mimeType: file.type,
      uploadedAt: new Date().toISOString(),
      optimizedVersions: uploadResults,
      validation: {
        isValid: validation.isValid,
        isPanorama: validation.isPanorama,
        errors: validation.errors,
        warnings: validation.warnings
      }
    };

    // Step 6: Save metadata to database
    await this.saveAssetMetadata(assetMetadata);

    // Step 7: Upload to CDN if requested
    if (uploadToCDN) {
      assetMetadata.cdn = await this.uploadToCDN(assetMetadata);
    }

    return assetMetadata;
  }

  /**
   * Validate image file
   */
  async validateImage(file: File, checkPanorama: boolean = true): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check file type
    if (!file.type.startsWith('image/')) {
      errors.push('File must be an image');
    }

    // Check file size (max 50MB)
    const maxSize = 50 * 1024 * 1024;
    if (file.size > maxSize) {
      errors.push(`File size too large. Maximum ${maxSize / 1024 / 1024}MB allowed`);
    }

    // Load image to check dimensions
    const imageData = await this.loadImageData(file);
    
    if (!imageData) {
      errors.push('Failed to load image data');
      return {
        isValid: false,
        isPanorama: false,
        errors,
        warnings,
        metadata: {
          width: 0,
          height: 0,
          aspectRatio: 0,
          fileSize: file.size,
          format: file.type
        }
      };
    }

    const { width, height } = imageData;
    const aspectRatio = width / height;

    // Check minimum dimensions
    if (width < 1024 || height < 512) {
      errors.push('Image too small. Minimum 1024x512 pixels required');
    }

    // Check if it's a panorama (2:1 aspect ratio)
    const isPanorama = aspectRatio >= 1.8 && aspectRatio <= 2.2;
    
    if (checkPanorama && !isPanorama) {
      warnings.push('Image may not be a proper 360° panorama (expected 2:1 aspect ratio)');
    }

    // Performance warnings
    if (width > 8192) {
      warnings.push('Very high resolution may impact performance');
    }

    if (file.size > 10 * 1024 * 1024) {
      warnings.push('Large file size may slow loading times');
    }

    return {
      isValid: errors.length === 0,
      isPanorama,
      errors,
      warnings,
      metadata: {
        width,
        height,
        aspectRatio,
        fileSize: file.size,
        format: file.type
      }
    };
  }

  /**
   * Load image data from file
   */
  private async loadImageData(file: File): Promise<{ width: number; height: number } | null> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
      };
      img.onerror = () => {
        resolve(null);
      };
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Process image (resize, compress, generate variants)
   */
  private async processImage(
    file: File,
    options: {
      generateThumbnail: boolean;
      generatePreview: boolean;
      compressOriginal: boolean;
      quality: number;
      maxWidth: number;
      maxHeight: number;
    }
  ): Promise<{
    original: Blob;
    thumbnail?: Blob;
    preview?: Blob;
    compressed?: Blob;
  }> {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d')!;
    const img = new Image();

    // Load original image
    await new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
      img.src = URL.createObjectURL(file);
    });

    const results: any = {};

    // Original (possibly compressed)
    if (options.compressOriginal && (img.width > options.maxWidth || img.height > options.maxHeight)) {
      const scale = Math.min(options.maxWidth / img.width, options.maxHeight / img.height);
      canvas.width = img.width * scale;
      canvas.height = img.height * scale;
      ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
      results.compressed = await this.canvasToBlob(canvas, options.quality);
    }
    results.original = file;

    // Thumbnail (256x128)
    if (options.generateThumbnail) {
      canvas.width = 256;
      canvas.height = 128;
      ctx.drawImage(img, 0, 0, 256, 128);
      results.thumbnail = await this.canvasToBlob(canvas, 70);
    }

    // Preview (1024x512)
    if (options.generatePreview) {
      canvas.width = 1024;
      canvas.height = 512;
      ctx.drawImage(img, 0, 0, 1024, 512);
      results.preview = await this.canvasToBlob(canvas, 80);
    }

    return results;
  }

  /**
   * Convert canvas to blob
   */
  private async canvasToBlob(canvas: HTMLCanvasElement, quality: number): Promise<Blob> {
    return new Promise((resolve) => {
      canvas.toBlob(
        (blob) => resolve(blob!),
        'image/jpeg',
        quality / 100
      );
    });
  }

  /**
   * Upload processed images to Supabase Storage
   */
  private async uploadToStorage(
    images: { original: Blob; thumbnail?: Blob; preview?: Blob; compressed?: Blob },
    basePath: string
  ): Promise<{ thumbnail?: string; preview?: string; compressed?: string; original: string }> {
    const results: any = {};
    const baseFilename = basePath.replace(/\.[^/.]+$/, '');

    // Upload original
    const { data: originalData, error: originalError } = await supabase.storage
      .from('tour-assets')
      .upload(basePath, images.original, {
        cacheControl: '3600',
        upsert: false
      });

    if (originalError) {
      throw new Error(`Failed to upload original: ${originalError.message}`);
    }

    const { data: { publicUrl: originalUrl } } = supabase.storage
      .from('tour-assets')
      .getPublicUrl(basePath);

    results.original = originalUrl;

    // Upload thumbnail
    if (images.thumbnail) {
      const thumbnailPath = `${baseFilename}_thumb.jpg`;
      const { error: thumbError } = await supabase.storage
        .from('tour-assets')
        .upload(thumbnailPath, images.thumbnail, {
          cacheControl: '3600',
          upsert: false
        });

      if (!thumbError) {
        const { data: { publicUrl: thumbUrl } } = supabase.storage
          .from('tour-assets')
          .getPublicUrl(thumbnailPath);
        results.thumbnail = thumbUrl;
      }
    }

    // Upload preview
    if (images.preview) {
      const previewPath = `${baseFilename}_preview.jpg`;
      const { error: previewError } = await supabase.storage
        .from('tour-assets')
        .upload(previewPath, images.preview, {
          cacheControl: '3600',
          upsert: false
        });

      if (!previewError) {
        const { data: { publicUrl: previewUrl } } = supabase.storage
          .from('tour-assets')
          .getPublicUrl(previewPath);
        results.preview = previewUrl;
      }
    }

    // Upload compressed
    if (images.compressed) {
      const compressedPath = `${baseFilename}_compressed.jpg`;
      const { error: compressedError } = await supabase.storage
        .from('tour-assets')
        .upload(compressedPath, images.compressed, {
          cacheControl: '3600',
          upsert: false
        });

      if (!compressedError) {
        const { data: { publicUrl: compressedUrl } } = supabase.storage
          .from('tour-assets')
          .getPublicUrl(compressedPath);
        results.compressed = compressedUrl;
      }
    }

    return results;
  }

  /**
   * Save asset metadata to database
   */
  private async saveAssetMetadata(metadata: AssetMetadata): Promise<void> {
    const { error } = await supabase
      .from('assets')
      .insert({
        id: metadata.id,
        filename: metadata.filename,
        original_name: metadata.originalName,
        size: metadata.size,
        width: metadata.width,
        height: metadata.height,
        aspect_ratio: metadata.aspectRatio,
        format: metadata.format,
        mime_type: metadata.mimeType,
        uploaded_at: metadata.uploadedAt,
        optimized_versions: metadata.optimizedVersions,
        validation: metadata.validation,
        cdn: metadata.cdn
      });

    if (error) {
      throw new Error(`Failed to save asset metadata: ${error.message}`);
    }
  }

  /**
   * Upload to CDN (placeholder for CDN integration)
   */
  private async uploadToCDN(metadata: AssetMetadata): Promise<{ url: string; transformations: Record<string, string> }> {
    // This would integrate with a CDN service like Cloudinary, ImageKit, etc.
    // For now, return the Supabase URL
    return {
      url: metadata.optimizedVersions?.original || '',
      transformations: {
        thumbnail: metadata.optimizedVersions?.thumbnail || '',
        preview: metadata.optimizedVersions?.preview || '',
        compressed: metadata.optimizedVersions?.compressed || ''
      }
    };
  }

  /**
   * Get asset by ID
   */
  async getAsset(assetId: string): Promise<AssetMetadata | null> {
    // Check cache first
    if (this.cache.has(assetId)) {
      return this.cache.get(assetId)!;
    }

    // Load from database
    const { data, error } = await supabase
      .from('assets')
      .select('*')
      .eq('id', assetId)
      .single();

    if (error || !data) {
      return null;
    }

    const metadata: AssetMetadata = {
      id: data.id,
      filename: data.filename,
      originalName: data.original_name,
      size: data.size,
      width: data.width,
      height: data.height,
      aspectRatio: data.aspect_ratio,
      format: data.format,
      mimeType: data.mime_type,
      uploadedAt: data.uploaded_at,
      optimizedVersions: data.optimized_versions,
      validation: data.validation,
      cdn: data.cdn
    };

    this.cache.set(assetId, metadata);
    return metadata;
  }

  /**
   * Delete asset
   */
  async deleteAsset(assetId: string): Promise<void> {
    const asset = await this.getAsset(assetId);
    if (!asset) {
      throw new Error('Asset not found');
    }

    // Delete from storage
    const filesToDelete = [
      asset.filename,
      asset.filename.replace(/\.[^/.]+$/, '_thumb.jpg'),
      asset.filename.replace(/\.[^/.]+$/, '_preview.jpg'),
      asset.filename.replace(/\.[^/.]+$/, '_compressed.jpg')
    ];

    await Promise.all(
      filesToDelete.map(filename =>
        supabase.storage.from('tour-assets').remove([`panoramas/${filename}`])
      )
    );

    // Delete from database
    const { error } = await supabase
      .from('assets')
      .delete()
      .eq('id', assetId);

    if (error) {
      throw new Error(`Failed to delete asset: ${error.message}`);
    }

    // Remove from cache
    this.cache.delete(assetId);
  }

  /**
   * List assets with pagination
   */
  async listAssets(
    page: number = 1,
    limit: number = 20,
    filters?: { format?: string; isPanorama?: boolean }
  ): Promise<{ assets: AssetMetadata[]; total: number; hasMore: boolean }> {
    let query = supabase
      .from('assets')
      .select('*', { count: 'exact' })
      .order('uploaded_at', { ascending: false })
      .range((page - 1) * limit, page * limit - 1);

    if (filters?.format) {
      query = query.eq('format', filters.format);
    }

    if (filters?.isPanorama !== undefined) {
      query = query.eq('validation->isPanorama', filters.isPanorama);
    }

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to list assets: ${error.message}`);
    }

    const assets: AssetMetadata[] = (data || []).map(item => ({
      id: item.id,
      filename: item.filename,
      originalName: item.original_name,
      size: item.size,
      width: item.width,
      height: item.height,
      aspectRatio: item.aspect_ratio,
      format: item.format,
      mimeType: item.mime_type,
      uploadedAt: item.uploaded_at,
      optimizedVersions: item.optimized_versions,
      validation: item.validation,
      cdn: item.cdn
    }));

    return {
      assets,
      total: count || 0,
      hasMore: (page * limit) < (count || 0)
    };
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }
}

// Export singleton instance
export const assetManager = AssetManager.getInstance();
