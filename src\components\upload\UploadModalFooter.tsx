
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface UploadModalFooterProps {
  currentStep: number;
  isSubmitting: boolean;
  canProceed: boolean;
  onBack: () => void;
  onNext: () => void;
  onSubmit: () => void;
}

const UploadModalFooter = ({ 
  currentStep, 
  isSubmitting, 
  canProceed, 
  onBack, 
  onNext, 
  onSubmit 
}: UploadModalFooterProps) => {
  return (
    <div className="flex justify-between pt-4">
      <Button
        variant="outline"
        onClick={onBack}
        disabled={isSubmitting}
      >
        {currentStep === 1 ? 'Cancel' : 'Back'}
      </Button>
      
      <Button
        onClick={currentStep === 4 ? onSubmit : onNext}
        disabled={!canProceed || isSubmitting}
        className="bg-blue-600 hover:bg-blue-700"
      >
        {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
        {currentStep === 4 ? 'Create Tour' : 'Next'}
      </Button>
    </div>
  );
};

export default UploadModalFooter;
