/**
 * HTTPS Enforcement Utilities
 * Ensures secure connections for production deployment
 */

/**
 * Force HTTPS redirect in production
 */
export const enforceHTTPS = () => {
  // Only enforce in production
  if (import.meta.env.PROD && import.meta.env.VITE_FORCE_HTTPS === 'true') {
    if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
      location.replace(`https:${location.href.substring(location.protocol.length)}`);
    }
  }
};

/**
 * Ensure all external URLs use HTTPS
 */
export const ensureHTTPS = (url: string): string => {
  if (!url) return url;
  
  try {
    const urlObj = new URL(url);
    
    // Force HTTPS for external URLs in production
    if (import.meta.env.PROD && urlObj.protocol === 'http:') {
      urlObj.protocol = 'https:';
    }
    
    return urlObj.toString();
  } catch {
    // If URL parsing fails, return original
    return url;
  }
};

/**
 * Get secure WebSocket URL
 */
export const getSecureWebSocketURL = (url: string): string => {
  if (!url) return url;
  
  try {
    const urlObj = new URL(url);
    
    // Convert to secure WebSocket in production
    if (import.meta.env.PROD) {
      if (urlObj.protocol === 'ws:') {
        urlObj.protocol = 'wss:';
      }
    }
    
    return urlObj.toString();
  } catch {
    return url;
  }
};

/**
 * Security headers for fetch requests
 */
export const getSecureHeaders = (): HeadersInit => {
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };

  // Add security headers in production
  if (import.meta.env.PROD) {
    headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains';
    headers['X-Content-Type-Options'] = 'nosniff';
    headers['X-Frame-Options'] = 'SAMEORIGIN';
    headers['X-XSS-Protection'] = '1; mode=block';
  }

  return headers;
};

/**
 * Secure cookie options
 */
export const getSecureCookieOptions = () => {
  return {
    secure: import.meta.env.PROD, // Only secure in production
    sameSite: 'strict' as const,
    httpOnly: false, // Client-side cookies for auth
    path: '/',
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
  };
};

/**
 * Check if current connection is secure
 */
export const isSecureConnection = (): boolean => {
  return location.protocol === 'https:' || location.hostname === 'localhost';
};

/**
 * Get secure asset URL
 */
export const getSecureAssetURL = (path: string): string => {
  if (!path) return path;
  
  // If it's already a full URL, ensure HTTPS
  if (path.startsWith('http')) {
    return ensureHTTPS(path);
  }
  
  // For relative paths, use current protocol
  if (path.startsWith('/')) {
    const protocol = import.meta.env.PROD ? 'https:' : location.protocol;
    return `${protocol}//${location.host}${path}`;
  }
  
  return path;
};

/**
 * Validate SSL certificate (client-side check)
 */
export const validateSSL = async (): Promise<boolean> => {
  if (!import.meta.env.PROD) return true;
  
  try {
    // Simple check to see if we can make a secure request
    const response = await fetch(location.origin, {
      method: 'HEAD',
      headers: getSecureHeaders(),
    });
    
    return response.ok;
  } catch {
    return false;
  }
};

/**
 * Initialize HTTPS enforcement
 */
export const initializeHTTPS = () => {
  // Enforce HTTPS redirect
  enforceHTTPS();
  
  // Set up security event listeners
  if (import.meta.env.PROD) {
    // Listen for mixed content warnings
    window.addEventListener('securitypolicyviolation', (event) => {
      console.warn('Security Policy Violation:', event.violatedDirective);
    });
    
    // Check SSL on page load
    validateSSL().then((isValid) => {
      if (!isValid) {
        console.warn('SSL validation failed');
      }
    });
  }
};

/**
 * Content Security Policy helpers
 */
export const CSP_DIRECTIVES = {
  defaultSrc: ["'self'"],
  scriptSrc: [
    "'self'",
    "'unsafe-inline'",
    "'unsafe-eval'",
    "https://dnbjrfgfugpmyrconepx.supabase.co",
    "https://vercel.live"
  ],
  styleSrc: [
    "'self'",
    "'unsafe-inline'",
    "https://fonts.googleapis.com"
  ],
  fontSrc: [
    "'self'",
    "https://fonts.gstatic.com"
  ],
  imgSrc: [
    "'self'",
    "data:",
    "blob:",
    "https:"
  ],
  mediaSrc: [
    "'self'",
    "blob:",
    "https:"
  ],
  connectSrc: [
    "'self'",
    "https://dnbjrfgfugpmyrconepx.supabase.co",
    "wss://dnbjrfgfugpmyrconepx.supabase.co",
    "https://vercel.live"
  ],
  frameSrc: [
    "'self'",
    "https://tour.panoee.com",
    "https://tourmkr.com",
    "https://massinteract.com"
  ],
  objectSrc: ["'none'"],
  baseUri: ["'self'"],
  formAction: ["'self'"]
} as const;
