import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Loader2, 
  Eye, 
  ExternalLink,
  Globe,
  Clock,
  Shield
} from 'lucide-react';
import { validateTourUrlEnhanced, type UrlValidationResult } from '@/utils/tourImportHelper';
import { checkIframeCompatibility } from '@/utils/brokenLinkChecker';

export interface TourPreviewData {
  title: string;
  description: string;
  category: string;
  location: string;
  embed_url: string;
  embed_type: string;
  business_type?: string;
}

interface TourImportPreviewProps {
  tourData: TourPreviewData;
  onImport: (tourData: TourPreviewData) => void;
  onCancel: () => void;
  isImporting?: boolean;
}

const TourImportPreview: React.FC<TourImportPreviewProps> = ({
  tourData,
  onImport,
  onCancel,
  isImporting = false
}) => {
  const [validation, setValidation] = useState<UrlValidationResult | null>(null);
  const [iframeCheck, setIframeCheck] = useState<any>(null);
  const [isValidating, setIsValidating] = useState(true);
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    validateTour();
  }, [tourData.embed_url]);

  const validateTour = async () => {
    setIsValidating(true);
    try {
      const [urlValidation, iframeCompatibility] = await Promise.all([
        validateTourUrlEnhanced(tourData.embed_url),
        checkIframeCompatibility(tourData.embed_url)
      ]);
      
      setValidation(urlValidation);
      setIframeCheck(iframeCompatibility);
    } catch (error) {
      console.error('Validation error:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'valid':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'broken':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Loader2 className="h-4 w-4 animate-spin" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'valid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'broken':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const canImport = validation && (validation.isValid && validation.isAccessible);
  const hasWarnings = validation && validation.warnings.length > 0;

  return (
    <div className="space-y-6">
      {/* Tour Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Tour Preview
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700">Title</label>
              <p className="text-sm text-gray-900">{tourData.title}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Category</label>
              <p className="text-sm text-gray-900">{tourData.category}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Location</label>
              <p className="text-sm text-gray-900">{tourData.location}</p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-700">Business Type</label>
              <p className="text-sm text-gray-900">{tourData.business_type || 'Not specified'}</p>
            </div>
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-700">Description</label>
            <p className="text-sm text-gray-900 mt-1">{tourData.description}</p>
          </div>

          <div>
            <label className="text-sm font-medium text-gray-700">Embed URL</label>
            <div className="flex items-center gap-2 mt-1">
              <p className="text-sm text-gray-900 flex-1 truncate">{tourData.embed_url}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(tourData.embed_url, '_blank')}
              >
                <ExternalLink className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Validation Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Validation Results
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {isValidating ? (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <Loader2 className="h-4 w-4 animate-spin" />
              Validating tour URL...
            </div>
          ) : validation ? (
            <div className="space-y-3">
              {/* Overall Status */}
              <div className="flex items-center gap-2">
                {getStatusIcon(validation.isValid && validation.isAccessible ? 'valid' : 'broken')}
                <Badge className={getStatusColor(validation.isValid && validation.isAccessible ? 'valid' : 'broken')}>
                  {validation.isValid && validation.isAccessible ? 'Valid' : 'Issues Detected'}
                </Badge>
                {validation.platform && (
                  <Badge variant="outline" className="ml-2">
                    <Globe className="h-3 w-3 mr-1" />
                    {validation.platform}
                  </Badge>
                )}
              </div>

              {/* Response Time */}
              {validation.status && (
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <Clock className="h-3 w-3" />
                  HTTP {validation.status}
                </div>
              )}

              {/* Embed Support */}
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Embed Support:</span>
                <Badge 
                  variant="outline" 
                  className={
                    validation.embedSupport === 'full' ? 'border-green-500 text-green-700' :
                    validation.embedSupport === 'limited' ? 'border-yellow-500 text-yellow-700' :
                    'border-red-500 text-red-700'
                  }
                >
                  {validation.embedSupport}
                </Badge>
              </div>

              {/* Warnings */}
              {validation.warnings.length > 0 && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      {validation.warnings.map((warning, index) => (
                        <div key={index} className="text-sm">• {warning}</div>
                      ))}
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* Error */}
              {validation.error && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4" />
                  <AlertDescription>{validation.error}</AlertDescription>
                </Alert>
              )}

              {/* Iframe Compatibility */}
              {iframeCheck && (
                <div className="pt-2 border-t">
                  <div className="flex items-center gap-2 mb-2">
                    <span className="text-sm font-medium">Iframe Compatibility:</span>
                    <Badge 
                      variant="outline"
                      className={iframeCheck.compatible ? 'border-green-500 text-green-700' : 'border-red-500 text-red-700'}
                    >
                      {iframeCheck.compatible ? 'Compatible' : 'Issues'}
                    </Badge>
                  </div>
                  
                  {!iframeCheck.compatible && iframeCheck.reason && (
                    <p className="text-sm text-red-600 mb-2">{iframeCheck.reason}</p>
                  )}
                  
                  {iframeCheck.suggestions.length > 0 && (
                    <div className="text-sm text-gray-600">
                      <p className="font-medium mb-1">Suggestions:</p>
                      <ul className="space-y-1">
                        {iframeCheck.suggestions.map((suggestion: string, index: number) => (
                          <li key={index} className="ml-2">• {suggestion}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <Alert variant="destructive">
              <XCircle className="h-4 w-4" />
              <AlertDescription>Failed to validate tour URL</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Live Preview */}
      {canImport && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Live Preview
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowPreview(!showPreview)}
              >
                {showPreview ? 'Hide' : 'Show'} Preview
              </Button>
            </CardTitle>
          </CardHeader>
          {showPreview && (
            <CardContent>
              <div className="w-full h-96 border rounded-lg overflow-hidden">
                <iframe
                  src={tourData.embed_url}
                  className="w-full h-full"
                  frameBorder="0"
                  allowFullScreen
                  title={`Preview: ${tourData.title}`}
                />
              </div>
            </CardContent>
          )}
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end gap-3">
        <Button variant="outline" onClick={onCancel} disabled={isImporting}>
          Cancel
        </Button>
        <Button
          onClick={() => onImport(tourData)}
          disabled={!canImport || isImporting}
          className="min-w-24"
        >
          {isImporting ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
              Importing...
            </>
          ) : (
            'Import Tour'
          )}
        </Button>
      </div>

      {/* Import Warning */}
      {hasWarnings && canImport && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            This tour has warnings but can still be imported. Review the issues above and proceed with caution.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default TourImportPreview;
