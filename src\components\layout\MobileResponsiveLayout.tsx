/**
 * Mobile-First Responsive Layout Component
 * Ensures optimal mobile experience with touch-friendly interfaces
 */

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { 
  Menu, 
  X, 
  ChevronLeft, 
  Home,
  Search,
  User,
  Settings
} from 'lucide-react';

interface MobileResponsiveLayoutProps {
  children: React.ReactNode;
  header?: React.ReactNode;
  sidebar?: React.ReactNode;
  footer?: React.ReactNode;
  showMobileNav?: boolean;
  showBackButton?: boolean;
  onBackClick?: () => void;
  className?: string;
}

const MobileResponsiveLayout = ({
  children,
  header,
  sidebar,
  footer,
  showMobileNav = true,
  showBackButton = false,
  onBackClick,
  className
}: MobileResponsiveLayoutProps) => {
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  // Detect screen size
  useEffect(() => {
    const checkScreenSize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => window.removeEventListener('resize', checkScreenSize);
  }, []);

  // Handle scroll for header effects
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Prevent body scroll when sidebar is open on mobile
  useEffect(() => {
    if (isMobile && sidebarOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobile, sidebarOpen]);

  return (
    <div className={cn("min-h-screen bg-background", className)}>
      {/* Mobile Header */}
      {isMobile && (
        <motion.header
          initial={{ y: -100 }}
          animate={{ y: 0 }}
          className={cn(
            "fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-b transition-all duration-300",
            scrolled && "shadow-sm"
          )}
        >
          <div className="flex items-center justify-between h-16 px-4">
            {/* Left side */}
            <div className="flex items-center gap-3">
              {showBackButton && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onBackClick}
                  className="p-2 h-10 w-10"
                >
                  <ChevronLeft className="w-5 h-5" />
                </Button>
              )}
              
              {sidebar && !showBackButton && (
                <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>
                  <SheetTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="p-2 h-10 w-10"
                    >
                      <Menu className="w-5 h-5" />
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="w-80 p-0">
                    <div className="h-full overflow-y-auto">
                      {sidebar}
                    </div>
                  </SheetContent>
                </Sheet>
              )}
            </div>

            {/* Center - Header content */}
            <div className="flex-1 flex justify-center">
              {header}
            </div>

            {/* Right side */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                className="p-2 h-10 w-10"
              >
                <Search className="w-5 h-5" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="p-2 h-10 w-10"
              >
                <User className="w-5 h-5" />
              </Button>
            </div>
          </div>
        </motion.header>
      )}

      {/* Desktop/Tablet Layout */}
      {!isMobile && (
        <div className="flex min-h-screen">
          {/* Sidebar */}
          {sidebar && (
            <motion.aside
              initial={{ x: -300 }}
              animate={{ x: 0 }}
              className={cn(
                "fixed left-0 top-0 h-full bg-background border-r z-40 overflow-y-auto",
                isTablet ? "w-64" : "w-72"
              )}
            >
              {sidebar}
            </motion.aside>
          )}

          {/* Main Content */}
          <div className={cn(
            "flex-1 flex flex-col",
            sidebar && (isTablet ? "ml-64" : "ml-72")
          )}>
            {/* Desktop Header */}
            {header && (
              <motion.header
                initial={{ y: -50 }}
                animate={{ y: 0 }}
                className={cn(
                  "sticky top-0 z-30 bg-background/95 backdrop-blur-sm border-b transition-all duration-300",
                  scrolled && "shadow-sm"
                )}
              >
                <div className="h-16 px-6 flex items-center">
                  {header}
                </div>
              </motion.header>
            )}

            {/* Main Content */}
            <main className="flex-1">
              {children}
            </main>

            {/* Desktop Footer */}
            {footer && (
              <footer className="border-t bg-background">
                {footer}
              </footer>
            )}
          </div>
        </div>
      )}

      {/* Mobile Layout */}
      {isMobile && (
        <div className="flex flex-col min-h-screen">
          {/* Main Content with top padding for fixed header */}
          <main className="flex-1 pt-16 pb-20">
            {children}
          </main>

          {/* Mobile Bottom Navigation */}
          {showMobileNav && (
            <motion.nav
              initial={{ y: 100 }}
              animate={{ y: 0 }}
              className="fixed bottom-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-sm border-t"
            >
              <div className="flex items-center justify-around h-16 px-4">
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex flex-col items-center gap-1 h-12 w-12 p-1"
                >
                  <Home className="w-5 h-5" />
                  <span className="text-xs">Home</span>
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex flex-col items-center gap-1 h-12 w-12 p-1"
                >
                  <Search className="w-5 h-5" />
                  <span className="text-xs">Search</span>
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex flex-col items-center gap-1 h-12 w-12 p-1"
                >
                  <User className="w-5 h-5" />
                  <span className="text-xs">Profile</span>
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="flex flex-col items-center gap-1 h-12 w-12 p-1"
                >
                  <Settings className="w-5 h-5" />
                  <span className="text-xs">Settings</span>
                </Button>
              </div>
            </motion.nav>
          )}

          {/* Mobile Footer */}
          {footer && (
            <footer className="border-t bg-background pb-16">
              {footer}
            </footer>
          )}
        </div>
      )}
    </div>
  );
};

export default MobileResponsiveLayout;
