-- Create admin_settings table for storing application configuration
CREATE TABLE IF NOT EXISTS public.admin_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  setting_key TEXT UNIQUE NOT NULL,
  setting_value JSONB NOT NULL,
  setting_type TEXT NOT NULL CHECK (setting_type IN ('general', 'email', 'security', 'storage', 'theme', 'chat', 'commerce')),
  description TEXT,
  is_public BOOLEAN DEFAULT false, -- Whether this setting can be accessed by non-admin users
  created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
  created_by UUID REFERENCES auth.users(id),
  updated_by UUID REFERENCES auth.users(id)
);

-- Add RLS policies
ALTER TABLE public.admin_settings ENABLE ROW LEVEL SECURITY;

-- Only admins can read/write settings
CREATE POLICY "Admin can manage settings" ON public.admin_settings
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles 
      WHERE profiles.id = auth.uid() 
      AND profiles.role = 'admin'
    )
  );

-- Public users can only read public settings
CREATE POLICY "Public can read public settings" ON public.admin_settings
  FOR SELECT USING (is_public = true);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON public.admin_settings(setting_key);
CREATE INDEX IF NOT EXISTS idx_admin_settings_type ON public.admin_settings(setting_type);
CREATE INDEX IF NOT EXISTS idx_admin_settings_public ON public.admin_settings(is_public);

-- Add trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_admin_settings_updated_at 
  BEFORE UPDATE ON public.admin_settings 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default chat settings
INSERT INTO public.admin_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
  ('chat_widget_enabled', 'true', 'chat', 'Enable/disable the floating WhatsApp chat widget', true),
  ('chat_business_phone', '"+2349077776066"', 'chat', 'WhatsApp business phone number', true),
  ('chat_business_name', '"VirtualRealTour Support"', 'chat', 'Business name displayed in chat widget', true),
  ('chat_default_message', '"Hi! I need help with VirtualRealTour. Can you assist me?"', 'chat', 'Default message for WhatsApp chat', true),
  ('chat_position', '"bottom-right"', 'chat', 'Position of the chat widget (bottom-right, bottom-left)', true),
  ('chat_show_on_pages', '["all"]', 'chat', 'Pages where chat widget should be shown', false),
  ('chat_business_hours', '{"enabled": false, "timezone": "Africa/Lagos", "hours": {"monday": {"open": "09:00", "close": "17:00", "enabled": true}, "tuesday": {"open": "09:00", "close": "17:00", "enabled": true}, "wednesday": {"open": "09:00", "close": "17:00", "enabled": true}, "thursday": {"open": "09:00", "close": "17:00", "enabled": true}, "friday": {"open": "09:00", "close": "17:00", "enabled": true}, "saturday": {"open": "10:00", "close": "14:00", "enabled": true}, "sunday": {"open": "10:00", "close": "14:00", "enabled": false}}}', 'chat', 'Business hours configuration for chat availability', true)
ON CONFLICT (setting_key) DO NOTHING;

-- Insert default general settings
INSERT INTO public.admin_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
  ('site_name', '"VirtualRealTour"', 'general', 'Website name', true),
  ('site_description', '"Professional 360° Virtual Tours in Nigeria"', 'general', 'Website description', true),
  ('site_url', '"https://virtualrealtour.ng"', 'general', 'Website URL', true),
  ('admin_email', '"<EMAIL>"', 'general', 'Administrator email address', false),
  ('timezone', '"Africa/Lagos"', 'general', 'Default timezone', true),
  ('language', '"en"', 'general', 'Default language', true),
  ('maintenance_mode', 'false', 'general', 'Enable maintenance mode', false),
  ('registration_enabled', 'true', 'general', 'Allow user registration', true),
  ('email_verification_required', 'true', 'general', 'Require email verification', true)
ON CONFLICT (setting_key) DO NOTHING;

-- Add comment for documentation
COMMENT ON TABLE public.admin_settings IS 'Stores application configuration settings that can be managed by administrators';
COMMENT ON COLUMN public.admin_settings.setting_key IS 'Unique identifier for the setting';
COMMENT ON COLUMN public.admin_settings.setting_value IS 'JSON value of the setting';
COMMENT ON COLUMN public.admin_settings.setting_type IS 'Category of the setting for organization';
COMMENT ON COLUMN public.admin_settings.is_public IS 'Whether this setting can be accessed by non-admin users';
