/**
 * VendorDashboard Component
 * Comprehensive vendor management interface
 * Mobile-first responsive design with analytics and product management
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Package, 
  TrendingUp, 
  DollarSign, 
  Users, 
  Plus, 
  Eye, 
  Edit, 
  Trash2,
  BarChart3,
  Calendar,
  Clock,
  Star,
  MessageCircle,
  Settings,
  Download,
  Filter
} from 'lucide-react';
import { cn } from '@/lib/utils';
import ProductCard, { type Product } from './ProductCard';
import OrderSummary, { type Order } from './OrderSummary';
import VendorProfile, { type Vendor } from './VendorProfile';

interface VendorStats {
  total_products: number;
  active_products: number;
  draft_products: number;
  total_orders: number;
  pending_orders: number;
  completed_orders: number;
  total_revenue: number;
  monthly_revenue: number;
  commission_paid: number;
  rating: number;
  response_time: string;
}

interface VendorDashboardProps {
  vendor: Vendor;
  stats: VendorStats;
  products: Product[];
  orders: Order[];
  onAddProduct?: () => void;
  onEditProduct?: (productId: string) => void;
  onDeleteProduct?: (productId: string) => void;
  onViewOrder?: (orderId: string) => void;
  onUpdateProfile?: () => void;
  className?: string;
}

const VendorDashboard = ({
  vendor,
  stats,
  products,
  orders,
  onAddProduct,
  onEditProduct,
  onDeleteProduct,
  onViewOrder,
  onUpdateProfile,
  className
}: VendorDashboardProps) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [productFilter, setProductFilter] = useState<'all' | 'active' | 'draft'>('all');
  const [orderFilter, setOrderFilter] = useState<'all' | 'pending' | 'completed'>('all');

  const filteredProducts = products.filter(product => {
    if (productFilter === 'all') return true;
    return product.status === productFilter;
  });

  const filteredOrders = orders.filter(order => {
    if (orderFilter === 'all') return true;
    if (orderFilter === 'pending') return ['pending', 'confirmed', 'processing'].includes(order.status);
    if (orderFilter === 'completed') return ['delivered', 'shipped'].includes(order.status);
    return true;
  });

  const StatCard = ({ 
    title, 
    value, 
    change, 
    icon: Icon, 
    color = 'blue' 
  }: { 
    title: string; 
    value: string | number; 
    change?: string; 
    icon: any; 
    color?: string; 
  }) => (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {change && (
              <p className={cn(
                "text-xs",
                change.startsWith('+') ? 'text-green-600' : 'text-red-600'
              )}>
                {change}
              </p>
            )}
          </div>
          <div className={cn(
            "w-12 h-12 rounded-lg flex items-center justify-center",
            color === 'blue' && 'bg-blue-100 text-blue-600',
            color === 'green' && 'bg-green-100 text-green-600',
            color === 'yellow' && 'bg-yellow-100 text-yellow-600',
            color === 'purple' && 'bg-purple-100 text-purple-600'
          )}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn("w-full space-y-6", className)}
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">Vendor Dashboard</h1>
          <p className="text-muted-foreground">Welcome back, {vendor.name}</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={onUpdateProfile}>
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
          <Button onClick={onAddProduct}>
            <Plus className="w-4 h-4 mr-2" />
            Add Product
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Products"
          value={stats.total_products}
          icon={Package}
          color="blue"
        />
        <StatCard
          title="Total Orders"
          value={stats.total_orders}
          change={`+${stats.pending_orders} pending`}
          icon={TrendingUp}
          color="green"
        />
        <StatCard
          title="Monthly Revenue"
          value={`₦${stats.monthly_revenue.toLocaleString()}`}
          icon={DollarSign}
          color="yellow"
        />
        <StatCard
          title="Rating"
          value={stats.rating.toFixed(1)}
          change={stats.response_time}
          icon={Star}
          color="purple"
        />
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Orders */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Recent Orders</CardTitle>
                  <Button variant="outline" size="sm">
                    <Eye className="w-4 h-4 mr-2" />
                    View All
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {orders.slice(0, 3).map((order) => (
                    <div key={order.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">{order.order_number}</div>
                        <div className="text-sm text-muted-foreground">{order.customer_name}</div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold">₦{order.total_amount.toLocaleString()}</div>
                        <Badge variant="outline" className="text-xs">
                          {order.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Products */}
            <Card>
              <CardHeader>
                <CardTitle>Top Products</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {products.slice(0, 3).map((product) => (
                    <div key={product.id} className="flex items-center gap-3 p-3 border rounded-lg">
                      <div className="w-12 h-12 bg-gray-100 rounded overflow-hidden">
                        {product.images[0] ? (
                          <img
                            src={product.images[0]}
                            alt={product.title}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Package className="w-4 h-4 text-gray-400" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium line-clamp-1">{product.title}</div>
                        <div className="text-sm text-muted-foreground">
                          ₦{product.price.toLocaleString()}
                        </div>
                      </div>
                      <Badge variant={product.status === 'active' ? 'default' : 'secondary'}>
                        {product.status}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                <Button variant="outline" className="h-20 flex-col" onClick={onAddProduct}>
                  <Plus className="w-6 h-6 mb-2" />
                  Add Product
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <BarChart3 className="w-6 h-6 mb-2" />
                  View Analytics
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <MessageCircle className="w-6 h-6 mb-2" />
                  Messages
                </Button>
                <Button variant="outline" className="h-20 flex-col">
                  <Download className="w-6 h-6 mb-2" />
                  Export Data
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Products Tab */}
        <TabsContent value="products" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                  <CardTitle>Product Management</CardTitle>
                  <CardDescription>
                    {filteredProducts.length} of {products.length} products
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant={productFilter === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setProductFilter('all')}
                  >
                    All
                  </Button>
                  <Button
                    variant={productFilter === 'active' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setProductFilter('active')}
                  >
                    Active
                  </Button>
                  <Button
                    variant={productFilter === 'draft' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setProductFilter('draft')}
                  >
                    Draft
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {filteredProducts.length === 0 ? (
                <div className="text-center py-8">
                  <Package className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="font-medium text-lg mb-2">No products found</h3>
                  <p className="text-muted-foreground mb-4">
                    {productFilter === 'all' 
                      ? "You haven't added any products yet."
                      : `No ${productFilter} products found.`
                    }
                  </p>
                  <Button onClick={onAddProduct}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Your First Product
                  </Button>
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredProducts.map((product) => (
                    <div key={product.id} className="relative">
                      <ProductCard
                        product={product}
                        showVendor={false}
                        showAddToCart={false}
                      />
                      <div className="absolute top-2 right-2 flex gap-1">
                        <Button
                          size="sm"
                          variant="secondary"
                          className="w-8 h-8 p-0"
                          onClick={() => onEditProduct?.(product.id)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="secondary"
                          className="w-8 h-8 p-0 text-red-600 hover:text-red-700"
                          onClick={() => onDeleteProduct?.(product.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Orders Tab */}
        <TabsContent value="orders" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                <div>
                  <CardTitle>Order Management</CardTitle>
                  <CardDescription>
                    {filteredOrders.length} of {orders.length} orders
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant={orderFilter === 'all' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setOrderFilter('all')}
                  >
                    All
                  </Button>
                  <Button
                    variant={orderFilter === 'pending' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setOrderFilter('pending')}
                  >
                    Pending
                  </Button>
                  <Button
                    variant={orderFilter === 'completed' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setOrderFilter('completed')}
                  >
                    Completed
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {filteredOrders.length === 0 ? (
                <div className="text-center py-8">
                  <TrendingUp className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                  <h3 className="font-medium text-lg mb-2">No orders found</h3>
                  <p className="text-muted-foreground">
                    {orderFilter === 'all' 
                      ? "You haven't received any orders yet."
                      : `No ${orderFilter} orders found.`
                    }
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredOrders.map((order) => (
                    <OrderSummary
                      key={order.id}
                      order={order}
                      variant="compact"
                      showCustomerInfo={false}
                      showVendorBreakdown={false}
                      onViewProduct={onViewOrder}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Total Revenue</span>
                    <span className="font-semibold">₦{stats.total_revenue.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Monthly Revenue</span>
                    <span className="font-semibold">₦{stats.monthly_revenue.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Commission Paid</span>
                    <span className="font-semibold">₦{stats.commission_paid.toLocaleString()}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Customer Rating</span>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                      <span className="font-semibold">{stats.rating.toFixed(1)}</span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Response Time</span>
                    <span className="font-semibold">{stats.response_time}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Order Completion Rate</span>
                    <span className="font-semibold">
                      {((stats.completed_orders / stats.total_orders) * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </motion.div>
  );
};

export default VendorDashboard;
