
import FileUploadZone from './FileUploadZone';
import FileList from './FileList';
import { FormData } from '@/hooks/useUploadForm';
import { Label } from '@/components/ui/label';

interface UploadStepTwoProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
}

const UploadStepTwo = ({ formData, setFormData }: UploadStepTwoProps) => {
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setFormData({
      ...formData,
      files: [...formData.files, ...files]
    });
  };

  const removeFile = (index: number) => {
    setFormData({
      ...formData,
      files: formData.files.filter((_, i) => i !== index)
    });
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <Label className="text-base font-semibold">Upload 360° Images</Label>
        <p className="text-sm text-muted-foreground">
          Upload panoramic images to create your virtual tour. Supported formats: JPG, PNG, JPEG
        </p>
        <FileUploadZone onFileUpload={handleFileUpload} />
        <FileList files={formData.files} onRemoveFile={removeFile} />
      </div>
    </div>
  );
};

export default UploadStepTwo;
