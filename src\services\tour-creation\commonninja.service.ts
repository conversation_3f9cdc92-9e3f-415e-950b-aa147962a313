/**
 * CommonNinja Tour Creation Service
 * Fallback implementation of TourCreationService for CommonNinja platform
 */

import {
  TourCreationService,
  TourConfig,
  SceneConfig,
  HotspotConfig,
  CreatedTour,
  TourScene,
  TourHotspot,
  TourCreationError,
  EmbedOptions,
  EmbedCallbacks,
  TourAnalytics
} from './types';

// CommonNinja API Response Types
// Actual CommonNinja API Response Structure
interface CommonNinjaWidget {
  id: string;
  name: string;
  description: string;
  type: string;
  previewImage: string;
  projectId: string;
  status: 'draft' | 'published';
  modelVersion: number;
  created: string;
  updated: string;
  data: Record<string, unknown>;
  embedCode: {
    html: string;
    script: string;
  };
  editorUrl: string;
}

interface CommonNinjaWidgetList {
  widgets: CommonNinjaWidget[];
  total: number;
  page: number;
  limit: number;
}

interface CommonNinjaScene {
  id: string;
  name: string;
  description: string;
  image_url: string;
  order_index: number;
  hotspots?: CommonNinjaHotspot[];
  widget_scene_id: string;
}

interface CommonNinjaHotspot {
  id: string;
  type: string;
  label: string;
  content: string;
  position: { x: number; y: number };
  style: Record<string, unknown>;
  actions: Record<string, unknown>;
  hotspot_id: string;
}

interface CommonNinjaAnalytics {
  views: number;
  unique_views: number;
  average_time_spent: number;
  hotspot_interactions: Record<string, number>;
  scene_views: Record<string, number>;
  conversion_events: Array<{ type: string; count: number }>;
  interactions: number;
  bounceRate: number;
  topPages: Array<{ page: string; views: number }>;
  deviceBreakdown: Record<string, number>;
  locationData: Array<{ country: string; views: number }>;
}

export class CommonNinjaService implements TourCreationService {
  readonly platform = 'commonninja' as const;
  private apiKey: string;
  private baseUrl: string;

  // Supported CommonNinja widget types for our use case
  public static readonly WIDGET_TYPES = {
    VIRTUAL_TOUR: 'virtual_tour',
    IMAGE_HOTSPOT: 'image_hotspot',
    CATALOG: 'catalog',
    IMAGE_CAROUSEL: 'image_carousel',
    VIDEO_CAROUSEL: 'video_carousel',
    IMAGE_GRID_SLIDER: 'image_grid_slider',
    BUSINESS_LISTINGS: 'business_listings',
    MAPS: 'maps',
    BOOKING: 'booking',
    INTERACTIVE_VIDEO: 'interactive_video',
    BEFORE_AFTER: 'before_after',
    STOP_MOTION_PLAYER: 'stop_motion_player',
    IMAGE_SLIDER: 'image_slider',
    SCROLL_PROGRESS: 'scroll_progress'
  } as const;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || import.meta.env.VITE_COMMONNINJA_API_KEY || '';
    this.baseUrl = 'https://api.commoninja.com/platform/api/v1';
  }

  get isConfigured(): boolean {
    return !!this.apiKey;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    if (!this.isConfigured) {
      throw new TourCreationError(
        'NOT_CONFIGURED',
        'CommonNinja API key is not configured. Please set VITE_COMMONNINJA_API_KEY in your environment.',
        null,
        'commonninja'
      );
    }

    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({
        message: `HTTP ${response.status}: ${response.statusText}`
      }));
      throw new TourCreationError(
        'API_ERROR',
        error.message || `CommonNinja API Error: ${response.status}`,
        error,
        'commonninja'
      );
    }

    return response.json();
  }

  async createTour(config: TourConfig): Promise<CreatedTour> {
    try {
      // Use correct CommonNinja API structure
      const widgetData = {
        name: config.title,
        type: 'virtual_tour',
        description: config.description || '',
        status: 'draft',
        data: {
          scenes: [],
          settings: {
            autoRotate: config.settings?.autoRotate || false,
            showControls: config.settings?.showControls !== false,
            enableVR: config.settings?.enableVR || false,
            customBranding: config.settings?.customBranding || false
          },
          metadata: {
            category: config.category,
            location: config.location,
            businessInfo: config.businessInfo
          }
        }
      };

      const response = await this.makeRequest<CommonNinjaWidget>('/widgets', {
        method: 'POST',
        body: JSON.stringify(widgetData),
      });

      return this.mapCommonNinjaTourToCreatedTour(response);
    } catch (error) {
      if (error instanceof TourCreationError) {
        throw error;
      }
      throw new TourCreationError(
        'CREATE_FAILED',
        'Failed to create tour on CommonNinja',
        error,
        'commonninja'
      );
    }
  }

  async updateTour(tourId: string, config: Partial<TourConfig>): Promise<CreatedTour> {
    try {
      const updateData = {
        name: config.title,
        description: config.description,
        data: {
          settings: config.settings,
          metadata: {
            category: config.category,
            location: config.location,
            businessInfo: config.businessInfo
          }
        }
      };

      const response = await this.makeRequest<CommonNinjaWidget>(`/widgets/${tourId}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
      });

      return this.mapCommonNinjaTourToCreatedTour(response);
    } catch (error) {
      throw new TourCreationError(
        'UPDATE_FAILED',
        'Failed to update tour on CommonNinja',
        error,
        'commonninja'
      );
    }
  }

  async deleteTour(tourId: string): Promise<void> {
    try {
      await this.makeRequest(`/widgets/${tourId}`, {
        method: 'DELETE',
      });
    } catch (error) {
      throw new TourCreationError(
        'DELETE_FAILED',
        'Failed to delete tour on CommonNinja',
        error,
        'commonninja'
      );
    }
  }

  async getTour(tourId: string): Promise<CreatedTour> {
    try {
      const response = await this.makeRequest<CommonNinjaWidget>(`/widgets/${tourId}`);
      return this.mapCommonNinjaTourToCreatedTour(response);
    } catch (error) {
      throw new TourCreationError(
        'GET_FAILED',
        'Failed to get tour from CommonNinja API',
        error,
        'commonninja'
      );
    }
  }

  async listTours(): Promise<CreatedTour[]> {
    try {
      const response = await this.makeRequest<CommonNinjaWidgetList>('/widgets?type=virtual_tour');
      return response.widgets.map((widget: CommonNinjaWidget) => this.mapCommonNinjaTourToCreatedTour(widget));
    } catch (error) {
      throw new TourCreationError(
        'LIST_FAILED',
        'Failed to list tours from CommonNinja API',
        error,
        'commonninja'
      );
    }
  }

  async addScene(tourId: string, scene: SceneConfig): Promise<TourScene> {
    try {
      let imageUrl = scene.imageUrl;

      // Upload image if file is provided
      if (scene.imageFile && !imageUrl) {
        imageUrl = await this.uploadImage(scene.imageFile);
      }

      const sceneData = {
        name: scene.name,
        description: scene.description,
        image_url: imageUrl,
        order_index: scene.orderIndex,
        settings: scene.settings,
      };

      const response = await this.makeRequest<CommonNinjaScene>(`/widgets/${tourId}/scenes`, {
        method: 'POST',
        body: JSON.stringify(sceneData),
      });

      return this.mapCommonNinjaSceneToTourScene(response);
    } catch (error) {
      throw new TourCreationError(
        'SCENE_ADD_FAILED',
        'Failed to add scene to CommonNinja tour',
        error,
        'commonninja'
      );
    }
  }

  async updateScene(tourId: string, sceneId: string, scene: Partial<SceneConfig>): Promise<TourScene> {
    try {
      const response = await this.makeRequest<CommonNinjaScene>(`/widgets/${tourId}/scenes/${sceneId}`, {
        method: 'PATCH',
        body: JSON.stringify(scene),
      });

      return this.mapCommonNinjaSceneToTourScene(response);
    } catch (error) {
      throw new TourCreationError(
        'SCENE_UPDATE_FAILED',
        'Failed to update scene on CommonNinja API',
        error,
        'commonninja'
      );
    }
  }

  async deleteScene(tourId: string, sceneId: string): Promise<void> {
    try {
      await this.makeRequest(`/widgets/${tourId}/scenes/${sceneId}`, {
        method: 'DELETE',
      });
    } catch (error) {
      throw new TourCreationError(
        'SCENE_DELETE_FAILED',
        'Failed to delete scene from CommonNinja',
        error,
        'commonninja'
      );
    }
  }

  async reorderScenes(tourId: string, sceneIds: string[]): Promise<void> {
    try {
      await this.makeRequest(`/widgets/${tourId}/scenes/reorder`, {
        method: 'POST',
        body: JSON.stringify({ scene_ids: sceneIds }),
      });
    } catch (error) {
      throw new TourCreationError(
        'SCENE_REORDER_FAILED',
        'Failed to reorder scenes on CommonNinja',
        error,
        'commonninja'
      );
    }
  }

  async addHotspot(tourId: string, sceneId: string, hotspot: HotspotConfig): Promise<TourHotspot> {
    try {
      const hotspotData = {
        type: hotspot.type,
        label: hotspot.label,
        content: hotspot.content,
        position: hotspot.position,
        style: hotspot.style,
        actions: hotspot.actions,
        target_scene_id: hotspot.targetSceneId,
        product_id: hotspot.productId,
        vendor_id: hotspot.vendorId,
        link_url: hotspot.linkUrl,
        whatsapp_phone: hotspot.whatsappPhone,
        whatsapp_message: hotspot.whatsappMessage,
        media_url: hotspot.mediaUrl,
      };

      const response = await this.makeRequest<CommonNinjaHotspot>(`/widgets/${tourId}/scenes/${sceneId}/hotspots`, {
        method: 'POST',
        body: JSON.stringify(hotspotData),
      });

      return this.mapCommonNinjaHotspotToTourHotspot(response);
    } catch (error) {
      throw new TourCreationError(
        'HOTSPOT_ADD_FAILED',
        'Failed to add hotspot to CommonNinja scene',
        error,
        'commonninja'
      );
    }
  }

  async updateHotspot(tourId: string, sceneId: string, hotspotId: string, hotspot: Partial<HotspotConfig>): Promise<TourHotspot> {
    try {
      const response = await this.makeRequest<CommonNinjaHotspot>(`/widgets/${tourId}/scenes/${sceneId}/hotspots/${hotspotId}`, {
        method: 'PATCH',
        body: JSON.stringify(hotspot),
      });

      return this.mapCommonNinjaHotspotToTourHotspot(response);
    } catch (error) {
      throw new TourCreationError(
        'HOTSPOT_UPDATE_FAILED',
        'Failed to update hotspot on CommonNinja API',
        error,
        'commonninja'
      );
    }
  }

  async deleteHotspot(tourId: string, sceneId: string, hotspotId: string): Promise<void> {
    try {
      await this.makeRequest(`/widgets/${tourId}/scenes/${sceneId}/hotspots/${hotspotId}`, {
        method: 'DELETE',
      });
    } catch (error) {
      throw new TourCreationError(
        'HOTSPOT_DELETE_FAILED',
        'Failed to delete hotspot from CommonNinja',
        error,
        'commonninja'
      );
    }
  }

  async publishTour(tourId: string): Promise<CreatedTour> {
    try {
      const response = await this.makeRequest<CommonNinjaWidget>(`/widgets/${tourId}`, {
        method: 'PUT',
        body: JSON.stringify({ status: 'published' }),
      });

      return this.mapCommonNinjaTourToCreatedTour(response);
    } catch (error) {
      throw new TourCreationError(
        'PUBLISH_FAILED',
        'Failed to publish tour on CommonNinja API',
        error,
        'commonninja'
      );
    }
  }

  async unpublishTour(tourId: string): Promise<CreatedTour> {
    try {
      const response = await this.makeRequest<CommonNinjaWidget>(`/widgets/${tourId}`, {
        method: 'PUT',
        body: JSON.stringify({ status: 'draft' }),
      });

      return this.mapCommonNinjaTourToCreatedTour(response);
    } catch (error) {
      throw new TourCreationError(
        'UNPUBLISH_FAILED',
        'Failed to unpublish tour on CommonNinja API',
        error,
        'commonninja'
      );
    }
  }

  async getAnalytics(tourId: string, dateRange?: { start: Date; end: Date }): Promise<TourAnalytics> {
    try {
      const params = new URLSearchParams();
      if (dateRange) {
        params.append('start_date', dateRange.start.toISOString());
        params.append('end_date', dateRange.end.toISOString());
      }

      const response = await this.makeRequest<CommonNinjaAnalytics>(`/widgets/${tourId}/analytics?${params}`);
      
      return {
        views: response.views || 0,
        uniqueViews: response.unique_views || 0,
        averageTimeSpent: response.average_time_spent || 0,
        hotspotInteractions: response.hotspot_interactions || {},
        sceneViews: response.scene_views || {},
        conversionEvents: (response.conversion_events || []).map((event: { type: string; timestamp?: string; data?: Record<string, unknown> }) => ({
          type: event.type,
          timestamp: new Date(event.timestamp || Date.now()),
          data: event.data
        })),
      };
    } catch (error) {
      throw new TourCreationError(
        'ANALYTICS_FAILED',
        'Failed to get analytics from CommonNinja',
        error,
        'commonninja'
      );
    }
  }

  getEmbedCode(tourId: string, options: EmbedOptions = {}): string {
    const width = options.width || '100%';
    const height = options.height || '400px';
    const allowFullscreen = options.allowFullscreen !== false;

    return `<iframe 
      src="https://widget.commoninja.com/virtual-tour/${tourId}" 
      width="${width}" 
      height="${height}" 
      frameborder="0" 
      ${allowFullscreen ? 'allowfullscreen' : ''}
      ${options.customCSS ? `style="${options.customCSS}"` : ''}
    ></iframe>`;
  }

  setupEmbedAPI(_tourId: string, callbacks?: EmbedCallbacks): void {
    // CommonNinja embed API setup
    if (typeof window !== 'undefined') {
      window.addEventListener('message', (event) => {
        if (event.origin !== 'https://widget.commoninja.com') return;
        
        const { type, data } = event.data;
        
        switch (type) {
          case 'hotspot_click':
            callbacks?.onHotspotClick?.(data);
            break;
          case 'scene_change':
            callbacks?.onSceneChange?.(data);
            break;
          case 'tour_complete':
            callbacks?.onTourComplete?.();
            break;
          case 'error':
            callbacks?.onError?.(data.message);
            break;
        }
      });
    }
  }

  private async uploadImage(file: File): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch(`${this.baseUrl}/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      const result = await response.json();
      return result.url;
    } catch (error) {
      throw new TourCreationError(
        'UPLOAD_FAILED',
        'Failed to upload image to CommonNinja',
        error,
        'commonninja'
      );
    }
  }

  /**
   * Enhanced Widget Creation Methods
   * Support for multiple CommonNinja widget types for ecommerce integration
   */

  async createImageHotspotWidget(config: {
    title: string;
    description?: string;
    imageUrl: string;
    hotspots: Array<{
      x: number;
      y: number;
      label: string;
      content: string;
      type: 'info' | 'product' | 'link' | 'whatsapp';
      action?: {
        url?: string;
        phone?: string;
        message?: string;
        productId?: string;
      };
    }>;
    settings?: {
      showLabels?: boolean;
      hotspotStyle?: string;
      animation?: string;
    };
  }): Promise<CommonNinjaWidget> {
    try {
      const widgetConfig = {
        widget_type: CommonNinjaService.WIDGET_TYPES.IMAGE_HOTSPOT,
        title: config.title,
        description: config.description,
        settings: {
          image_url: config.imageUrl,
          hotspots: config.hotspots,
          show_labels: config.settings?.showLabels !== false,
          hotspot_style: config.settings?.hotspotStyle || 'default',
          animation: config.settings?.animation || 'pulse',
        },
      };

      const response = await this.makeRequest<CommonNinjaWidget>('/widgets', {
        method: 'POST',
        body: JSON.stringify(widgetConfig),
      });

      return response;
    } catch (error) {
      throw new TourCreationError(
        'WIDGET_CREATE_FAILED',
        'Failed to create image hotspot widget',
        error,
        'commonninja'
      );
    }
  }

  async createCatalogWidget(config: {
    title: string;
    description?: string;
    products: Array<{
      id: string;
      name: string;
      description: string;
      price: number;
      imageUrl: string;
      category?: string;
      vendor?: string;
    }>;
    settings?: {
      layout?: 'grid' | 'list' | 'masonry';
      columns?: number;
      showPrices?: boolean;
      enableFilters?: boolean;
      enableSearch?: boolean;
    };
  }): Promise<CommonNinjaWidget> {
    try {
      const widgetData = {
        name: config.title,
        type: 'catalog',
        description: config.description || '',
        status: 'draft' as const,
        data: {
          products: config.products,
          settings: {
            layout: config.settings?.layout || 'grid',
            columns: config.settings?.columns || 3,
            showPrices: config.settings?.showPrices !== false,
            enableFilters: config.settings?.enableFilters !== false,
            enableSearch: config.settings?.enableSearch !== false,
          }
        }
      };

      const response = await this.makeRequest<CommonNinjaWidget>('/widgets', {
        method: 'POST',
        body: JSON.stringify(widgetData),
      });

      return response;
    } catch (error) {
      throw new TourCreationError(
        'WIDGET_CREATE_FAILED',
        'Failed to create catalog widget',
        error,
        'commonninja'
      );
    }
  }

  async createBookingWidget(config: {
    title: string;
    description?: string;
    businessInfo: {
      name: string;
      phone: string;
      email: string;
      address?: string;
    };
    services: Array<{
      id: string;
      name: string;
      description: string;
      duration: number;
      price: number;
    }>;
    settings?: {
      timeSlots?: string[];
      workingDays?: string[];
      advanceBookingDays?: number;
    };
  }): Promise<CommonNinjaWidget> {
    try {
      const widgetConfig = {
        widget_type: CommonNinjaService.WIDGET_TYPES.BOOKING,
        title: config.title,
        description: config.description,
        settings: {
          business_info: config.businessInfo,
          services: config.services,
          time_slots: config.settings?.timeSlots || ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00'],
          working_days: config.settings?.workingDays || ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
          advance_booking_days: config.settings?.advanceBookingDays || 30,
        },
      };

      const response = await this.makeRequest<CommonNinjaWidget>('/widgets', {
        method: 'POST',
        body: JSON.stringify(widgetConfig),
      });

      return response;
    } catch (error) {
      throw new TourCreationError(
        'WIDGET_CREATE_FAILED',
        'Failed to create booking widget',
        error,
        'commonninja'
      );
    }
  }



  private mapCommonNinjaTourToCreatedTour(widget: CommonNinjaWidget): CreatedTour {
    return {
      id: widget.id,
      title: widget.name,
      description: widget.description,
      embedUrl: widget.embedCode?.html || `https://widget.commoninja.com/widget/${widget.id}`,
      editUrl: widget.editorUrl || `https://app.commoninja.com/widget/${widget.id}/edit`,
      previewUrl: `https://widget.commoninja.com/widget/${widget.id}?preview=true`,
      scenes: Array.isArray(widget.data?.scenes)
        ? (widget.data.scenes as CommonNinjaScene[]).map((scene: CommonNinjaScene) => this.mapCommonNinjaSceneToTourScene(scene))
        : [],
      platform: 'commonninja',
      platformSpecific: {
        commonNinjaWidgetId: widget.id,
      },
      settings: (widget.data?.settings as Record<string, unknown>) || {},
      createdAt: new Date(widget.created),
      updatedAt: new Date(widget.updated),
    };
  }

  private mapCommonNinjaSceneToTourScene(commonNinjaScene: CommonNinjaScene): TourScene {
    return {
      id: commonNinjaScene.id,
      name: commonNinjaScene.name,
      description: commonNinjaScene.description,
      imageUrl: commonNinjaScene.image_url,
      orderIndex: commonNinjaScene.order_index,
      hotspots: Array.isArray(commonNinjaScene.hotspots)
        ? commonNinjaScene.hotspots.map((hotspot: CommonNinjaHotspot) => this.mapCommonNinjaHotspotToTourHotspot(hotspot))
        : [],
      platformSpecific: {
        commonNinjaId: commonNinjaScene.widget_scene_id,
      },
    };
  }

  private mapCommonNinjaHotspotToTourHotspot(commonNinjaHotspot: CommonNinjaHotspot): TourHotspot {
    return {
      id: commonNinjaHotspot.id,
      type: 'custom' as const, // Map CommonNinja types to our internal types
      label: commonNinjaHotspot.label,
      content: commonNinjaHotspot.content,
      position: {
        x: commonNinjaHotspot.position.x,
        y: commonNinjaHotspot.position.y,
        z: 0 // Default z position for 2D hotspots
      },
      style: commonNinjaHotspot.style,
      actions: [], // Convert CommonNinja actions to our action format
      platformSpecific: {
        commonNinjaId: commonNinjaHotspot.hotspot_id,
        customData: {
          originalActions: commonNinjaHotspot.actions
        }
      },
    };
  }
}
