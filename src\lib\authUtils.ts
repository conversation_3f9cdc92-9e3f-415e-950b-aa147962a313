/**
 * Authentication Utilities for Fast Admin Detection
 * Provides instant admin role detection to prevent slow redirects
 */

// Known admin emails for instant detection
const ADMIN_EMAILS = [
  '<EMAIL>',
  // Add more admin emails here as needed
];

/**
 * Instantly check if user is admin based on email
 * This provides immediate feedback without waiting for profile fetch
 */
export const isAdminByEmail = (email: string): boolean => {
  return ADMIN_EMAILS.includes(email.toLowerCase());
};

/**
 * Get immediate redirect path based on user email
 * Provides instant redirect without waiting for profile
 */
export const getImmediateRedirect = (user: any, requestedPath?: string): string => {
  if (requestedPath && requestedPath !== '/auth') {
    return requestedPath;
  }
  
  if (user?.email && isAdminByEmail(user.email)) {
    return '/admin';
  }
  
  return '/dashboard';
};

/**
 * Check if current path matches user's expected dashboard
 */
export const isOnCorrectDashboard = (user: any, profile: any, currentPath: string): boolean => {
  if (!user) return true;
  
  const isAdmin = profile?.role === 'admin' || isAdminByEmail(user.email);
  
  if (isAdmin && currentPath.startsWith('/admin')) return true;
  if (!isAdmin && currentPath === '/dashboard') return true;
  
  return false;
};
