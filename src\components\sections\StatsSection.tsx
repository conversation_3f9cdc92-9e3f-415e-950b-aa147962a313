
import { Globe, Users, Award, Zap } from 'lucide-react'

const StatsSection = () => {
  const stats = [
    { icon: Globe, label: 'Countries Served', value: '20+' },
    { icon: Users, label: 'Happy Clients', value: '500+' },
    { icon: Award, label: 'Tours Created', value: '2,000+' },
    { icon: Zap, label: 'Views Generated', value: '1M+' }
  ]

  return (
    <section className="relative py-20 lg:py-24 overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0">
        <img
          src="https://images.unsplash.com/photo-1564013799919-ab600027ffc6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
          alt="Luxury hotel lobby showcasing hospitality virtual tours"
          className="w-full h-full object-cover object-center"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-black/70"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
      </div>

      {/* Content */}
      <div className="container px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 lg:mb-20">
          <div className="inline-flex items-center rounded-full bg-white/10 backdrop-blur-sm border border-white/20 px-4 py-2 text-sm font-medium text-white mb-6">
            📊 Our Impact
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
            Trusted by Businesses
            <br />
            <span className="text-theme-primary">Worldwide</span>
          </h2>
          <p className="text-lg text-white/80 max-w-3xl mx-auto leading-relaxed">
            Join thousands of satisfied clients who have transformed their spaces with our professional virtual tour solutions.
          </p>
        </div>

        {/* Enhanced Stats Grid */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8 max-w-6xl mx-auto">
          {stats.map((stat, index) => (
            <div
              key={stat.label}
              className="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20 text-center shadow-lg hover:bg-white/20 transition-all duration-300 hover:scale-105"
            >
              <div className="w-12 h-12 bg-theme-primary/20 backdrop-blur-sm rounded-lg flex items-center justify-center mx-auto mb-4">
                <stat.icon className="w-6 h-6 text-theme-primary" />
              </div>
              <div className="text-2xl lg:text-3xl font-bold text-white mb-1 drop-shadow-md">{stat.value}</div>
              <div className="text-sm text-white/80">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default StatsSection
