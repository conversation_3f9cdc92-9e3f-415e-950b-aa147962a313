
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import CategorySelect from './CategorySelect';
import { FormData } from '@/hooks/useUploadForm';

interface UploadStepOneProps {
  formData: FormData;
  setFormData: (data: FormData) => void;
}

const UploadStepOne = ({ formData, setFormData }: UploadStepOneProps) => {
  return (
    <div className="space-y-6">
      {/* Basic Information Section */}
      <div className="space-y-4">
        <Label className="text-base font-semibold">Basic Information</Label>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="title">Tour Title *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="e.g., Luxury Villa in Lekki Phase 1"
              required
              className="h-10"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="category">Category *</Label>
            <CategorySelect
              value={formData.category}
              onValueChange={(value) => setFormData({ ...formData, category: value })}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Describe what visitors will experience in this virtual tour..."
            rows={3}
            className="min-h-[80px]"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="location">Location *</Label>
          <Input
            id="location"
            value={formData.location}
            onChange={(e) => setFormData({ ...formData, location: e.target.value })}
            placeholder="e.g., Victoria Island, Lagos"
            required
            className="h-10"
          />
        </div>
      </div>

      {/* Business Information Section */}
      <div className="border-t pt-6 space-y-4">
        <div>
          <h3 className="text-lg font-semibold">Business Information</h3>
          <p className="text-sm text-muted-foreground mt-1">
            Optional information for Google Business integration and SEO optimization
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="businessName">Business Name</Label>
            <Input
              id="businessName"
              value={formData.businessName}
              onChange={(e) => setFormData({ ...formData, businessName: e.target.value })}
              placeholder="e.g., Luxury Hotels Lagos"
              className="h-10"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="businessType">Business Type</Label>
            <Input
              id="businessType"
              value={formData.businessType}
              onChange={(e) => setFormData({ ...formData, businessType: e.target.value })}
              placeholder="e.g., Hotel, Restaurant, Office"
              className="h-10"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="contactPhone">Contact Phone</Label>
            <Input
              id="contactPhone"
              value={formData.contactPhone}
              onChange={(e) => setFormData({ ...formData, contactPhone: e.target.value })}
              placeholder="e.g., +234 ************"
              type="tel"
              className="h-10"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="contactEmail">Contact Email</Label>
            <Input
              id="contactEmail"
              value={formData.contactEmail}
              onChange={(e) => setFormData({ ...formData, contactEmail: e.target.value })}
              placeholder="e.g., <EMAIL>"
              type="email"
              className="h-10"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="website">Website</Label>
            <Input
              id="website"
              value={formData.website}
              onChange={(e) => setFormData({ ...formData, website: e.target.value })}
              placeholder="e.g., https://www.business.com"
              type="url"
              className="h-10"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="businessHours">Business Hours</Label>
            <Input
              id="businessHours"
              value={formData.businessHours}
              onChange={(e) => setFormData({ ...formData, businessHours: e.target.value })}
              placeholder="e.g., Mon-Fri 9AM-6PM"
              className="h-10"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default UploadStepOne;
