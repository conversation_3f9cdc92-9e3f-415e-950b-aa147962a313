
import { Card, CardContent } from '@/components/ui/card';
import { Users, MapPin, Eye, Calendar, Shield, Star } from 'lucide-react';
import { Tour, Profile } from '@/lib/supabase';

interface AdminStatsCardsProps {
  tours: Tour[];
  users: Profile[];
  analytics: Record<string, unknown>[];
}

const AdminStatsCards = ({ tours, users, analytics }: AdminStatsCardsProps) => {
  const stats = {
    totalTours: tours.length,
    publishedTours: tours.filter(t => t.status === 'published').length,
    pendingTours: tours.filter(t => t.status === 'draft').length,
    totalUsers: users.length,
    totalViews: tours.reduce((sum, tour) => sum + tour.views, 0),
    featuredTours: tours.filter(t => t.featured).length,
    recentSignups: users.filter(u => {
      const signupDate = new Date(u.created_at);
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      return signupDate > weekAgo;
    }).length,
    todayViews: analytics.filter(a => {
      const viewDate = new Date(a.viewed_at);
      const today = new Date();
      return viewDate.toDateString() === today.toDateString();
    }).length
  };

  const statCards = [
    {
      title: 'Total Tours',
      value: stats.totalTours,
      subtitle: `${stats.publishedTours} published`,
      icon: MapPin,
      color: 'text-blue-600'
    },
    {
      title: 'Total Users',
      value: stats.totalUsers,
      subtitle: `+${stats.recentSignups} this week`,
      icon: Users,
      color: 'text-green-600'
    },
    {
      title: 'Total Views',
      value: stats.totalViews.toLocaleString(),
      subtitle: `${stats.todayViews} today`,
      icon: Eye,
      color: 'text-purple-600'
    },
    {
      title: 'Pending Approval',
      value: stats.pendingTours,
      subtitle: 'Requires attention',
      icon: Calendar,
      color: 'text-orange-600'
    }
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
      {statCards.map((stat, index) => (
        <Card key={index} className="hover:shadow-md transition-shadow duration-200">
          <CardContent className="p-4 sm:p-6">
            <div className="flex items-center justify-between">
              <div className="space-y-1 sm:space-y-2 min-w-0 flex-1">
                <p className="text-xs sm:text-sm font-medium text-muted-foreground truncate">{stat.title}</p>
                <p className="text-xl sm:text-2xl md:text-3xl font-bold tracking-tight">{stat.value}</p>
                <p className="text-xs text-muted-foreground flex items-center gap-1 truncate">
                  {stat.subtitle}
                </p>
              </div>
              <div className={`p-2 sm:p-3 rounded-full bg-gradient-to-br from-background to-muted flex-shrink-0`}>
                <stat.icon className={`w-5 h-5 sm:w-6 sm:h-6 ${stat.color}`} />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

export default AdminStatsCards;
