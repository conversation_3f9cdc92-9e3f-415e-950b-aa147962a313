import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowRight, MapPin, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { EnhancedTourCard } from '@/components/tours/EnhancedTourCard';
import { supabase } from '@/integrations/supabase/client';

interface Tour {
  id: string;
  title: string;
  description: string;
  location: string;
  category: string;
  views: number;
  slug?: string;
  thumbnail_url?: string;
  tour_url: string;
}

const MapboxStyleFeaturedTours = () => {
  const [tours, setTours] = useState<Tour[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchFeaturedTours = async () => {
      try {
        const { data, error } = await supabase
          .from('tours')
          .select('*')
          .eq('status', 'published')
          .order('views', { ascending: false })
          .limit(6);

        if (error) throw error;
        setTours(data || []);
      } catch (error) {
        console.error('Error fetching tours:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedTours();
  }, []);

  if (loading) {
    return (
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Featured Virtual Tours
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              Explore stunning 360° virtual tours created by businesses across Nigeria
            </p>
          </div>

          {/* Loading State */}
          <div className="animate-pulse">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className="bg-gray-200 rounded-lg h-80" />
              ))}
            </div>
          </div>
          <p className="text-gray-500 text-lg mt-8 text-center">Loading featured tours...</p>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Featured Virtual Tours
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            Explore stunning 360° virtual tours created by businesses across Nigeria
          </p>
        </div>

        {/* Mapbox-Style Tour Carousel */}
        {tours.length > 0 && (
          <div className="relative">
            {/* Main Carousel Container */}
            <div className="overflow-hidden rounded-lg">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {tours.map((tour, index) => (
                  <motion.div
                    key={tour.id}
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="group"
                  >
                    <Link to={`/tour/${tour.slug || tour.id}`} className="block">
                      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                        {/* Tour Preview */}
                        <div className="relative">
                          <EnhancedTourCard
                            tour={tour}
                            showActions={false}
                            showEmbedded={true}
                            autoLoad={false}
                            height="240px"
                            className="border-0 shadow-none"
                          />
                          {/* Overlay with category badge */}
                          <div className="absolute top-4 left-4">
                            <Badge variant="secondary" className="bg-white/90 text-gray-800 font-medium">
                              {tour.category}
                            </Badge>
                          </div>
                          {/* Featured badge */}
                          <div className="absolute top-4 right-4">
                            <Badge className="bg-blue-600 text-white font-medium">
                              Featured
                            </Badge>
                          </div>
                          {/* Hover overlay */}
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300 flex items-center justify-center">
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                              <div className="bg-white/90 backdrop-blur-sm rounded-full p-3">
                                <ArrowRight className="w-6 h-6 text-gray-900" />
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Tour Info */}
                        <div className="p-6">
                          <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">
                            {tour.title}
                          </h3>
                          <p className="text-gray-600 mb-4 line-clamp-2 leading-relaxed">
                            {tour.description}
                          </p>
                          
                          {/* Tour Stats */}
                          <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                            <div className="flex items-center">
                              <MapPin className="w-4 h-4 mr-1" />
                              <span className="truncate">{tour.location}</span>
                            </div>
                            <div className="flex items-center">
                              <Eye className="w-4 h-4 mr-1" />
                              <span>{tour.views || 0} views</span>
                            </div>
                          </div>

                          {/* View Tour Button */}
                          <div className="pt-4 border-t border-gray-100">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-900">
                                Virtual Tour
                              </span>
                              <div className="flex items-center text-blue-600 font-medium text-sm group-hover:text-blue-700">
                                <span>Explore</span>
                                <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Bottom Section */}
            <div className="mt-16 text-center">
              <div className="mb-8">
                <p className="text-gray-600 mb-4">
                  Discover more virtual tours from businesses across Nigeria
                </p>
                <Button size="lg" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-3" asChild>
                  <Link to="/showcase">
                    View All Tours
                    <ArrowRight className="w-5 h-5 ml-2" />
                  </Link>
                </Button>
              </div>

              {/* Tour Categories */}
              <div className="flex flex-wrap justify-center gap-3">
                {['Property', 'Hospitality', 'Commercial', 'Education', 'Healthcare', 'Tourism'].map((category) => (
                  <Link
                    key={category}
                    to={`/showcase?category=${category.toLowerCase()}`}
                    className="px-4 py-2 text-sm font-medium text-gray-600 bg-gray-100 rounded-full hover:bg-blue-100 hover:text-blue-600 transition-colors duration-300"
                  >
                    {category}
                  </Link>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {tours.length === 0 && !loading && (
          <div className="text-center py-16">
            <p className="text-gray-500 text-lg">No featured tours available at the moment.</p>
          </div>
        )}
      </div>
    </section>
  );
};

export default MapboxStyleFeaturedTours;
