/**
 * WhatsAppAutomationManager Component
 * Admin interface for managing WhatsApp automation rules and templates
 * Mobile-first responsive design for automation management
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { 
  MessageCircle, 
  Plus, 
  Edit, 
  Trash2, 
  Play, 
  Pause, 
  Clock, 
  Users,
  TrendingUp,
  Settings,
  Send,
  Bot,
  Zap,
  Target,
  BarChart3,
  Calendar
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface AutomationRule {
  id: string;
  name: string;
  description: string;
  trigger: {
    type: 'order_status' | 'cart_abandoned' | 'new_customer' | 'vendor_signup' | 'time_based';
    conditions: any;
  };
  action: {
    type: 'send_message' | 'send_template' | 'assign_tag' | 'create_task';
    data: any;
  };
  isActive: boolean;
  created_at: string;
  stats: {
    triggered: number;
    sent: number;
    delivered: number;
    read: number;
  };
}

interface MessageTemplate {
  id: string;
  name: string;
  category: 'order' | 'marketing' | 'support' | 'vendor';
  subject: string;
  content: string;
  variables: string[];
  isActive: boolean;
  usage_count: number;
}

interface WhatsAppAutomationManagerProps {
  className?: string;
}

const WhatsAppAutomationManager = ({ className }: WhatsAppAutomationManagerProps) => {
  const [automationRules, setAutomationRules] = useState<AutomationRule[]>([]);
  const [messageTemplates, setMessageTemplates] = useState<MessageTemplate[]>([]);
  const [selectedRule, setSelectedRule] = useState<AutomationRule | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<MessageTemplate | null>(null);
  const [isCreateRuleOpen, setIsCreateRuleOpen] = useState(false);
  const [isCreateTemplateOpen, setIsCreateTemplateOpen] = useState(false);

  // Sample data
  useEffect(() => {
    setAutomationRules([
      {
        id: '1',
        name: 'Order Confirmation',
        description: 'Send confirmation when order is placed',
        trigger: {
          type: 'order_status',
          conditions: { status: 'pending' }
        },
        action: {
          type: 'send_template',
          data: { template_id: 'order_confirmation' }
        },
        isActive: true,
        created_at: '2024-01-15T10:00:00Z',
        stats: {
          triggered: 156,
          sent: 156,
          delivered: 152,
          read: 148
        }
      },
      {
        id: '2',
        name: 'Abandoned Cart Reminder',
        description: 'Remind customers about items in cart after 1 hour',
        trigger: {
          type: 'cart_abandoned',
          conditions: { delay_minutes: 60 }
        },
        action: {
          type: 'send_template',
          data: { template_id: 'abandoned_cart' }
        },
        isActive: true,
        created_at: '2024-01-10T14:30:00Z',
        stats: {
          triggered: 89,
          sent: 89,
          delivered: 85,
          read: 67
        }
      },
      {
        id: '3',
        name: 'Welcome New Vendors',
        description: 'Welcome message for approved vendors',
        trigger: {
          type: 'vendor_signup',
          conditions: { status: 'approved' }
        },
        action: {
          type: 'send_template',
          data: { template_id: 'vendor_welcome' }
        },
        isActive: true,
        created_at: '2024-01-05T09:15:00Z',
        stats: {
          triggered: 23,
          sent: 23,
          delivered: 23,
          read: 21
        }
      }
    ]);

    setMessageTemplates([
      {
        id: '1',
        name: 'Order Confirmation',
        category: 'order',
        subject: 'Order Confirmed - {{order_number}}',
        content: 'Hi {{customer_name}}! Your order {{order_number}} for ₦{{total_amount}} has been confirmed. Track: {{tracking_url}}',
        variables: ['customer_name', 'order_number', 'total_amount', 'tracking_url'],
        isActive: true,
        usage_count: 156
      },
      {
        id: '2',
        name: 'Abandoned Cart',
        category: 'marketing',
        subject: 'Don\'t forget your items!',
        content: 'Hi! You left {{item_count}} items in your cart worth ₦{{cart_value}}. Complete your purchase: {{cart_url}}',
        variables: ['item_count', 'cart_value', 'cart_url'],
        isActive: true,
        usage_count: 89
      },
      {
        id: '3',
        name: 'Vendor Welcome',
        category: 'vendor',
        subject: 'Welcome to VirtualRealTour!',
        content: 'Welcome {{vendor_name}}! Your account is approved. Commission rate: {{commission_rate}}%. Start selling: {{dashboard_url}}',
        variables: ['vendor_name', 'commission_rate', 'dashboard_url'],
        isActive: true,
        usage_count: 23
      }
    ]);
  }, []);

  const toggleRuleStatus = (ruleId: string) => {
    setAutomationRules(prev => 
      prev.map(rule => 
        rule.id === ruleId 
          ? { ...rule, isActive: !rule.isActive }
          : rule
      )
    );
    toast.success('Automation rule updated');
  };

  const toggleTemplateStatus = (templateId: string) => {
    setMessageTemplates(prev => 
      prev.map(template => 
        template.id === templateId 
          ? { ...template, isActive: !template.isActive }
          : template
      )
    );
    toast.success('Message template updated');
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800';
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'order': return 'bg-blue-100 text-blue-800';
      case 'marketing': return 'bg-purple-100 text-purple-800';
      case 'support': return 'bg-orange-100 text-orange-800';
      case 'vendor': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const StatCard = ({ 
    title, 
    value, 
    icon: Icon, 
    color = 'blue',
    change 
  }: { 
    title: string; 
    value: string | number; 
    icon: any; 
    color?: string;
    change?: string;
  }) => (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold">{value}</p>
            {change && (
              <p className="text-xs text-green-600 mt-1">{change}</p>
            )}
          </div>
          <div className={cn(
            "w-12 h-12 rounded-lg flex items-center justify-center",
            color === 'blue' && 'bg-blue-100 text-blue-600',
            color === 'green' && 'bg-green-100 text-green-600',
            color === 'purple' && 'bg-purple-100 text-purple-600',
            color === 'orange' && 'bg-orange-100 text-orange-600'
          )}>
            <Icon className="w-6 h-6" />
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const AutomationRuleCard = ({ rule }: { rule: AutomationRule }) => (
    <Card className="hover:shadow-lg transition-all duration-300">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg line-clamp-1">{rule.name}</h3>
            <p className="text-sm text-muted-foreground line-clamp-2">{rule.description}</p>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getStatusColor(rule.isActive)} variant="outline">
              {rule.isActive ? 'Active' : 'Inactive'}
            </Badge>
            <Switch
              checked={rule.isActive}
              onCheckedChange={() => toggleRuleStatus(rule.id)}
            />
          </div>
        </div>

        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Trigger:</span>
            <Badge variant="outline" className="capitalize">
              {rule.trigger.type.replace('_', ' ')}
            </Badge>
          </div>
          
          <div className="grid grid-cols-4 gap-2 text-xs">
            <div className="text-center">
              <div className="font-semibold">{rule.stats.triggered}</div>
              <div className="text-muted-foreground">Triggered</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">{rule.stats.sent}</div>
              <div className="text-muted-foreground">Sent</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">{rule.stats.delivered}</div>
              <div className="text-muted-foreground">Delivered</div>
            </div>
            <div className="text-center">
              <div className="font-semibold">{rule.stats.read}</div>
              <div className="text-muted-foreground">Read</div>
            </div>
          </div>
        </div>

        <div className="flex justify-between mt-4">
          <Button variant="outline" size="sm" onClick={() => setSelectedRule(rule)}>
            <Edit className="w-3 h-3 mr-1" />
            Edit
          </Button>
          <div className="flex gap-1">
            <Button variant="ghost" size="sm">
              <BarChart3 className="w-3 h-3" />
            </Button>
            <Button variant="ghost" size="sm">
              <Trash2 className="w-3 h-3 text-red-500" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const MessageTemplateCard = ({ template }: { template: MessageTemplate }) => (
    <Card className="hover:shadow-lg transition-all duration-300">
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-lg line-clamp-1">{template.name}</h3>
            <p className="text-sm text-muted-foreground line-clamp-1">{template.subject}</p>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getCategoryColor(template.category)} variant="outline">
              {template.category}
            </Badge>
            <Switch
              checked={template.isActive}
              onCheckedChange={() => toggleTemplateStatus(template.id)}
            />
          </div>
        </div>

        <div className="space-y-2">
          <div className="text-sm bg-gray-50 p-2 rounded line-clamp-2">
            {template.content}
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Variables:</span>
            <span className="font-medium">{template.variables.length}</span>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Usage:</span>
            <span className="font-medium">{template.usage_count} times</span>
          </div>
        </div>

        <div className="flex justify-between mt-4">
          <Button variant="outline" size="sm" onClick={() => setSelectedTemplate(template)}>
            <Edit className="w-3 h-3 mr-1" />
            Edit
          </Button>
          <div className="flex gap-1">
            <Button variant="ghost" size="sm">
              <Send className="w-3 h-3" />
            </Button>
            <Button variant="ghost" size="sm">
              <Trash2 className="w-3 h-3 text-red-500" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">WhatsApp Automation</h1>
          <p className="text-muted-foreground">Manage automated messaging and customer communication</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            New Rule
          </Button>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Active Rules"
          value={automationRules.filter(r => r.isActive).length}
          icon={Bot}
          color="blue"
          change="+2 this week"
        />
        <StatCard
          title="Messages Sent"
          value="1,234"
          icon={Send}
          color="green"
          change="+15% this month"
        />
        <StatCard
          title="Delivery Rate"
          value="98.5%"
          icon={Target}
          color="purple"
          change="+0.3% this week"
        />
        <StatCard
          title="Response Rate"
          value="67%"
          icon={TrendingUp}
          color="orange"
          change="+5% this month"
        />
      </div>

      {/* Main Content */}
      <Tabs defaultValue="rules" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="rules">Automation Rules</TabsTrigger>
          <TabsTrigger value="templates">Message Templates</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Automation Rules */}
        <TabsContent value="rules" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Automation Rules</h2>
            <Button onClick={() => setIsCreateRuleOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Rule
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {automationRules.map((rule) => (
              <AutomationRuleCard key={rule.id} rule={rule} />
            ))}
          </div>
        </TabsContent>

        {/* Message Templates */}
        <TabsContent value="templates" className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Message Templates</h2>
            <Button onClick={() => setIsCreateTemplateOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Create Template
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
            {messageTemplates.map((template) => (
              <MessageTemplateCard key={template.id} template={template} />
            ))}
          </div>
        </TabsContent>

        {/* Analytics */}
        <TabsContent value="analytics" className="space-y-4">
          <h2 className="text-xl font-semibold">Analytics & Performance</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Message Performance</CardTitle>
                <CardDescription>Last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span>Messages Sent</span>
                    <span className="font-semibold">1,234</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Delivered</span>
                    <span className="font-semibold">1,215 (98.5%)</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Read</span>
                    <span className="font-semibold">1,089 (88.2%)</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span>Replied</span>
                    <span className="font-semibold">827 (67.0%)</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Performing Rules</CardTitle>
                <CardDescription>By engagement rate</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {automationRules.map((rule, index) => (
                    <div key={rule.id} className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-sm">{rule.name}</div>
                        <div className="text-xs text-muted-foreground">
                          {rule.stats.read}/{rule.stats.sent} read
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-semibold text-sm">
                          {((rule.stats.read / rule.stats.sent) * 100).toFixed(1)}%
                        </div>
                        <div className="text-xs text-muted-foreground">
                          #{index + 1}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Create Rule Dialog */}
      <Dialog open={isCreateRuleOpen} onOpenChange={setIsCreateRuleOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Automation Rule</DialogTitle>
            <DialogDescription>
              Set up automated WhatsApp messaging based on triggers
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="rule-name">Rule Name</Label>
              <Input id="rule-name" placeholder="Enter rule name" />
            </div>
            <div>
              <Label htmlFor="rule-description">Description</Label>
              <Textarea id="rule-description" placeholder="Describe what this rule does" />
            </div>
            <div>
              <Label htmlFor="trigger-type">Trigger Type</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select trigger type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="order_status">Order Status Change</SelectItem>
                  <SelectItem value="cart_abandoned">Cart Abandoned</SelectItem>
                  <SelectItem value="new_customer">New Customer</SelectItem>
                  <SelectItem value="vendor_signup">Vendor Signup</SelectItem>
                  <SelectItem value="time_based">Time Based</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsCreateRuleOpen(false)}>
                Cancel
              </Button>
              <Button>Create Rule</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Create Template Dialog */}
      <Dialog open={isCreateTemplateOpen} onOpenChange={setIsCreateTemplateOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Create Message Template</DialogTitle>
            <DialogDescription>
              Create reusable message templates for automation
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="template-name">Template Name</Label>
              <Input id="template-name" placeholder="Enter template name" />
            </div>
            <div>
              <Label htmlFor="template-category">Category</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="order">Order</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                  <SelectItem value="support">Support</SelectItem>
                  <SelectItem value="vendor">Vendor</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="template-subject">Subject</Label>
              <Input id="template-subject" placeholder="Message subject" />
            </div>
            <div>
              <Label htmlFor="template-content">Message Content</Label>
              <Textarea 
                id="template-content" 
                placeholder="Use {{variable_name}} for dynamic content"
                rows={4}
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setIsCreateTemplateOpen(false)}>
                Cancel
              </Button>
              <Button>Create Template</Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default WhatsAppAutomationManager;
