/**
 * VirtualRealTour Service
 * Underground integration with CommonNinja while maintaining our branding
 * Users never see CommonNinja - everything appears as VirtualRealTour
 */

interface TourCreationConfig {
  title: string;
  description: string;
  category: string;
  location: string;
  businessInfo?: {
    name: string;
    phone: string;
    email: string;
    whatsapp: string;
  };
}

interface TourWidget {
  id: string;
  embedCode: string;
  editUrl: string;
  previewUrl: string;
  publicUrl: string;
  status: 'draft' | 'published';
  createdAt: string;
  updatedAt: string;
}

class VirtualRealTourService {
  private baseUrl = 'https://api.virtualrealtour.ng';
  private commonNinjaApiKey = import.meta.env.VITE_COMMONNINJA_API_KEY;

  /**
   * Create a new tour widget (underground CommonNinja integration)
   */
  async createTour(config: TourCreationConfig): Promise<TourWidget> {
    try {
      // Step 1: Create CommonNinja widget underground
      const commonNinjaWidget = await this.createCommonNinjaWidget(config);
      
      // Step 2: Store in our database with our URLs
      const tourWidget: TourWidget = {
        id: `vrt_${Date.now()}`,
        embedCode: this.wrapEmbedCode(commonNinjaWidget.embedCode),
        editUrl: `${this.baseUrl}/editor/${commonNinjaWidget.id}`,
        previewUrl: `${this.baseUrl}/preview/${commonNinjaWidget.id}`,
        publicUrl: `https://virtualrealtour.ng/tour/${commonNinjaWidget.id}`,
        status: 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Step 3: Save to our database
      await this.saveTourWidget(tourWidget);

      return tourWidget;
    } catch (error) {
      console.error('Failed to create tour:', error);
      throw new Error('Failed to create virtual tour');
    }
  }

  /**
   * Get tour editor URL (appears as our URL but proxies to CommonNinja)
   */
  getTourEditorUrl(tourId: string): string {
    // This would be our proxy URL that redirects to CommonNinja with our branding
    return `${this.baseUrl}/editor/${tourId}?platform=virtualrealtour&branding=true`;
  }

  /**
   * Get tour preview URL (our branded URL)
   */
  getTourPreviewUrl(tourId: string): string {
    return `https://virtualrealtour.ng/tour/${tourId}`;
  }

  /**
   * Update tour configuration
   */
  async updateTour(tourId: string, updates: Partial<TourCreationConfig>): Promise<TourWidget> {
    try {
      // Update both our database and CommonNinja underground
      const existingTour = await this.getTourWidget(tourId);
      
      if (existingTour) {
        // Update CommonNinja widget
        await this.updateCommonNinjaWidget(tourId, updates);
        
        // Update our database
        const updatedTour = {
          ...existingTour,
          updatedAt: new Date().toISOString()
        };
        
        await this.saveTourWidget(updatedTour);
        return updatedTour;
      }
      
      throw new Error('Tour not found');
    } catch (error) {
      console.error('Failed to update tour:', error);
      throw new Error('Failed to update virtual tour');
    }
  }

  /**
   * Publish tour (make it live)
   */
  async publishTour(tourId: string): Promise<TourWidget> {
    try {
      const tour = await this.getTourWidget(tourId);
      
      if (tour) {
        // Publish on CommonNinja underground
        await this.publishCommonNinjaWidget(tourId);
        
        // Update our database
        const publishedTour = {
          ...tour,
          status: 'published' as const,
          updatedAt: new Date().toISOString()
        };
        
        await this.saveTourWidget(publishedTour);
        return publishedTour;
      }
      
      throw new Error('Tour not found');
    } catch (error) {
      console.error('Failed to publish tour:', error);
      throw new Error('Failed to publish virtual tour');
    }
  }

  /**
   * Get embed code with our branding
   */
  getEmbedCode(tourId: string): string {
    return `
      <iframe 
        src="https://virtualrealtour.ng/embed/${tourId}"
        width="100%" 
        height="400"
        frameborder="0"
        allowfullscreen
        title="VirtualRealTour - Professional 360° Virtual Tour"
      ></iframe>
    `;
  }

  /**
   * Add e-commerce integration to tour
   */
  async addEcommerceIntegration(tourId: string, products: any[]): Promise<void> {
    try {
      // Add products to CommonNinja widget underground
      await this.addProductsToCommonNinjaWidget(tourId, products);
      
      // Update our database with e-commerce flag
      const tour = await this.getTourWidget(tourId);
      if (tour) {
        await this.saveTourWidget({
          ...tour,
          // Add e-commerce metadata
          updatedAt: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Failed to add e-commerce integration:', error);
      throw new Error('Failed to add e-commerce integration');
    }
  }

  // Private methods for underground CommonNinja integration

  private async createCommonNinjaWidget(config: TourCreationConfig): Promise<any> {
    // This would make actual API calls to CommonNinja
    // For now, return mock data
    return {
      id: `cn_${Date.now()}`,
      embedCode: `<iframe src="https://widget.commoninja.com/virtual-tour/cn_${Date.now()}" width="100%" height="400"></iframe>`,
      editUrl: `https://app.commoninja.com/editor/virtual-tour/cn_${Date.now()}`
    };
  }

  private async updateCommonNinjaWidget(tourId: string, updates: any): Promise<void> {
    // Update CommonNinja widget via their API
    console.log('Updating CommonNinja widget:', tourId, updates);
  }

  private async publishCommonNinjaWidget(tourId: string): Promise<void> {
    // Publish CommonNinja widget via their API
    console.log('Publishing CommonNinja widget:', tourId);
  }

  private async addProductsToCommonNinjaWidget(tourId: string, products: any[]): Promise<void> {
    // Add products to CommonNinja widget via their API
    console.log('Adding products to CommonNinja widget:', tourId, products);
  }

  private wrapEmbedCode(originalEmbedCode: string): string {
    // Replace CommonNinja URLs with our proxy URLs
    return originalEmbedCode
      .replace(/widget\.commoninja\.com/g, 'widget.virtualrealtour.ng')
      .replace(/commoninja/g, 'virtualrealtour');
  }

  private async saveTourWidget(widget: TourWidget): Promise<void> {
    // Save to our Supabase database
    console.log('Saving tour widget to database:', widget);
  }

  private async getTourWidget(tourId: string): Promise<TourWidget | null> {
    // Get from our Supabase database
    console.log('Getting tour widget from database:', tourId);
    return null;
  }

  /**
   * Get analytics for tour (our own analytics + CommonNinja data)
   */
  async getTourAnalytics(tourId: string): Promise<any> {
    try {
      // Combine our analytics with CommonNinja data
      const ourAnalytics = await this.getOurAnalytics(tourId);
      const commonNinjaAnalytics = await this.getCommonNinjaAnalytics(tourId);
      
      return {
        ...ourAnalytics,
        ...commonNinjaAnalytics,
        provider: 'VirtualRealTour'
      };
    } catch (error) {
      console.error('Failed to get tour analytics:', error);
      return null;
    }
  }

  private async getOurAnalytics(tourId: string): Promise<any> {
    // Get analytics from our database
    return {
      views: 0,
      interactions: 0,
      conversions: 0
    };
  }

  private async getCommonNinjaAnalytics(tourId: string): Promise<any> {
    // Get analytics from CommonNinja API
    return {
      hotspotClicks: 0,
      averageViewTime: 0,
      deviceBreakdown: {}
    };
  }

  /**
   * Generate WhatsApp integration for e-commerce
   */
  generateWhatsAppIntegration(businessPhone: string, productName: string, tourUrl: string): string {
    const message = encodeURIComponent(
      `Hi! I'm interested in ${productName} from your virtual tour: ${tourUrl}`
    );
    return `https://wa.me/${businessPhone}?text=${message}`;
  }

  /**
   * Create custom branded tour URLs
   */
  createCustomTourUrl(tourId: string, customSlug?: string): string {
    const slug = customSlug || tourId;
    return `https://virtualrealtour.ng/tour/${slug}`;
  }

  /**
   * Export tour data (for backup or migration)
   */
  async exportTourData(tourId: string): Promise<any> {
    try {
      const tour = await this.getTourWidget(tourId);
      const analytics = await this.getTourAnalytics(tourId);
      
      return {
        tour,
        analytics,
        exportedAt: new Date().toISOString(),
        platform: 'VirtualRealTour'
      };
    } catch (error) {
      console.error('Failed to export tour data:', error);
      throw new Error('Failed to export tour data');
    }
  }
}

// Export singleton instance
export const virtualRealTourService = new VirtualRealTourService();
export default VirtualRealTourService;
