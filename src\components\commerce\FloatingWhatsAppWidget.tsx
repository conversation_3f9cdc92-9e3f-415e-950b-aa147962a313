/**
 * Floating WhatsApp Widget Component
 * A floating WhatsApp chat button for customer support
 * Mobile-first responsive design with Nigerian market optimization
 */

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MessageCircle, X, Phone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { usePublicChatSettings } from '@/hooks/useChatSettings';

const FloatingWhatsAppWidget = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { chatSettings, isLoading } = usePublicChatSettings();

  // Use admin settings with fallbacks to original values
  const whatsappNumber = chatSettings?.businessPhone || '+2349077776066';
  const defaultMessage = chatSettings?.defaultMessage || 'Hi! I need help with VirtualRealTour. Can you assist me?';
  const businessName = chatSettings?.businessName || 'VirtualRealTour Support';
  const isWidgetEnabled = chatSettings?.enabled !== false; // Default to true if not set

  const handleWhatsAppClick = (customMessage?: string) => {
    const message = customMessage || defaultMessage;
    const whatsappUrl = `https://wa.me/${whatsappNumber.replace('+', '')}?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
    setIsExpanded(false);
  };

  const handlePhoneClick = () => {
    window.location.href = `tel:${whatsappNumber}`;
  };

  // Don't show widget if disabled by admin, loading, or not enabled
  if (isLoading || !isWidgetEnabled) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-40 md:bottom-6 md:right-6">
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.8 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="mb-4"
          >
            <Card className="w-80 max-w-[calc(100vw-2rem)] shadow-xl border-0 bg-white/95 backdrop-blur-sm">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                      <MessageCircle className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-sm font-semibold">{businessName}</CardTitle>
                      <CardDescription className="text-xs">We're here to help!</CardDescription>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsExpanded(false)}
                    className="h-8 w-8 p-0 hover:bg-gray-100"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="pt-0 space-y-3">
                <p className="text-sm text-gray-600">
                  Need help with shopping, orders, or virtual tours? Chat with us on WhatsApp!
                </p>
                
                <div className="space-y-2">
                  <Button
                    onClick={() => handleWhatsAppClick()}
                    className="w-full bg-green-500 hover:bg-green-600 text-white"
                    size="sm"
                  >
                    <MessageCircle className="w-4 h-4 mr-2" />
                    Start WhatsApp Chat
                  </Button>
                  
                  <Button
                    onClick={handlePhoneClick}
                    variant="outline"
                    className="w-full"
                    size="sm"
                  >
                    <Phone className="w-4 h-4 mr-2" />
                    Call {whatsappNumber}
                  </Button>
                </div>

                <div className="space-y-2">
                  <p className="text-xs text-gray-500 font-medium">Quick Messages:</p>
                  <div className="space-y-1">
                    <button
                      onClick={() => handleWhatsAppClick('Hi! I need help with placing an order.')}
                      className="w-full text-left text-xs text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      • Help with placing an order
                    </button>
                    <button
                      onClick={() => handleWhatsAppClick('Hi! I want to track my order status.')}
                      className="w-full text-left text-xs text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      • Track my order
                    </button>
                    <button
                      onClick={() => handleWhatsAppClick('Hi! I need help with a virtual tour.')}
                      className="w-full text-left text-xs text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      • Virtual tour support
                    </button>
                    <button
                      onClick={() => handleWhatsAppClick('Hi! I want to become a vendor on VirtualRealTour.')}
                      className="w-full text-left text-xs text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      • Become a vendor
                    </button>
                  </div>
                </div>

                <div className="pt-2 border-t">
                  <p className="text-xs text-gray-400 text-center">
                    Available 24/7 • Response within 5 minutes
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Floating WhatsApp Button */}
      <motion.div
        initial={{ scale: 0, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
      >
        <Button
          onClick={() => setIsExpanded(!isExpanded)}
          size="sm"
          className={cn(
            "relative h-12 w-12 rounded-full shadow-lg",
            "bg-green-500 hover:bg-green-600",
            "transition-all duration-300",
            "border-2 border-white/20"
          )}
        >
          <AnimatePresence mode="wait">
            {isExpanded ? (
              <motion.div
                key="close"
                initial={{ rotate: -90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: 90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <X className="w-5 h-5 text-white" />
              </motion.div>
            ) : (
              <motion.div
                key="whatsapp"
                initial={{ rotate: 90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: -90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <MessageCircle className="w-5 h-5 text-white" />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Online Status Indicator */}
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-white">
            <motion.div
              className="w-full h-full bg-green-400 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
          </div>
        </Button>
      </motion.div>
    </div>
  );
};

export default FloatingWhatsAppWidget;
