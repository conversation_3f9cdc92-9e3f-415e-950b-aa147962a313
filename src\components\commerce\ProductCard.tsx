/**
 * ProductCard Component
 * Reusable product display component following VRT design principles
 * Mobile-first responsive design with shadcn/ui components
 */

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ShoppingCart, 
  Eye, 
  Heart, 
  Star, 
  MapPin,
  Package,
  Truck,
  MessageCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface Product {
  id: string;
  title: string;
  description?: string;
  price: number;
  compare_at_price?: number;
  images: string[];
  category?: string;
  vendor: {
    id: string;
    name: string;
    whatsapp_number?: string;
  };
  inventory_quantity: number;
  status: 'draft' | 'active' | 'inactive' | 'out_of_stock';
  tags?: string[];
}

interface ProductCardProps {
  product: Product;
  variant?: 'default' | 'compact' | 'featured';
  showVendor?: boolean;
  showAddToCart?: boolean;
  showQuickView?: boolean;
  onAddToCart?: (productId: string) => void;
  onQuickView?: (productId: string) => void;
  onContactVendor?: (vendorId: string, productId: string) => void;
  className?: string;
}

const ProductCard = ({
  product,
  variant = 'default',
  showVendor = true,
  showAddToCart = true,
  showQuickView = true,
  onAddToCart,
  onQuickView,
  onContactVendor,
  className
}: ProductCardProps) => {
  const [isHovered, setIsHovered] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isWishlisted, setIsWishlisted] = useState(false);

  const isOnSale = product.compare_at_price && product.compare_at_price > product.price;
  const discountPercentage = isOnSale 
    ? Math.round(((product.compare_at_price! - product.price) / product.compare_at_price!) * 100)
    : 0;

  const isOutOfStock = product.status === 'out_of_stock' || product.inventory_quantity === 0;
  const isUnavailable = product.status !== 'active' || isOutOfStock;

  const handleAddToCart = () => {
    if (!isUnavailable && onAddToCart) {
      onAddToCart(product.id);
    }
  };

  const handleQuickView = () => {
    if (onQuickView) {
      onQuickView(product.id);
    }
  };

  const handleContactVendor = () => {
    if (onContactVendor && product.vendor.whatsapp_number) {
      onContactVendor(product.vendor.id, product.id);
    }
  };

  const cardVariants = {
    default: "w-full max-w-sm",
    compact: "w-full max-w-xs",
    featured: "w-full max-w-md"
  };

  const imageVariants = {
    default: "aspect-square",
    compact: "aspect-[4/3]",
    featured: "aspect-[3/2]"
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(cardVariants[variant], className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <Card className="h-full overflow-hidden group hover:shadow-lg transition-all duration-300">
        {/* Product Image */}
        <div className={cn("relative overflow-hidden bg-gray-100", imageVariants[variant])}>
          {product.images.length > 0 ? (
            <img
              src={product.images[currentImageIndex]}
              alt={product.title}
              className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-200">
              <Package className="w-12 h-12 text-gray-400" />
            </div>
          )}

          {/* Badges */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {isOnSale && (
              <Badge variant="destructive" className="text-xs">
                -{discountPercentage}%
              </Badge>
            )}
            {isOutOfStock && (
              <Badge variant="secondary" className="text-xs">
                Out of Stock
              </Badge>
            )}
            {product.category && (
              <Badge variant="outline" className="text-xs bg-white/90">
                {product.category}
              </Badge>
            )}
          </div>

          {/* Action Buttons */}
          <div className={cn(
            "absolute top-2 right-2 flex flex-col gap-1 transition-opacity duration-300",
            isHovered ? "opacity-100" : "opacity-0"
          )}>
            <Button
              size="sm"
              variant="secondary"
              className="w-8 h-8 p-0 bg-white/90 hover:bg-white"
              onClick={() => setIsWishlisted(!isWishlisted)}
            >
              <Heart className={cn("w-4 h-4", isWishlisted && "fill-red-500 text-red-500")} />
            </Button>
            {showQuickView && (
              <Button
                size="sm"
                variant="secondary"
                className="w-8 h-8 p-0 bg-white/90 hover:bg-white"
                onClick={handleQuickView}
              >
                <Eye className="w-4 h-4" />
              </Button>
            )}
          </div>

          {/* Image Navigation */}
          {product.images.length > 1 && (
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex gap-1">
              {product.images.map((_, index) => (
                <button
                  key={index}
                  className={cn(
                    "w-2 h-2 rounded-full transition-all duration-200",
                    index === currentImageIndex 
                      ? "bg-white scale-125" 
                      : "bg-white/60 hover:bg-white/80"
                  )}
                  onClick={() => setCurrentImageIndex(index)}
                />
              ))}
            </div>
          )}
        </div>

        {/* Product Info */}
        <CardContent className="p-4 flex-1">
          <div className="space-y-2">
            {/* Vendor Info */}
            {showVendor && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <MapPin className="w-3 h-3" />
                <span>{product.vendor.name}</span>
              </div>
            )}

            {/* Product Title */}
            <h3 className="font-semibold text-sm line-clamp-2 leading-tight">
              {product.title}
            </h3>

            {/* Product Description */}
            {product.description && variant !== 'compact' && (
              <p className="text-xs text-muted-foreground line-clamp-2">
                {product.description}
              </p>
            )}

            {/* Price */}
            <div className="flex items-center gap-2">
              <span className="font-bold text-lg text-primary">
                ₦{product.price.toLocaleString()}
              </span>
              {isOnSale && (
                <span className="text-sm text-muted-foreground line-through">
                  ₦{product.compare_at_price!.toLocaleString()}
                </span>
              )}
            </div>

            {/* Tags */}
            {product.tags && product.tags.length > 0 && variant === 'featured' && (
              <div className="flex flex-wrap gap-1">
                {product.tags.slice(0, 3).map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}

            {/* Stock Status */}
            <div className="flex items-center gap-1 text-xs">
              <Package className="w-3 h-3" />
              <span className={cn(
                product.inventory_quantity > 10 
                  ? "text-green-600" 
                  : product.inventory_quantity > 0 
                    ? "text-yellow-600" 
                    : "text-red-600"
              )}>
                {product.inventory_quantity > 10 
                  ? "In Stock" 
                  : product.inventory_quantity > 0 
                    ? `Only ${product.inventory_quantity} left` 
                    : "Out of Stock"
                }
              </span>
            </div>
          </div>
        </CardContent>

        {/* Action Buttons */}
        <CardFooter className="p-4 pt-0 space-y-2">
          {showAddToCart && (
            <Button
              className="w-full"
              onClick={handleAddToCart}
              disabled={isUnavailable}
              variant={isUnavailable ? "secondary" : "default"}
            >
              <ShoppingCart className="w-4 h-4 mr-2" />
              {isUnavailable ? "Unavailable" : "Add to Cart"}
            </Button>
          )}

          {/* Contact Vendor Button */}
          {product.vendor.whatsapp_number && (
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={handleContactVendor}
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Contact Vendor
            </Button>
          )}
        </CardFooter>
      </Card>
    </motion.div>
  );
};

export default ProductCard;
