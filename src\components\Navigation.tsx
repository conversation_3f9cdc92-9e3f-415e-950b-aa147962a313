import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';
import { isAdminByEmail } from '@/lib/authUtils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Phone, User, Settings, Menu, X } from 'lucide-react';

const Navigation = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();
  const { user, profile, signOut } = useAuth();

  // Smart dashboard link with instant admin detection
  const isAdmin = profile?.role === 'admin' || (user?.email && isAdminByEmail(user.email));

  const getDashboardLink = () => {
    return isAdmin ? '/admin' : '/dashboard';
  };

  const getDashboardLabel = () => {
    return isAdmin ? 'Admin Dashboard' : 'Dashboard';
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Force scrolled state for dashboard pages (white background with dark text)
  const isDashboardPage = location.pathname.startsWith('/admin') ||
                          location.pathname.startsWith('/dashboard') ||
                          location.pathname.startsWith('/vendor');
  const shouldUseScrolledStyle = isScrolled || isDashboardPage;

  const navigationItems = [
    { label: 'Home', href: '/' },
    { label: 'Services', href: '/services' },
    { label: 'Tours', href: '/showcase' },
    { label: 'About', href: '/about' },
    { label: 'Contact', href: '/contact' }
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <motion.nav
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      className={cn(
        "fixed top-0 left-0 right-0 z-50 transition-all duration-700",
        shouldUseScrolledStyle
          ? "bg-white/95 backdrop-blur-xl shadow-lg border-b border-primary/10"
          : "bg-transparent"
      )}
      style={{
        background: shouldUseScrolledStyle
          ? 'rgba(255, 255, 255, 0.95)'
          : 'transparent',
        backdropFilter: shouldUseScrolledStyle ? 'blur(20px)' : 'none',
        WebkitBackdropFilter: shouldUseScrolledStyle ? 'blur(20px)' : 'none',
      }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16 lg:h-20">
          {/* Logo */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="flex items-center"
          >
            <Link to="/" className="relative group">
              <img
                src={shouldUseScrolledStyle ? "/lovable-uploads/vrt-logo-all.png" : "/lovable-uploads/vrt-logo-light.png"}
                alt="VirtualRealTour Logo"
                className={cn(
                  "h-12 w-auto sm:h-14 lg:h-16 object-contain",
                  "group-hover:scale-105 transition-all duration-300",
                  shouldUseScrolledStyle ? "drop-shadow-md" : "drop-shadow-2xl"
                )}
              />
            </Link>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center gap-8">
            {navigationItems.map((item, index) => (
              <motion.div
                key={item.href}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
              >
                <Link
                  to={item.href}
                  className={cn(
                    "relative font-medium text-lg transition-all duration-300 hover:scale-105",
                    "before:absolute before:bottom-0 before:left-0 before:w-0 before:h-0.5",
                    "before:bg-gradient-to-r before:from-primary before:to-blue-400",
                    "before:transition-all before:duration-300 hover:before:w-full",
                    isActive(item.href)
                      ? shouldUseScrolledStyle
                        ? "text-primary font-semibold"
                        : "text-white font-semibold drop-shadow-md"
                      : shouldUseScrolledStyle
                        ? "text-gray-700 hover:text-primary"
                        : "text-white hover:text-blue-200 drop-shadow-md"
                  )}
                >
                  {item.label}
                </Link>
              </motion.div>
            ))}
          </div>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center gap-4">
            {/* Phone Number */}
            <div className="flex items-center text-sm lg:text-base">
              <a
                href="tel:+2349077776066"
                className={cn(
                  "flex items-center transition-colors duration-300",
                  shouldUseScrolledStyle
                    ? "text-gray-600 hover:text-primary"
                    : "text-white/90 hover:text-blue-200"
                )}
              >
                <Phone className="w-4 h-4 lg:w-5 lg:h-5 mr-1 icon-hover" />
                <span className="hidden lg:inline">+234 ************</span>
              </a>
            </div>

            {/* User Actions */}
            {user ? (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "flex items-center space-x-2 transition-all duration-300",
                      shouldUseScrolledStyle
                        ? "text-gray-700 hover:text-primary hover:bg-primary/10"
                        : "text-white hover:text-blue-200 hover:bg-white/10"
                    )}
                  >
                    <User className="w-4 h-4" />
                    <span className="max-w-20 truncate">{user.email}</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48 bg-white border border-gray-200 shadow-lg z-50">
                  <DropdownMenuItem asChild>
                    <Link to={getDashboardLink()} className="flex items-center">
                      <Settings className="w-4 h-4 mr-2" />
                      {getDashboardLabel()}
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => signOut()}>
                    Sign Out
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            ) : (
              <div className="flex items-center gap-3">
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.8 }}
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    asChild
                    className={cn(
                      "font-medium transition-all duration-300 hover:scale-105",
                      isScrolled
                        ? "text-primary hover:bg-primary/10"
                        : "text-white hover:bg-white/10 border border-white/20"
                    )}
                  >
                    <Link to="/auth">Sign In</Link>
                  </Button>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 1 }}
                >
                  <Button
                    size="sm"
                    asChild
                    className={cn(
                      "font-medium transition-all duration-300 hover:scale-105",
                      "bg-gradient-to-r from-primary to-blue-600",
                      "text-white shadow-md hover:shadow-lg",
                      "border border-primary/20 hover:border-primary/40"
                    )}
                    style={{
                      boxShadow: isScrolled
                        ? '0 4px 16px rgba(59, 130, 246, 0.25)'
                        : '0 8px 32px rgba(0, 0, 0, 0.3)'
                    }}
                  >
                    <Link to="/contact">Get Quote</Link>
                  </Button>
                </motion.div>
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            type="button"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label={isMobileMenuOpen ? "Close navigation menu" : "Open navigation menu"}
            aria-expanded={isMobileMenuOpen ? 'true' : 'false'}
            className={cn(
              "lg:hidden p-2 rounded-lg transition-all duration-300 min-h-[44px] min-w-[44px] flex items-center justify-center touch-feedback mobile-friendly",
              shouldUseScrolledStyle ? "text-primary hover:bg-primary/10" : "text-white hover:bg-white/10"
            )}
          >
            {isMobileMenuOpen ? (
              <X className="w-6 h-6 icon-hover" />
            ) : (
              <Menu className="w-6 h-6 icon-hover" />
            )}
          </button>
        </div>

        {/* Mobile Menu */}
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{
            opacity: isMobileMenuOpen ? 1 : 0,
            height: isMobileMenuOpen ? 'auto' : 0
          }}
          transition={{ duration: 0.3 }}
          className="lg:hidden overflow-hidden bg-white/95 backdrop-blur-xl border-t border-primary/10"
        >
          <div className="px-4 py-6 space-y-4">
            {navigationItems.map((item) => (
              <Link
                key={item.href}
                to={item.href}
                className={cn(
                  "block font-medium py-2 transition-colors duration-300",
                  isActive(item.href)
                    ? "text-primary font-semibold"
                    : "text-gray-700 hover:text-primary"
                )}
                onClick={() => setIsMobileMenuOpen(false)}
              >
                {item.label}
              </Link>
            ))}

            <div className="pt-4 border-t border-primary/10 space-y-3">
              <a href="tel:+2349077776066" className="flex items-center text-gray-600 py-2">
                <Phone className="w-4 h-4 mr-2" />
                +234 ************
              </a>

              {user ? (
                <>
                  <Link
                    to={getDashboardLink()}
                    onClick={() => setIsMobileMenuOpen(false)}
                    className="flex items-center text-gray-700 hover:text-primary py-2"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    {getDashboardLabel()}
                  </Link>
                  <Button
                    variant="ghost"
                    onClick={() => {
                      signOut();
                      setIsMobileMenuOpen(false);
                    }}
                    className="w-full justify-start text-gray-700"
                  >
                    Sign Out
                  </Button>
                </>
              ) : (
                <div className="flex flex-col gap-3">
                  <Button variant="outline" className="w-full" asChild>
                    <Link to="/auth" onClick={() => setIsMobileMenuOpen(false)}>
                      Sign In
                    </Link>
                  </Button>
                  <Button className="w-full bg-primary hover:bg-primary/90" asChild>
                    <Link to="/contact" onClick={() => setIsMobileMenuOpen(false)}>
                      Get Quote
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </motion.nav>
  );
};

export default Navigation;