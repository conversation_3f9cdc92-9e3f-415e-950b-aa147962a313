import { createRoot } from 'react-dom/client'
import App from './App.tsx'
import './index.css'
// import { register as registerSW } from './utils/serviceWorker'

// Temporarily disable service worker to debug display issues
// registerSW({
//   onSuccess: (registration) => {
//     console.log('VRT: Service Worker registered successfully:', registration);
//   },
//   onUpdate: (registration) => {
//     console.log('VRT: New content available, please refresh:', registration);
//     // Show user notification about update
//     if ('Notification' in window && Notification.permission === 'granted') {
//       new Notification('VirtualRealTour Update Available', {
//         body: 'A new version is available. Please refresh to update.',
//         icon: '/favicon-32x32.png'
//       });
//     }
//   },
//   onOffline: () => {
//     console.log('VRT: App is running in offline mode');
//   },
//   onOnline: () => {
//     console.log('VRT: App is back online');
//   }
// });

createRoot(document.getElementById("root")!).render(<App />);
