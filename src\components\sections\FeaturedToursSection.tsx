
import { useQuery } from '@tanstack/react-query'
import { useMemo, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Link } from 'react-router-dom'
import { supabase } from '@/lib/supabase'
import EnhancedTourCard from '@/components/EnhancedTourCard'
import { getTourCardSettings } from '@/config/tourCardSettings'
import { usePerformanceMonitor, useMemoryCleanup } from '@/hooks/usePerformance'
import { preloadTourResources } from '@/utils/performanceOptimization'
import { PERFORMANCE_CONFIG } from '@/config/performanceConfig'
import { useScrollAnimation, useStaggeredScrollAnimation, getAnimationClasses } from '@/hooks/useScrollAnimation'

const FeaturedToursSection = () => {
  const { start, end } = usePerformanceMonitor('featured-tours-section');

  // Enable memory cleanup for this component
  useMemoryCleanup();

  // Scroll animations - only for CTA, header shows immediately
  const { elementRef: ctaRef, isVisible: ctaVisible } = useScrollAnimation({
    threshold: 0.1,
    rootMargin: '0px 0px 150px 0px'
  });

  // Fetch featured tours with performance optimization
  const { data: tours = [], isLoading } = useQuery({
    queryKey: ['featured-tours'],
    queryFn: async () => {
      start();

      const { data, error } = await supabase
        .from('tours')
        .select(`
          *,
          profiles (
            full_name,
            email
          )
        `)
        .eq('featured', true)
        .eq('status', 'published')
        .order('created_at', { ascending: false })
        .limit(6);

      if (error) {
        console.error('Error fetching featured tours:', error);
        end();
        return [];
      }

      end();
      return data || [];
    },
    staleTime: PERFORMANCE_CONFIG.QUERY_STALE_TIME,
    cacheTime: PERFORMANCE_CONFIG.QUERY_CACHE_TIME,
  });

  // Preload tour resources for better performance
  useEffect(() => {
    if (tours.length > 0) {
      const tourUrls = tours
        .slice(0, PERFORMANCE_CONFIG.PRELOAD_TOURS_COUNT)
        .map(tour => tour.embed_url)
        .filter(Boolean);
      preloadTourResources(tourUrls);
    }
  }, [tours]);

  // Staggered animations for tour cards
  const { setElementRef: setTourCardRef, visibleItems: tourCardsVisible } = useStaggeredScrollAnimation(
    Math.min(tours.length, 6),
    { threshold: 0.1, rootMargin: '0px 0px 100px 0px', staggerDelay: 200 }
  );

  // Memoize tour cards for performance
  const tourCards = useMemo(() => {
    return tours.slice(0, 6).map((tour, index) => {
      const settings = getTourCardSettings('homepage');
      return (
        <EnhancedTourCard
          key={tour.id}
          tour={tour}
          showEmbedded={settings.showEmbedded}
          showActions={settings.showActions}
          autoLoad={settings.autoLoad}
          className={`safe-fade-in ${tourCardsVisible[index] ? 'animate' : ''} hover-lift`}
        />
      );
    });
  }, [tours, setTourCardRef, tourCardsVisible]);

  // Remove custom handler - EnhancedTourCard handles navigation

  if (tours.length === 0) {
    return (
      <section className="bg-muted/50">
        <div className="container py-16 lg:py-24">
          <div className="text-center">
            <div className="inline-flex items-center rounded-full glass-card bg-yellow-100/80 border border-yellow-200/50 px-4 py-2 text-sm font-medium text-yellow-800 mb-6 shadow-enhanced-md">
              ⭐ Featured Tours
            </div>
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
              Handpicked Virtual Tours
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto mb-8">
              Discover our carefully curated selection of premium virtual tour experiences
            </p>
            <p className="text-sm text-muted-foreground mb-8">
              Our featured tours are being curated! Check back soon for more premium experiences.
            </p>
            <Button asChild size="lg">
              <Link to="/showcase">
                Browse All Tours
              </Link>
            </Button>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="bg-muted/50 section-gradient-subtle">
      <div className="container py-16 lg:py-24">
        <div className="text-center mb-12">
          <div className="inline-flex items-center rounded-full glass-card bg-yellow-100/80 border border-yellow-200/50 px-4 py-2 text-sm font-medium text-yellow-800 mb-6 shadow-enhanced-md">
            ⭐ Featured Tours
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
            Handpicked Virtual Tours
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover our carefully curated selection of premium virtual tour experiences
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {isLoading ? (
            // Simple loading skeleton
            Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-gray-100 rounded-lg h-64 animate-pulse"></div>
            ))
          ) : (
            tourCards
          )}
        </div>

        <div
          ref={ctaRef}
          className={`text-center safe-fade-in ${ctaVisible ? 'animate' : ''}`}
        >
          <Button asChild size="lg" className="shadow-enhanced-lg hover-lift">
            <Link to="/showcase">
              View All Tours
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}

export default FeaturedToursSection
