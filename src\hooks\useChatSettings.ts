import { useState, useEffect } from 'react';
import { toast } from 'sonner';

export interface ChatSettings {
  enabled: boolean;
  businessPhone: string;
  businessName: string;
  defaultMessage: string;
  position: 'bottom-right' | 'bottom-left';
}

export interface CloudPanoSettings {
  enabled: boolean;
  apiKey: string;
  accountEmail: string;
  accountPassword: string;
  baseUrl: string;
  createTourUrl: string;
  viewerUrl: string;
}

// Default chat settings
const defaultChatSettings: ChatSettings = {
  enabled: true,
  businessPhone: '+*************',
  businessName: 'VirtualRealTour Support',
  defaultMessage: 'Hi! I need help with VirtualRealTour. Can you assist me?',
  position: 'bottom-right'
};

// Local storage keys
const CHAT_SETTINGS_KEY = 'vrt_chat_settings';
const CLOUDPANO_SETTINGS_KEY = 'vrt_cloudpano_settings';

export const useChatSettings = () => {
  const [chatSettings, setChatSettings] = useState<ChatSettings>(defaultChatSettings);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(CHAT_SETTINGS_KEY);
      if (stored) {
        const parsedSettings = JSON.parse(stored);
        setChatSettings({ ...defaultChatSettings, ...parsedSettings });
      }
    } catch (error) {
      console.error('Error loading chat settings:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update settings function
  const updateChatSettings = async (updates: Partial<ChatSettings>) => {
    setIsUpdating(true);
    try {
      const newSettings = { ...chatSettings, ...updates };
      setChatSettings(newSettings);
      localStorage.setItem(CHAT_SETTINGS_KEY, JSON.stringify(newSettings));
      toast.success('Chat settings updated successfully');
    } catch (error) {
      console.error('Error updating chat settings:', error);
      toast.error('Failed to update chat settings');
    } finally {
      setIsUpdating(false);
    }
  };

  return {
    chatSettings,
    isLoading,
    updateChatSettings,
    isUpdating,
    refetch: () => {} // No-op for compatibility
  };
};

// Hook for public access to chat settings (for the floating widget)
export const usePublicChatSettings = () => {
  const [chatSettings, setChatSettings] = useState<ChatSettings>(defaultChatSettings);
  const [isLoading, setIsLoading] = useState(true);

  // Load settings from localStorage
  useEffect(() => {
    try {
      const stored = localStorage.getItem(CHAT_SETTINGS_KEY);
      if (stored) {
        const parsedSettings = JSON.parse(stored);
        setChatSettings({ ...defaultChatSettings, ...parsedSettings });
      }
    } catch (error) {
      console.error('Error loading public chat settings:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Listen for storage changes (when admin updates settings)
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === CHAT_SETTINGS_KEY && e.newValue) {
        try {
          const parsedSettings = JSON.parse(e.newValue);
          setChatSettings({ ...defaultChatSettings, ...parsedSettings });
        } catch (error) {
          console.error('Error parsing updated chat settings:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  return {
    chatSettings,
    isLoading
  };
};

// Default CloudPano settings
const defaultCloudPanoSettings: CloudPanoSettings = {
  enabled: true,
  apiKey: '',
  accountEmail: '',
  accountPassword: '',
  baseUrl: 'https://app.cloudpano.com',
  createTourUrl: 'https://app.cloudpano.com/',
  viewerUrl: 'https://viewer.cloudpano.com'
};

export const useCloudPanoSettings = () => {
  const [cloudPanoSettings, setCloudPanoSettings] = useState<CloudPanoSettings>(defaultCloudPanoSettings);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem(CLOUDPANO_SETTINGS_KEY);
      if (stored) {
        const parsedSettings = JSON.parse(stored);
        setCloudPanoSettings({ ...defaultCloudPanoSettings, ...parsedSettings });
      }
    } catch (error) {
      console.error('Error loading CloudPano settings:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update settings function
  const updateCloudPanoSettings = async (updates: Partial<CloudPanoSettings>) => {
    setIsUpdating(true);
    try {
      const newSettings = { ...cloudPanoSettings, ...updates };
      setCloudPanoSettings(newSettings);
      localStorage.setItem(CLOUDPANO_SETTINGS_KEY, JSON.stringify(newSettings));
      toast.success('CloudPano settings updated successfully');
    } catch (error) {
      console.error('Error updating CloudPano settings:', error);
      toast.error('Failed to update CloudPano settings');
    } finally {
      setIsUpdating(false);
    }
  };

  return {
    cloudPanoSettings,
    isLoading,
    updateCloudPanoSettings,
    isUpdating,
    refetch: () => {} // No-op for compatibility
  };
};
