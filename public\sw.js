// Service Worker for Virtual Real Tour
// Provides offline capabilities, edge caching, and stale-while-revalidate patterns

const CACHE_VERSION = 'v2';
const STATIC_CACHE = `vrt-static-${CACHE_VERSION}`;
const DYNAMIC_CACHE = `vrt-dynamic-${CACHE_VERSION}`;
const IMAGE_CACHE = `vrt-images-${CACHE_VERSION}`;
const TOUR_CACHE = `vrt-tours-${CACHE_VERSION}`;
const API_CACHE = `vrt-api-${CACHE_VERSION}`;

// Cache configuration with TTL and strategies
const CACHE_CONFIG = {
  static: {
    name: STATIC_CACHE,
    maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
    strategy: 'cache-first'
  },
  dynamic: {
    name: DYNAMIC_CACHE,
    maxAge: 24 * 60 * 60 * 1000, // 24 hours
    strategy: 'network-first'
  },
  images: {
    name: I<PERSON><PERSON>_CACHE,
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    strategy: 'cache-first'
  },
  tours: {
    name: TOUR_CACHE,
    maxAge: 60 * 60 * 1000, // 1 hour
    strategy: 'stale-while-revalidate'
  },
  api: {
    name: API_CACHE,
    maxAge: 5 * 60 * 1000, // 5 minutes
    strategy: 'stale-while-revalidate'
  }
};

// Assets to cache immediately
const STATIC_ASSETS = [
  '/',
  '/index.html',
  '/manifest.json',
  '/offline.html',
  // Add critical CSS and JS files
  '/assets/index.css',
  '/assets/index.js',
];

// Tour-related assets that should be cached
const TOUR_ASSETS = [
  '/api/tours',
  '/dashboard',
  '/admin',
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE).then((cache) => {
        console.log('Service Worker: Caching static assets');
        return cache.addAll(STATIC_ASSETS);
      }),
      // Cache shell for offline page
      caches.open(DYNAMIC_CACHE).then((cache) => {
        return cache.add('/offline.html');
      })
    ])
  );
  
  // Force activation of new service worker
  self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          // Delete old caches
          if (cacheName !== STATIC_CACHE && 
              cacheName !== DYNAMIC_CACHE && 
              cacheName !== IMAGE_CACHE) {
            console.log('Service Worker: Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // Take control of all pages
  self.clients.claim();
});

// Fetch event - implement caching strategies
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Handle different types of requests with appropriate strategies
  if (isImageRequest(request)) {
    event.respondWith(handleImageRequest(request));
  } else if (isAPIRequest(request)) {
    event.respondWith(handleAPIRequest(request));
  } else if (isStaticAsset(request)) {
    event.respondWith(handleStaticAsset(request));
  } else {
    event.respondWith(handlePageRequest(request));
  }
});

// Image requests - Cache First with WebP/AVIF optimization
async function handleImageRequest(request) {
  const cache = await caches.open(IMAGE_CACHE);
  
  try {
    // Try to get from cache first
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Fetch from network
    const networkResponse = await fetch(request);
    
    // Cache successful responses
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Service Worker: Image request failed:', error);
    // Return placeholder image for failed requests
    return new Response(
      '<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg"><rect width="100%" height="100%" fill="#f3f4f6"/><text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af">Image unavailable</text></svg>',
      { headers: { 'Content-Type': 'image/svg+xml' } }
    );
  }
}

// API requests - Stale-While-Revalidate with edge caching
async function handleAPIRequest(request) {
  const url = new URL(request.url);

  // Determine cache strategy based on endpoint
  if (url.pathname.includes('/tours')) {
    return handleTourRequest(request);
  } else {
    return handleGenericAPIRequest(request);
  }
}

// Tour-specific requests with advanced caching
async function handleTourRequest(request) {
  const cache = await caches.open(TOUR_CACHE);
  const cachedResponse = await cache.match(request);

  // Check if cached response is still fresh
  if (cachedResponse) {
    const cachedDate = new Date(cachedResponse.headers.get('sw-cached-date') || 0);
    const isStale = Date.now() - cachedDate.getTime() > CACHE_CONFIG.tours.maxAge;

    if (!isStale) {
      // Fresh cache - return immediately
      return cachedResponse;
    } else {
      // Stale cache - return stale data while revalidating in background
      revalidateInBackground(request, cache);
      return cachedResponse;
    }
  }

  // No cache - fetch from network
  return fetchAndCache(request, cache, 'tours');
}

// Generic API requests with network-first strategy
async function handleGenericAPIRequest(request) {
  const cache = await caches.open(API_CACHE);

  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Add timestamp for cache management
      const responseToCache = networkResponse.clone();
      const headers = new Headers(responseToCache.headers);
      headers.set('sw-cached-date', new Date().toISOString());

      const modifiedResponse = new Response(responseToCache.body, {
        status: responseToCache.status,
        statusText: responseToCache.statusText,
        headers: headers
      });

      cache.put(request, modifiedResponse);
    }

    return networkResponse;
  } catch (error) {
    console.log('Service Worker: API request failed, trying cache:', error);

    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    return new Response(
      JSON.stringify({
        error: 'Offline',
        message: 'This content is not available offline',
        timestamp: new Date().toISOString()
      }),
      {
        status: 503,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

// Static assets - Cache First
async function handleStaticAsset(request) {
  const cache = await caches.open(STATIC_CACHE);
  
  try {
    // Try cache first
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Fetch from network and cache
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Service Worker: Static asset request failed:', error);
    throw error;
  }
}

// Page requests - Network First with offline fallback
async function handlePageRequest(request) {
  const cache = await caches.open(DYNAMIC_CACHE);
  
  try {
    // Try network first
    const networkResponse = await fetch(request);
    
    // Cache successful page responses
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Service Worker: Page request failed, trying cache:', error);
    
    // Try cache
    const cachedResponse = await cache.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    // Return offline page
    return cache.match('/offline.html');
  }
}

// Helper functions for stale-while-revalidate
async function revalidateInBackground(request, cache) {
  try {
    console.log('Service Worker: Revalidating in background:', request.url);
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      const headers = new Headers(networkResponse.headers);
      headers.set('sw-cached-date', new Date().toISOString());
      headers.set('sw-revalidated', 'true');

      const responseToCache = new Response(networkResponse.body, {
        status: networkResponse.status,
        statusText: networkResponse.statusText,
        headers: headers
      });

      await cache.put(request, responseToCache);
      console.log('Service Worker: Background revalidation complete');
    }
  } catch (error) {
    console.log('Service Worker: Background revalidation failed:', error);
  }
}

async function fetchAndCache(request, cache, cacheType) {
  try {
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      const headers = new Headers(networkResponse.headers);
      headers.set('sw-cached-date', new Date().toISOString());
      headers.set('sw-cache-type', cacheType);

      const responseToCache = new Response(networkResponse.body, {
        status: networkResponse.status,
        statusText: networkResponse.statusText,
        headers: headers
      });

      await cache.put(request, responseToCache);
    }

    return networkResponse;
  } catch (error) {
    console.log('Service Worker: Fetch and cache failed:', error);
    throw error;
  }
}

// Request type detection functions
function isImageRequest(request) {
  return request.destination === 'image' ||
         /\.(jpg|jpeg|png|gif|webp|avif|svg)$/i.test(request.url);
}

function isAPIRequest(request) {
  return request.url.includes('/api/') ||
         request.url.includes('supabase.co') ||
         request.url.includes('dnbjrfgfugpmyrconepx.supabase.co');
}

function isStaticAsset(request) {
  return request.destination === 'script' ||
         request.destination === 'style' ||
         /\.(js|css|woff|woff2|ttf|eot)$/i.test(request.url);
}

// Background sync for tour uploads and e-commerce
self.addEventListener('sync', (event) => {
  if (event.tag === 'tour-upload') {
    event.waitUntil(syncTourUploads());
  }

  if (event.tag === 'cart-sync') {
    event.waitUntil(syncCartData());
  }

  if (event.tag === 'order-sync') {
    event.waitUntil(syncOrderData());
  }

  if (event.tag === 'whatsapp-message') {
    event.waitUntil(syncWhatsAppMessages());
  }
});

async function syncTourUploads() {
  // Handle background sync for tour uploads when back online
  console.log('Service Worker: Syncing tour uploads...');
  
  try {
    // Get pending uploads from IndexedDB
    const pendingUploads = await getPendingUploads();
    
    for (const upload of pendingUploads) {
      try {
        await uploadTour(upload);
        await removePendingUpload(upload.id);
      } catch (error) {
        console.log('Service Worker: Failed to sync upload:', error);
      }
    }
  } catch (error) {
    console.log('Service Worker: Sync failed:', error);
  }
}

// IndexedDB operations for offline functionality
async function getPendingUploads() {
  // Returns empty array for now - IndexedDB implementation can be added when needed
  return [];
}

// E-commerce IndexedDB operations
async function getPendingCartActions() {
  // Returns empty array for now - IndexedDB implementation can be added when needed
  return [];
}

async function removePendingCartAction(id) {
  console.log('Removing pending cart action:', id);
}

async function getPendingOrders() {
  // Returns empty array for now - IndexedDB implementation can be added when needed
  return [];
}

async function removePendingOrder(id) {
  console.log('Removing pending order:', id);
}

async function getPendingWhatsAppMessages() {
  // Returns empty array for now - IndexedDB implementation can be added when needed
  return [];
}

async function removePendingWhatsAppMessage(id) {
  console.log('Removing pending WhatsApp message:', id);
}

async function uploadTour(upload) {
  // Upload tour data to the server
  return fetch('/api/tours', {
    method: 'POST',
    body: JSON.stringify(upload),
    headers: { 'Content-Type': 'application/json' }
  });
}

async function removePendingUpload(id) {
  // Remove upload from pending queue
  console.log('Removing pending upload:', id);
}

// E-commerce background sync functions
async function syncCartData() {
  console.log('Service Worker: Syncing cart data...');

  try {
    const pendingCartActions = await getPendingCartActions();

    for (const action of pendingCartActions) {
      try {
        await fetch('/api/cart/sync', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(action)
        });
        await removePendingCartAction(action.id);
      } catch (error) {
        console.log('Service Worker: Failed to sync cart action:', error);
      }
    }
  } catch (error) {
    console.log('Service Worker: Cart sync failed:', error);
  }
}

async function syncOrderData() {
  console.log('Service Worker: Syncing order data...');

  try {
    const pendingOrders = await getPendingOrders();

    for (const order of pendingOrders) {
      try {
        await fetch('/api/orders', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(order)
        });
        await removePendingOrder(order.id);
      } catch (error) {
        console.log('Service Worker: Failed to sync order:', error);
      }
    }
  } catch (error) {
    console.log('Service Worker: Order sync failed:', error);
  }
}

async function syncWhatsAppMessages() {
  console.log('Service Worker: Syncing WhatsApp messages...');

  try {
    const pendingMessages = await getPendingWhatsAppMessages();

    for (const message of pendingMessages) {
      try {
        await fetch('/api/whatsapp/send', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(message)
        });
        await removePendingWhatsAppMessage(message.id);
      } catch (error) {
        console.log('Service Worker: Failed to sync WhatsApp message:', error);
      }
    }
  } catch (error) {
    console.log('Service Worker: WhatsApp sync failed:', error);
  }
}

// Message handling for cache management
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'CACHE_TOUR') {
    event.waitUntil(cacheTour(event.data.tourId));
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    event.waitUntil(clearAllCaches());
  }
});

async function cacheTour(tourId) {
  console.log('Service Worker: Caching tour:', tourId);
  
  try {
    const cache = await caches.open(DYNAMIC_CACHE);
    
    // Cache tour data
    await cache.add(`/api/tours/${tourId}`);
    
    // Cache tour images (if available)
    const tourResponse = await fetch(`/api/tours/${tourId}`);
    const tourData = await tourResponse.json();
    
    if (tourData.images) {
      const imageCache = await caches.open(IMAGE_CACHE);
      for (const imageUrl of tourData.images) {
        try {
          await imageCache.add(imageUrl);
        } catch (error) {
          console.log('Failed to cache image:', imageUrl, error);
        }
      }
    }
  } catch (error) {
    console.log('Service Worker: Failed to cache tour:', error);
  }
}

async function clearAllCaches() {
  console.log('Service Worker: Clearing all caches...');
  
  const cacheNames = await caches.keys();
  await Promise.all(
    cacheNames.map(cacheName => caches.delete(cacheName))
  );
}
