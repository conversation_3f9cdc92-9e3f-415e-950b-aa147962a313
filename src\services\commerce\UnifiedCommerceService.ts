/**
 * Unified Commerce Service
 * Consolidates all commerce operations across vendors, orders, and payments
 */

import { supabase } from '@/lib/supabase';
import { whatsappService } from './EnhancedWhatsAppService';

export interface UnifiedOrder {
  id: string;
  order_number: string;
  customer_name: string;
  customer_phone: string;
  customer_email?: string;
  total_amount: number;
  status: 'pending' | 'processing' | 'completed' | 'cancelled';
  payment_method: 'whatsapp' | 'bank_transfer' | 'card';
  payment_status: string;
  created_at: string;
  vendor_id?: string;
  vendor_name?: string;
  tour_context?: {
    tour_id: string;
    tour_title: string;
    scene_id?: string;
  };
  items: OrderItem[];
}

export interface OrderItem {
  id: string;
  product_id: string;
  product_title: string;
  product_image?: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  vendor_id: string;
  vendor_name: string;
}

export interface CommerceStats {
  totalOrders: number;
  totalRevenue: number;
  pendingOrders: number;
  completedOrders: number;
  whatsappOrders: number;
  avgOrderValue: number;
  topVendors: VendorPerformance[];
  recentOrders: UnifiedOrder[];
}

export interface VendorPerformance {
  vendor_id: string;
  vendor_name: string;
  total_orders: number;
  total_revenue: number;
  avg_order_value: number;
  completion_rate: number;
  response_time: string;
}

export interface OrderFilters {
  status?: string;
  payment_method?: string;
  vendor_id?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export class UnifiedCommerceService {
  /**
   * Get comprehensive commerce statistics
   */
  async getCommerceStats(): Promise<CommerceStats> {
    try {
      // Fetch all orders with vendor and product information
      const { data: orders, error: ordersError } = await supabase
        .from('orders')
        .select(`
          *,
          order_items (
            *,
            products (
              title,
              images,
              vendor:vendors (
                id,
                name
              )
            )
          )
        `)
        .order('created_at', { ascending: false });

      if (ordersError) throw ordersError;

      // Process orders into unified format
      const unifiedOrders = this.processOrdersData(orders || []);

      // Calculate statistics
      const stats: CommerceStats = {
        totalOrders: unifiedOrders.length,
        totalRevenue: unifiedOrders.reduce((sum, order) => sum + order.total_amount, 0),
        pendingOrders: unifiedOrders.filter(o => o.status === 'pending').length,
        completedOrders: unifiedOrders.filter(o => o.status === 'completed').length,
        whatsappOrders: unifiedOrders.filter(o => o.payment_method === 'whatsapp').length,
        avgOrderValue: unifiedOrders.length > 0 
          ? unifiedOrders.reduce((sum, order) => sum + order.total_amount, 0) / unifiedOrders.length 
          : 0,
        topVendors: await this.getTopVendors(),
        recentOrders: unifiedOrders.slice(0, 10)
      };

      return stats;
    } catch (error) {
      console.error('Error fetching commerce stats:', error);
      throw new Error('Failed to fetch commerce statistics');
    }
  }

  /**
   * Get filtered orders
   */
  async getFilteredOrders(filters: OrderFilters): Promise<UnifiedOrder[]> {
    try {
      let query = supabase
        .from('orders')
        .select(`
          *,
          order_items (
            *,
            products (
              title,
              images,
              vendor:vendors (
                id,
                name
              )
            )
          )
        `);

      // Apply filters
      if (filters.status && filters.status !== 'all') {
        query = query.eq('status', filters.status);
      }

      if (filters.payment_method && filters.payment_method !== 'all') {
        query = query.eq('payment_method', filters.payment_method);
      }

      if (filters.vendor_id) {
        query = query.eq('vendor_id', filters.vendor_id);
      }

      if (filters.date_from) {
        query = query.gte('created_at', filters.date_from);
      }

      if (filters.date_to) {
        query = query.lte('created_at', filters.date_to);
      }

      // Apply search filter
      if (filters.search) {
        query = query.or(`
          order_number.ilike.%${filters.search}%,
          customer_name.ilike.%${filters.search}%,
          customer_phone.ilike.%${filters.search}%
        `);
      }

      const { data: orders, error } = await query
        .order('created_at', { ascending: false });

      if (error) throw error;

      return this.processOrdersData(orders || []);
    } catch (error) {
      console.error('Error fetching filtered orders:', error);
      throw new Error('Failed to fetch orders');
    }
  }

  /**
   * Update order status
   */
  async updateOrderStatus(orderId: string, status: string, notes?: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('orders')
        .update({ 
          status,
          notes: notes || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId);

      if (error) throw error;

      // Send WhatsApp notification if applicable
      if (status === 'completed' || status === 'processing') {
        await this.sendOrderStatusNotification(orderId, status);
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      throw new Error('Failed to update order status');
    }
  }

  /**
   * Get vendor performance metrics
   */
  async getVendorPerformance(vendorId?: string): Promise<VendorPerformance[]> {
    try {
      let query = supabase
        .from('vendors')
        .select(`
          id,
          name,
          orders (
            id,
            total_amount,
            status,
            created_at,
            updated_at
          )
        `);

      if (vendorId) {
        query = query.eq('id', vendorId);
      }

      const { data: vendors, error } = await query;

      if (error) throw error;

      return vendors?.map(vendor => {
        const orders = vendor.orders || [];
        const completedOrders = orders.filter((o: any) => o.status === 'completed');
        const totalRevenue = completedOrders.reduce((sum: number, o: any) => sum + o.total_amount, 0);

        return {
          vendor_id: vendor.id,
          vendor_name: vendor.name,
          total_orders: orders.length,
          total_revenue: totalRevenue,
          avg_order_value: completedOrders.length > 0 ? totalRevenue / completedOrders.length : 0,
          completion_rate: orders.length > 0 ? (completedOrders.length / orders.length) * 100 : 0,
          response_time: this.calculateAverageResponseTime(orders)
        };
      }) || [];
    } catch (error) {
      console.error('Error fetching vendor performance:', error);
      throw new Error('Failed to fetch vendor performance');
    }
  }

  /**
   * Process WhatsApp order
   */
  async processWhatsAppOrder(orderData: {
    customer_name: string;
    customer_phone: string;
    customer_email?: string;
    items: Array<{
      product_id: string;
      quantity: number;
    }>;
    tour_context?: {
      tour_id: string;
      scene_id?: string;
    };
  }): Promise<UnifiedOrder> {
    try {
      // Calculate order total
      const { data: products, error: productsError } = await supabase
        .from('products')
        .select('id, title, price, vendor_id, vendors(name)')
        .in('id', orderData.items.map(item => item.product_id));

      if (productsError) throw productsError;

      const orderItems = orderData.items.map(item => {
        const product = products?.find(p => p.id === item.product_id);
        if (!product) throw new Error(`Product ${item.product_id} not found`);

        return {
          product_id: item.product_id,
          quantity: item.quantity,
          unit_price: product.price,
          total_price: product.price * item.quantity,
          vendor_id: product.vendor_id,
          product_title: product.title,
          vendor_name: product.vendors?.name || 'Unknown Vendor'
        };
      });

      const totalAmount = orderItems.reduce((sum, item) => sum + item.total_price, 0);

      // Create order
      const orderNumber = `WA-${Date.now()}`;
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          order_number: orderNumber,
          customer_name: orderData.customer_name,
          customer_phone: orderData.customer_phone,
          customer_email: orderData.customer_email,
          total_amount: totalAmount,
          status: 'pending',
          payment_method: 'whatsapp',
          payment_status: 'pending',
          tour_context: orderData.tour_context
        })
        .select()
        .single();

      if (orderError) throw orderError;

      // Create order items
      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(
          orderItems.map(item => ({
            order_id: order.id,
            product_id: item.product_id,
            quantity: item.quantity,
            unit_price: item.unit_price,
            total_price: item.total_price
          }))
        );

      if (itemsError) throw itemsError;

      // Send WhatsApp message
      await whatsappService.sendOrderConfirmation(order.id, orderData.customer_phone);

      return {
        ...order,
        items: orderItems.map((item, index) => ({
          id: `temp-${index}`,
          ...item
        }))
      };
    } catch (error) {
      console.error('Error processing WhatsApp order:', error);
      throw new Error('Failed to process WhatsApp order');
    }
  }

  /**
   * Private helper methods
   */
  private processOrdersData(rawOrders: any[]): UnifiedOrder[] {
    return rawOrders.map(order => ({
      id: order.id,
      order_number: order.order_number,
      customer_name: order.customer_name,
      customer_phone: order.customer_phone,
      customer_email: order.customer_email,
      total_amount: order.total_amount,
      status: order.status,
      payment_method: order.payment_method,
      payment_status: order.payment_status,
      created_at: order.created_at,
      vendor_id: order.vendor_id,
      tour_context: order.tour_context,
      items: (order.order_items || []).map((item: any) => ({
        id: item.id,
        product_id: item.product_id,
        product_title: item.products?.title || 'Unknown Product',
        product_image: item.products?.images?.[0],
        quantity: item.quantity,
        unit_price: item.unit_price,
        total_price: item.total_price,
        vendor_id: item.products?.vendor?.id || '',
        vendor_name: item.products?.vendor?.name || 'Unknown Vendor'
      }))
    }));
  }

  private async getTopVendors(): Promise<VendorPerformance[]> {
    const performance = await this.getVendorPerformance();
    return performance
      .sort((a, b) => b.total_revenue - a.total_revenue)
      .slice(0, 5);
  }

  private calculateAverageResponseTime(orders: any[]): string {
    // Simplified calculation - in real implementation, track actual response times
    return '2.5 hours';
  }

  private async sendOrderStatusNotification(orderId: string, status: string): Promise<void> {
    try {
      const { data: order } = await supabase
        .from('orders')
        .select('customer_phone, order_number')
        .eq('id', orderId)
        .single();

      if (order?.customer_phone) {
        await whatsappService.sendOrderStatusUpdate(
          order.customer_phone,
          order.order_number,
          status
        );
      }
    } catch (error) {
      console.error('Error sending order notification:', error);
      // Don't throw error as this is not critical
    }
  }
}

export const unifiedCommerceService = new UnifiedCommerceService();
