/**
 * CloudPano Tour Creation Service
 * Integration with CloudPano API for 360° virtual tour creation
 */

import {
  TourCreationService,
  TourConfig,
  SceneConfig,
  HotspotConfig,
  CreatedTour,
  TourScene,
  TourHotspot,
  TourCreationError,
  EmbedOptions,
  EmbedCallbacks,
  TourAnalytics
} from './types';

// CloudPano API Response Types
interface CloudPanoTour {
  id: string;
  title: string;
  description?: string;
  thumbnail?: string;
  embed_url: string;
  edit_url: string;
  viewer_url: string;
  status: 'draft' | 'published';
  created_at: string;
  updated_at: string;
  scenes: CloudPanoScene[];
  settings: {
    auto_rotate?: boolean;
    show_controls?: boolean;
    enable_vr?: boolean;
    custom_branding?: boolean;
  };
}

interface CloudPanoScene {
  id: string;
  title: string;
  image_url: string;
  thumbnail_url?: string;
  order_index: number;
  hotspots: CloudPanoHotspot[];
  settings: {
    initial_view?: {
      yaw: number;
      pitch: number;
      zoom: number;
    };
  };
}

interface CloudPanoHotspot {
  id: string;
  type: 'info' | 'link' | 'scene' | 'product';
  position: {
    yaw: number;
    pitch: number;
  };
  title: string;
  content?: string;
  target_scene_id?: string;
  link_url?: string;
  product_data?: any;
}

interface CloudPanoUploadResponse {
  id: string;
  url: string;
  thumbnail_url?: string;
}

export class CloudPanoService implements TourCreationService {
  readonly platform = 'cloudpano' as const;
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || import.meta.env.VITE_CLOUDPANO_API_KEY || '';
    this.baseUrl = 'https://api.cloudpano.com/v1';

    // Try to get settings from localStorage
    try {
      const stored = localStorage.getItem('vrt_cloudpano_settings');
      if (stored) {
        const settings = JSON.parse(stored);
        if (settings.apiKey) {
          this.apiKey = settings.apiKey;
        }
      }
    } catch (error) {
      console.warn('Could not load CloudPano settings from localStorage:', error);
    }
  }

  get isConfigured(): boolean {
    return !!this.apiKey;
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    if (!this.isConfigured) {
      throw new TourCreationError(
        'NOT_CONFIGURED',
        'CloudPano API key is not configured. Please set VITE_CLOUDPANO_API_KEY in your environment.',
        null,
        'cloudpano'
      );
    }

    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({
        message: `HTTP ${response.status}: ${response.statusText}`
      }));
      throw new TourCreationError(
        'API_ERROR',
        error.message || `CloudPano API Error: ${response.status}`,
        error,
        'cloudpano'
      );
    }

    return response.json();
  }

  async createTour(config: TourConfig): Promise<CreatedTour> {
    try {
      const tourData = {
        title: config.title,
        description: config.description || '',
        settings: {
          auto_rotate: config.settings?.autoRotate || false,
          show_controls: config.settings?.showControls !== false,
          enable_vr: config.settings?.enableVR || false,
          custom_branding: config.settings?.customBranding || false
        },
        metadata: {
          category: config.category,
          location: config.location,
          business_info: config.businessInfo
        }
      };

      const response = await this.makeRequest<CloudPanoTour>('/tours', {
        method: 'POST',
        body: JSON.stringify(tourData),
      });

      return this.mapCloudPanoTourToCreatedTour(response);
    } catch (error) {
      throw new TourCreationError(
        'TOUR_CREATE_FAILED',
        'Failed to create tour on CloudPano',
        error,
        'cloudpano'
      );
    }
  }

  async updateTour(tourId: string, config: Partial<TourConfig>): Promise<CreatedTour> {
    try {
      const updateData = {
        title: config.title,
        description: config.description,
        settings: config.settings,
        metadata: {
          category: config.category,
          location: config.location,
          business_info: config.businessInfo
        }
      };

      const response = await this.makeRequest<CloudPanoTour>(`/tours/${tourId}`, {
        method: 'PUT',
        body: JSON.stringify(updateData),
      });

      return this.mapCloudPanoTourToCreatedTour(response);
    } catch (error) {
      throw new TourCreationError(
        'UPDATE_FAILED',
        'Failed to update tour on CloudPano',
        error,
        'cloudpano'
      );
    }
  }

  async deleteTour(tourId: string): Promise<void> {
    try {
      await this.makeRequest(`/tours/${tourId}`, {
        method: 'DELETE',
      });
    } catch (error) {
      throw new TourCreationError(
        'DELETE_FAILED',
        'Failed to delete tour on CloudPano',
        error,
        'cloudpano'
      );
    }
  }

  async getTour(tourId: string): Promise<CreatedTour> {
    try {
      const response = await this.makeRequest<CloudPanoTour>(`/tours/${tourId}`);
      return this.mapCloudPanoTourToCreatedTour(response);
    } catch (error) {
      throw new TourCreationError(
        'FETCH_FAILED',
        'Failed to fetch tour from CloudPano',
        error,
        'cloudpano'
      );
    }
  }

  async uploadImage(file: File): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('image', file);

      const response = await fetch(`${this.baseUrl}/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.status}`);
      }

      const result: CloudPanoUploadResponse = await response.json();
      return result.url;
    } catch (error) {
      throw new TourCreationError(
        'UPLOAD_FAILED',
        'Failed to upload image to CloudPano',
        error,
        'cloudpano'
      );
    }
  }

  async addScene(tourId: string, scene: SceneConfig): Promise<TourScene> {
    try {
      let imageUrl = scene.imageUrl;

      // Upload image if file is provided
      if (scene.imageFile && !imageUrl) {
        imageUrl = await this.uploadImage(scene.imageFile);
      }

      const sceneData = {
        title: scene.name,
        image_url: imageUrl,
        order_index: scene.orderIndex,
        settings: {
          initial_view: {
            yaw: 0,
            pitch: 0,
            zoom: 50
          }
        }
      };

      const response = await this.makeRequest<CloudPanoScene>(`/tours/${tourId}/scenes`, {
        method: 'POST',
        body: JSON.stringify(sceneData),
      });

      return this.mapCloudPanoSceneToTourScene(response);
    } catch (error) {
      throw new TourCreationError(
        'SCENE_ADD_FAILED',
        'Failed to add scene to CloudPano tour',
        error,
        'cloudpano'
      );
    }
  }

  async addHotspot(tourId: string, sceneId: string, hotspot: HotspotConfig): Promise<TourHotspot> {
    try {
      const hotspotData = {
        type: hotspot.type,
        position: {
          yaw: hotspot.position.yaw,
          pitch: hotspot.position.pitch
        },
        title: hotspot.title,
        content: hotspot.content,
        target_scene_id: hotspot.targetSceneId,
        link_url: hotspot.linkUrl,
        product_data: hotspot.productData
      };

      const response = await this.makeRequest<CloudPanoHotspot>(`/tours/${tourId}/scenes/${sceneId}/hotspots`, {
        method: 'POST',
        body: JSON.stringify(hotspotData),
      });

      return this.mapCloudPanoHotspotToTourHotspot(response);
    } catch (error) {
      throw new TourCreationError(
        'HOTSPOT_ADD_FAILED',
        'Failed to add hotspot to CloudPano scene',
        error,
        'cloudpano'
      );
    }
  }

  async publishTour(tourId: string): Promise<CreatedTour> {
    try {
      const response = await this.makeRequest<CloudPanoTour>(`/tours/${tourId}/publish`, {
        method: 'POST',
      });

      return this.mapCloudPanoTourToCreatedTour(response);
    } catch (error) {
      throw new TourCreationError(
        'PUBLISH_FAILED',
        'Failed to publish tour on CloudPano',
        error,
        'cloudpano'
      );
    }
  }

  async getAnalytics(tourId: string): Promise<TourAnalytics> {
    try {
      const response = await this.makeRequest<any>(`/tours/${tourId}/analytics`);
      
      return {
        views: response.views || 0,
        uniqueVisitors: response.unique_visitors || 0,
        averageViewTime: response.average_view_time || 0,
        hotspotClicks: response.hotspot_clicks || 0,
        sceneViews: response.scene_views || [],
        deviceBreakdown: response.device_breakdown || {},
        geographicData: response.geographic_data || {}
      };
    } catch (error) {
      throw new TourCreationError(
        'ANALYTICS_FAILED',
        'Failed to fetch analytics from CloudPano',
        error,
        'cloudpano'
      );
    }
  }

  generateEmbedCode(tourId: string, options: EmbedOptions = {}): string {
    const {
      width = '100%',
      height = '600px',
      autoplay = false,
      showControls = true,
      allowFullscreen = true
    } = options;

    const params = new URLSearchParams();
    if (autoplay) params.append('autoplay', '1');
    if (!showControls) params.append('controls', '0');

    // Get viewer URL from settings or use default
    let viewerUrl = 'https://viewer.cloudpano.com';
    try {
      const stored = localStorage.getItem('vrt_cloudpano_settings');
      if (stored) {
        const settings = JSON.parse(stored);
        if (settings.viewerUrl) {
          viewerUrl = settings.viewerUrl;
        }
      }
    } catch (error) {
      console.warn('Could not load CloudPano viewer URL from settings:', error);
    }

    const src = `${viewerUrl}/tours/${tourId}${params.toString() ? '?' + params.toString() : ''}`;

    return `<iframe src="${src}" width="${width}" height="${height}" frameborder="0" ${allowFullscreen ? 'allowfullscreen' : ''} title="VirtualRealTour - 360° Virtual Tour"></iframe>`;
  }

  private mapCloudPanoTourToCreatedTour(tour: CloudPanoTour): CreatedTour {
    return {
      id: tour.id,
      title: tour.title,
      description: tour.description,
      embedUrl: tour.embed_url,
      editUrl: tour.edit_url,
      previewUrl: tour.viewer_url,
      scenes: tour.scenes?.map(scene => this.mapCloudPanoSceneToTourScene(scene)) || [],
      platform: 'cloudpano',
      platformSpecific: {
        cloudPanoTourId: tour.id,
      },
      settings: {
        autoRotate: tour.settings?.auto_rotate,
        showControls: tour.settings?.show_controls,
        enableVR: tour.settings?.enable_vr,
        customBranding: tour.settings?.custom_branding
      },
      createdAt: new Date(tour.created_at),
      updatedAt: new Date(tour.updated_at)
    };
  }

  private mapCloudPanoSceneToTourScene(scene: CloudPanoScene): TourScene {
    return {
      id: scene.id,
      name: scene.title,
      imageUrl: scene.image_url,
      thumbnailUrl: scene.thumbnail_url,
      orderIndex: scene.order_index,
      hotspots: scene.hotspots?.map(hotspot => this.mapCloudPanoHotspotToTourHotspot(hotspot)) || [],
      settings: scene.settings
    };
  }

  private mapCloudPanoHotspotToTourHotspot(hotspot: CloudPanoHotspot): TourHotspot {
    return {
      id: hotspot.id,
      type: hotspot.type,
      position: hotspot.position,
      title: hotspot.title,
      content: hotspot.content,
      targetSceneId: hotspot.target_scene_id,
      linkUrl: hotspot.link_url,
      productData: hotspot.product_data
    };
  }
}

export const cloudPanoService = new CloudPanoService();
