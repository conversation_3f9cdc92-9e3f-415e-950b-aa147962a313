/**
 * PSV Factory Tests
 * Unit tests for the Photo Sphere Viewer factory
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PSVFactory } from '@/lib/photosphere/factory';
import { 
  setupTestEnvironment, 
  createTestScene, 
  createTestScenes,
  expectPSVToBeInitialized,
  mockSupabase
} from '@/test/utils/psvTestUtils';

// Mock the PSV library
vi.mock('@photo-sphere-viewer/core', () => ({
  Viewer: vi.fn().mockImplementation(() => ({
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    removeAllListeners: vi.fn(),
    destroy: vi.fn(),
    refresh: vi.fn(),
    resize: vi.fn(),
    getPlugin: vi.fn(),
    container: document.createElement('div')
  }))
}));

vi.mock('@photo-sphere-viewer/autorotate-plugin', () => ({
  AutorotatePlugin: vi.fn()
}));

vi.mock('@photo-sphere-viewer/markers-plugin', () => ({
  MarkersPlugin: vi.fn()
}));

vi.mock('@photo-sphere-viewer/virtual-tour-plugin', () => ({
  VirtualTourPlugin: vi.fn()
}));

vi.mock('@photo-sphere-viewer/gallery-plugin', () => ({
  GalleryPlugin: vi.fn()
}));

vi.mock('@photo-sphere-viewer/settings-plugin', () => ({
  SettingsPlugin: vi.fn()
}));

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: mockSupabase
}));

describe('PSVFactory', () => {
  let cleanup: () => void;
  let container: HTMLElement;

  beforeEach(() => {
    cleanup = setupTestEnvironment();
    container = document.createElement('div');
    container.id = 'test-viewer';
    document.body.appendChild(container);
  });

  afterEach(() => {
    cleanup();
    document.body.removeChild(container);
    vi.clearAllMocks();
  });

  describe('create', () => {
    it('should create a basic PSV instance', async () => {
      const instance = await PSVFactory.create({
        container: '#test-viewer',
        panorama: '/test-image.jpg'
      });

      expectPSVToBeInitialized(instance);
      expect(instance.viewer).toBeDefined();
      expect(instance.plugins).toBeDefined();
    });

    it('should create PSV with autorotate plugin', async () => {
      const instance = await PSVFactory.create({
        container: '#test-viewer',
        panorama: '/test-image.jpg',
        enableAutorotate: true
      });

      expectPSVToBeInitialized(instance);
      expect(instance.plugins.autorotate).toBeDefined();
    });

    it('should create PSV with markers plugin', async () => {
      const instance = await PSVFactory.create({
        container: '#test-viewer',
        panorama: '/test-image.jpg',
        enableMarkers: true
      });

      expectPSVToBeInitialized(instance);
      expect(instance.plugins.markers).toBeDefined();
      expect(instance.markerService).toBeDefined();
    });

    it('should create PSV with enterprise features', async () => {
      const instance = await PSVFactory.create({
        container: '#test-viewer',
        panorama: '/test-image.jpg',
        enterprise: {
          autoRotate: { enabled: true },
          quality: { resolution: 'high' }
        }
      });

      expectPSVToBeInitialized(instance);
      expect(instance.enterpriseFeatures).toBeDefined();
    });

    it('should handle invalid container', async () => {
      await expect(PSVFactory.create({
        container: '#non-existent',
        panorama: '/test-image.jpg'
      })).rejects.toThrow();
    });

    it('should handle invalid panorama', async () => {
      await expect(PSVFactory.create({
        container: '#test-viewer',
        panorama: ''
      })).rejects.toThrow();
    });
  });

  describe('createSimple', () => {
    it('should create a simple PSV instance', async () => {
      const instance = await PSVFactory.createSimple(
        '#test-viewer',
        '/test-image.jpg'
      );

      expectPSVToBeInitialized(instance);
      expect(instance.plugins.virtualTour).toBeUndefined();
    });

    it('should create simple PSV with options', async () => {
      const onReady = vi.fn();
      const instance = await PSVFactory.createSimple(
        '#test-viewer',
        '/test-image.jpg',
        {
          enableAutorotate: true,
          onReady
        }
      );

      expectPSVToBeInitialized(instance);
      expect(instance.plugins.autorotate).toBeDefined();
    });
  });

  describe('createVirtualTour', () => {
    it('should create virtual tour with multiple scenes', async () => {
      const scenes = createTestScenes(3);
      const instance = await PSVFactory.createVirtualTour(
        '#test-viewer',
        scenes
      );

      expectPSVToBeInitialized(instance);
      expect(instance.plugins.virtualTour).toBeDefined();
      expect(instance.plugins.gallery).toBeDefined();
    });

    it('should handle empty scenes array', async () => {
      await expect(PSVFactory.createVirtualTour(
        '#test-viewer',
        []
      )).rejects.toThrow('At least one scene is required');
    });

    it('should create virtual tour with custom options', async () => {
      const scenes = createTestScenes(2);
      const onSceneChange = vi.fn();
      
      const instance = await PSVFactory.createVirtualTour(
        '#test-viewer',
        scenes,
        {
          enableGallery: false,
          onSceneChange
        }
      );

      expectPSVToBeInitialized(instance);
      expect(instance.plugins.virtualTour).toBeDefined();
    });
  });

  describe('createVirtualTourFromId', () => {
    it('should create virtual tour from database ID', async () => {
      // Mock successful database response
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: {
                id: 'test-tour',
                title: 'Test Tour',
                scenes: createTestScenes(2)
              },
              error: null
            })
          })
        })
      });

      const instance = await PSVFactory.createVirtualTourFromId(
        '#test-viewer',
        'test-tour'
      );

      expectPSVToBeInitialized(instance);
      expect(instance.plugins.virtualTour).toBeDefined();
    });

    it('should handle tour not found', async () => {
      // Mock tour not found
      mockSupabase.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: 'Tour not found' }
            })
          })
        })
      });

      await expect(PSVFactory.createVirtualTourFromId(
        '#test-viewer',
        'non-existent-tour'
      )).rejects.toThrow('Tour non-existent-tour not found');
    });
  });

  describe('memory management', () => {
    it('should register instance for memory management', async () => {
      const instance = await PSVFactory.create({
        container: '#test-viewer',
        panorama: '/test-image.jpg'
      });

      expect(instance.destroy).toBeTypeOf('function');
      
      // Test cleanup
      instance.destroy();
      expect(instance.viewer.destroy).toHaveBeenCalled();
    });

    it('should cleanup marker service on destroy', async () => {
      const instance = await PSVFactory.create({
        container: '#test-viewer',
        panorama: '/test-image.jpg',
        enableMarkers: true
      });

      const destroySpy = vi.spyOn(instance.markerService!, 'destroy');
      
      instance.destroy();
      expect(destroySpy).toHaveBeenCalled();
    });

    it('should cleanup enterprise features on destroy', async () => {
      const instance = await PSVFactory.create({
        container: '#test-viewer',
        panorama: '/test-image.jpg',
        enterprise: { autoRotate: { enabled: true } }
      });

      const destroySpy = vi.spyOn(instance.enterpriseFeatures!, 'destroy');
      
      instance.destroy();
      expect(destroySpy).toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('should handle WebGL not supported', async () => {
      // Mock WebGL not supported
      vi.spyOn(HTMLCanvasElement.prototype, 'getContext').mockReturnValue(null);

      await expect(PSVFactory.create({
        container: '#test-viewer',
        panorama: '/test-image.jpg'
      })).rejects.toThrow();
    });

    it('should handle panorama load error', async () => {
      // Mock image load error
      const originalImage = window.Image;
      window.Image = class MockImage {
        onerror: (() => void) | null = null;
        constructor() {
          setTimeout(() => {
            if (this.onerror) {
              this.onerror();
            }
          }, 10);
        }
      } as any;

      await expect(PSVFactory.create({
        container: '#test-viewer',
        panorama: '/invalid-image.jpg'
      })).rejects.toThrow();

      window.Image = originalImage;
    });

    it('should call error callback on failure', async () => {
      const onError = vi.fn();
      
      try {
        await PSVFactory.create({
          container: '#non-existent',
          panorama: '/test-image.jpg',
          onError
        });
      } catch (error) {
        // Expected to throw
      }

      expect(onError).toHaveBeenCalled();
    });
  });

  describe('performance monitoring', () => {
    it('should enable performance monitoring when requested', async () => {
      const instance = await PSVFactory.create({
        container: '#test-viewer',
        panorama: '/test-image.jpg',
        enablePerformanceMonitoring: true
      });

      expectPSVToBeInitialized(instance);
      // Performance monitoring would be tested through integration tests
    });
  });

  describe('configuration validation', () => {
    it('should validate required options', async () => {
      await expect(PSVFactory.create({
        container: '',
        panorama: '/test-image.jpg'
      })).rejects.toThrow();

      await expect(PSVFactory.create({
        container: '#test-viewer',
        panorama: ''
      })).rejects.toThrow();
    });

    it('should merge custom configuration', async () => {
      const customConfig = {
        navbar: ['zoom', 'move', 'fullscreen'],
        defaultZoomLvl: 30
      };

      const instance = await PSVFactory.create({
        container: '#test-viewer',
        panorama: '/test-image.jpg',
        customConfig
      });

      expectPSVToBeInitialized(instance);
      expect(instance.config).toMatchObject(customConfig);
    });
  });
});
