export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      featured_tour_assignments: {
        Row: {
          created_at: string
          display_order: number | null
          id: string
          is_active: boolean | null
          section_type: string
          tour_id: string
          updated_at: string
        }
        Insert: {
          created_at?: string
          display_order?: number | null
          id?: string
          is_active?: boolean | null
          section_type: string
          tour_id: string
          updated_at?: string
        }
        Update: {
          created_at?: string
          display_order?: number | null
          id?: string
          is_active?: boolean | null
          section_type?: string
          tour_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "featured_tour_assignments_tour_id_fkey"
            columns: ["tour_id"]
            isOneToOne: false
            referencedRelation: "tours"
            referencedColumns: ["id"]
          },
        ]
      }
      hotspots: {
        Row: {
          content: string | null
          created_at: string
          id: string
          label: string | null
          link_url: string | null
          position_x: number
          position_y: number
          position_z: number
          scene_id: string
          target_scene_id: string | null
          type: Database["public"]["Enums"]["hotspot_type"]
          whatsapp_message: string | null
          whatsapp_phone: string | null
        }
        Insert: {
          content?: string | null
          created_at?: string
          id?: string
          label?: string | null
          link_url?: string | null
          position_x: number
          position_y: number
          position_z: number
          scene_id: string
          target_scene_id?: string | null
          type: Database["public"]["Enums"]["hotspot_type"]
          whatsapp_message?: string | null
          whatsapp_phone?: string | null
        }
        Update: {
          content?: string | null
          created_at?: string
          id?: string
          label?: string | null
          link_url?: string | null
          position_x?: number
          position_y?: number
          position_z?: number
          scene_id?: string
          target_scene_id?: string | null
          type?: Database["public"]["Enums"]["hotspot_type"]
          whatsapp_message?: string | null
          whatsapp_phone?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "hotspots_scene_id_fkey"
            columns: ["scene_id"]
            isOneToOne: false
            referencedRelation: "scenes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "hotspots_target_scene_id_fkey"
            columns: ["target_scene_id"]
            isOneToOne: false
            referencedRelation: "scenes"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          email: string
          full_name: string | null
          id: string
          role: Database["public"]["Enums"]["user_role"] | null
          updated_at: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          email: string
          full_name?: string | null
          id: string
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          email?: string
          full_name?: string | null
          id?: string
          role?: Database["public"]["Enums"]["user_role"] | null
          updated_at?: string
        }
        Relationships: []
      }
      scenes: {
        Row: {
          created_at: string
          description: string | null
          id: string
          image_url: string
          name: string
          order_index: number
          tour_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          image_url: string
          name: string
          order_index?: number
          tour_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          image_url?: string
          name?: string
          order_index?: number
          tour_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "scenes_tour_id_fkey"
            columns: ["tour_id"]
            isOneToOne: false
            referencedRelation: "tours"
            referencedColumns: ["id"]
          },
        ]
      }
      tour_analytics: {
        Row: {
          id: string
          time_spent_seconds: number | null
          tour_id: string
          viewed_at: string
          viewer_ip: string | null
          viewer_location: string | null
        }
        Insert: {
          id?: string
          time_spent_seconds?: number | null
          tour_id: string
          viewed_at?: string
          viewer_ip?: string | null
          viewer_location?: string | null
        }
        Update: {
          id?: string
          time_spent_seconds?: number | null
          tour_id?: string
          viewed_at?: string
          viewer_ip?: string | null
          viewer_location?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "tour_analytics_tour_id_fkey"
            columns: ["tour_id"]
            isOneToOne: false
            referencedRelation: "tours"
            referencedColumns: ["id"]
          },
        ]
      }
      tours: {
        Row: {
          category: Database["public"]["Enums"]["tour_category"]
          created_at: string
          description: string | null
          embed_type: string | null
          embed_url: string | null
          featured: boolean | null
          id: string
          location: string | null
          scenes_count: number | null
          status: Database["public"]["Enums"]["tour_status"] | null
          thumbnail_url: string | null
          title: string
          updated_at: string
          user_id: string
          views: number | null
        }
        Insert: {
          category: Database["public"]["Enums"]["tour_category"]
          created_at?: string
          description?: string | null
          embed_type?: string | null
          embed_url?: string | null
          featured?: boolean | null
          id?: string
          location?: string | null
          scenes_count?: number | null
          status?: Database["public"]["Enums"]["tour_status"] | null
          thumbnail_url?: string | null
          title: string
          updated_at?: string
          user_id: string
          views?: number | null
        }
        Update: {
          category?: Database["public"]["Enums"]["tour_category"]
          created_at?: string
          description?: string | null
          embed_type?: string | null
          embed_url?: string | null
          featured?: boolean | null
          id?: string
          location?: string | null
          scenes_count?: number | null
          status?: Database["public"]["Enums"]["tour_status"] | null
          thumbnail_url?: string | null
          title?: string
          updated_at?: string
          user_id?: string
          views?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "tours_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_featured_tours_for_section: {
        Args: { section_name: string }
        Returns: {
          tour_id: string
          title: string
          description: string
          category: string
          location: string
          thumbnail_url: string
          embed_url: string
          embed_type: string
          views: number
          scenes_count: number
          display_order: number
        }[]
      }
      increment_tour_views: {
        Args: { tour_uuid: string }
        Returns: undefined
      }
    }
    Enums: {
      hotspot_type:
        | "navigation"
        | "info"
        | "whatsapp"
        | "link"
        | "audio"
        | "video"
      tour_category:
        | "property"
        | "education"
        | "hospitality"
        | "tourism"
        | "culture"
        | "commercial"
        | "healthcare"
        | "government"
      tour_status: "draft" | "processing" | "published" | "archived"
      user_role: "user" | "admin"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {
      hotspot_type: [
        "navigation",
        "info",
        "whatsapp",
        "link",
        "audio",
        "video",
      ],
      tour_category: [
        "property",
        "education",
        "hospitality",
        "tourism",
        "culture",
        "commercial",
        "healthcare",
        "government",
      ],
      tour_status: ["draft", "processing", "published", "archived"],
      user_role: ["user", "admin"],
    },
  },
} as const
