/**
 * Virtual Tour Service
 * Server-side node management and real-time collaboration for PSV Virtual Tour Plugin
 */

import { supabase } from '@/lib/supabase';
import type { TourScene, TourMarker, TourLink } from './types';

export interface VirtualTourNode {
  id: string;
  name: string;
  panorama: string;
  thumbnail?: string;
  caption?: string;
  description?: string;
  links: TourLink[];
  markers: TourMarker[];
  gps?: [number, number, number?];
  panoData?: any;
  sphereCorrection?: {
    pan?: number;
    tilt?: number;
    roll?: number;
  };
  data?: Record<string, any>;
}

export interface VirtualTourData {
  id: string;
  title: string;
  description?: string;
  nodes: VirtualTourNode[];
  startNodeId: string;
  settings: {
    autoRotate?: boolean;
    showControls?: boolean;
    enableVR?: boolean;
    customBranding?: boolean;
  };
  metadata?: Record<string, any>;
}

/**
 * Virtual Tour Service for server-side node management
 */
export class VirtualTourService {
  private tourCache = new Map<string, VirtualTourData>();
  private nodeCache = new Map<string, VirtualTourNode>();

  /**
   * Load complete virtual tour data from database
   */
  async loadTour(tourId: string): Promise<VirtualTourData | null> {
    try {
      // Check cache first
      if (this.tourCache.has(tourId)) {
        return this.tourCache.get(tourId)!;
      }

      // Load tour from database
      const { data: tour, error: tourError } = await supabase
        .from('tours')
        .select(`
          id,
          title,
          description,
          tour_config,
          scenes (
            id,
            name,
            image_url,
            thumbnail_url,
            description,
            order_index,
            initial_view,
            hotspots (
              id,
              type,
              label,
              content,
              position_x,
              position_y,
              position_z,
              style,
              actions,
              target_scene_id,
              product_id,
              vendor_id,
              link_url,
              whatsapp_phone,
              whatsapp_message,
              media_url
            )
          )
        `)
        .eq('id', tourId)
        .eq('status', 'published')
        .single();

      if (tourError || !tour) {
        console.error('Failed to load tour:', tourError);
        return null;
      }

      // Convert database format to VirtualTourData
      const nodes: VirtualTourNode[] = tour.scenes
        .sort((a: any, b: any) => a.order_index - b.order_index)
        .map((scene: any) => this.convertSceneToNode(scene, tour.scenes));

      const tourData: VirtualTourData = {
        id: tour.id,
        title: tour.title,
        description: tour.description,
        nodes,
        startNodeId: nodes[0]?.id || '',
        settings: tour.tour_config?.settings || {
          autoRotate: false,
          showControls: true,
          enableVR: false,
          customBranding: true
        },
        metadata: tour.tour_config?.metadata || {}
      };

      // Cache the tour data
      this.tourCache.set(tourId, tourData);
      
      // Cache individual nodes
      nodes.forEach(node => {
        this.nodeCache.set(node.id, node);
      });

      return tourData;
    } catch (error) {
      console.error('Error loading virtual tour:', error);
      return null;
    }
  }

  /**
   * Get a specific node by ID (for server-side loading)
   */
  async getNode(nodeId: string): Promise<VirtualTourNode | null> {
    try {
      // Check cache first
      if (this.nodeCache.has(nodeId)) {
        return this.nodeCache.get(nodeId)!;
      }

      // Load node from database
      const { data: scene, error } = await supabase
        .from('scenes')
        .select(`
          id,
          name,
          image_url,
          thumbnail_url,
          description,
          order_index,
          initial_view,
          tour_id,
          hotspots (
            id,
            type,
            label,
            content,
            position_x,
            position_y,
            position_z,
            style,
            actions,
            target_scene_id,
            product_id,
            vendor_id,
            link_url,
            whatsapp_phone,
            whatsapp_message,
            media_url
          )
        `)
        .eq('id', nodeId)
        .single();

      if (error || !scene) {
        console.error('Failed to load scene:', error);
        return null;
      }

      // Get all scenes from the same tour for link generation
      const { data: allScenes } = await supabase
        .from('scenes')
        .select('id, name')
        .eq('tour_id', scene.tour_id);

      const node = this.convertSceneToNode(scene, allScenes || []);
      
      // Cache the node
      this.nodeCache.set(nodeId, node);
      
      return node;
    } catch (error) {
      console.error('Error loading node:', error);
      return null;
    }
  }

  /**
   * Convert database scene to VirtualTourNode
   */
  private convertSceneToNode(scene: any, allScenes: any[]): VirtualTourNode {
    // Convert hotspots to markers and links
    const markers: TourMarker[] = [];
    const links: TourLink[] = [];

    scene.hotspots?.forEach((hotspot: any) => {
      if (hotspot.type === 'navigation' && hotspot.target_scene_id) {
        // Navigation hotspot becomes a link
        links.push({
          nodeId: hotspot.target_scene_id,
          position: {
            yaw: `${hotspot.position_x}deg`,
            pitch: `${hotspot.position_y}deg`
          },
          markerStyle: {
            html: `<div class="psv-link-marker">
              <div class="psv-link-icon">🧭</div>
              <div class="psv-link-tooltip">${hotspot.label || 'Go to scene'}</div>
            </div>`,
            className: 'psv-navigation-link',
            tooltip: hotspot.label || 'Navigate to scene',
            data: { hotspotId: hotspot.id }
          },
          linkStyle: {
            color: hotspot.style?.color || '#3b82f6',
            hoverColor: hotspot.style?.hoverColor || '#1d4ed8'
          },
          data: {
            originalHotspot: hotspot
          }
        });
      } else {
        // Other hotspots become markers
        markers.push({
          id: hotspot.id,
          type: hotspot.type,
          position: {
            yaw: `${hotspot.position_x}deg`,
            pitch: `${hotspot.position_y}deg`
          },
          title: hotspot.label,
          content: hotspot.content,
          html: this.generateMarkerHTML(hotspot),
          className: `psv-marker psv-marker-${hotspot.type}`,
          style: hotspot.style || {},
          data: {
            originalHotspot: hotspot,
            productData: hotspot.product_id ? {
              id: hotspot.product_id,
              vendorId: hotspot.vendor_id
            } : undefined,
            linkUrl: hotspot.link_url,
            mediaUrl: hotspot.media_url,
            whatsappPhone: hotspot.whatsapp_phone,
            whatsappMessage: hotspot.whatsapp_message
          }
        });
      }
    });

    return {
      id: scene.id,
      name: scene.name,
      panorama: scene.image_url,
      thumbnail: scene.thumbnail_url,
      description: scene.description,
      links,
      markers,
      data: {
        orderIndex: scene.order_index,
        initialView: scene.initial_view
      }
    };
  }

  /**
   * Generate HTML for marker display
   */
  private generateMarkerHTML(hotspot: any): string {
    const iconMap = {
      product: '🛍️',
      info: 'ℹ️',
      link: '🔗',
      media: '📷',
      custom: '📍'
    };

    const icon = iconMap[hotspot.type as keyof typeof iconMap] || '📍';
    const color = hotspot.style?.color || '#3b82f6';

    return `
      <div class="psv-marker-content" style="background-color: ${color};">
        <div class="psv-marker-icon">${icon}</div>
        <div class="psv-marker-tooltip">${hotspot.label || hotspot.type}</div>
      </div>
    `;
  }

  /**
   * Preload strategy for connected scenes
   */
  shouldPreloadNode(node: VirtualTourNode, link: TourLink): boolean {
    // Preload nodes that are directly connected
    return node.links.some(l => l.nodeId === link.nodeId);
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.tourCache.clear();
    this.nodeCache.clear();
  }

  /**
   * Get cached tour data
   */
  getCachedTour(tourId: string): VirtualTourData | undefined {
    return this.tourCache.get(tourId);
  }

  /**
   * Update node in cache (for real-time collaboration)
   */
  updateNodeInCache(nodeId: string, updates: Partial<VirtualTourNode>): void {
    const existingNode = this.nodeCache.get(nodeId);
    if (existingNode) {
      const updatedNode = { ...existingNode, ...updates };
      this.nodeCache.set(nodeId, updatedNode);
    }
  }
}

// Singleton instance
export const virtualTourService = new VirtualTourService();
