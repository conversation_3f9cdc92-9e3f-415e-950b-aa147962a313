/**
 * Photo Sphere Viewer Types
 * Production-ready type definitions for PSV v5 integration
 */

import type { Viewer } from '@photo-sphere-viewer/core';
import type { VirtualTourPlugin } from '@photo-sphere-viewer/virtual-tour-plugin';
import type { MarkersPlugin } from '@photo-sphere-viewer/markers-plugin';
import type { AutorotatePlugin } from '@photo-sphere-viewer/autorotate-plugin';
import type { GalleryPlugin } from '@photo-sphere-viewer/gallery-plugin';
import type { SettingsPlugin } from '@photo-sphere-viewer/settings-plugin';

// Core viewer configuration
export interface PSVConfig {
  container: HTMLElement | string;
  panorama: string;
  plugins?: Array<[any, any?]>;
  navbar?: string[] | boolean;
  caption?: string;
  description?: string;
  loadingImg?: string;
  loadingTxt?: string;
  size?: {
    width: string | number;
    height: string | number;
  };
  fisheye?: boolean;
  minFov?: number;
  maxFov?: number;
  defaultZoomLvl?: number;
  defaultLat?: number;
  defaultLong?: number;
  sphereCorrection?: {
    pan?: number;
    tilt?: number;
    roll?: number;
  };
  moveSpeed?: number;
  zoomSpeed?: number;
  autorotateDelay?: number;
  autorotateIdle?: boolean;
  autorotateSpeed?: string;
  transition?: {
    duration?: number;
    loader?: boolean;
    blur?: boolean;
  };
  withCredentials?: boolean;
  requestHeaders?: Record<string, string>;
  canvasBackground?: string;
  mousemove?: boolean;
  mousewheelCtrlKey?: boolean;
  touchmoveTwoFingers?: boolean;
  useXmpData?: boolean;
  panoData?: any;
  overlay?: any;
  overlayOpacity?: number;
  moveInertia?: boolean;
  clickEventOnMarker?: boolean;
  keyboard?: boolean | string[];
}

// Tour scene configuration
export interface TourScene {
  id: string;
  name: string;
  panorama: string;
  thumbnail?: string;
  caption?: string;
  description?: string;
  links?: TourLink[];
  markers?: TourMarker[];
  gps?: [number, number, number?]; // [longitude, latitude, altitude]
  panoData?: any;
  sphereCorrection?: {
    pan?: number;
    tilt?: number;
    roll?: number;
  };
  data?: Record<string, any>;
}

// Navigation links between scenes
export interface TourLink {
  nodeId: string;
  position: {
    yaw: number | string;
    pitch: number | string;
  };
  markerStyle?: {
    html?: string;
    className?: string;
    style?: Record<string, any>;
    scale?: [number, number];
    anchor?: string;
    visible?: boolean;
    tooltip?: string | { content: string; position?: string };
    data?: Record<string, any>;
  };
  linkStyle?: {
    color?: string;
    hoverColor?: string;
    lineWidth?: number;
    arrowStyle?: {
      color?: string;
      hoverColor?: string;
      scale?: number;
    };
  };
  data?: Record<string, any>;
}

// Interactive markers/hotspots
export interface TourMarker {
  id: string;
  type: 'navigation' | 'product' | 'info' | 'link' | 'media' | 'custom';
  position: {
    yaw: number | string;
    pitch: number | string;
  };
  title?: string;
  content?: string;
  html?: string;
  className?: string;
  style?: Record<string, any>;
  scale?: [number, number];
  anchor?: string;
  visible?: boolean;
  tooltip?: string | { content: string; position?: string };
  data?: Record<string, any>;
  
  // E-commerce specific
  productData?: {
    id: string;
    name: string;
    price: number;
    currency?: string;
    image: string;
    description: string;
    whatsappMessage?: string;
    vendorId?: string;
  };
  
  // Navigation specific
  targetSceneId?: string;
  
  // Link specific
  linkUrl?: string;
  linkTarget?: '_blank' | '_self' | '_parent' | '_top';
  
  // Media specific
  mediaUrl?: string;
  mediaType?: 'image' | 'video' | 'audio';
  
  // Custom actions
  onClick?: (marker: TourMarker, viewer: Viewer) => void;
  onEnter?: (marker: TourMarker, viewer: Viewer) => void;
  onLeave?: (marker: TourMarker, viewer: Viewer) => void;
}

// Virtual tour configuration
export interface VirtualTourConfig {
  nodes: TourScene[];
  startNodeId?: string;
  preload?: boolean | ((node: TourScene, link: TourLink) => boolean);
  transition?: {
    duration?: number;
    loader?: boolean;
    blur?: boolean;
  };
  linksOnCompass?: boolean;
  getNode?: (nodeId: string) => Promise<TourScene> | TourScene;
  dataMode?: 'client' | 'server';
  renderMode?: '3d' | 'markers';
  markerStyle?: any;
  arrowStyle?: any;
}

// Gallery configuration
export interface GalleryConfig {
  items: Array<{
    id: string;
    name: string;
    panorama: string;
    thumbnail?: string;
    data?: Record<string, any>;
  }>;
  visibleOnLoad?: boolean;
  hideOnClick?: boolean;
  thumbnailSize?: {
    width: number;
    height: number;
  };
}

// Settings configuration
export interface SettingsConfig {
  persist?: boolean;
  storage?: {
    set: (key: string, value: any) => void;
    get: (key: string) => any;
    remove: (key: string) => void;
  };
  lang?: Record<string, string>;
}

// Error handling
export class PSVError extends Error {
  constructor(
    public code: string,
    message: string,
    public originalError?: any,
    public context?: Record<string, any>
  ) {
    super(message);
    this.name = 'PSVError';
  }
}

// Event types
export interface PSVEvents {
  'ready': () => void;
  'size-updated': (size: { width: number; height: number }) => void;
  'zoom-updated': (data: { zoomLevel: number }) => void;
  'position-updated': (data: { position: { yaw: number; pitch: number } }) => void;
  'click': (data: { rightclick: boolean; target: any }) => void;
  'dblclick': (data: { target: any }) => void;
  'fullscreen-updated': (enabled: boolean) => void;
  'before-render': () => void;
  'render': () => void;
  'before-rotate': () => void;
  'autorotate': (enabled: boolean) => void;
  'stop-all': () => void;
  'config-changed': (config: Partial<PSVConfig>) => void;
  'panorama-load-progress': (data: { progress: number }) => void;
  'panorama-loaded': () => void;
  'panorama-error': (error: any) => void;
  'hide-notification': (notificationId: string) => void;
  'show-notification': (notificationId: string) => void;
  'hide-overlay': () => void;
  'show-overlay': () => void;
  'hide-panel': (panelId: string) => void;
  'show-panel': (panelId: string) => void;
  'hide-tooltip': (tooltipData: any) => void;
  'show-tooltip': (tooltipData: any) => void;
}

// Plugin instances
export interface PSVPlugins {
  virtualTour?: VirtualTourPlugin;
  markers?: MarkersPlugin;
  autorotate?: AutorotatePlugin;
  gallery?: GalleryPlugin;
  settings?: SettingsPlugin;
}

// Complete viewer instance
export interface PSVInstance {
  viewer: Viewer;
  plugins: PSVPlugins;
  config: PSVConfig;
  markerService?: any; // Will be typed properly when imported
  enterpriseFeatures?: any; // Will be typed properly when imported
  destroy: () => void;
  refresh: () => void;
  resize: () => void;
}

// Asset validation
export interface AssetValidation {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  metadata?: {
    width: number;
    height: number;
    aspectRatio: number;
    fileSize: number;
    format: string;
  };
}

// Performance monitoring
export interface PSVPerformanceMetrics {
  initTime: number;
  renderTime: number;
  memoryUsage: number;
  frameRate: number;
  loadTime: number;
  errorCount: number;
}
