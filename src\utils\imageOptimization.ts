// Image Optimization Pipeline
// Handles WebP/AVIF conversion, format detection, and progressive loading

export interface ImageFormat {
  webp: string;
  avif?: string;
  original: string;
  placeholder?: string;
}

export interface ImageOptimizationOptions {
  quality?: number;
  width?: number;
  height?: number;
  generatePlaceholder?: boolean;
  formats?: ('webp' | 'avif' | 'jpeg' | 'png')[];
}

// Browser support detection
export class FormatSupport {
  private static cache = new Map<string, boolean>();

  static async supportsWebP(): Promise<boolean> {
    if (this.cache.has('webp')) {
      return this.cache.get('webp')!;
    }

    const supported = await this.testFormat('webp');
    this.cache.set('webp', supported);
    return supported;
  }

  static async supportsAVIF(): Promise<boolean> {
    if (this.cache.has('avif')) {
      return this.cache.get('avif')!;
    }

    const supported = await this.testFormat('avif');
    this.cache.set('avif', supported);
    return supported;
  }

  private static testFormat(format: string): Promise<boolean> {
    return new Promise((resolve) => {
      const img = new Image();
      img.onload = () => resolve(img.width === 1 && img.height === 1);
      img.onerror = () => resolve(false);
      
      // Test images for format support
      const testImages = {
        webp: 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA',
        avif: 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAEAAAABAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A='
      };
      
      img.src = testImages[format as keyof typeof testImages];
    });
  }
}

// Image optimization utilities
export class ImageOptimizer {
  private canvas: HTMLCanvasElement;
  private ctx: CanvasRenderingContext2D;

  constructor() {
    this.canvas = document.createElement('canvas');
    this.ctx = this.canvas.getContext('2d')!;
  }

  // Convert image to different formats
  async optimizeImage(
    file: File | string,
    options: ImageOptimizationOptions = {}
  ): Promise<ImageFormat> {
    const {
      quality = 0.8,
      width,
      height,
      generatePlaceholder = true,
      formats = ['webp', 'avif']
    } = options;

    const img = await this.loadImage(file);
    const { width: imgWidth, height: imgHeight } = this.calculateDimensions(
      img.naturalWidth,
      img.naturalHeight,
      width,
      height
    );

    // Set canvas dimensions
    this.canvas.width = imgWidth;
    this.canvas.height = imgHeight;

    // Draw image to canvas
    this.ctx.drawImage(img, 0, 0, imgWidth, imgHeight);

    const result: ImageFormat = {
      original: typeof file === 'string' ? file : URL.createObjectURL(file),
      webp: '',
      avif: undefined,
      placeholder: undefined
    };

    // Generate WebP
    if (formats.includes('webp') && await FormatSupport.supportsWebP()) {
      result.webp = this.canvas.toDataURL('image/webp', quality);
    }

    // Generate AVIF (if supported by browser)
    if (formats.includes('avif') && await FormatSupport.supportsAVIF()) {
      try {
        result.avif = this.canvas.toDataURL('image/avif', quality);
      } catch (error) {
        console.warn('AVIF generation failed:', error);
      }
    }

    // Generate placeholder
    if (generatePlaceholder) {
      result.placeholder = await this.generatePlaceholder(img);
    }

    return result;
  }

  // Generate low-quality placeholder
  private async generatePlaceholder(img: HTMLImageElement): Promise<string> {
    const placeholderSize = 20;
    const tempCanvas = document.createElement('canvas');
    const tempCtx = tempCanvas.getContext('2d')!;

    tempCanvas.width = placeholderSize;
    tempCanvas.height = placeholderSize;

    // Draw small, blurred version
    tempCtx.filter = 'blur(2px)';
    tempCtx.drawImage(img, 0, 0, placeholderSize, placeholderSize);

    return tempCanvas.toDataURL('image/jpeg', 0.1);
  }

  // Load image from file or URL
  private loadImage(source: File | string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => resolve(img);
      img.onerror = reject;

      if (typeof source === 'string') {
        img.src = source;
      } else {
        img.src = URL.createObjectURL(source);
      }
    });
  }

  // Calculate optimal dimensions
  private calculateDimensions(
    originalWidth: number,
    originalHeight: number,
    targetWidth?: number,
    targetHeight?: number
  ): { width: number; height: number } {
    if (!targetWidth && !targetHeight) {
      return { width: originalWidth, height: originalHeight };
    }

    const aspectRatio = originalWidth / originalHeight;

    if (targetWidth && targetHeight) {
      return { width: targetWidth, height: targetHeight };
    }

    if (targetWidth) {
      return { width: targetWidth, height: targetWidth / aspectRatio };
    }

    if (targetHeight) {
      return { width: targetHeight * aspectRatio, height: targetHeight };
    }

    return { width: originalWidth, height: originalHeight };
  }
}

// Image URL builder with format selection
export class ImageUrlBuilder {
  private baseUrl: string;
  private supportWebP: boolean = false;
  private supportAVIF: boolean = false;

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
    this.initializeSupport();
  }

  private async initializeSupport() {
    this.supportWebP = await FormatSupport.supportsWebP();
    this.supportAVIF = await FormatSupport.supportsAVIF();
  }

  // Build optimized image URL with format selection
  buildUrl(
    imagePath: string,
    options: {
      width?: number;
      height?: number;
      quality?: number;
      format?: 'auto' | 'webp' | 'avif' | 'jpeg' | 'png';
    } = {}
  ): string {
    const { width, height, quality = 80, format = 'auto' } = options;
    
    let selectedFormat = format;
    
    // Auto-select best format
    if (format === 'auto') {
      if (this.supportAVIF) {
        selectedFormat = 'avif';
      } else if (this.supportWebP) {
        selectedFormat = 'webp';
      } else {
        selectedFormat = 'jpeg';
      }
    }

    const params = new URLSearchParams();
    
    if (width) params.set('w', width.toString());
    if (height) params.set('h', height.toString());
    if (quality !== 80) params.set('q', quality.toString());
    if (selectedFormat !== 'jpeg') params.set('f', selectedFormat);

    const queryString = params.toString();
    const separator = imagePath.includes('?') ? '&' : '?';
    
    return `${this.baseUrl}${imagePath}${queryString ? separator + queryString : ''}`;
  }

  // Generate responsive image sources
  generateSources(
    imagePath: string,
    breakpoints: { width: number; descriptor?: string }[] = [
      { width: 320, descriptor: '320w' },
      { width: 640, descriptor: '640w' },
      { width: 1024, descriptor: '1024w' },
      { width: 1920, descriptor: '1920w' }
    ]
  ): { srcSet: string; sizes: string } {
    const srcSet = breakpoints
      .map(({ width, descriptor }) => {
        const url = this.buildUrl(imagePath, { width });
        return `${url} ${descriptor || `${width}w`}`;
      })
      .join(', ');

    const sizes = breakpoints
      .map(({ width }, index) => {
        if (index === breakpoints.length - 1) {
          return `${width}px`;
        }
        return `(max-width: ${width}px) ${width}px`;
      })
      .join(', ');

    return { srcSet, sizes };
  }
}

// Batch image processing
export class BatchImageProcessor {
  private optimizer: ImageOptimizer;
  private concurrency: number;

  constructor(concurrency: number = 3) {
    this.optimizer = new ImageOptimizer();
    this.concurrency = concurrency;
  }

  async processImages(
    files: File[],
    options: ImageOptimizationOptions = {},
    onProgress?: (completed: number, total: number) => void
  ): Promise<ImageFormat[]> {
    const results: ImageFormat[] = [];
    const chunks = this.chunkArray(files, this.concurrency);

    let completed = 0;

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(async (file) => {
        const result = await this.optimizer.optimizeImage(file, options);
        completed++;
        onProgress?.(completed, files.length);
        return result;
      });

      const chunkResults = await Promise.all(chunkPromises);
      results.push(...chunkResults);
    }

    return results;
  }

  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}

// Utility functions
export function getOptimalImageFormat(): 'avif' | 'webp' | 'jpeg' {
  // This will be determined at runtime
  return 'jpeg'; // Default fallback
}

export async function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
}

export function generateImageSizes(
  containerWidth: number,
  devicePixelRatio: number = window.devicePixelRatio || 1
): number[] {
  const baseWidth = containerWidth * devicePixelRatio;
  return [
    Math.round(baseWidth * 0.5),
    Math.round(baseWidth),
    Math.round(baseWidth * 1.5),
    Math.round(baseWidth * 2)
  ].filter((width, index, arr) => arr.indexOf(width) === index);
}

// Export singleton instances
export const imageOptimizer = new ImageOptimizer();
export const imageUrlBuilder = new ImageUrlBuilder();
export const batchProcessor = new BatchImageProcessor();
