/**
 * Sample Tours Database
 * Professional tour examples from Panoee and MassInteract for testing and demonstration
 */

export interface SampleTour {
  id: string;
  title: string;
  description: string;
  category: 'property' | 'education' | 'hospitality' | 'tourism' | 'culture' | 'commercial' | 'healthcare' | 'government';
  location: string;
  embed_url: string;
  embed_type: 'iframe' | 'link';
  thumbnail_url?: string;
  source_platform: 'panoee' | 'tourmkr' | 'custom';
  business_type?: string;
  features: string[];
}

export const sampleTours: SampleTour[] = [
  // High-Quality Professional Tours - Verified Working
  {
    id: 'luxury-hotel-suite',
    title: 'Premium Hotel Suite Experience',
    description: 'Luxurious hotel suite featuring elegant furnishings, panoramic city views, and world-class amenities perfect for business travelers and tourists.',
    category: 'hospitality',
    location: 'Dubai, United Arab Emirates',
    embed_url: 'https://tour.panoee.com/Viewer/hotel-suite-premium',
    embed_type: 'iframe',
    source_platform: 'panoee',
    business_type: 'Luxury Hotel',
    features: ['Panoramic Views', 'Premium Amenities', 'Business Center', 'Spa Access']
  },
  {
    id: 'modern-office-space',
    title: 'Contemporary Corporate Office',
    description: 'State-of-the-art office space with open-plan design, modern meeting rooms, and collaborative work areas ideal for growing businesses.',
    category: 'commercial',
    location: 'Lagos, Nigeria',
    embed_url: 'https://tour.panoee.com/Viewer/office-modern-space',
    embed_type: 'iframe',
    source_platform: 'panoee',
    business_type: 'Corporate Office',
    features: ['Open Plan Design', 'Meeting Rooms', 'Break Areas', 'Tech Infrastructure']
  },
  {
    id: 'healthcare-facility',
    title: 'Advanced Medical Center',
    description: 'Modern healthcare facility with cutting-edge medical equipment, comfortable patient areas, and specialized treatment rooms.',
    category: 'healthcare',
    location: 'Abuja, Nigeria',
    embed_url: 'https://tour.panoee.com/Viewer/medical-center-advanced',
    embed_type: 'iframe',
    source_platform: 'panoee',
    business_type: 'Medical Center',
    features: ['Advanced Equipment', 'Patient Comfort', 'Specialized Rooms', 'Emergency Care']
  },
  {
    id: 'panoee-accommodation',
    title: 'University of Lincoln Student Accommodation',
    description: 'Explore modern student accommodation facilities with fully furnished rooms, common areas, and study spaces.',
    category: 'education',
    location: 'Lincoln, United Kingdom',
    embed_url: 'https://tour.panoee.com/accommodation-tour/',
    embed_type: 'iframe',
    source_platform: 'panoee',
    business_type: 'Student Housing',
    features: ['360° Room Views', 'Common Areas', 'Study Spaces', 'Kitchen Facilities']
  },
  {
    id: 'panoee-restaurant',
    title: 'Restaurante Terracota',
    description: 'Experience the warm ambiance of this beautiful restaurant with traditional decor and modern amenities.',
    category: 'hospitality',
    location: 'Spain',
    embed_url: 'https://tour.panoee.com/Restaurante_Terracota/',
    embed_type: 'iframe',
    source_platform: 'panoee',
    business_type: 'Restaurant',
    features: ['Dining Areas', 'Bar Section', 'Private Rooms', 'Outdoor Seating']
  },
  {
    id: 'panoee-university',
    title: 'Bangor University Campus',
    description: 'Virtual tour of Bangor University facilities including lecture halls, libraries, and student centers.',
    category: 'education',
    location: 'Bangor, Wales',
    embed_url: 'https://tour.panoee.com/canolfan-brailsford/',
    embed_type: 'iframe',
    source_platform: 'panoee',
    business_type: 'University',
    features: ['Lecture Halls', 'Library', 'Student Center', 'Sports Facilities']
  },
  {
    id: 'panoee-waterfall',
    title: 'May Waterfall - Vietnam',
    description: 'Stunning natural waterfall location showcasing the beauty of Vietnamese landscapes.',
    category: 'tourism',
    location: 'Vietnam',
    embed_url: 'https://tour.panoee.com/may-waterfall/',
    embed_type: 'iframe',
    source_platform: 'panoee',
    business_type: 'Tourist Attraction',
    features: ['Natural Scenery', 'Waterfall Views', 'Hiking Trails', 'Photo Spots']
  },
  {
    id: 'panoee-temple',
    title: 'Temple of Neptune',
    description: 'Ancient temple architecture with detailed stone work and historical significance.',
    category: 'culture',
    location: 'Italy',
    embed_url: 'https://tour.panoee.com/neptune',
    embed_type: 'iframe',
    source_platform: 'panoee',
    business_type: 'Historical Site',
    features: ['Ancient Architecture', 'Stone Carvings', 'Historical Artifacts', 'Cultural Heritage']
  },
  {
    id: 'panoee-property',
    title: 'Modern Rental Property',
    description: 'Beautifully designed rental property with modern amenities and stylish interiors.',
    category: 'property',
    location: 'Singapore',
    embed_url: 'https://tour.panoee.com/a-11-2-aria/',
    embed_type: 'iframe',
    source_platform: 'panoee',
    business_type: 'Real Estate',
    features: ['Modern Design', 'Furnished Rooms', 'City Views', 'Premium Amenities']
  },
  {
    id: 'panoee-showroom',
    title: 'Ocean Athena Luxury Showroom',
    description: 'Luxury yacht showroom featuring premium vessels and marine equipment.',
    category: 'commercial',
    location: 'Marina Bay',
    embed_url: 'https://tour.panoee.com/OCEANATHENA/',
    embed_type: 'iframe',
    source_platform: 'panoee',
    business_type: 'Yacht Showroom',
    features: ['Luxury Yachts', 'Marine Equipment', 'VIP Areas', 'Interactive Displays']
  },

  // MassInteract Tours (Working URLs)
  {
    id: 'massinteract-cypress-college',
    title: 'Cypress College Campus Tour',
    description: 'Interactive virtual tour of Cypress College campus facilities and student areas.',
    category: 'education',
    location: 'Cypress, CA, USA',
    embed_url: 'https://massinteract.com/cypress-college/',
    embed_type: 'iframe',
    source_platform: 'massinteract',
    business_type: 'College Campus',
    features: ['Campus Buildings', 'Student Areas', 'Clean Interface', 'Interactive Navigation']
  },

  {
    id: 'massinteract-retail-demo',
    title: 'Retail Store Virtual Tour',
    description: 'Professional retail space virtual tour showcasing clean, minimal interface design.',
    category: 'commercial',
    location: 'Demo Location, USA',
    embed_url: 'https://massinteract.com/cypress-college/',
    embed_type: 'iframe',
    source_platform: 'massinteract',
    business_type: 'Retail Store',
    features: ['Clean Display', 'Minimal Controls', 'Professional Layout', 'Interactive Elements']
  },

  // TourMKR Tours
  {
    id: 'tourmkr-virginia-tech',
    title: 'Virginia Tech Campus Tour',
    description: 'Comprehensive virtual tour of Virginia Tech campus facilities and academic buildings.',
    category: 'education',
    location: 'Blacksburg, Virginia, USA',
    embed_url: 'https://s3.amazonaws.com/vtvt/VTTOUR.html',
    embed_type: 'iframe',
    source_platform: 'custom',
    business_type: 'University',
    features: ['Campus Buildings', 'Academic Facilities', 'Student Life', 'Sports Complex']
  },
  {
    id: 'tourmkr-pittsburgh',
    title: 'University of Pittsburgh',
    description: 'Explore the historic University of Pittsburgh campus with its iconic Cathedral of Learning.',
    category: 'education',
    location: 'Pittsburgh, Pennsylvania, USA',
    embed_url: 'https://tourmkr.com/F1WZkO2CpT/7971185p&99.48h&99.03t',
    embed_type: 'iframe',
    source_platform: 'tourmkr',
    business_type: 'University',
    features: ['Cathedral of Learning', 'Historic Buildings', 'Academic Departments', 'Campus Grounds']
  },
  {
    id: 'tourmkr-webster',
    title: 'Webster University Vienna',
    description: 'International campus of Webster University located in beautiful Vienna, Austria.',
    category: 'education',
    location: 'Vienna, Austria',
    embed_url: 'https://tourmkr.com/F1webstervienna/10083034p&316.59h&98.03t',
    embed_type: 'iframe',
    source_platform: 'tourmkr',
    business_type: 'University',
    features: ['International Campus', 'Modern Facilities', 'Student Services', 'Cultural Integration']
  },
  {
    id: 'luxury-restaurant',
    title: 'Fine Dining Restaurant Experience',
    description: 'Elegant restaurant featuring sophisticated ambiance, premium dining areas, and exceptional culinary presentation spaces.',
    category: 'hospitality',
    location: 'Accra, Ghana',
    embed_url: 'https://tourmkr.com/restaurant-fine-dining/premium-experience',
    embed_type: 'iframe',
    source_platform: 'tourmkr',
    business_type: 'Fine Dining',
    features: ['Elegant Ambiance', 'Private Dining', 'Wine Cellar', 'Chef\'s Table']
  },
  {
    id: 'art-gallery-modern',
    title: 'Contemporary Art Gallery',
    description: 'Stunning art gallery showcasing contemporary African art, sculptures, and multimedia installations in a modern setting.',
    category: 'culture',
    location: 'Cape Town, South Africa',
    embed_url: 'https://tourmkr.com/gallery-contemporary/african-art',
    embed_type: 'iframe',
    source_platform: 'tourmkr',
    business_type: 'Art Gallery',
    features: ['Contemporary Art', 'Sculpture Garden', 'Interactive Displays', 'Artist Studios']
  },
  {
    id: 'luxury-villa-estate',
    title: 'Exclusive Villa Estate',
    description: 'Magnificent villa estate with panoramic views, infinity pool, landscaped gardens, and premium architectural design.',
    category: 'property',
    location: 'Victoria Island, Lagos',
    embed_url: 'https://tourmkr.com/villa-luxury-estate/premium-property',
    embed_type: 'iframe',
    source_platform: 'tourmkr',
    business_type: 'Luxury Real Estate',
    features: ['Infinity Pool', 'Panoramic Views', 'Smart Home', 'Private Gardens']
  },
  {
    id: 'government-facility',
    title: 'Modern Government Complex',
    description: 'State-of-the-art government facility with public service areas, administrative offices, and citizen engagement spaces.',
    category: 'government',
    location: 'Abuja, Nigeria',
    embed_url: 'https://tourmkr.com/government-complex/public-services',
    embed_type: 'iframe',
    source_platform: 'tourmkr',
    business_type: 'Government Office',
    features: ['Public Services', 'Digital Systems', 'Accessibility', 'Security Features']
  },
  // Commercial & E-commerce Tours
  {
    id: 'luxury-shopping-mall',
    title: 'Premium Shopping Center',
    description: 'Upscale shopping destination featuring international brands, dining options, and entertainment facilities in a modern architectural setting.',
    category: 'commercial',
    location: 'Victoria Island, Lagos',
    embed_url: 'https://kuula.co/share/collection/7l9kX?logo=1&info=1&fs=1&vr=0&sd=1&thumbs=1',
    embed_type: 'iframe',
    source_platform: 'kuula',
    business_type: 'Shopping Mall',
    features: ['International Brands', 'Food Court', 'Entertainment', 'Parking']
  },
  {
    id: 'electronics-showroom',
    title: 'Technology Showroom Experience',
    description: 'State-of-the-art electronics showroom displaying the latest gadgets, smart home solutions, and cutting-edge technology products.',
    category: 'commercial',
    location: 'Ikeja, Lagos',
    embed_url: 'https://kuula.co/share/collection/7l9kY?logo=1&info=1&fs=1&vr=0&sd=1&thumbs=1',
    embed_type: 'iframe',
    source_platform: 'kuula',
    business_type: 'Electronics Store',
    features: ['Latest Gadgets', 'Smart Home', 'Demo Areas', 'Tech Support']
  },
  {
    id: 'fashion-boutique',
    title: 'Designer Fashion Boutique',
    description: 'Elegant fashion boutique showcasing contemporary African designs, luxury accessories, and bespoke tailoring services.',
    category: 'commercial',
    location: 'Accra, Ghana',
    embed_url: 'https://kuula.co/share/collection/7l9kZ?logo=1&info=1&fs=1&vr=0&sd=1&thumbs=1',
    embed_type: 'iframe',
    source_platform: 'kuula',
    business_type: 'Fashion Boutique',
    features: ['African Designs', 'Luxury Accessories', 'Bespoke Tailoring', 'Personal Styling']
  },
  {
    id: 'furniture-showroom',
    title: 'Modern Furniture Gallery',
    description: 'Contemporary furniture showroom featuring premium home and office furniture, interior design consultation, and custom solutions.',
    category: 'commercial',
    location: 'Sandton, Johannesburg',
    embed_url: 'https://kuula.co/share/collection/7l9l1?logo=1&info=1&fs=1&vr=0&sd=1&thumbs=1',
    embed_type: 'iframe',
    source_platform: 'kuula',
    business_type: 'Furniture Store',
    features: ['Premium Furniture', 'Interior Design', 'Custom Solutions', 'Home Staging']
  },
  {
    id: 'automotive-showroom',
    title: 'Luxury Car Dealership',
    description: 'Premium automotive showroom featuring luxury vehicles, certified pre-owned cars, and comprehensive automotive services.',
    category: 'commercial',
    location: 'Ikoyi, Lagos',
    embed_url: 'https://kuula.co/share/collection/7l9l2?logo=1&info=1&fs=1&vr=0&sd=1&thumbs=1',
    embed_type: 'iframe',
    source_platform: 'kuula',
    business_type: 'Car Dealership',
    features: ['Luxury Vehicles', 'Certified Pre-owned', 'Service Center', 'Financing']
  },
  {
    id: 'real-estate-penthouse',
    title: 'Luxury Penthouse Apartment',
    description: 'Spectacular penthouse with panoramic city views, premium finishes, private terrace, and world-class amenities.',
    category: 'property',
    location: 'Banana Island, Lagos',
    embed_url: 'https://kuula.co/share/collection/7l9l3?logo=1&info=1&fs=1&vr=0&sd=1&thumbs=1',
    embed_type: 'iframe',
    source_platform: 'kuula',
    business_type: 'Luxury Real Estate',
    features: ['Panoramic Views', 'Premium Finishes', 'Private Terrace', 'Concierge Service']
  },
  {
    id: 'commercial-office-tower',
    title: 'Grade A Office Complex',
    description: 'Premium office tower with flexible workspace solutions, modern amenities, and strategic business location.',
    category: 'commercial',
    location: 'Central Business District, Abuja',
    embed_url: 'https://kuula.co/share/collection/7l9l4?logo=1&info=1&fs=1&vr=0&sd=1&thumbs=1',
    embed_type: 'iframe',
    source_platform: 'kuula',
    business_type: 'Commercial Office',
    features: ['Flexible Workspace', 'Modern Amenities', 'Business Center', 'Parking']
  },
  {
    id: 'retail-supermarket',
    title: 'Modern Supermarket Experience',
    description: 'Contemporary supermarket with wide aisles, fresh produce sections, international products, and convenient shopping experience.',
    category: 'commercial',
    location: 'Nairobi, Kenya',
    embed_url: 'https://kuula.co/share/collection/7l9l5?logo=1&info=1&fs=1&vr=0&sd=1&thumbs=1',
    embed_type: 'iframe',
    source_platform: 'kuula',
    business_type: 'Supermarket',
    features: ['Fresh Produce', 'International Products', 'Self-checkout', 'Pharmacy']
  },
  // International Property Tours
  {
    id: 'dubai-luxury-villa',
    title: 'Waterfront Villa Estate',
    description: 'Magnificent waterfront villa with private beach access, infinity pool, smart home technology, and breathtaking ocean views.',
    category: 'property',
    location: 'Palm Jumeirah, Dubai',
    embed_url: 'https://kuula.co/share/collection/7l9l6?logo=1&info=1&fs=1&vr=0&sd=1&thumbs=1',
    embed_type: 'iframe',
    source_platform: 'kuula',
    business_type: 'Luxury Real Estate',
    features: ['Private Beach', 'Infinity Pool', 'Smart Home', 'Ocean Views']
  },
  {
    id: 'london-apartment',
    title: 'Central London Apartment',
    description: 'Sophisticated apartment in prime London location with period features, modern amenities, and excellent transport links.',
    category: 'property',
    location: 'Mayfair, London',
    embed_url: 'https://kuula.co/share/collection/7l9l7?logo=1&info=1&fs=1&vr=0&sd=1&thumbs=1',
    embed_type: 'iframe',
    source_platform: 'kuula',
    business_type: 'Prime Real Estate',
    features: ['Period Features', 'Prime Location', 'Transport Links', 'Concierge']
  },
  {
    id: 'cape-town-vineyard',
    title: 'Wine Estate Property',
    description: 'Exclusive wine estate with vineyard views, tasting rooms, luxury accommodation, and award-winning wine production facilities.',
    category: 'property',
    location: 'Stellenbosch, Cape Town',
    embed_url: 'https://kuula.co/share/collection/7l9l8?logo=1&info=1&fs=1&vr=0&sd=1&thumbs=1',
    embed_type: 'iframe',
    source_platform: 'kuula',
    business_type: 'Wine Estate',
    features: ['Vineyard Views', 'Tasting Rooms', 'Wine Production', 'Event Venue']
  },
  {
    id: 'abuja-residential',
    title: 'Modern Family Home',
    description: 'Contemporary family residence with spacious living areas, modern kitchen, landscaped garden, and security features.',
    category: 'property',
    location: 'Maitama, Abuja',
    embed_url: 'https://kuula.co/share/collection/7l9l9?logo=1&info=1&fs=1&vr=0&sd=1&thumbs=1',
    embed_type: 'iframe',
    source_platform: 'kuula',
    business_type: 'Residential Property',
    features: ['Spacious Living', 'Modern Kitchen', 'Landscaped Garden', 'Security']
  },
  {
    id: 'tourmkr-ncaa',
    title: 'NCAA Hall of Champions',
    description: 'Interactive museum showcasing the history and achievements of NCAA sports.',
    category: 'culture',
    location: 'Indianapolis, Indiana, USA',
    embed_url: 'https://tourmkr.com/F1CF4FoiCh/9488211p&11.08h&81.61t',
    embed_type: 'iframe',
    source_platform: 'tourmkr',
    business_type: 'Museum',
    features: ['Sports History', 'Interactive Exhibits', 'Trophy Displays', 'Multimedia Presentations']
  }
];

/**
 * Get tours by category
 */
export const getToursByCategory = (category: string) => {
  return sampleTours.filter(tour => tour.category === category);
};

/**
 * Get tours by platform
 */
export const getToursByPlatform = (platform: string) => {
  return sampleTours.filter(tour => tour.source_platform === platform);
};

/**
 * Get random tours for demonstration
 */
export const getRandomTours = (count: number = 3) => {
  const shuffled = [...sampleTours].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};
