/**
 * Unified Vendor Dashboard
 * Streamlined tour-first workflow with mobile-responsive design
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import Navigation from '@/components/Navigation';
import {
  MapPin,
  Plus,
  Eye,
  Edit,
  Star,
  Package,
  ShoppingCart,
  TrendingUp,
  Download,
  Upload,
  Settings,
  BarChart3,
  MessageCircle,
  Image as ImageIcon,
  CheckCircle,
  Clock,
  DollarSign,
  Store
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

interface DashboardStats {
  tours: {
    total: number;
    published: number;
    draft: number;
    pending_approval: number;
  };
  products: {
    total: number;
    active: number;
    draft: number;
    out_of_stock: number;
  };
  orders: {
    pending: number;
    processing: number;
    completed: number;
    total_today: number;
  };
  revenue: {
    today: number;
    week: number;
    month: number;
    growth: number;
  };
}

const VendorDashboardUnified = () => {
  const { user, profile } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // Check if user is a vendor
  const { data: vendor, isLoading: vendorLoading } = useQuery({
    queryKey: ['vendor-profile', user?.email],
    queryFn: async () => {
      if (!user?.email) return null;

      const { data, error } = await supabase
        .from('vendors')
        .select('*')
        .eq('email', user.email)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // No vendor record found
        }
        throw error;
      }

      return data;
    },
    enabled: !!user?.email,
  });

  // Fetch vendor tours
  const { data: tours = [] } = useQuery({
    queryKey: ['vendor-tours', user?.id],
    queryFn: async () => {
      if (!user?.id) return [];

      const { data, error } = await supabase
        .from('tours')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    enabled: !!user?.id,
  });

  // Calculate stats from real data
  const stats = {
    tours: {
      total: tours.length,
      published: tours.filter(t => t.status === 'published').length,
      draft: tours.filter(t => t.status === 'draft').length,
      pending_approval: tours.filter(t => t.status === 'pending').length
    },
    products: {
      total: 0, // Will be implemented when products are linked
      active: 0,
      draft: 0,
      out_of_stock: 0
    },
    orders: {
      pending: 0, // Will be implemented when orders are linked
      processing: 0,
      completed: 0,
      total_today: 0
    },
    revenue: {
      today: 0,
      week: 0,
      month: 0,
      growth: 0
    }
  };

  if (vendorLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!vendor) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto pt-32 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="p-8 text-center">
              <Store className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <h2 className="text-xl font-semibold mb-2">Not a Vendor</h2>
              <p className="text-muted-foreground mb-4">
                You don't have vendor access yet. Contact an administrator to become a vendor.
              </p>
              <Button onClick={() => window.location.href = '/dashboard'}>
                Go to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="pt-24 pb-12">
        <div className="container mx-auto px-4">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8"
          >
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-foreground">
                Vendor Dashboard
              </h1>
              <p className="text-muted-foreground">
                Welcome back, {profile?.full_name || user?.email}
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export Data
              </Button>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create Tour
              </Button>
            </div>
          </motion.div>

          {/* Stats Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Tours</p>
                    <p className="text-2xl font-bold">{stats.tours.total}</p>
                    <p className="text-xs text-muted-foreground">
                      {stats.tours.published} published
                    </p>
                  </div>
                  <MapPin className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Products</p>
                    <p className="text-2xl font-bold">{stats.products.total}</p>
                    <p className="text-xs text-muted-foreground">
                      {stats.products.active} active
                    </p>
                  </div>
                  <Package className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Orders</p>
                    <p className="text-2xl font-bold">{stats.orders.pending + stats.orders.processing}</p>
                    <p className="text-xs text-muted-foreground">
                      {stats.orders.total_today} today
                    </p>
                  </div>
                  <ShoppingCart className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Revenue</p>
                    <p className="text-2xl font-bold">₦{stats.revenue.month.toLocaleString()}</p>
                    <p className="text-xs text-green-600">
                      +{stats.revenue.growth}% this month
                    </p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Main Dashboard Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  <span className="hidden sm:inline">Overview</span>
                </TabsTrigger>
                <TabsTrigger value="tours" className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span className="hidden sm:inline">My Tours</span>
                </TabsTrigger>
                <TabsTrigger value="products" className="flex items-center gap-2">
                  <Package className="w-4 h-4" />
                  <span className="hidden sm:inline">Products</span>
                </TabsTrigger>
                <TabsTrigger value="orders" className="flex items-center gap-2">
                  <ShoppingCart className="w-4 h-4" />
                  <span className="hidden sm:inline">Orders</span>
                </TabsTrigger>
              </TabsList>

              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-6">
                {/* Quick Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                      <Button variant="outline" className="h-20 flex-col">
                        <Plus className="w-6 h-6 mb-2" />
                        Create Tour
                      </Button>
                      <Button variant="outline" className="h-20 flex-col">
                        <Package className="w-6 h-6 mb-2" />
                        Add Product
                      </Button>
                      <Button variant="outline" className="h-20 flex-col">
                        <BarChart3 className="w-6 h-6 mb-2" />
                        View Analytics
                      </Button>
                      <Button variant="outline" className="h-20 flex-col">
                        <MessageCircle className="w-6 h-6 mb-2" />
                        Messages
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Activity */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Tours</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {[
                          { name: 'Lagos Hotel Tour', status: 'published', views: 245 },
                          { name: 'Restaurant 360°', status: 'draft', views: 0 },
                          { name: 'Office Space Tour', status: 'pending_approval', views: 12 }
                        ].map((tour, index) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex-1">
                              <h4 className="font-medium">{tour.name}</h4>
                              <p className="text-sm text-muted-foreground">{tour.views} views</p>
                            </div>
                            <Badge variant={
                              tour.status === 'published' ? 'default' : 
                              tour.status === 'draft' ? 'secondary' : 'outline'
                            }>
                              {tour.status.replace('_', ' ')}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Orders</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {[
                          { id: '#ORD-001', customer: 'John Doe', amount: 25000, status: 'completed' },
                          { id: '#ORD-002', customer: 'Jane Smith', amount: 15000, status: 'processing' },
                          { id: '#ORD-003', customer: 'Mike Johnson', amount: 35000, status: 'pending' }
                        ].map((order, index) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex-1">
                              <h4 className="font-medium">{order.id}</h4>
                              <p className="text-sm text-muted-foreground">
                                {order.customer} • ₦{order.amount.toLocaleString()}
                              </p>
                            </div>
                            <Badge variant={
                              order.status === 'completed' ? 'default' : 
                              order.status === 'processing' ? 'secondary' : 'outline'
                            }>
                              {order.status}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Tours Tab */}
              <TabsContent value="tours" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>My Tours</span>
                      <Button>
                        <Plus className="w-4 h-4 mr-2" />
                        Create Tour
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Tour management coming soon</h3>
                      <p className="text-muted-foreground">
                        Create and manage your virtual tours with integrated product hotspots
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Products Tab */}
              <TabsContent value="products" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>My Products</span>
                      <Button>
                        <Plus className="w-4 h-4 mr-2" />
                        Add Product
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Product management coming soon</h3>
                      <p className="text-muted-foreground">
                        Manage your products and link them to tour hotspots
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Orders Tab */}
              <TabsContent value="orders" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Order Management</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <ShoppingCart className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Order management coming soon</h3>
                      <p className="text-muted-foreground">
                        Track and manage orders from your tour-embedded products
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default VendorDashboardUnified;
