/**
 * Unified Vendor Dashboard
 * Streamlined tour-first workflow with mobile-responsive design
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';
import Navigation from '@/components/Navigation';
import {
  MapPin,
  Plus,
  Eye,
  Edit,
  Star,
  Package,
  ShoppingCart,
  TrendingUp,
  Download,
  Upload,
  Settings,
  BarChart3,
  MessageCircle,
  Image as ImageIcon,
  CheckCircle,
  Clock,
  DollarSign,
  Store,
  Target
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

interface DashboardStats {
  tours: {
    total: number;
    published: number;
    draft: number;
    pending_approval: number;
  };
  products: {
    total: number;
    active: number;
    draft: number;
    out_of_stock: number;
  };
  orders: {
    pending: number;
    processing: number;
    completed: number;
    total_today: number;
  };
  revenue: {
    today: number;
    week: number;
    month: number;
    growth: number;
  };
}

const VendorDashboardUnified = () => {
  const { user, profile } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');

  // Check if user is admin
  const isAdmin = profile?.role === 'admin';

  // Fetch vendor data (all vendors for admin, specific vendor for regular users)
  const { data: vendor, isLoading: vendorLoading } = useQuery({
    queryKey: ['vendor-profile', user?.email, isAdmin],
    queryFn: async () => {
      if (!user?.email) return null;

      if (isAdmin) {
        // Admin gets a summary vendor object to access the interface
        return {
          id: 'admin-view',
          name: 'Admin View',
          email: user.email,
          status: 'approved',
          commission_rate: 0.15,
          created_at: new Date().toISOString()
        };
      }

      const { data, error } = await supabase
        .from('vendors')
        .select('*')
        .eq('email', user.email)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // No vendor record found
        }
        throw error;
      }

      return data;
    },
    enabled: !!user?.email,
  });

  // Fetch tours (all for admin, user's own for regular vendors)
  const { data: tours = [] } = useQuery({
    queryKey: ['vendor-tours', user?.id, isAdmin],
    queryFn: async () => {
      if (!user?.id) return [];

      let query = supabase
        .from('tours')
        .select('*');

      // Admin sees all tours, regular vendors see only their own
      if (!isAdmin) {
        query = query.eq('user_id', user.id);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    enabled: !!user?.id,
  });

  // Calculate stats from real data
  const stats = {
    tours: {
      total: tours.length,
      published: tours.filter(t => t.status === 'published').length,
      draft: tours.filter(t => t.status === 'draft').length,
      pending_approval: tours.filter(t => t.status === 'pending').length
    },
    products: {
      total: 0, // Will be implemented when products are linked
      active: 0,
      draft: 0,
      out_of_stock: 0
    },
    orders: {
      pending: 0, // Will be implemented when orders are linked
      processing: 0,
      completed: 0,
      total_today: 0
    },
    revenue: {
      today: 0,
      week: 0,
      month: 0,
      growth: 0
    }
  };

  if (vendorLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!vendor) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto pt-32 py-8">
          <Card className="max-w-md mx-auto">
            <CardContent className="p-8 text-center">
              <Store className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
              <h2 className="text-xl font-semibold mb-2">Not a Vendor</h2>
              <p className="text-muted-foreground mb-4">
                You don't have vendor access yet. Contact an administrator to become a vendor.
              </p>
              <Button onClick={() => window.location.href = '/dashboard'}>
                Go to Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="pt-24 pb-12">
        <div className="container mx-auto px-4">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8"
          >
            <div>
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl md:text-3xl font-bold text-foreground">
                  Vendor Dashboard
                </h1>
                {isAdmin && (
                  <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200">
                    Admin View - All Vendor Data
                  </Badge>
                )}
              </div>
              <p className="text-muted-foreground">
                {isAdmin ? 'Admin preview of vendor interface' : `Welcome back, ${profile?.full_name || user?.email}`}
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Export Data
              </Button>
              <Button>
                <Plus className="w-4 h-4 mr-2" />
                Create Tour
              </Button>
            </div>
          </motion.div>

          {/* Stats Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Total Tours</p>
                    <p className="text-2xl font-bold">{stats.tours.total}</p>
                    <p className="text-xs text-muted-foreground">
                      {stats.tours.published} published
                    </p>
                  </div>
                  <MapPin className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Products</p>
                    <p className="text-2xl font-bold">{stats.products.total}</p>
                    <p className="text-xs text-muted-foreground">
                      {stats.products.active} active
                    </p>
                  </div>
                  <Package className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Orders</p>
                    <p className="text-2xl font-bold">{stats.orders.pending + stats.orders.processing}</p>
                    <p className="text-xs text-muted-foreground">
                      {stats.orders.total_today} today
                    </p>
                  </div>
                  <ShoppingCart className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">Revenue</p>
                    <p className="text-2xl font-bold">₦{stats.revenue.month.toLocaleString()}</p>
                    <p className="text-xs text-green-600">
                      +{stats.revenue.growth}% this month
                    </p>
                  </div>
                  <TrendingUp className="w-8 h-8 text-orange-600" />
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Main Dashboard Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
              <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
                <TabsTrigger value="overview" className="flex items-center gap-2">
                  <BarChart3 className="w-4 h-4" />
                  <span className="hidden sm:inline">Overview</span>
                </TabsTrigger>
                <TabsTrigger value="create-tour" className="flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  <span className="hidden sm:inline">Create Tour</span>
                </TabsTrigger>
                <TabsTrigger value="tours" className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span className="hidden sm:inline">My Tours</span>
                </TabsTrigger>
                <TabsTrigger value="store" className="flex items-center gap-2">
                  <Store className="w-4 h-4" />
                  <span className="hidden sm:inline">Store</span>
                </TabsTrigger>
                <TabsTrigger value="products" className="flex items-center gap-2">
                  <Package className="w-4 h-4" />
                  <span className="hidden sm:inline">Products</span>
                </TabsTrigger>
                <TabsTrigger value="orders" className="flex items-center gap-2">
                  <ShoppingCart className="w-4 h-4" />
                  <span className="hidden sm:inline">Orders</span>
                </TabsTrigger>
              </TabsList>

              {/* Overview Tab */}
              <TabsContent value="overview" className="space-y-6">
                {/* Quick Actions */}
                <Card>
                  <CardHeader>
                    <CardTitle>Quick Actions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                      <Button
                        variant="outline"
                        className="h-20 flex-col"
                        onClick={() => setActiveTab('create-tour')}
                      >
                        <Plus className="w-6 h-6 mb-2" />
                        Create Tour
                      </Button>
                      <Button
                        variant="outline"
                        className="h-20 flex-col"
                        onClick={() => setActiveTab('store')}
                      >
                        <Store className="w-6 h-6 mb-2" />
                        Manage Store
                      </Button>
                      <Button
                        variant="outline"
                        className="h-20 flex-col"
                        onClick={() => setActiveTab('products')}
                      >
                        <Package className="w-6 h-6 mb-2" />
                        Products
                      </Button>
                      <Button
                        variant="outline"
                        className="h-20 flex-col"
                        onClick={() => setActiveTab('orders')}
                      >
                        <ShoppingCart className="w-6 h-6 mb-2" />
                        Orders
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Recent Activity */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Tours</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {[
                          { name: 'Lagos Hotel Tour', status: 'published', views: 245 },
                          { name: 'Restaurant 360°', status: 'draft', views: 0 },
                          { name: 'Office Space Tour', status: 'pending_approval', views: 12 }
                        ].map((tour, index) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex-1">
                              <h4 className="font-medium">{tour.name}</h4>
                              <p className="text-sm text-muted-foreground">{tour.views} views</p>
                            </div>
                            <Badge variant={
                              tour.status === 'published' ? 'default' : 
                              tour.status === 'draft' ? 'secondary' : 'outline'
                            }>
                              {tour.status.replace('_', ' ')}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Recent Orders</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {[
                          { id: '#ORD-001', customer: 'John Doe', amount: 25000, status: 'completed' },
                          { id: '#ORD-002', customer: 'Jane Smith', amount: 15000, status: 'processing' },
                          { id: '#ORD-003', customer: 'Mike Johnson', amount: 35000, status: 'pending' }
                        ].map((order, index) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex-1">
                              <h4 className="font-medium">{order.id}</h4>
                              <p className="text-sm text-muted-foreground">
                                {order.customer} • ₦{order.amount.toLocaleString()}
                              </p>
                            </div>
                            <Badge variant={
                              order.status === 'completed' ? 'default' : 
                              order.status === 'processing' ? 'secondary' : 'outline'
                            }>
                              {order.status}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>

              {/* Tours Tab */}
              <TabsContent value="tours" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>My Tours</span>
                      <Button>
                        <Plus className="w-4 h-4 mr-2" />
                        Create Tour
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Tour management coming soon</h3>
                      <p className="text-muted-foreground">
                        Create and manage your virtual tours with integrated product hotspots
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Products Tab */}
              <TabsContent value="products" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>My Products</span>
                      <Button>
                        <Plus className="w-4 h-4 mr-2" />
                        Add Product
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Product management coming soon</h3>
                      <p className="text-muted-foreground">
                        Manage your products and link them to tour hotspots
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Create Tour Tab */}
              <TabsContent value="create-tour" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Create New Tour</CardTitle>
                    <CardDescription>
                      Upload 360° images and create immersive virtual tours
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* Tour Creation Form */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium">Tour Title</label>
                          <Input placeholder="Enter tour title..." className="mt-1" />
                        </div>
                        <div>
                          <label className="text-sm font-medium">Description</label>
                          <Textarea
                            className="mt-1 h-24"
                            placeholder="Describe your tour..."
                          />
                        </div>
                        <div>
                          <label className="text-sm font-medium">Category</label>
                          <Select>
                            <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="real-estate">Real Estate</SelectItem>
                              <SelectItem value="hospitality">Hospitality</SelectItem>
                              <SelectItem value="retail">Retail</SelectItem>
                              <SelectItem value="education">Education</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="space-y-4">
                        <div>
                          <label className="text-sm font-medium">Upload 360° Images</label>
                          <div className="mt-1 border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                            <ImageIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                            <p className="text-sm text-gray-600 mb-2">
                              Drag and drop your 360° images here
                            </p>
                            <Button variant="outline" size="sm">
                              <Upload className="w-4 h-4 mr-2" />
                              Choose Files
                            </Button>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <Button className="w-full">
                            <Plus className="w-4 h-4 mr-2" />
                            Create Tour
                          </Button>
                          <Button variant="outline" className="w-full">
                            <Eye className="w-4 h-4 mr-2" />
                            Preview
                          </Button>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Store Management Tab */}
              <TabsContent value="store" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Store Setup */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Store Setup</CardTitle>
                      <CardDescription>
                        Configure your store and connect to WooCommerce
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <label className="text-sm font-medium">Store Name</label>
                        <Input placeholder="Your store name..." className="mt-1" />
                      </div>
                      <div>
                        <label className="text-sm font-medium">WooCommerce URL</label>
                        <Input placeholder="https://yourstore.com" className="mt-1" />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium">Consumer Key</label>
                          <Input placeholder="ck_..." className="mt-1" />
                        </div>
                        <div>
                          <label className="text-sm font-medium">Consumer Secret</label>
                          <Input type="password" placeholder="cs_..." className="mt-1" />
                        </div>
                      </div>
                      <Button className="w-full">
                        <Settings className="w-4 h-4 mr-2" />
                        Connect Store
                      </Button>
                    </CardContent>
                  </Card>

                  {/* Quick Product Upload */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Quick Product Upload</CardTitle>
                      <CardDescription>
                        Add products to your inventory
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <label className="text-sm font-medium">Product Name</label>
                        <Input placeholder="Product name..." className="mt-1" />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm font-medium">Price (₦)</label>
                          <Input placeholder="0.00" className="mt-1" />
                        </div>
                        <div>
                          <label className="text-sm font-medium">Stock</label>
                          <Input placeholder="0" className="mt-1" />
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Product Image</label>
                        <div className="mt-1 border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                          <ImageIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-xs text-gray-600">Upload product image</p>
                        </div>
                      </div>
                      <Button className="w-full">
                        <Package className="w-4 h-4 mr-2" />
                        Add Product
                      </Button>
                    </CardContent>
                  </Card>
                </div>

                {/* Hotspot Assignment */}
                <Card>
                  <CardHeader>
                    <CardTitle>Hotspot-Product Assignment</CardTitle>
                    <CardDescription>
                      Assign products to tour hotspots for interactive shopping
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                      <div>
                        <label className="text-sm font-medium">Select Tour</label>
                        <Select>
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="Choose a tour..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="lagos-hotel">Lagos Hotel Tour</SelectItem>
                            <SelectItem value="restaurant">Restaurant 360°</SelectItem>
                            <SelectItem value="office">Office Space Tour</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <label className="text-sm font-medium">Select Product</label>
                        <Select>
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="Choose a product..." />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="room-package">Premium Room Package</SelectItem>
                            <SelectItem value="dining">Dining Experience</SelectItem>
                            <SelectItem value="furniture">Office Furniture Set</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div className="flex items-end">
                        <Button className="w-full">
                          <Target className="w-4 h-4 mr-2" />
                          Assign Hotspot
                        </Button>
                      </div>
                    </div>

                    {/* Existing Assignments */}
                    <div className="mt-6">
                      <h4 className="font-medium mb-3">Current Assignments</h4>
                      <div className="space-y-2">
                        {[
                          { tour: 'Lagos Hotel Tour', product: 'Premium Room Package', hotspot: 'Bedroom View' },
                          { tour: 'Restaurant 360°', product: 'Dining Experience', hotspot: 'Main Dining Area' }
                        ].map((assignment, index) => (
                          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                            <div className="flex-1">
                              <p className="font-medium">{assignment.tour}</p>
                              <p className="text-sm text-muted-foreground">
                                {assignment.product} → {assignment.hotspot}
                              </p>
                            </div>
                            <Button variant="ghost" size="sm">
                              <Edit className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Orders Tab */}
              <TabsContent value="orders" className="space-y-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Order Management</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center py-8">
                      <ShoppingCart className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Order management coming soon</h3>
                      <p className="text-muted-foreground">
                        Track and manage orders from your tour-embedded products
                      </p>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default VendorDashboardUnified;
