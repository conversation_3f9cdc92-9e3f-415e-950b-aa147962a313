import { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Play, ChevronDown, MapPin, Eye, Star, Users, Building, Globe, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import EnhancedTourCard from '@/components/EnhancedTourCard';
import Navigation from '@/components/Navigation';
import EnhancedHeroSection from '@/components/sections/EnhancedHeroSection';
import Footer from '@/components/Footer';
import { getCleanEmbedUrl, getCleanIframeProps } from '@/utils/cleanTourEmbedding';
import CleanTourDisplay from '@/components/CleanTourDisplay';
import useEmblaCarousel from 'embla-carousel-react';

const MapboxStyleWelcome = () => {
  const [activeTab, setActiveTab] = useState('property');

  // Embla Carousel setup - Manual navigation only
  const [emblaRef, emblaApi] = useEmblaCarousel(
    {
      loop: true,
      align: 'center',
      skipSnaps: false,
      dragFree: false,
      containScroll: 'trimSnaps'
    }
    // No autoplay - manual navigation only
  );

  const scrollPrev = useCallback(() => {
    if (emblaApi) emblaApi.scrollPrev();
  }, [emblaApi]);

  const scrollNext = useCallback(() => {
    if (emblaApi) emblaApi.scrollNext();
  }, [emblaApi]);

  const [selectedIndex, setSelectedIndex] = useState(0);

  const onSelect = useCallback(() => {
    if (!emblaApi) return;
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, [emblaApi]);

  useEffect(() => {
    if (!emblaApi) return;
    onSelect();
    emblaApi.on('select', onSelect);
  }, [emblaApi, onSelect]);

  // Fetch featured tours for carousel
  const { data: tours = [] } = useQuery({
    queryKey: ['featured-tours'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tours')
        .select(`
          *,
          profiles (
            full_name,
            email
          )
        `)
        .eq('status', 'published')
        .eq('featured', true)
        .order('created_at', { ascending: false })
        .limit(8);

      if (error) throw error;
      return data || [];
    },
  });

  // Fetch demo tours for carousel section
  const { data: demoTours = [] } = useQuery({
    queryKey: ['demo-tours-carousel'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tours')
        .select(`
          *,
          profiles (
            full_name,
            email
          )
        `)
        .eq('status', 'published')
        .order('views', { ascending: false })
        .limit(8);

      if (error) throw error;
      return data || [];
    },
  });

  // Filter out Alfara and combine tours for carousel
  const filteredFeaturedTours = (tours || []).filter(tour =>
    !tour.title?.toLowerCase().includes('alfara')
  );

  const filteredDemoTours = (demoTours || []).filter(tour =>
    !tour.title?.toLowerCase().includes('alfara') &&
    !(filteredFeaturedTours || []).some(featured => featured.id === tour.id)
  );

  // Combine for carousel - prioritize featured tours
  const carouselTours = [
    ...filteredFeaturedTours.slice(0, 3),
    ...filteredDemoTours.slice(0, 3)
  ].slice(0, 6);

  // Get available categories from actual tours
  const allTours = [...(tours || []), ...(demoTours || [])];
  const availableCategories = Array.from(new Set(allTours.map(tour => tour.category))).filter(Boolean);

  // Define all possible categories with their display info
  const allCategoryOptions = [
    { id: 'property', label: 'Real Estate', icon: '🏠' },
    { id: 'hospitality', label: 'Hospitality', icon: '🏨' },
    { id: 'commercial', label: 'Commercial', icon: '🏢' },
    { id: 'education', label: 'Education', icon: '🎓' },
    { id: 'healthcare', label: 'Healthcare', icon: '🏥' },
    { id: 'tourism', label: 'Tourism', icon: '🗺️' },
    { id: 'culture', label: 'Culture', icon: '🎭' },
    { id: 'government', label: 'Government', icon: '🏛️' }
  ];

  // Filter to only show categories that have tours
  const displayCategories = allCategoryOptions.filter(category =>
    availableCategories.includes(category.id as any)
  );

  // Ensure activeTab is valid, fallback to first available category
  const validActiveTab = displayCategories.find(cat => cat.id === activeTab)
    ? activeTab
    : displayCategories[0]?.id || 'property';

  // Update activeTab if it's invalid
  useEffect(() => {
    if (validActiveTab !== activeTab && displayCategories.length > 0) {
      setActiveTab(validActiveTab);
    }
  }, [validActiveTab, activeTab, displayCategories]);

  // Manual navigation only - no autoplay

  // Fetch tours by category for customer stories
  const { data: categoryTours = {} } = useQuery({
    queryKey: ['category-tours'],
    queryFn: async () => {
      const categories = ['property', 'hospitality', 'commercial', 'education', 'healthcare'];
      const categoryData: Record<string, any> = {};

      for (const category of categories) {
        const { data, error } = await supabase
          .from('tours')
          .select('*')
          .eq('category', category)
          .eq('status', 'published')
          .order('views', { ascending: false })
          .limit(1);

        if (!error && data?.[0]) {
          categoryData[category] = data[0];
        }
      }

      return categoryData;
    },
  });

  const customerStories = {
    'property': {
      title: "Luxury Properties Showcase with Immersive 360° Tours",
      description: "Premium real estate developers across Nigeria use VRT to showcase properties with stunning virtual tours that drive sales.",
      image: categoryTours.property?.thumbnail_url || "https://images.unsplash.com/photo-**********-ce09059eeffa?w=800&h=600&fit=crop",
      logo: "/lovable-uploads/vrt-logo-all.png",
      link: "/showcase?category=property"
    },
    'hospitality': {
      title: "Hotels Increase Bookings with Virtual Property Tours",
      description: "Leading hotels and resorts use VRT to provide guests with immersive previews that boost confidence and reduce cancellations.",
      image: categoryTours.hospitality?.thumbnail_url || "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=800&h=600&fit=crop",
      logo: "/lovable-uploads/vrt-logo-all.png",
      link: "/showcase?category=hospitality"
    },
    'commercial': {
      title: "Retail Stores Drive Sales with Interactive Shopping Experiences",
      description: "Fashion retailers and shopping centers use VRT to create virtual storefronts with integrated e-commerce capabilities.",
      image: categoryTours.commercial?.thumbnail_url || "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop",
      logo: "/lovable-uploads/vrt-logo-all.png",
      link: "/showcase?category=commercial"
    },
    'education': {
      title: "Universities Attract Students with Virtual Campus Tours",
      description: "Educational institutions use VRT to provide prospective students with comprehensive virtual campus experiences.",
      image: categoryTours.education?.thumbnail_url || "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=800&h=600&fit=crop",
      logo: "/lovable-uploads/vrt-logo-all.png",
      link: "/showcase?category=education"
    },
    'healthcare': {
      title: "Medical Facilities Improve Patient Experience with Virtual Tours",
      description: "Hospitals and clinics use VRT to familiarize patients with facilities, reducing anxiety and improving satisfaction.",
      image: categoryTours.healthcare?.thumbnail_url || "https://images.unsplash.com/photo-1519494026892-80bbd2d6fd0d?w=800&h=600&fit=crop",
      logo: "/lovable-uploads/vrt-logo-all.png",
      link: "/showcase?category=healthcare"
    }
  };

  const trustedLogos = [
    { name: "Transcorp Hilton", logo: "https://images.unsplash.com/photo-1566073771259-6a8506099945?w=120&h=40&fit=crop&q=80" },
    { name: "Landmark Group", logo: "https://images.unsplash.com/photo-**********-b33ff0c44a43?w=120&h=40&fit=crop&q=80" },
    { name: "Real Estate Partners", logo: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=120&h=40&fit=crop&q=80" },
    { name: "Hospitality Leaders", logo: "https://images.unsplash.com/photo-**********-ff40c63fe5fa?w=120&h=40&fit=crop&q=80" },
    { name: "Tourism Board", logo: "https://images.unsplash.com/photo-1541888946425-d81bb19240f5?w=120&h=40&fit=crop&q=80" },
    { name: "Education Sector", logo: "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?w=120&h=40&fit=crop&q=80" }
  ];

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section - Use Our Existing Hero */}
      <EnhancedHeroSection />

      {/* Exact Mapbox Carousel Section */}
      <section className="relative py-20 bg-gray-900 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600/20 to-purple-600/20" />
        </div>

        <div className="relative z-10 max-w-[1600px] mx-auto px-8">
          {/* Carousel Container */}
          <div className="relative">
            {/* Navigation Arrows - Mobile Responsive */}
            <button
              type="button"
              aria-label="Previous slide"
              onClick={scrollPrev}
              className="absolute left-0 md:left-0 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 md:w-14 md:h-14 bg-black/20 backdrop-blur-sm border border-white/10 rounded-full hover:bg-black/40 transition-all duration-300 flex items-center justify-center text-white/80 hover:text-white touch-target"
            >
              <ChevronLeft className="w-5 h-5 md:w-7 md:h-7" />
            </button>
            <button
              type="button"
              aria-label="Next slide"
              onClick={scrollNext}
              className="absolute right-0 md:right-0 top-1/2 transform -translate-y-1/2 z-20 w-12 h-12 md:w-14 md:h-14 bg-black/20 backdrop-blur-sm border border-white/10 rounded-full hover:bg-black/40 transition-all duration-300 flex items-center justify-center text-white/80 hover:text-white touch-target"
            >
              <ChevronRight className="w-5 h-5 md:w-7 md:h-7" />
            </button>

            {/* Embla Carousel - Improved Mobile Responsive Layout */}
            <div className="overflow-x-hidden mx-0 sm:mx-4 md:mx-16" ref={emblaRef}>
              <div className="flex items-center gap-4">
                {carouselTours.map((tour, index) => (
                  <div
                    key={tour.id}
                    className={cn(
                      "min-w-0 transition-all duration-700 transform relative",
                      selectedIndex === index
                        ? "flex-[0_0_100%] sm:flex-[0_0_85%] md:flex-[0_0_70%] z-20 mx-0 sm:mx-2 md:mx-4"
                        : "flex-[0_0_80%] sm:flex-[0_0_65%] md:flex-[0_0_50%] z-10 mx-0 sm:-mx-4 md:-mx-8"
                    )}
                  >
                    <div
                      className={cn(
                        "relative rounded-xl overflow-hidden transition-all duration-700 transform cursor-pointer w-full aspect-video bg-gray-800",
                        selectedIndex === index
                          ? "scale-100 opacity-100"
                          : "scale-95 opacity-50 hover:opacity-70"
                      )}
                      onClick={() => emblaApi?.scrollTo(index)}
                    >
                      {/* Clean Tour Display - Direct iframe embed */}
                      {tour.embed_url && !tour.embed_url.includes('metaport') ? (
                        <iframe
                          src={getCleanEmbedUrl(tour.embed_url)}
                          className="w-full h-full border-0 clean-tour-iframe rounded-xl"
                          title={tour.title}
                          {...getCleanIframeProps()}
                        />
                      ) : (
                        <img
                          src={tour.thumbnail_url || "https://images.unsplash.com/photo-**********-ce09059eeffa?w=1200&h=675&fit=crop"}
                          alt={tour.title}
                          className="w-full h-full object-cover rounded-xl"
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>


          </div>
        </div>

        {/* Bottom Fade */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-900 to-transparent" />
      </section>

      {/* Trusted By Section */}
      <section className="py-12 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <p className="text-sm font-medium text-gray-500 uppercase tracking-wide">TRUSTED BY THE INDUSTRY LEADERS</p>
          </div>
          <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
            {trustedLogos.map((company, index) => (
              <img
                key={index}
                src={company.logo}
                alt={company.name}
                className="h-8 w-auto grayscale hover:grayscale-0 transition-all duration-300"
              />
            ))}
          </div>
          <div className="text-center mt-8">
            <Link to="/showcase" className="text-blue-600 hover:text-blue-700 font-medium">
              View customer stories →
            </Link>
          </div>
        </div>
      </section>

      {/* Product Sections - Exact Mapbox Style */}

      {/* Virtual Tours Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Bring locations to life with beautiful tours
              </h2>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Use VRT APIs and SDKs, ready-made tour styles, and live updating data to build customizable virtual tours for web, mobile, and AR.
              </p>
              <Link to="/services" className="text-blue-600 hover:text-blue-700 font-semibold text-lg">
                Virtual Real Tour →
              </Link>
            </div>
            <div className="relative">
              <div className="aspect-video bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg overflow-hidden shadow-xl relative">
                {/* Professional Demo Video Background */}
                <img
                  src="https://images.unsplash.com/photo-**********-b33ff0c44a43?w=1200&h=675&fit=crop&q=80"
                  alt="Virtual Tour Demo"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
                  <Button size="lg" variant="ghost" className="text-white hover:bg-white/20 backdrop-blur-sm">
                    <Play className="w-8 h-8 mr-3" />
                    Watch Demo
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* E-commerce Integration Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="order-2 lg:order-1">
              <div className="aspect-video bg-white rounded-lg shadow-xl overflow-hidden">
                <img
                  src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=1200&h=675&fit=crop&q=80"
                  alt="E-commerce Integration - Virtual Shopping Experience"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
            <div className="order-1 lg:order-2">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Optimize shopping with integrated e-commerce
              </h2>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Advanced shopping engines, accurate product placement, and intuitive WhatsApp checkout for mobile and web.
              </p>
              <Link to="/services" className="text-blue-600 hover:text-blue-700 font-semibold text-lg">
                VRT E-commerce →
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Search & Discovery Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Transform location features with search
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              VRT powers location search for precise addresses, place names, and points of interest via easy-to-use APIs and SDKs.
            </p>
            <div className="mt-8">
              <Link to="/services" className="text-blue-600 hover:text-blue-700 font-semibold text-lg">
                VRT Search →
              </Link>
            </div>
          </div>
          <div className="relative max-w-4xl mx-auto">
            <div className="aspect-video bg-gradient-to-br from-green-500 to-blue-600 rounded-lg overflow-hidden shadow-xl">
              <div className="absolute inset-0 flex items-center justify-center">
                <Button size="lg" variant="ghost" className="text-white hover:bg-white/20">
                  <MapPin className="w-8 h-8 mr-3" />
                  Explore Search
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Analytics & Data Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Generate insights with comprehensive location data
              </h2>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Build with global datasets for Analytics, Traffic, and User Behavior, informed by thousands of monthly active users across Nigeria.
              </p>
              <Link to="/dashboard" className="text-blue-600 hover:text-blue-700 font-semibold text-lg">
                VRT Analytics →
              </Link>
            </div>
            <div className="relative">
              <div className="aspect-video bg-gradient-to-br from-purple-600 to-pink-600 rounded-lg overflow-hidden shadow-xl">
                <div className="absolute inset-0 flex items-center justify-center">
                  <Button size="lg" variant="ghost" className="text-white hover:bg-white/20">
                    <Globe className="w-8 h-8 mr-3" />
                    View Analytics
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Stories Section - Exact Mapbox Style */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Customer stories
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              The world's leading businesses, from startups to global enterprises, build with Virtual Real Tour
            </p>
          </div>

          {/* Tab Navigation */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {Object.keys(customerStories).map((key) => (
              <button
                key={key}
                type="button"
                onClick={() => setActiveTab(key)}
                className={cn(
                  "px-6 py-3 rounded-lg font-medium transition-all duration-300",
                  activeTab === key
                    ? "bg-blue-600 text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                )}
              >
                {key === 'property' ? 'Real Estate' :
                 key === 'commercial' ? 'Retail' :
                 key.charAt(0).toUpperCase() + key.slice(1)}
              </button>
            ))}
          </div>

          {/* Active Story */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div className="relative">
              <img
                src={customerStories[activeTab as keyof typeof customerStories].image}
                alt="Customer Story"
                className="w-full aspect-video object-cover rounded-lg shadow-xl"
              />
              <div className="absolute bottom-4 left-4">
                <img
                  src={customerStories[activeTab as keyof typeof customerStories].logo}
                  alt="Customer Logo"
                  className="h-8 w-auto"
                />
              </div>
            </div>
            <div>
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                {customerStories[activeTab as keyof typeof customerStories].title}
              </h3>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                {customerStories[activeTab as keyof typeof customerStories].description}
              </p>
              <Link
                to={customerStories[activeTab as keyof typeof customerStories].link}
                className="text-blue-600 hover:text-blue-700 font-semibold text-lg inline-flex items-center"
              >
                Discover VRT for {activeTab === 'property' ? 'Real Estate' :
                                  activeTab === 'commercial' ? 'Retail' :
                                  activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} →
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Tours that do more - Mapbox Style Section */}
      <section className="py-20 bg-gradient-to-b from-white to-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Tours that do more
            </h2>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
              The virtual tour platform of choice for businesses, developers, and innovators
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3" asChild>
                <Link to="/showcase">Get started for free</Link>
              </Button>
              <Button size="lg" variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-3" asChild>
                <Link to="/contact">Contact sales</Link>
              </Button>
            </div>
          </div>

          {/* Interactive Carousel with Transparent Transitions - Only show if tours exist */}
          {displayCategories.length > 0 ? (
            <div className="relative">
              {/* Navigation Tabs - Only show categories with tours */}
              <div className="flex flex-wrap justify-center gap-2 mb-12">
                {displayCategories.map((tab) => (
                <button
                  type="button"
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-300 ${
                    validActiveTab === tab.id
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'bg-white text-gray-600 hover:bg-gray-50 border border-gray-200'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Content Area with Transparent Transitions */}
            <div className="relative min-h-[600px] bg-white rounded-2xl shadow-xl overflow-hidden">
              {/* Background Tours with Transparent Overlay */}
              <div className="absolute inset-0">
                {tours.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-8 opacity-20">
                    {tours.slice(0, 8).map((tour, index) => (
                      <motion.div
                        key={tour.id}
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 0.3, scale: 1 }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className="aspect-video bg-gray-200 rounded-lg overflow-hidden"
                      >
                        <CleanTourDisplay
                          tour={tour}
                          className="opacity-30"
                          containerClassName="h-full"
                          rounded={true}
                        />
                      </motion.div>
                    ))}
                  </div>
                )}
              </div>

              {/* Foreground Content - Opaque Card */}
              <div className="relative z-10 p-8 md:p-12 lg:p-16 bg-white bg-opacity-100 rounded-2xl shadow-xl">
                <div className="grid lg:grid-cols-2 gap-12 items-center">
                  {/* Left Content */}
                  <motion.div
                    key={validActiveTab}
                    initial={{ opacity: 0, x: -30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5 }}
                    className="space-y-6"
                  >
                    <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                      {validActiveTab === 'property' && '🏠 Real Estate Solutions'}
                      {validActiveTab === 'hospitality' && '🏨 Hospitality Experiences'}
                      {validActiveTab === 'commercial' && '🏢 Commercial Spaces'}
                      {validActiveTab === 'education' && '🎓 Educational Tours'}
                      {validActiveTab === 'healthcare' && '🏥 Healthcare Facilities'}
                      {validActiveTab === 'tourism' && '🗺️ Tourism & Travel'}
                      {validActiveTab === 'culture' && '🎭 Cultural Experiences'}
                      {validActiveTab === 'government' && '🏛️ Government Facilities'}
                    </div>

                    <h3 className="text-3xl md:text-4xl font-bold text-gray-900">
                      {validActiveTab === 'property' && 'Showcase Properties Like Never Before'}
                      {validActiveTab === 'hospitality' && 'Immersive Hotel & Restaurant Tours'}
                      {validActiveTab === 'commercial' && 'Transform Business Presentations'}
                      {validActiveTab === 'education' && 'Virtual Campus & Classroom Tours'}
                      {validActiveTab === 'healthcare' && 'Safe Facility Walkthroughs'}
                      {validActiveTab === 'tourism' && 'Explore Destinations Virtually'}
                      {validActiveTab === 'culture' && 'Preserve & Share Cultural Heritage'}
                      {validActiveTab === 'government' && 'Transparent Public Facility Access'}
                    </h3>

                    <p className="text-lg text-gray-600 leading-relaxed">
                      {validActiveTab === 'property' && 'Give potential buyers and renters an immersive experience of your properties. Our 360° virtual tours increase engagement by 300% and reduce unnecessary site visits.'}
                      {validActiveTab === 'hospitality' && 'Let guests explore your hotel rooms, restaurants, and amenities before booking. Virtual tours increase booking confidence and reduce cancellations by 40%.'}
                      {validActiveTab === 'commercial' && 'Present your office spaces, retail locations, and commercial properties with stunning virtual tours that close deals faster and attract quality tenants.'}
                      {validActiveTab === 'education' && 'Provide prospective students and parents with virtual campus tours, classroom visits, and facility walkthroughs from anywhere in the world.'}
                      {validActiveTab === 'healthcare' && 'Offer patients and families virtual tours of your medical facilities, helping them feel comfortable and informed before their visit.'}
                      {validActiveTab === 'tourism' && 'Showcase tourist destinations, cultural sites, and attractions with immersive virtual tours that inspire travel and boost bookings.'}
                      {validActiveTab === 'culture' && 'Preserve and share cultural heritage sites, museums, and historical landmarks with immersive virtual experiences that educate and inspire visitors worldwide.'}
                      {validActiveTab === 'government' && 'Provide citizens with transparent access to public facilities, government buildings, and civic spaces, promoting openness and accessibility in public service.'}
                    </p>

                    <div className="flex flex-col sm:flex-row gap-4">
                      <Button className="bg-blue-600 hover:bg-blue-700 text-white" asChild>
                        <Link to="/showcase">View Examples</Link>
                      </Button>
                      <Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white" asChild>
                        <Link to="/contact">Get Started</Link>
                      </Button>
                    </div>
                  </motion.div>

                  {/* Right Content - Featured Tour */}
                  <motion.div
                    key={`${validActiveTab}-tour`}
                    initial={{ opacity: 0, x: 30 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5 }}
                    className="relative"
                  >
                    {allTours.length > 0 && (
                      <>
                        {/* Clean Tour Display - Direct iframe embed */}
                        <div className="aspect-video mb-6 rounded-xl overflow-hidden bg-gray-800">
                          {(() => {
                            const currentTour = allTours?.find(tour => tour.category?.toLowerCase().includes(validActiveTab)) || allTours?.[0];
                            if (!currentTour) {
                              return (
                                <img
                                  src="https://images.unsplash.com/photo-**********-ce09059eeffa?w=1200&h=675&fit=crop"
                                  alt="Sample virtual tour"
                                  className="w-full h-full object-cover"
                                />
                              );
                            }
                            return currentTour.embed_url && !currentTour.embed_url.includes('metaport') ? (
                              <iframe
                                src={getCleanEmbedUrl(currentTour.embed_url)}
                                className="w-full h-full border-0 clean-tour-iframe"
                                title={currentTour.title || "Virtual Tour"}
                                {...getCleanIframeProps()}
                              />
                            ) : (
                              <img
                                src={currentTour.thumbnail_url || "https://images.unsplash.com/photo-**********-ce09059eeffa?w=1200&h=675&fit=crop"}
                                alt={currentTour.title || "Tour preview"}
                                className="w-full h-full object-cover"
                              />
                            );
                          })()}
                        </div>

                        {/* Content Card Below */}
                        <div className="bg-white rounded-xl shadow-2xl overflow-hidden border border-gray-200 p-6">
                          {(() => {
                            const currentTour = allTours?.find(tour => tour.category?.toLowerCase().includes(validActiveTab)) || allTours?.[0];
                            return (
                              <>
                                <div className="flex items-center justify-between mb-3">
                                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                                    {currentTour?.category || validActiveTab}
                                  </Badge>
                                  <div className="flex items-center text-sm text-gray-500">
                                    <Eye className="w-4 h-4 mr-1" />
                                    <span>{currentTour?.views || 0}</span>
                                  </div>
                                </div>
                                <h4 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                                  {currentTour?.title || `Sample ${validActiveTab} tour`}
                                </h4>
                                <p className="text-gray-600 text-sm line-clamp-2">
                                  {currentTour?.description || `Experience immersive ${validActiveTab} virtual tours that showcase spaces in stunning detail.`}
                                </p>
                              </>
                            );
                          })()}
                        </div>
                      </>
                    )}
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
          ) : (
            /* Fallback when no tours available */
            <div className="text-center py-16">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Coming Soon
              </h3>
              <p className="text-lg text-gray-600 mb-8">
                We're preparing amazing virtual tours for you. Check back soon!
              </p>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white" asChild>
                <Link to="/contact">Get Notified</Link>
              </Button>
            </div>
          )}

          {/* Bottom Stats */}
          <div className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">500+</div>
              <div className="text-gray-600">Tours Created</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">50+</div>
              <div className="text-gray-600">Cities Covered</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">1M+</div>
              <div className="text-gray-600">Views Generated</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-blue-600 mb-2">99%</div>
              <div className="text-gray-600">Client Satisfaction</div>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Success Stories */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Trusted by industry leaders
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              See how businesses across Nigeria are using VirtualRealTour to showcase their spaces and drive results.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Testimonial 1 */}
            <div className="bg-white rounded-xl p-8 shadow-lg">
              <div className="flex items-center mb-4">
                <img
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=face&q=80"
                  alt="Customer"
                  className="w-12 h-12 rounded-full mr-4"
                />
                <div>
                  <h4 className="font-semibold text-gray-900">Adebayo Johnson</h4>
                  <p className="text-sm text-gray-600">Real Estate Developer</p>
                </div>
              </div>
              <p className="text-gray-700 leading-relaxed">
                "VirtualRealTour increased our property inquiries by 300%. Clients can now explore properties remotely, saving time for everyone."
              </p>
            </div>

            {/* Testimonial 2 */}
            <div className="bg-white rounded-xl p-8 shadow-lg">
              <div className="flex items-center mb-4">
                <img
                  src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=60&h=60&fit=crop&crop=face&q=80"
                  alt="Customer"
                  className="w-12 h-12 rounded-full mr-4"
                />
                <div>
                  <h4 className="font-semibold text-gray-900">Fatima Abdullahi</h4>
                  <p className="text-sm text-gray-600">Hotel Manager</p>
                </div>
              </div>
              <p className="text-gray-700 leading-relaxed">
                "Our hotel bookings increased by 45% after implementing virtual tours. Guests love exploring our facilities before arrival."
              </p>
            </div>

            {/* Testimonial 3 */}
            <div className="bg-white rounded-xl p-8 shadow-lg">
              <div className="flex items-center mb-4">
                <img
                  src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=60&h=60&fit=crop&crop=face&q=80"
                  alt="Customer"
                  className="w-12 h-12 rounded-full mr-4"
                />
                <div>
                  <h4 className="font-semibold text-gray-900">Chukwuma Okafor</h4>
                  <p className="text-sm text-gray-600">University Administrator</p>
                </div>
              </div>
              <p className="text-gray-700 leading-relaxed">
                "Virtual campus tours helped us reach international students. Applications from abroad increased by 60% this year."
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section - Enhanced */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Ready to get started?
          </h2>
          <p className="text-xl text-gray-600 mb-12">
            Join thousands of businesses already using VirtualRealTour to showcase their spaces.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg" asChild>
              <Link to="/auth">Start Free Trial</Link>
            </Button>
            <Button size="lg" variant="outline" className="border-gray-300 text-gray-700 px-8 py-4 text-lg" asChild>
              <Link to="/contact">Schedule Demo</Link>
            </Button>
          </div>

          {/* Trust indicators */}
          <div className="mt-12 flex flex-col sm:flex-row items-center justify-center gap-8 text-sm text-gray-500">
            <div className="flex items-center">
              <Star className="w-4 h-4 text-yellow-400 mr-1" />
              <span>4.9/5 Customer Rating</span>
            </div>
            <div className="flex items-center">
              <Users className="w-4 h-4 text-blue-500 mr-1" />
              <span>500+ Active Users</span>
            </div>
            <div className="flex items-center">
              <Building className="w-4 h-4 text-green-500 mr-1" />
              <span>50+ Cities Covered</span>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default MapboxStyleWelcome;
