
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Eye,
  Globe, 
  Smartphone, 
  Users, 
  Zap, 
  Shield, 
  BarChart3, 
  Headphones
} from 'lucide-react'

const EnhancedFeaturesSection = () => {
  const features = [
    {
      icon: Eye,
      title: "360° Virtual Tours",
      description: "Create stunning immersive 360° experiences with high-quality photography and seamless navigation.",
      color: "text-blue-600"
    },
    {
      icon: Smartphone,
      title: "Multi-Platform Support",
      description: "Perfect viewing experience across VR headsets, desktops, tablets, and mobile devices.",
      color: "text-blue-600"
    },
    {
      icon: Globe,
      title: "Interactive Hotspots",
      description: "Add information points, navigation links, contact forms, and rich media overlays.",
      color: "text-blue-600"
    },
    {
      icon: Users,
      title: "Business Solutions",
      description: "Tailored virtual tour solutions for real estate, hospitality, education, and retail sectors.",
      color: "text-blue-600"
    },
    {
      icon: BarChart3,
      title: "Analytics Dashboard",
      description: "Track visitor engagement, view statistics, and gain insights into tour performance.",
      color: "text-blue-600"
    },
    {
      icon: Shield,
      title: "Secure & Reliable",
      description: "Enterprise-grade security with custom access controls and privacy settings.",
      color: "text-blue-600"
    }
  ]

  return (
    <section className="py-16 lg:py-20 bg-gray-50 section-gradient-subtle">
      <div className="container px-4 sm:px-6 lg:px-8">
        {/* Clean Header */}
        <div className="text-center mb-12 lg:mb-16">
          <Badge variant="secondary" className="mb-4 px-4 py-2 bg-blue-50 text-blue-700 border-blue-200 font-medium text-sm rounded-lg">
            Our Features
          </Badge>
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
            Why Choose <span className="text-blue-600">VirtualRealTour?</span>
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Professional virtual tour solutions with cutting-edge technology and industry-leading features
          </p>
        </div>

        {/* Clean Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 max-w-7xl mx-auto">
          {features.map((feature, index) => (
            <Card 
              key={feature.title} 
              className="bg-white border border-gray-200 rounded-lg hover:shadow-md transition-shadow duration-300 text-center p-6"
            >
              <CardContent className="p-0">
                <div className="w-16 h-16 rounded-lg bg-blue-100 flex items-center justify-center mx-auto mb-6">
                  <feature.icon className={`w-8 h-8 ${feature.color} icon-hover`} />
                </div>
                
                <h3 className="text-xl font-semibold text-gray-900 mb-4">
                  {feature.title}
                </h3>
                
                <p className="text-gray-600 leading-relaxed text-sm">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  )
}

export default EnhancedFeaturesSection
