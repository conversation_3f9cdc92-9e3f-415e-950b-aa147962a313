
import { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Plus } from 'lucide-react';

interface Tour {
  id: string;
  title: string;
  thumbnail_url: string;
  category: string;
}

interface AddTourFormProps {
  availableTours: Tour[];
}

const AddTourForm = ({ availableTours }: AddTourFormProps) => {
  const queryClient = useQueryClient();
  const [selectedSection, setSelectedSection] = useState<string>('demo');
  const [selectedTourId, setSelectedTourId] = useState<string>('');
  const [displayOrder, setDisplayOrder] = useState<number>(0);

  const addAssignmentMutation = useMutation({
    mutationFn: async () => {
      if (!selectedTourId || !selectedSection) {
        throw new Error('Please select a tour and section');
      }

      const { error } = await supabase
        .from('featured_tour_assignments')
        .insert({
          tour_id: selectedTourId,
          section_type: selectedSection,
          display_order: displayOrder,
          is_active: true
        });

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['featured-assignments'] });
      setSelectedTourId('');
      setDisplayOrder(0);
      toast.success('Tour added to section successfully');
    },
    onError: (error) => {
      toast.error(`Failed to add tour: ${error.message}`);
    },
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Add Tour to Section</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <Label htmlFor="section">Section</Label>
            <Select value={selectedSection} onValueChange={setSelectedSection}>
              <SelectTrigger>
                <SelectValue placeholder="Select section" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="demo">Demo Section</SelectItem>
                <SelectItem value="featured">Featured Tours</SelectItem>
                <SelectItem value="showcase">Showcase Page</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="tour">Tour</Label>
            <Select value={selectedTourId} onValueChange={setSelectedTourId}>
              <SelectTrigger>
                <SelectValue placeholder="Select tour" />
              </SelectTrigger>
              <SelectContent>
                {availableTours.map((tour) => (
                  <SelectItem key={tour.id} value={tour.id}>
                    {tour.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Label htmlFor="order">Display Order</Label>
            <Input
              type="number"
              value={displayOrder}
              onChange={(e) => setDisplayOrder(parseInt(e.target.value) || 0)}
              placeholder="0"
            />
          </div>
          
          <div className="flex items-end">
            <Button 
              onClick={() => addAssignmentMutation.mutate()}
              disabled={!selectedTourId || addAssignmentMutation.isPending}
              className="w-full"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Tour
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AddTourForm;
