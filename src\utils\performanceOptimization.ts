/**
 * Performance Optimization Utilities
 * Lightning-fast loading for tour cards and pages
 */

/**
 * Preload critical tour resources
 */
export const preloadTourResources = (tourUrls: string[]) => {
  // Preload first 3 tour URLs for faster loading
  tourUrls.slice(0, 3).forEach((url) => {
    const link = document.createElement('link');
    link.rel = 'prefetch';
    link.href = url;
    document.head.appendChild(link);
  });
};

/**
 * Optimize iframe loading with performance hints
 */
export const getOptimizedIframeProps = () => ({
  loading: 'lazy' as const,
  importance: 'low' as const,
  referrerPolicy: 'no-referrer' as const,
  sandbox: 'allow-scripts allow-same-origin allow-presentation',
  style: {
    contentVisibility: 'auto',
    containIntrinsicSize: '280px'
  }
});

/**
 * Debounce function for performance
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Throttle function for scroll events
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

/**
 * Intersection Observer with performance optimizations
 */
export const createOptimizedObserver = (
  callback: IntersectionObserverCallback,
  options?: IntersectionObserverInit
) => {
  const defaultOptions: IntersectionObserverInit = {
    rootMargin: '50px',
    threshold: 0.1,
    ...options
  };

  return new IntersectionObserver(callback, defaultOptions);
};

/**
 * Image optimization for tour thumbnails
 */
export const optimizeImageUrl = (url: string, width: number = 400, quality: number = 80): string => {
  try {
    const urlObj = new URL(url);
    
    // Add optimization parameters for common image services
    if (urlObj.hostname.includes('unsplash.com')) {
      urlObj.searchParams.set('w', width.toString());
      urlObj.searchParams.set('q', quality.toString());
      urlObj.searchParams.set('fm', 'webp');
      urlObj.searchParams.set('auto', 'format');
    }
    
    return urlObj.toString();
  } catch {
    return url;
  }
};

/**
 * Lazy loading utility for images
 */
export const lazyLoadImage = (img: HTMLImageElement, src: string) => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        img.src = src;
        img.classList.remove('lazy');
        observer.unobserve(img);
      }
    });
  });

  observer.observe(img);
};

/**
 * Performance metrics tracking
 */
export const trackPerformance = (name: string, startTime: number) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  if (duration > 100) {
    console.warn(`Slow operation detected: ${name} took ${duration.toFixed(2)}ms`);
  }
  
  return duration;
};

/**
 * Memory cleanup for tour components
 */
export const cleanupTourResources = () => {
  // Remove prefetch links after use
  const prefetchLinks = document.querySelectorAll('link[rel="prefetch"]');
  prefetchLinks.forEach((link) => {
    if (link.parentNode) {
      link.parentNode.removeChild(link);
    }
  });
};

/**
 * Optimize tour URL for faster loading
 */
export const optimizeTourUrl = (url: string): string => {
  try {
    const urlObj = new URL(url);
    
    // Add performance parameters
    urlObj.searchParams.set('preload', 'metadata');
    urlObj.searchParams.set('loading', 'lazy');
    urlObj.searchParams.set('performance', 'fast');
    
    return urlObj.toString();
  } catch {
    return url;
  }
};

/**
 * Batch DOM updates for better performance
 */
export const batchDOMUpdates = (updates: (() => void)[]) => {
  requestAnimationFrame(() => {
    updates.forEach(update => update());
  });
};

/**
 * Check if user prefers reduced motion
 */
export const prefersReducedMotion = (): boolean => {
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

/**
 * Optimize animations based on user preferences
 */
export const getOptimizedAnimationDuration = (defaultDuration: number): number => {
  return prefersReducedMotion() ? 0 : defaultDuration;
};
