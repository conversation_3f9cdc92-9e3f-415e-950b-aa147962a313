import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveAdminDialogProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * A standardized, mobile-first responsive dialog/modal content wrapper for admin use.
 * - Full width on mobile, max-w on desktop
 * - Max height and scroll handling
 * - Responsive padding and gap
 */
const ResponsiveAdminDialog = ({ children, className }: ResponsiveAdminDialogProps) => (
  <div
    className={cn(
      'w-full max-w-full sm:max-w-lg md:max-w-2xl lg:max-w-3xl xl:max-w-4xl mx-auto rounded-xl border bg-white dark:bg-gray-950 shadow-lg p-3 sm:p-5 md:p-8 flex flex-col gap-3 max-h-[90vh] overflow-y-auto',
      className
    )}
    tabIndex={-1}
  >
    {children}
  </div>
);

export default ResponsiveAdminDialog;
