/**
 * Secure Tour Embed Component
 * Wraps external tours in secure iframe with no source URL leakage
 */

import { useState, useEffect, useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Pause, 
  Maximize, 
  Volume2, 
  VolumeX,
  Settings,
  Share2,
  Eye,
  Loader2,
  AlertCircle,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface SecureTourEmbedProps {
  tourId: string;
  title?: string;
  description?: string;
  thumbnail?: string;
  platform: 'cloudpano' | 'commonninja' | 'wpvr' | 'custom';
  embedUrl?: string;
  autoplay?: boolean;
  showControls?: boolean;
  showBranding?: boolean;
  className?: string;
  onLoad?: () => void;
  onError?: (error: string) => void;
}

const SecureTourEmbed = ({
  tourId,
  title,
  description,
  thumbnail,
  platform,
  embedUrl,
  autoplay = false,
  showControls = true,
  showBranding = false,
  className,
  onLoad,
  onError
}: SecureTourEmbedProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isPlaying, setIsPlaying] = useState(autoplay);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showThumbnail, setShowThumbnail] = useState(!autoplay);
  
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Generate secure embed URL that proxies through our backend
  const getSecureEmbedUrl = () => {
    const baseUrl = window.location.origin;
    const params = new URLSearchParams({
      tour_id: tourId,
      platform,
      autoplay: autoplay.toString(),
      controls: showControls.toString(),
      branding: showBranding.toString(),
      muted: isMuted.toString()
    });
    
    return `${baseUrl}/api/embed/tour?${params.toString()}`;
  };

  // Handle iframe load
  const handleIframeLoad = () => {
    setIsLoading(false);
    setHasError(false);
    onLoad?.();
  };

  // Handle iframe error
  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.('Failed to load tour');
  };

  // Start tour playback
  const handlePlay = () => {
    setShowThumbnail(false);
    setIsPlaying(true);
    
    // Send message to iframe to start tour
    if (iframeRef.current?.contentWindow) {
      iframeRef.current.contentWindow.postMessage({
        type: 'TOUR_PLAY',
        tourId
      }, '*');
    }
  };

  // Pause tour
  const handlePause = () => {
    setIsPlaying(false);
    
    if (iframeRef.current?.contentWindow) {
      iframeRef.current.contentWindow.postMessage({
        type: 'TOUR_PAUSE',
        tourId
      }, '*');
    }
  };

  // Toggle mute
  const handleMute = () => {
    const newMutedState = !isMuted;
    setIsMuted(newMutedState);
    
    if (iframeRef.current?.contentWindow) {
      iframeRef.current.contentWindow.postMessage({
        type: 'TOUR_MUTE',
        muted: newMutedState,
        tourId
      }, '*');
    }
  };

  // Toggle fullscreen
  const handleFullscreen = () => {
    if (!document.fullscreenElement && containerRef.current) {
      containerRef.current.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  // Handle share
  const handleShare = async () => {
    const shareUrl = `${window.location.origin}/tour/${tourId}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: title || 'Virtual Tour',
          text: description || 'Check out this amazing virtual tour!',
          url: shareUrl
        });
      } catch (error) {
        // Fallback to clipboard
        navigator.clipboard.writeText(shareUrl);
      }
    } else {
      // Fallback to clipboard
      navigator.clipboard.writeText(shareUrl);
    }
  };

  // Listen for fullscreen changes
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Listen for messages from iframe
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data.tourId !== tourId) return;
      
      switch (event.data.type) {
        case 'TOUR_LOADED':
          handleIframeLoad();
          break;
        case 'TOUR_ERROR':
          handleIframeError();
          break;
        case 'TOUR_STARTED':
          setIsPlaying(true);
          break;
        case 'TOUR_PAUSED':
          setIsPlaying(false);
          break;
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [tourId]);

  return (
    <div 
      ref={containerRef}
      className={cn(
        "relative w-full bg-black rounded-lg overflow-hidden",
        isFullscreen && "fixed inset-0 z-50 rounded-none",
        className
      )}
    >
      {/* Thumbnail Overlay */}
      {showThumbnail && thumbnail && (
        <div className="absolute inset-0 z-10">
          <img 
            src={thumbnail} 
            alt={title || 'Tour thumbnail'}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center">
            <Button
              size="lg"
              onClick={handlePlay}
              className="bg-white/20 backdrop-blur-sm border border-white/30 hover:bg-white/30"
            >
              <Play className="w-8 h-8 text-white" />
            </Button>
          </div>
          
          {/* Tour Info Overlay */}
          {(title || description) && (
            <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 to-transparent">
              {title && (
                <h3 className="text-white text-xl font-semibold mb-2">{title}</h3>
              )}
              {description && (
                <p className="text-white/80 text-sm">{description}</p>
              )}
            </div>
          )}
        </div>
      )}

      {/* Loading State */}
      {isLoading && !showThumbnail && (
        <div className="absolute inset-0 z-10 bg-black flex items-center justify-center">
          <div className="text-center text-white">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
            <p>Loading virtual tour...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {hasError && (
        <div className="absolute inset-0 z-10 bg-black flex items-center justify-center">
          <div className="text-center text-white">
            <AlertCircle className="w-8 h-8 mx-auto mb-4 text-red-400" />
            <p className="mb-4">Failed to load tour</p>
            <Button onClick={() => window.location.reload()} variant="outline">
              Retry
            </Button>
          </div>
        </div>
      )}

      {/* Secure Iframe */}
      <iframe
        ref={iframeRef}
        src={getSecureEmbedUrl()}
        title={`Virtual Tour: ${tour?.title || 'Interactive Tour'}`}
        className="w-full h-full border-0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
        allowFullScreen
        sandbox="allow-scripts allow-same-origin allow-presentation"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        style={{ display: showThumbnail ? 'none' : 'block' }}
      />

      {/* Controls Overlay */}
      {showControls && !showThumbnail && (
        <div className="absolute bottom-4 left-4 right-4 z-20">
          <div className="bg-black/50 backdrop-blur-sm rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={isPlaying ? handlePause : handlePlay}
                  className="text-white hover:bg-white/20"
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </Button>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleMute}
                  className="text-white hover:bg-white/20"
                >
                  {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                </Button>
              </div>

              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleShare}
                  className="text-white hover:bg-white/20"
                >
                  <Share2 className="w-4 h-4" />
                </Button>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleFullscreen}
                  className="text-white hover:bg-white/20"
                >
                  <Maximize className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Platform Badge */}
      {showBranding && (
        <div className="absolute top-4 right-4 z-20">
          <Badge variant="secondary" className="bg-black/50 text-white border-white/20">
            <Eye className="w-3 h-3 mr-1" />
            VirtualRealTour
          </Badge>
        </div>
      )}

      {/* Platform Source Badge (for admin/debug) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="absolute top-4 left-4 z-20">
          <Badge variant="outline" className="bg-black/50 text-white border-white/20">
            {platform.toUpperCase()}
          </Badge>
        </div>
      )}
    </div>
  );
};

export default SecureTourEmbed;
