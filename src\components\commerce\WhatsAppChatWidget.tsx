/**
 * WhatsAppChatWidget Component
 * Floating WhatsApp chat widget with smart context awareness
 * Mobile-first responsive design with tour and product context
 */

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  MessageCircle, 
  X, 
  Send, 
  Phone, 
  Clock, 
  CheckCircle,
  Package,
  ShoppingCart,
  MapPin,
  User,
  Minimize2,
  Maximize2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { enhancedWhatsAppService } from '@/services/commerce/EnhancedWhatsAppService';

interface WhatsAppChatWidgetProps {
  businessPhone?: string;
  businessName?: string;
  context?: {
    type: 'tour' | 'product' | 'order' | 'general';
    data?: any;
  };
  position?: 'bottom-right' | 'bottom-left';
  className?: string;
}

interface QuickMessage {
  id: string;
  text: string;
  icon: any;
  category: string;
}

const WhatsAppChatWidget = ({
  businessPhone = '+2348123456789',
  businessName = 'VirtualRealTour Support',
  context,
  position = 'bottom-right',
  className
}: WhatsAppChatWidgetProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [message, setMessage] = useState('');
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    phone: '',
    email: ''
  });
  const [selectedQuickMessage, setSelectedQuickMessage] = useState<string | null>(null);
  const [isOnline, setIsOnline] = useState(true);

  // Quick message templates based on context
  const quickMessages: QuickMessage[] = [
    {
      id: 'general_inquiry',
      text: 'Hi! I have a general question.',
      icon: MessageCircle,
      category: 'general'
    },
    {
      id: 'product_info',
      text: 'I need more information about this product.',
      icon: Package,
      category: 'product'
    },
    {
      id: 'order_status',
      text: 'I want to check my order status.',
      icon: ShoppingCart,
      category: 'order'
    },
    {
      id: 'tour_inquiry',
      text: 'I\'m interested in this virtual tour.',
      icon: MapPin,
      category: 'tour'
    },
    {
      id: 'technical_support',
      text: 'I\'m having technical issues.',
      icon: Phone,
      category: 'support'
    },
    {
      id: 'business_inquiry',
      text: 'I want to become a vendor.',
      icon: User,
      category: 'business'
    }
  ];

  // Filter quick messages based on context
  const getRelevantQuickMessages = () => {
    if (!context) return quickMessages.slice(0, 4);

    switch (context.type) {
      case 'product':
        return quickMessages.filter(msg => 
          ['product_info', 'general_inquiry', 'technical_support'].includes(msg.id)
        );
      case 'order':
        return quickMessages.filter(msg => 
          ['order_status', 'general_inquiry', 'technical_support'].includes(msg.id)
        );
      case 'tour':
        return quickMessages.filter(msg => 
          ['tour_inquiry', 'general_inquiry', 'business_inquiry'].includes(msg.id)
        );
      default:
        return quickMessages.slice(0, 4);
    }
  };

  // Generate context-aware message
  const generateContextMessage = () => {
    if (!context) return '';

    switch (context.type) {
      case 'product':
        return `Hi! I'm interested in "${context.data?.title || 'this product'}" (₦${context.data?.price?.toLocaleString() || 'N/A'}). `;
      case 'order':
        return `Hi! I need help with my order ${context.data?.order_number || ''}. `;
      case 'tour':
        return `Hi! I'm interested in the "${context.data?.title || 'virtual tour'}" experience. `;
      default:
        return 'Hi! ';
    }
  };

  // Handle quick message selection
  const handleQuickMessage = (quickMsg: QuickMessage) => {
    const contextMessage = generateContextMessage();
    setMessage(contextMessage + quickMsg.text);
    setSelectedQuickMessage(quickMsg.id);
  };

  // Handle send message
  const handleSendMessage = () => {
    if (!message.trim()) {
      toast.error('Please enter a message');
      return;
    }

    const contextInfo = context ? `\n\n*Context:* ${context.type} - ${JSON.stringify(context.data, null, 2)}` : '';
    const customerDetails = customerInfo.name ? `\n\n*Customer:* ${customerInfo.name}\n*Phone:* ${customerInfo.phone}\n*Email:* ${customerInfo.email}` : '';
    
    const fullMessage = encodeURIComponent(message + contextInfo + customerDetails);
    const whatsappUrl = `https://wa.me/${businessPhone.replace(/[^\d]/g, '')}?text=${fullMessage}`;
    
    window.open(whatsappUrl, '_blank');
    
    toast.success('Opening WhatsApp...');
    setIsOpen(false);
    setMessage('');
  };

  // Simulate online status
  useEffect(() => {
    const interval = setInterval(() => {
      setIsOnline(Math.random() > 0.1); // 90% online
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4'
  };

  return (
    <div className={cn(
      "fixed z-50",
      positionClasses[position],
      className
    )}>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className="mb-4"
          >
            <Card className={cn(
              "w-80 sm:w-96 shadow-2xl border-0",
              isMinimized && "h-16 overflow-hidden"
            )}>
              {/* Header */}
              <CardHeader className="pb-3 bg-green-600 text-white rounded-t-lg">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="relative">
                      <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                        <MessageCircle className="w-6 h-6 text-green-600" />
                      </div>
                      <div className={cn(
                        "absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white",
                        isOnline ? "bg-green-400" : "bg-gray-400"
                      )} />
                    </div>
                    <div>
                      <CardTitle className="text-white text-sm">{businessName}</CardTitle>
                      <CardDescription className="text-green-100 text-xs">
                        {isOnline ? (
                          <span className="flex items-center gap-1">
                            <CheckCircle className="w-3 h-3" />
                            Online now
                          </span>
                        ) : (
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            Usually replies quickly
                          </span>
                        )}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-8 h-8 p-0 text-white hover:bg-green-700"
                      onClick={() => setIsMinimized(!isMinimized)}
                    >
                      {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-8 h-8 p-0 text-white hover:bg-green-700"
                      onClick={() => setIsOpen(false)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>

              {!isMinimized && (
                <CardContent className="p-4 space-y-4">
                  {/* Context Banner */}
                  {context && (
                    <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                      <div className="flex items-center gap-2 text-blue-800">
                        {context.type === 'product' && <Package className="w-4 h-4" />}
                        {context.type === 'order' && <ShoppingCart className="w-4 h-4" />}
                        {context.type === 'tour' && <MapPin className="w-4 h-4" />}
                        <span className="text-sm font-medium capitalize">{context.type} Context</span>
                      </div>
                      {context.data && (
                        <p className="text-xs text-blue-600 mt-1">
                          {context.data.title || context.data.order_number || 'Context available'}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Quick Messages */}
                  <div>
                    <Label className="text-xs text-muted-foreground">Quick Messages</Label>
                    <div className="grid grid-cols-1 gap-2 mt-2">
                      {getRelevantQuickMessages().map((quickMsg) => {
                        const Icon = quickMsg.icon;
                        return (
                          <Button
                            key={quickMsg.id}
                            variant={selectedQuickMessage === quickMsg.id ? "default" : "outline"}
                            size="sm"
                            className="justify-start h-auto p-3 text-left"
                            onClick={() => handleQuickMessage(quickMsg)}
                          >
                            <Icon className="w-4 h-4 mr-2 flex-shrink-0" />
                            <span className="text-xs">{quickMsg.text}</span>
                          </Button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Custom Message */}
                  <div>
                    <Label htmlFor="custom-message" className="text-xs text-muted-foreground">
                      Custom Message
                    </Label>
                    <Textarea
                      id="custom-message"
                      placeholder="Type your message here..."
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      className="mt-1 min-h-[80px] text-sm"
                      rows={3}
                    />
                  </div>

                  {/* Customer Info (Optional) */}
                  <div className="space-y-2">
                    <Label className="text-xs text-muted-foreground">
                      Contact Info (Optional)
                    </Label>
                    <div className="grid grid-cols-2 gap-2">
                      <Input
                        placeholder="Your name"
                        value={customerInfo.name}
                        onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
                        className="text-sm"
                      />
                      <Input
                        placeholder="Phone number"
                        value={customerInfo.phone}
                        onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                        className="text-sm"
                      />
                    </div>
                    <Input
                      placeholder="Email (optional)"
                      type="email"
                      value={customerInfo.email}
                      onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
                      className="text-sm"
                    />
                  </div>

                  {/* Send Button */}
                  <Button
                    onClick={handleSendMessage}
                    className="w-full bg-green-600 hover:bg-green-700"
                    disabled={!message.trim()}
                  >
                    <Send className="w-4 h-4 mr-2" />
                    Send via WhatsApp
                  </Button>

                  {/* Footer */}
                  <div className="text-center">
                    <p className="text-xs text-muted-foreground">
                      Powered by WhatsApp Business
                    </p>
                  </div>
                </CardContent>
              )}
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Floating Button */}
      <motion.div
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <Button
          onClick={() => setIsOpen(!isOpen)}
          className={cn(
            "w-14 h-14 rounded-full shadow-lg bg-green-600 hover:bg-green-700 border-0",
            isOpen && "bg-gray-600 hover:bg-gray-700"
          )}
        >
          <AnimatePresence mode="wait">
            {isOpen ? (
              <motion.div
                key="close"
                initial={{ rotate: -90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: 90, opacity: 0 }}
                transition={{ duration: 0.2 }}
              >
                <X className="w-6 h-6 text-white" />
              </motion.div>
            ) : (
              <motion.div
                key="open"
                initial={{ rotate: 90, opacity: 0 }}
                animate={{ rotate: 0, opacity: 1 }}
                exit={{ rotate: -90, opacity: 0 }}
                transition={{ duration: 0.2 }}
                className="relative"
              >
                <MessageCircle className="w-6 h-6 text-white" />
                {context && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full" />
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </Button>
      </motion.div>

      {/* Notification Badge */}
      {!isOpen && context && (
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="absolute -top-2 -left-2"
        >
          <Badge variant="destructive" className="text-xs px-1">
            {context.type}
          </Badge>
        </motion.div>
      )}
    </div>
  );
};

export default WhatsAppChatWidget;
