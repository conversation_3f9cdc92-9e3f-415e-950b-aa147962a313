/**
 * Basic Tour Editor
 * A simple, working tour editor for 360° images with hotspot placement
 */

import { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Upload, 
  Plus, 
  Save, 
  Eye, 
  Share2, 
  MapPin,
  ShoppingCart,
  Link,
  Info,
  X
} from 'lucide-react';
import { toast } from 'sonner';

interface Scene {
  id: string;
  name: string;
  imageUrl: string;
  hotspots: Hotspot[];
}

interface Hotspot {
  id: string;
  type: 'navigation' | 'product' | 'info' | 'link';
  x: number;
  y: number;
  title: string;
  content: string;
  targetSceneId?: string;
  productId?: string;
  linkUrl?: string;
}

interface BasicTourEditorProps {
  tourId?: string;
  tourData: {
    title: string;
    description: string;
    category: string;
    location: string;
  };
  onSave?: (tourData: any) => void;
  onPublish?: (tourData: any) => void;
  onClose?: () => void;
}

const BasicTourEditor = ({
  tourId,
  tourData,
  onSave,
  onPublish,
  onClose
}: BasicTourEditorProps) => {
  const [scenes, setScenes] = useState<Scene[]>([]);
  const [currentSceneIndex, setCurrentSceneIndex] = useState(0);
  const [isAddingHotspot, setIsAddingHotspot] = useState(false);
  const [hotspotType, setHotspotType] = useState<Hotspot['type']>('info');
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const currentScene = scenes[currentSceneIndex];

  // Auto-save functionality
  useEffect(() => {
    if (scenes.length > 0) {
      const autoSaveTimer = setTimeout(() => {
        const tourData = {
          scenes,
          embedUrl: `https://virtualrealtour.ng/embed/tour/${tourId}`,
          totalScenes: scenes.length,
          totalHotspots: scenes.reduce((total, scene) => total + scene.hotspots.length, 0),
          lastAutoSave: new Date().toISOString()
        };
        onSave?.(tourData);
        toast.success('Auto-saved', { duration: 2000 });
      }, 5000); // Auto-save every 5 seconds

      return () => clearTimeout(autoSaveTimer);
    }
  }, [scenes, tourId, onSave]);

  const processFiles = (files: FileList) => {
    Array.from(files).forEach((file, index) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const imageUrl = e.target?.result as string;
          const newScene: Scene = {
            id: `scene-${Date.now()}-${index}`,
            name: `Scene ${scenes.length + index + 1}`,
            imageUrl,
            hotspots: []
          };
          setScenes(prev => [...prev, newScene]);
          toast.success(`Scene "${newScene.name}" added successfully!`);
        };
        reader.readAsDataURL(file);
      } else {
        toast.error(`${file.name} is not a valid image file`);
      }
    });
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;
    processFiles(files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    if (files) {
      processFiles(files);
    }
  };

  const handleSceneClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!isAddingHotspot || !currentScene) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const x = ((event.clientX - rect.left) / rect.width) * 100;
    const y = ((event.clientY - rect.top) / rect.height) * 100;

    const newHotspot: Hotspot = {
      id: `hotspot-${Date.now()}`,
      type: hotspotType,
      x,
      y,
      title: `${hotspotType.charAt(0).toUpperCase() + hotspotType.slice(1)} Hotspot`,
      content: 'Click to edit this hotspot'
    };

    setScenes(prev => prev.map((scene, index) => 
      index === currentSceneIndex 
        ? { ...scene, hotspots: [...scene.hotspots, newHotspot] }
        : scene
    ));

    setIsAddingHotspot(false);
    toast.success('Hotspot added! Click on it to edit.');
  };

  const removeHotspot = (hotspotId: string) => {
    setScenes(prev => prev.map((scene, index) => 
      index === currentSceneIndex 
        ? { ...scene, hotspots: scene.hotspots.filter(h => h.id !== hotspotId) }
        : scene
    ));
    toast.success('Hotspot removed');
  };

  const handleSave = () => {
    const tourData = {
      scenes,
      embedUrl: `https://virtualrealtour.ng/tour/${tourId}`,
      totalScenes: scenes.length,
      totalHotspots: scenes.reduce((total, scene) => total + scene.hotspots.length, 0)
    };
    onSave?.(tourData);
  };

  const handlePublish = () => {
    const tourData = {
      scenes,
      embedUrl: `https://virtualrealtour.ng/tour/${tourId}`,
      totalScenes: scenes.length,
      totalHotspots: scenes.reduce((total, scene) => total + scene.hotspots.length, 0)
    };
    onPublish?.(tourData);
  };

  const getHotspotIcon = (type: Hotspot['type']) => {
    switch (type) {
      case 'navigation': return <MapPin className="w-4 h-4" />;
      case 'product': return <ShoppingCart className="w-4 h-4" />;
      case 'link': return <Link className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Editor Header */}
      <Card className="mb-4">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">{tourData.title}</CardTitle>
              <p className="text-sm text-muted-foreground">{tourData.location}</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                Save
              </Button>
              <Button size="sm" onClick={handlePublish}>
                <Share2 className="w-4 h-4 mr-2" />
                Publish
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-4">
        {/* Scene List */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <CardTitle className="text-base">Scenes</CardTitle>
            <div
              className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer ${
                isDragOver ? 'border-primary bg-primary/5' : 'border-muted-foreground/25 hover:border-primary/50'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm font-medium mb-1">Upload 360° Images</p>
              <p className="text-xs text-muted-foreground">
                Drag & drop or click to browse
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                Supports: JPG, PNG, WebP
              </p>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handleImageUpload}
              className="hidden"
              aria-label="Upload 360 degree panoramic images"
              title="Upload panoramic images for your virtual tour"
            />
          </CardHeader>
          <CardContent className="space-y-2">
            {scenes.map((scene, index) => (
              <div
                key={scene.id}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  index === currentSceneIndex ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
                }`}
                onClick={() => setCurrentSceneIndex(index)}
              >
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 bg-muted rounded overflow-hidden">
                    <img src={scene.imageUrl} alt={scene.name} className="w-full h-full object-cover" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{scene.name}</p>
                    <p className="text-xs text-muted-foreground">{scene.hotspots.length} hotspots</p>
                  </div>
                </div>
              </div>
            ))}
            {scenes.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Upload className="w-8 h-8 mx-auto mb-2" />
                <p className="text-sm">Upload 360° images to start</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Main Editor */}
        <Card className="lg:col-span-3">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-base">
                {currentScene ? currentScene.name : 'Select a scene to edit'}
              </CardTitle>
              {currentScene && (
                <div className="flex items-center gap-2">
                  <Label htmlFor="hotspot-type" className="text-sm">Add Hotspot:</Label>
                  <select
                    id="hotspot-type"
                    value={hotspotType}
                    onChange={(e) => setHotspotType(e.target.value as Hotspot['type'])}
                    className="text-sm border rounded px-2 py-1"
                    title="Select hotspot type"
                  >
                    <option value="info">Info</option>
                    <option value="product">Product</option>
                    <option value="navigation">Navigation</option>
                    <option value="link">Link</option>
                  </select>
                  <Button
                    size="sm"
                    variant={isAddingHotspot ? "default" : "outline"}
                    onClick={() => setIsAddingHotspot(!isAddingHotspot)}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {isAddingHotspot ? 'Click on image' : 'Add Hotspot'}
                  </Button>
                </div>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {currentScene ? (
              <div className="relative">
                <div
                  className="relative w-full h-96 bg-muted rounded-lg overflow-hidden cursor-crosshair"
                  onClick={handleSceneClick}
                >
                  <img
                    src={currentScene.imageUrl}
                    alt={currentScene.name}
                    className="w-full h-full object-cover"
                  />
                  {/* Hotspots */}
                  {currentScene.hotspots.map((hotspot) => (
                    <div
                      key={hotspot.id}
                      className="absolute transform -translate-x-1/2 -translate-y-1/2 group"
                      style={{
                        left: `${Math.max(0, Math.min(100, hotspot.x))}%`,
                        top: `${Math.max(0, Math.min(100, hotspot.y))}%`
                      }}
                    >
                      <div className="relative">
                        <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white shadow-lg cursor-pointer hover:scale-110 transition-transform">
                          {getHotspotIcon(hotspot.type)}
                        </div>
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeHotspot(hotspot.id);
                          }}
                          className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center text-white opacity-0 group-hover:opacity-100 transition-opacity"
                          title={`Remove ${hotspot.title} hotspot`}
                          aria-label={`Remove ${hotspot.title} hotspot`}
                        >
                          <X className="w-2 h-2" />
                        </button>
                        <div className="absolute top-10 left-1/2 transform -translate-x-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                          {hotspot.title}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                {isAddingHotspot && (
                  <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <p className="text-sm text-blue-800">
                      Click anywhere on the image to place a {hotspotType} hotspot
                    </p>
                  </div>
                )}
              </div>
            ) : (
              <div className="h-96 flex items-center justify-center text-muted-foreground">
                <div className="text-center">
                  <MapPin className="w-12 h-12 mx-auto mb-4" />
                  <p>Upload 360° images to start creating your virtual tour</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default BasicTourEditor;
