/**
 * Enterprise Features for Photo Sphere Viewer
 * Advanced controls, settings, and professional tour creation features
 */

import type { Viewer } from '@photo-sphere-viewer/core';
import type { AutorotatePlugin } from '@photo-sphere-viewer/autorotate-plugin';
import type { GalleryPlugin } from '@photo-sphere-viewer/gallery-plugin';
import type { SettingsPlugin } from '@photo-sphere-viewer/settings-plugin';
import type { PSVInstance } from './types';
import { toast } from 'sonner';

export interface EnterpriseSettings {
  autoRotate: {
    enabled: boolean;
    speed: string;
    delay: number;
    idle: boolean;
  };
  navigation: {
    showCompass: boolean;
    showZoom: boolean;
    showFullscreen: boolean;
    showDownload: boolean;
    showMove: boolean;
  };
  quality: {
    resolution: 'low' | 'medium' | 'high' | 'ultra';
    antialiasing: boolean;
    textureFiltering: boolean;
  };
  performance: {
    enableCache: boolean;
    preloadDistance: number;
    maxCacheSize: number;
  };
  accessibility: {
    keyboardNavigation: boolean;
    screenReaderSupport: boolean;
    highContrast: boolean;
  };
  branding: {
    showLogo: boolean;
    logoUrl?: string;
    customCSS?: string;
    hideAttribution: boolean;
  };
}

export interface TourAnalytics {
  viewCount: number;
  averageViewTime: number;
  hotspotClicks: Record<string, number>;
  sceneViews: Record<string, number>;
  userInteractions: {
    zoom: number;
    rotate: number;
    fullscreen: number;
  };
  deviceStats: {
    mobile: number;
    desktop: number;
    tablet: number;
  };
  geolocation?: {
    country: string;
    city: string;
    coordinates?: [number, number];
  }[];
}

export interface ExportOptions {
  format: 'json' | 'xml' | 'csv';
  includeImages: boolean;
  includeAnalytics: boolean;
  compression: 'none' | 'zip' | 'gzip';
  quality: 'original' | 'optimized' | 'compressed';
}

/**
 * Enterprise Features Manager
 */
export class EnterpriseFeatures {
  private viewer: Viewer | null = null;
  private plugins: {
    autorotate?: AutorotatePlugin;
    gallery?: GalleryPlugin;
    settings?: SettingsPlugin;
  } = {};
  private settings: EnterpriseSettings;
  private analytics: TourAnalytics;
  private eventListeners: Map<string, Function[]> = new Map();

  constructor(initialSettings?: Partial<EnterpriseSettings>) {
    this.settings = {
      autoRotate: {
        enabled: false,
        speed: '2rpm',
        delay: 2000,
        idle: true
      },
      navigation: {
        showCompass: true,
        showZoom: true,
        showFullscreen: true,
        showDownload: false,
        showMove: true
      },
      quality: {
        resolution: 'high',
        antialiasing: true,
        textureFiltering: true
      },
      performance: {
        enableCache: true,
        preloadDistance: 2,
        maxCacheSize: 50
      },
      accessibility: {
        keyboardNavigation: true,
        screenReaderSupport: true,
        highContrast: false
      },
      branding: {
        showLogo: true,
        hideAttribution: false
      },
      ...initialSettings
    };

    this.analytics = {
      viewCount: 0,
      averageViewTime: 0,
      hotspotClicks: {},
      sceneViews: {},
      userInteractions: {
        zoom: 0,
        rotate: 0,
        fullscreen: 0
      },
      deviceStats: {
        mobile: 0,
        desktop: 0,
        tablet: 0
      }
    };
  }

  /**
   * Initialize enterprise features with PSV instance
   */
  initialize(psvInstance: PSVInstance): void {
    this.viewer = psvInstance.viewer;
    this.plugins = {
      autorotate: psvInstance.plugins.autorotate,
      gallery: psvInstance.plugins.gallery,
      settings: psvInstance.plugins.settings
    };

    this.setupEventListeners();
    this.applySettings();
    this.initializeAnalytics();
  }

  /**
   * Setup event listeners for analytics and interactions
   */
  private setupEventListeners(): void {
    if (!this.viewer) return;

    // Track view start
    this.viewer.addEventListener('ready', () => {
      this.analytics.viewCount++;
      this.trackEvent('tour_started');
    });

    // Track user interactions
    this.viewer.addEventListener('zoom-updated', () => {
      this.analytics.userInteractions.zoom++;
      this.trackEvent('zoom_changed');
    });

    this.viewer.addEventListener('position-updated', () => {
      this.analytics.userInteractions.rotate++;
    });

    this.viewer.addEventListener('fullscreen-updated', (enabled) => {
      this.analytics.userInteractions.fullscreen++;
      this.trackEvent('fullscreen_toggled', { enabled });
    });

    // Track autorotate events
    if (this.plugins.autorotate) {
      this.plugins.autorotate.addEventListener('autorotate', (enabled) => {
        this.trackEvent('autorotate_toggled', { enabled });
      });
    }

    // Track gallery interactions
    if (this.plugins.gallery) {
      this.plugins.gallery.addEventListener('show', () => {
        this.trackEvent('gallery_opened');
      });

      this.plugins.gallery.addEventListener('hide', () => {
        this.trackEvent('gallery_closed');
      });
    }
  }

  /**
   * Apply current settings to the viewer
   */
  private applySettings(): void {
    if (!this.viewer) return;

    // Apply autorotate settings
    if (this.plugins.autorotate) {
      if (this.settings.autoRotate.enabled) {
        this.plugins.autorotate.setOptions({
          autorotateSpeed: this.settings.autoRotate.speed,
          autostartDelay: this.settings.autoRotate.delay,
          autorotateIdle: this.settings.autoRotate.idle
        });
        this.plugins.autorotate.start();
      } else {
        this.plugins.autorotate.stop();
      }
    }

    // Apply navigation settings
    this.updateNavigationBar();

    // Apply quality settings
    this.applyQualitySettings();

    // Apply accessibility settings
    this.applyAccessibilitySettings();

    // Apply branding
    this.applyBranding();
  }

  /**
   * Update navigation bar based on settings
   */
  private updateNavigationBar(): void {
    if (!this.viewer) return;

    const navbarItems = [];
    
    if (this.settings.navigation.showMove) navbarItems.push('move');
    if (this.settings.navigation.showZoom) navbarItems.push('zoom');
    if (this.plugins.autorotate && this.settings.autoRotate.enabled) navbarItems.push('autorotate');
    if (this.settings.navigation.showCompass) navbarItems.push('compass');
    if (this.plugins.gallery) navbarItems.push('gallery');
    if (this.plugins.settings) navbarItems.push('settings');
    if (this.settings.navigation.showDownload) navbarItems.push('download');
    if (this.settings.navigation.showFullscreen) navbarItems.push('fullscreen');

    // Update navbar (this would require PSV API support)
    console.log('Navigation items:', navbarItems);
  }

  /**
   * Apply quality settings
   */
  private applyQualitySettings(): void {
    // Quality settings would be applied through PSV configuration
    // This is a placeholder for quality management
    console.log('Applied quality settings:', this.settings.quality);
  }

  /**
   * Apply accessibility settings
   */
  private applyAccessibilitySettings(): void {
    if (!this.viewer) return;

    // Keyboard navigation
    if (this.settings.accessibility.keyboardNavigation) {
      this.viewer.container.setAttribute('tabindex', '0');
      this.setupKeyboardControls();
    }

    // High contrast mode
    if (this.settings.accessibility.highContrast) {
      this.viewer.container.classList.add('psv-high-contrast');
    } else {
      this.viewer.container.classList.remove('psv-high-contrast');
    }
  }

  /**
   * Setup keyboard controls
   */
  private setupKeyboardControls(): void {
    if (!this.viewer) return;

    this.viewer.container.addEventListener('keydown', (e) => {
      switch (e.key) {
        case 'ArrowLeft':
          this.viewer!.rotate({ yaw: -0.1 });
          break;
        case 'ArrowRight':
          this.viewer!.rotate({ yaw: 0.1 });
          break;
        case 'ArrowUp':
          this.viewer!.rotate({ pitch: 0.1 });
          break;
        case 'ArrowDown':
          this.viewer!.rotate({ pitch: -0.1 });
          break;
        case '+':
        case '=':
          this.viewer!.zoom(this.viewer!.getZoomLevel() + 10);
          break;
        case '-':
          this.viewer!.zoom(this.viewer!.getZoomLevel() - 10);
          break;
        case 'f':
        case 'F':
          if (this.viewer!.isFullscreenEnabled()) {
            this.viewer!.exitFullscreen();
          } else {
            this.viewer!.enterFullscreen();
          }
          break;
        case ' ':
          if (this.plugins.autorotate) {
            if (this.plugins.autorotate.isEnabled()) {
              this.plugins.autorotate.stop();
            } else {
              this.plugins.autorotate.start();
            }
          }
          e.preventDefault();
          break;
      }
    });
  }

  /**
   * Apply branding settings
   */
  private applyBranding(): void {
    if (!this.viewer) return;

    // Custom CSS
    if (this.settings.branding.customCSS) {
      const style = document.createElement('style');
      style.textContent = this.settings.branding.customCSS;
      document.head.appendChild(style);
    }

    // Hide attribution
    if (this.settings.branding.hideAttribution) {
      const attribution = this.viewer.container.querySelector('.psv-attribution');
      if (attribution) {
        (attribution as HTMLElement).style.display = 'none';
      }
    }

    // Custom logo
    if (this.settings.branding.showLogo && this.settings.branding.logoUrl) {
      this.addCustomLogo(this.settings.branding.logoUrl);
    }
  }

  /**
   * Add custom logo to viewer
   */
  private addCustomLogo(logoUrl: string): void {
    if (!this.viewer) return;

    const logo = document.createElement('img');
    logo.src = logoUrl;
    logo.className = 'psv-custom-logo';
    logo.style.cssText = `
      position: absolute;
      top: 10px;
      left: 10px;
      max-width: 100px;
      max-height: 50px;
      z-index: 100;
      opacity: 0.8;
    `;

    this.viewer.container.appendChild(logo);
  }

  /**
   * Initialize analytics tracking
   */
  private initializeAnalytics(): void {
    // Detect device type
    const userAgent = navigator.userAgent;
    if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
      if (/iPad/.test(userAgent)) {
        this.analytics.deviceStats.tablet++;
      } else {
        this.analytics.deviceStats.mobile++;
      }
    } else {
      this.analytics.deviceStats.desktop++;
    }

    // Start session timer
    const startTime = Date.now();
    window.addEventListener('beforeunload', () => {
      const sessionTime = Date.now() - startTime;
      this.analytics.averageViewTime = sessionTime / 1000; // Convert to seconds
    });
  }

  /**
   * Track custom events
   */
  private trackEvent(eventName: string, data?: any): void {
    const event = {
      name: eventName,
      timestamp: new Date().toISOString(),
      data: data || {}
    };

    // Emit event to listeners
    const listeners = this.eventListeners.get(eventName) || [];
    listeners.forEach(listener => listener(event));

    console.log('Analytics event:', event);
  }

  /**
   * Update settings
   */
  updateSettings(newSettings: Partial<EnterpriseSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.applySettings();
    toast.success('Settings updated successfully');
  }

  /**
   * Get current settings
   */
  getSettings(): EnterpriseSettings {
    return { ...this.settings };
  }

  /**
   * Get analytics data
   */
  getAnalytics(): TourAnalytics {
    return { ...this.analytics };
  }

  /**
   * Export tour data
   */
  async exportTour(options: ExportOptions): Promise<Blob> {
    const tourData = {
      settings: this.settings,
      analytics: options.includeAnalytics ? this.analytics : undefined,
      exportedAt: new Date().toISOString(),
      version: '1.0'
    };

    let content: string;
    let mimeType: string;

    switch (options.format) {
      case 'json':
        content = JSON.stringify(tourData, null, 2);
        mimeType = 'application/json';
        break;
      case 'xml':
        content = this.convertToXML(tourData);
        mimeType = 'application/xml';
        break;
      case 'csv':
        content = this.convertToCSV(tourData);
        mimeType = 'text/csv';
        break;
      default:
        throw new Error('Unsupported export format');
    }

    return new Blob([content], { type: mimeType });
  }

  /**
   * Convert data to XML format
   */
  private convertToXML(data: any): string {
    // Simple XML conversion (would need proper XML library for production)
    return `<?xml version="1.0" encoding="UTF-8"?>
<tour>
  <settings>${JSON.stringify(data.settings)}</settings>
  <analytics>${JSON.stringify(data.analytics)}</analytics>
  <exportedAt>${data.exportedAt}</exportedAt>
</tour>`;
  }

  /**
   * Convert data to CSV format
   */
  private convertToCSV(data: any): string {
    // Simple CSV conversion for analytics data
    const analytics = data.analytics;
    let csv = 'Metric,Value\n';
    csv += `View Count,${analytics.viewCount}\n`;
    csv += `Average View Time,${analytics.averageViewTime}\n`;
    csv += `Zoom Interactions,${analytics.userInteractions.zoom}\n`;
    csv += `Rotate Interactions,${analytics.userInteractions.rotate}\n`;
    csv += `Fullscreen Interactions,${analytics.userInteractions.fullscreen}\n`;
    return csv;
  }

  /**
   * Add event listener
   */
  addEventListener(eventName: string, listener: Function): void {
    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, []);
    }
    this.eventListeners.get(eventName)!.push(listener);
  }

  /**
   * Remove event listener
   */
  removeEventListener(eventName: string, listener: Function): void {
    const listeners = this.eventListeners.get(eventName);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * Cleanup enterprise features
   */
  destroy(): void {
    this.eventListeners.clear();
    this.viewer = null;
    this.plugins = {};
  }
}
