/**
 * Simple Tour Editor
 * Lightweight tour editor for quick edits and basic functionality
 */

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Save, 
  Play, 
  Eye, 
  Plus,
  Trash2,
  Upload,
  Camera
} from 'lucide-react';
import { toast } from 'sonner';

import PhotoSphereTourViewer from '../tour-viewer/PhotoSphereTourViewer';
import type { TourScene } from '@/lib/photosphere/types';

export interface SimpleTourEditorProps {
  tourId?: string;
  initialScenes?: TourScene[];
  onSave?: (tourData: any) => void;
  onPublish?: (tourData: any) => void;
  onClose?: () => void;
  className?: string;
}

const SimpleTourEditor: React.FC<SimpleTourEditorProps> = ({
  tourId,
  initialScenes = [],
  onSave,
  onPublish,
  onClose,
  className = ''
}) => {
  const [scenes, setScenes] = useState<TourScene[]>(initialScenes);
  const [currentSceneIndex, setCurrentSceneIndex] = useState(0);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const currentScene = scenes[currentSceneIndex];

  // Handle file upload
  const handleFileUpload = useCallback((files: FileList) => {
    Array.from(files).forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const imageUrl = e.target?.result as string;
          const newScene: TourScene = {
            id: `scene-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            name: file.name.replace(/\.[^/.]+$/, ''),
            panorama: imageUrl,
            links: [],
            markers: [],
            data: {
              orderIndex: scenes.length
            }
          };
          
          setScenes(prev => [...prev, newScene]);
          setHasUnsavedChanges(true);
          toast.success(`Added scene: ${newScene.name}`);
        };
        reader.readAsDataURL(file);
      }
    });
  }, [scenes.length]);

  // Save tour
  const handleSave = useCallback(async () => {
    try {
      const tourData = {
        scenes,
        settings: {
          autoRotate: false,
          showControls: true,
          enableVR: false
        },
        metadata: {
          lastModified: new Date().toISOString(),
          version: '1.0'
        }
      };

      if (onSave) {
        await onSave(tourData);
      }

      setHasUnsavedChanges(false);
      toast.success('Tour saved successfully!');
    } catch (error) {
      toast.error('Failed to save tour');
      console.error('Save error:', error);
    }
  }, [scenes, onSave]);

  // Publish tour
  const handlePublish = useCallback(async () => {
    try {
      await handleSave(); // Save first
      
      if (onPublish) {
        await onPublish({
          scenes,
          status: 'published',
          publishedAt: new Date().toISOString()
        });
      }

      toast.success('Tour published successfully!');
    } catch (error) {
      toast.error('Failed to publish tour');
      console.error('Publish error:', error);
    }
  }, [scenes, onPublish, handleSave]);

  // Delete scene
  const handleDeleteScene = useCallback((index: number) => {
    if (scenes.length <= 1) {
      toast.error('Cannot delete the last scene');
      return;
    }

    setScenes(prev => prev.filter((_, i) => i !== index));
    setCurrentSceneIndex(prev => Math.min(prev, scenes.length - 2));
    setHasUnsavedChanges(true);
    toast.success('Scene deleted');
  }, [scenes.length]);

  return (
    <div className={`simple-tour-editor h-screen flex flex-col ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b bg-background">
        <div className="flex items-center gap-4">
          <h1 className="text-xl font-semibold">Tour Editor</h1>
          {hasUnsavedChanges && (
            <Badge variant="secondary">Unsaved Changes</Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsPreviewMode(!isPreviewMode)}
          >
            <Eye className="w-4 h-4 mr-2" />
            {isPreviewMode ? 'Edit' : 'Preview'}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleSave}
            disabled={!hasUnsavedChanges}
          >
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
          
          <Button
            size="sm"
            onClick={handlePublish}
          >
            <Play className="w-4 h-4 mr-2" />
            Publish
          </Button>
          
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
            >
              Close
            </Button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Scene Management */}
        <div className="w-80 border-r bg-muted/30 flex flex-col">
          <div className="p-4 border-b">
            <h2 className="font-semibold mb-4">Scenes ({scenes.length})</h2>
            
            {/* Upload Button */}
            <Button
              variant="outline"
              className="w-full"
              onClick={() => {
                const input = document.createElement('input');
                input.type = 'file';
                input.multiple = true;
                input.accept = 'image/*';
                input.onchange = (e) => {
                  const files = (e.target as HTMLInputElement).files;
                  if (files) handleFileUpload(files);
                };
                input.click();
              }}
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload 360° Images
            </Button>
          </div>
          
          {/* Scene List */}
          <div className="flex-1 overflow-y-auto p-4 space-y-2">
            {scenes.map((scene, index) => (
              <Card
                key={scene.id}
                className={`cursor-pointer transition-colors ${
                  index === currentSceneIndex 
                    ? 'border-primary bg-primary/5' 
                    : 'hover:bg-muted/50'
                }`}
                onClick={() => setCurrentSceneIndex(index)}
              >
                <CardContent className="p-3">
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{scene.name}</p>
                      <p className="text-xs text-muted-foreground">
                        Scene {index + 1}
                      </p>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteScene(index);
                      }}
                      disabled={scenes.length <= 1}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Center - Viewer */}
        <div className="flex-1 flex flex-col">
          {currentScene ? (
            <>
              {/* Scene Info */}
              <div className="p-4 border-b bg-background">
                <div className="flex items-center justify-between">
                  <h3 className="font-medium">{currentScene.name}</h3>
                  <Badge variant="outline">
                    Scene {currentSceneIndex + 1} of {scenes.length}
                  </Badge>
                </div>
              </div>
              
              {/* Viewer */}
              <div className="flex-1">
                <PhotoSphereTourViewer
                  scenes={scenes}
                  tourId={tourId}
                  initialSceneId={currentScene.id}
                  enableVirtualTour={scenes.length > 1}
                  enableGallery={scenes.length > 1}
                  enableSettings={true}
                  showControls={true}
                  onSceneChange={(sceneId) => {
                    const sceneIndex = scenes.findIndex(s => s.id === sceneId);
                    if (sceneIndex !== -1) {
                      setCurrentSceneIndex(sceneIndex);
                    }
                  }}
                  className="h-full"
                />
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center bg-muted">
              <div className="text-center">
                <Camera className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-semibold mb-2">No Scenes</h3>
                <p className="text-muted-foreground mb-4">Upload 360° images to start creating your tour</p>
                <Button
                  onClick={() => {
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.multiple = true;
                    input.accept = 'image/*';
                    input.onchange = (e) => {
                      const files = (e.target as HTMLInputElement).files;
                      if (files) handleFileUpload(files);
                    };
                    input.click();
                  }}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Upload Images
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Right Panel - Scene Properties */}
        {currentScene && (
          <div className="w-80 border-l bg-muted/30 p-4">
            <h2 className="font-semibold mb-4">Scene Properties</h2>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="scene-name">Scene Name</Label>
                <Input
                  id="scene-name"
                  value={currentScene.name}
                  onChange={(e) => {
                    const newName = e.target.value;
                    setScenes(prev => prev.map((scene, index) => 
                      index === currentSceneIndex
                        ? { ...scene, name: newName }
                        : scene
                    ));
                    setHasUnsavedChanges(true);
                  }}
                />
              </div>
              
              <div>
                <Label htmlFor="scene-description">Description</Label>
                <Textarea
                  id="scene-description"
                  value={currentScene.description || ''}
                  onChange={(e) => {
                    const newDescription = e.target.value;
                    setScenes(prev => prev.map((scene, index) => 
                      index === currentSceneIndex
                        ? { ...scene, description: newDescription }
                        : scene
                    ));
                    setHasUnsavedChanges(true);
                  }}
                  rows={3}
                />
              </div>
              
              <div className="pt-4 border-t">
                <h3 className="font-medium mb-2">Tour Statistics</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Total Scenes:</span>
                    <span>{scenes.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Current Scene:</span>
                    <span>{currentSceneIndex + 1}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SimpleTourEditor;
