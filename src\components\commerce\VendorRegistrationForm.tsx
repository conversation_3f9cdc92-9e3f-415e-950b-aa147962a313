/**
 * VendorRegistrationForm Component
 * Complete vendor onboarding form with document upload
 * Mobile-first responsive design following VRT patterns
 */

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Upload, 
  CheckCircle, 
  AlertCircle, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Building, 
  FileText,
  MessageCircle,
  Camera,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { commerceService } from '@/services/commerce/CommerceService';

const vendorRegistrationSchema = z.object({
  name: z.string().min(2, 'Business name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone: z.string().min(10, 'Please enter a valid phone number'),
  whatsapp_number: z.string().min(10, 'Please enter a valid WhatsApp number'),
  business_address: z.string().min(10, 'Please enter your business address'),
  business_description: z.string().min(20, 'Please provide a detailed business description'),
  logo_url: z.string().optional(),
  terms_accepted: z.boolean().refine(val => val === true, 'You must accept the terms and conditions')
});

type VendorRegistrationData = z.infer<typeof vendorRegistrationSchema>;

interface VendorRegistrationFormProps {
  onSuccess?: (vendorId: string) => void;
  onCancel?: () => void;
  className?: string;
}

const VendorRegistrationForm = ({
  onSuccess,
  onCancel,
  className
}: VendorRegistrationFormProps) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    watch,
    setValue,
    trigger
  } = useForm<VendorRegistrationData>({
    resolver: zodResolver(vendorRegistrationSchema),
    mode: 'onChange'
  });

  const totalSteps = 4;
  const progress = (currentStep / totalSteps) * 100;

  const handleNext = async () => {
    let fieldsToValidate: (keyof VendorRegistrationData)[] = [];
    
    switch (currentStep) {
      case 1:
        fieldsToValidate = ['name', 'email'];
        break;
      case 2:
        fieldsToValidate = ['phone', 'whatsapp_number', 'business_address'];
        break;
      case 3:
        fieldsToValidate = ['business_description'];
        break;
    }

    const isStepValid = await trigger(fieldsToValidate);
    
    if (isStepValid && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        toast.error('Logo file size must be less than 5MB');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setLogoPreview(result);
        setValue('logo_url', result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleDocumentUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter(file => {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast.error(`${file.name} is too large. Maximum size is 10MB.`);
        return false;
      }
      return true;
    });

    setUploadedFiles(prev => [...prev, ...validFiles]);
  };

  const removeDocument = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const onSubmit = async (data: VendorRegistrationData) => {
    try {
      setIsSubmitting(true);

      // Register vendor
      const vendor = await commerceService.registerVendor({
        name: data.name,
        email: data.email,
        phone: data.phone,
        whatsapp_number: data.whatsapp_number,
        business_address: data.business_address,
        business_description: data.business_description,
        logo_url: data.logo_url
      });

      toast.success('Registration submitted successfully!', {
        description: 'Your application is under review. You will be notified via WhatsApp once approved.'
      });

      if (onSuccess) {
        onSuccess(vendor.id);
      }
    } catch (error) {
      console.error('Registration error:', error);
      toast.error('Failed to submit registration. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const StepIndicator = () => (
    <div className="mb-8">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-bold">Vendor Registration</h2>
        <Badge variant="outline">
          Step {currentStep} of {totalSteps}
        </Badge>
      </div>
      <Progress value={progress} className="h-2" />
      <div className="flex justify-between mt-2 text-sm text-muted-foreground">
        <span>Basic Info</span>
        <span>Contact</span>
        <span>Business</span>
        <span>Review</span>
      </div>
    </div>
  );

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div className="text-center mb-6">
              <User className="w-12 h-12 mx-auto mb-4 text-blue-600" />
              <h3 className="text-xl font-semibold">Basic Information</h3>
              <p className="text-muted-foreground">Let's start with your business basics</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Business Name *</Label>
                <Input
                  id="name"
                  placeholder="Enter your business name"
                  {...register('name')}
                  className={cn(errors.name && 'border-red-500')}
                />
                {errors.name && (
                  <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="email">Business Email *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register('email')}
                  className={cn(errors.email && 'border-red-500')}
                />
                {errors.email && (
                  <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="logo">Business Logo (Optional)</Label>
                <div className="mt-2">
                  {logoPreview ? (
                    <div className="relative w-32 h-32 mx-auto">
                      <img
                        src={logoPreview}
                        alt="Logo preview"
                        className="w-full h-full object-cover rounded-lg border"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute -top-2 -right-2 w-6 h-6 p-0"
                        onClick={() => {
                          setLogoPreview(null);
                          setValue('logo_url', '');
                        }}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ) : (
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <Camera className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                      <p className="text-sm text-gray-600 mb-2">Upload your business logo</p>
                      <Input
                        type="file"
                        accept="image/*"
                        onChange={handleLogoUpload}
                        className="hidden"
                        id="logo-upload"
                      />
                      <Label htmlFor="logo-upload" className="cursor-pointer">
                        <Button type="button" variant="outline" size="sm">
                          Choose File
                        </Button>
                      </Label>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div className="text-center mb-6">
              <Phone className="w-12 h-12 mx-auto mb-4 text-blue-600" />
              <h3 className="text-xl font-semibold">Contact Information</h3>
              <p className="text-muted-foreground">How can customers reach you?</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  placeholder="+234 xxx xxx xxxx"
                  {...register('phone')}
                  className={cn(errors.phone && 'border-red-500')}
                />
                {errors.phone && (
                  <p className="text-sm text-red-500 mt-1">{errors.phone.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="whatsapp_number">WhatsApp Number *</Label>
                <Input
                  id="whatsapp_number"
                  placeholder="+234 xxx xxx xxxx"
                  {...register('whatsapp_number')}
                  className={cn(errors.whatsapp_number && 'border-red-500')}
                />
                {errors.whatsapp_number && (
                  <p className="text-sm text-red-500 mt-1">{errors.whatsapp_number.message}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  This will be used for order notifications and customer communication
                </p>
              </div>

              <div>
                <Label htmlFor="business_address">Business Address *</Label>
                <Textarea
                  id="business_address"
                  placeholder="Enter your complete business address"
                  {...register('business_address')}
                  className={cn(errors.business_address && 'border-red-500')}
                  rows={3}
                />
                {errors.business_address && (
                  <p className="text-sm text-red-500 mt-1">{errors.business_address.message}</p>
                )}
              </div>
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div className="text-center mb-6">
              <Building className="w-12 h-12 mx-auto mb-4 text-blue-600" />
              <h3 className="text-xl font-semibold">Business Details</h3>
              <p className="text-muted-foreground">Tell us about your business</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="business_description">Business Description *</Label>
                <Textarea
                  id="business_description"
                  placeholder="Describe your business, products, and services..."
                  {...register('business_description')}
                  className={cn(errors.business_description && 'border-red-500')}
                  rows={5}
                />
                {errors.business_description && (
                  <p className="text-sm text-red-500 mt-1">{errors.business_description.message}</p>
                )}
                <p className="text-xs text-muted-foreground mt-1">
                  This will be displayed on your vendor profile
                </p>
              </div>

              <div>
                <Label>Supporting Documents (Optional)</Label>
                <div className="mt-2">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <FileText className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                    <p className="text-sm text-gray-600 mb-2">
                      Upload business registration, tax certificates, etc.
                    </p>
                    <Input
                      type="file"
                      multiple
                      accept=".pdf,.jpg,.jpeg,.png"
                      onChange={handleDocumentUpload}
                      className="hidden"
                      id="document-upload"
                    />
                    <Label htmlFor="document-upload" className="cursor-pointer">
                      <Button type="button" variant="outline" size="sm">
                        <Upload className="w-4 h-4 mr-2" />
                        Choose Files
                      </Button>
                    </Label>
                  </div>

                  {uploadedFiles.length > 0 && (
                    <div className="mt-4 space-y-2">
                      {uploadedFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                          <span className="text-sm truncate">{file.name}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeDocument(index)}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 4:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="space-y-6"
          >
            <div className="text-center mb-6">
              <CheckCircle className="w-12 h-12 mx-auto mb-4 text-green-600" />
              <h3 className="text-xl font-semibold">Review & Submit</h3>
              <p className="text-muted-foreground">Please review your information</p>
            </div>

            <Card>
              <CardContent className="p-6 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Business Name</Label>
                    <p className="text-sm">{watch('name')}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Email</Label>
                    <p className="text-sm">{watch('email')}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Phone</Label>
                    <p className="text-sm">{watch('phone')}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">WhatsApp</Label>
                    <p className="text-sm">{watch('whatsapp_number')}</p>
                  </div>
                </div>
                
                <div>
                  <Label className="text-sm font-medium">Business Address</Label>
                  <p className="text-sm">{watch('business_address')}</p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium">Business Description</Label>
                  <p className="text-sm line-clamp-3">{watch('business_description')}</p>
                </div>
              </CardContent>
            </Card>

            <div className="flex items-center space-x-2">
              <input
                type="checkbox"
                id="terms"
                {...register('terms_accepted')}
                className="rounded"
              />
              <Label htmlFor="terms" className="text-sm">
                I accept the{' '}
                <a href="#" className="text-blue-600 hover:underline">
                  Terms and Conditions
                </a>{' '}
                and{' '}
                <a href="#" className="text-blue-600 hover:underline">
                  Privacy Policy
                </a>
              </Label>
            </div>
            {errors.terms_accepted && (
              <p className="text-sm text-red-500">{errors.terms_accepted.message}</p>
            )}
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className={cn("max-w-2xl mx-auto p-6", className)}>
      <Card>
        <CardHeader>
          <StepIndicator />
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)}>
            {renderStep()}

            <div className="flex justify-between mt-8">
              <Button
                type="button"
                variant="outline"
                onClick={currentStep === 1 ? onCancel : handlePrevious}
                disabled={isSubmitting}
              >
                {currentStep === 1 ? 'Cancel' : 'Previous'}
              </Button>

              {currentStep < totalSteps ? (
                <Button
                  type="button"
                  onClick={handleNext}
                  disabled={isSubmitting}
                >
                  Next
                </Button>
              ) : (
                <Button
                  type="submit"
                  disabled={!isValid || isSubmitting}
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Application'}
                </Button>
              )}
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default VendorRegistrationForm;
