import React from 'react';
import { cn } from '@/lib/utils';

interface ResponsiveAdminTableProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * A standardized, mobile-first responsive table wrapper for admin dashboard use.
 * - Horizontal scroll on small screens
 * - Full width, max-w, and min-w-0
 */
const ResponsiveAdminTable = ({ children, className }: ResponsiveAdminTableProps) => (
  <div className={cn('w-full max-w-full overflow-x-auto min-w-0', className)}>
    <table className="w-full min-w-[600px] border-collapse">
      {children}
    </table>
  </div>
);

export default ResponsiveAdminTable;
