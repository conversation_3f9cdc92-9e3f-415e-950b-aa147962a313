
import { useState, useEffect, useMemo } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, Eye, MapPin, Calendar, Play, Image } from 'lucide-react';
import { Link } from 'react-router-dom';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { supabase, Tour } from '@/lib/supabase';
import { createTourUrl } from '@/lib/slugUtils';
import EnhancedTourCard from '@/components/EnhancedTourCard';
import { getTourCardSettings } from '@/config/tourCardSettings';
import { preloadTourResources } from '@/utils/performanceOptimization';

const Showcase = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [featuredOnly, setFeaturedOnly] = useState(false);

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'property', label: 'Property' },
    { value: 'education', label: 'Education' },
    { value: 'hospitality', label: 'Hospitality' },
    { value: 'tourism', label: 'Tourism' },
    { value: 'culture', label: 'Culture' },
    { value: 'commercial', label: 'Commercial' },
    { value: 'healthcare', label: 'Healthcare' },
    { value: 'government', label: 'Government' }
  ];

  const { data: tours = [], isLoading, error } = useQuery({
    queryKey: ['showcase-tours', searchTerm, selectedCategory, featuredOnly],
    queryFn: async () => {
      let query = supabase
        .from('tours')
        .select(`
          *,
          profiles (
            full_name,
            email
          )
        `)
        .eq('status', 'published')
        .order('featured', { ascending: false })
        .order('created_at', { ascending: false });

      if (selectedCategory !== 'all') {
        query = query.eq('category', selectedCategory);
      }

      if (featuredOnly) {
        query = query.eq('featured', true);
      }

      if (searchTerm) {
        query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,location.ilike.%${searchTerm}%`);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching tours:', error);
        throw error;
      }

      return data as Tour[];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  });

  // Preload tour resources for better performance
  useEffect(() => {
    if (tours.length > 0) {
      const tourUrls = tours.slice(0, 6).map(tour => tour.embed_url).filter(Boolean);
      preloadTourResources(tourUrls);
    }
  }, [tours]);

  // Memoize tour cards for performance
  const tourCards = useMemo(() => {
    return tours.map((tour) => {
      const settings = getTourCardSettings('showcase');
      return (
        <EnhancedTourCard
          key={tour.id}
          tour={tour}
          showEmbedded={settings.showEmbedded}
          showActions={settings.showActions}
          autoLoad={settings.autoLoad}
          className="hover-lift"
        />
      );
    });
  }, [tours]);

  // Removed custom TourCard - now using EnhancedTourCard everywhere for consistency

  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />

      {/* Enhanced Hero Section with Background */}
      <section className="relative pt-32 lg:pt-40 pb-16 overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
            alt="Shopping mall and retail store virtual tour showcase"
            className="w-full h-full object-cover object-center"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-black/70"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center mb-12">
            <div className="inline-flex items-center rounded-full bg-white/10 backdrop-blur-sm border border-white/20 px-4 py-2 text-sm font-medium text-white mb-6">
              🌍 Virtual Experiences
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 drop-shadow-lg">
              Explore Virtual Tours
            </h1>
            <p className="text-xl text-white/90 max-w-3xl mx-auto drop-shadow-md">
              Discover immersive 360° experiences from around Nigeria. Step into properties,
              educational institutions, hospitality venues, and cultural landmarks.
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 sm:px-6 py-8">

        {/* Search and Filters */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  placeholder="Search tours, locations, or descriptions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-12"
                />
              </div>
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="h-12">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map(category => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Button
              variant={featuredOnly ? "default" : "outline"}
              onClick={() => setFeaturedOnly(!featuredOnly)}
              className="h-12"
            >
              <Filter className="w-4 h-4 mr-2" />
              {featuredOnly ? 'All Tours' : 'Featured Only'}
            </Button>
          </div>
        </div>

        {/* Results */}
        {isLoading ? (
          <div className="flex items-center justify-center py-16">
            <LoadingSpinner size="lg" />
          </div>
        ) : error ? (
          <div className="text-center py-16">
            <p className="text-red-600 mb-4">Failed to load tours</p>
            <Button onClick={() => window.location.reload()}>Try Again</Button>
          </div>
        ) : tours.length === 0 ? (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <Image className="w-16 h-16 mx-auto mb-4 text-gray-400" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No tours found</h3>
              <p className="text-gray-600 mb-4">
                {searchTerm || selectedCategory !== 'all' || featuredOnly
                  ? 'Try adjusting your search filters'
                  : 'No virtual tours are currently published'}
              </p>
              <Button onClick={() => {
                setSearchTerm('');
                setSelectedCategory('all');
                setFeaturedOnly(false);
              }}>
                Clear Filters
              </Button>
            </div>
          </div>
        ) : (
          <>
            <div className="flex items-center justify-between mb-6">
              <p className="text-gray-600">
                {tours.length} tour{tours.length !== 1 ? 's' : ''} found
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {tourCards}
            </div>
          </>
        )}
      </div>

      <Footer />
    </div>
  );
};

export default Showcase;
