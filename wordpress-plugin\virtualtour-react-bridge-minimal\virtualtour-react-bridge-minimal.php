<?php
/*
Plugin Name: VirtualRealTour React Bridge (Minimal)
Description: Minimal version for testing - enables CORS and basic API endpoints for React integration.
Version: 1.0.0
Author: VirtualRealTour Dev Team
*/

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Simple CORS handler
 */
add_action('init', function () {
    if (isset($_SERVER['HTTP_ORIGIN'])) {
        $allowed_origins = array(
            'http://localhost:3000',
            'http://localhost:3001', 
            'http://localhost:5173',
            'https://virtualrealtour.ng',
            'https://staging.virtualrealtour.ng',
            'https://tour-nigeria-vista.vercel.app'
        );
        
        if (in_array($_SERVER['HTTP_ORIGIN'], $allowed_origins)) {
            header("Access-Control-Allow-Origin: " . $_SERVER['HTTP_ORIGIN']);
            header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
            header("Access-Control-Allow-Credentials: true");
            header("Access-Control-Allow-Headers: Authorization, Content-Type, X-WP-Nonce");
        }
    }
    
    // Handle preflight OPTIONS requests
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        status_header(200);
        exit();
    }
});

/**
 * Add ping endpoint
 */
add_action('rest_api_init', function () {
    register_rest_route('virtualtour/v1', '/ping', array(
        'methods' => 'GET',
        'callback' => function () {
            return new WP_REST_Response(array(
                'status' => 'connected',
                'timestamp' => current_time('mysql'),
                'wordpress_version' => get_bloginfo('version'),
                'woocommerce_active' => class_exists('WooCommerce'),
                'wpvr_active' => class_exists('WPVR')
            ), 200);
        },
        'permission_callback' => '__return_true'
    ));
});

/**
 * Add WooCommerce test endpoint
 */
add_action('rest_api_init', function () {
    register_rest_route('virtualtour/v1', '/woo-test', array(
        'methods' => 'GET',
        'callback' => function () {
            if (!class_exists('WooCommerce')) {
                return new WP_REST_Response(array(
                    'status' => 'error',
                    'message' => 'WooCommerce plugin is not active'
                ), 400);
            }
            
            $products = wc_get_products(array('limit' => 3));
            
            return new WP_REST_Response(array(
                'status' => 'woocommerce_connected',
                'products_count' => count($products),
                'woocommerce_version' => WC()->version
            ), 200);
        },
        'permission_callback' => '__return_true'
    ));
});

/**
 * Simple admin page
 */
add_action('admin_menu', function () {
    add_options_page(
        'VRT Bridge (Minimal)',
        'VRT Bridge',
        'manage_options',
        'vrt-bridge-minimal',
        function () {
            echo '<div class="wrap">';
            echo '<h1>VirtualRealTour React Bridge (Minimal)</h1>';
            echo '<div class="card" style="padding: 20px; background: white; margin: 20px 0;">';
            echo '<h2>Status</h2>';
            echo '<p><strong>Plugin:</strong> ✅ Active</p>';
            echo '<p><strong>WooCommerce:</strong> ' . (class_exists('WooCommerce') ? '✅ Active' : '❌ Not Active') . '</p>';
            echo '<p><strong>WPVR:</strong> ' . (class_exists('WPVR') ? '✅ Active' : '❌ Not Active') . '</p>';
            echo '</div>';
            
            echo '<div class="card" style="padding: 20px; background: white; margin: 20px 0;">';
            echo '<h2>API Endpoints</h2>';
            echo '<p><strong>Ping:</strong> <code>' . home_url('/wp-json/virtualtour/v1/ping') . '</code></p>';
            echo '<p><strong>WooCommerce Test:</strong> <code>' . home_url('/wp-json/virtualtour/v1/woo-test') . '</code></p>';
            echo '</div>';
            
            echo '<div class="card" style="padding: 20px; background: white; margin: 20px 0;">';
            echo '<h2>React Configuration</h2>';
            echo '<pre style="background: #f4f4f4; padding: 15px; border-radius: 4px;">';
            echo 'VITE_WORDPRESS_URL=' . home_url() . "\n";
            echo 'VITE_WOO_CONSUMER_KEY=ck_405631cb8df6b2245d0695f814f05e47ca6befbd' . "\n";
            echo 'VITE_WOO_CONSUMER_SECRET=cs_51c2556e9ad57d2ef40c82f6e3bc266e99103f98' . "\n";
            echo 'VITE_WP_APP_PASSWORD=JiQN tXNq ieO1 hRDv 6gjd dtQj' . "\n";
            echo 'VITE_WP_USERNAME=iwalk-wp-application-pass';
            echo '</pre>';
            echo '</div>';
            echo '</div>';
        }
    );
});

/**
 * Activation hook
 */
register_activation_hook(__FILE__, function () {
    flush_rewrite_rules();
});

/**
 * Deactivation hook
 */
register_deactivation_hook(__FILE__, function () {
    flush_rewrite_rules();
});
?>
