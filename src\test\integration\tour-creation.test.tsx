/**
 * Tour Creation Integration Tests
 * End-to-end tests for the complete tour creation workflow
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AdvancedTourEditor } from '@/components/tour-editor/AdvancedTourEditor';
import { AssetUploader } from '@/components/assets/AssetUploader';
import { 
  setupTestEnvironment, 
  createMockFile, 
  mockSupabase 
} from '@/test/utils/psvTestUtils';

// Mock all dependencies
vi.mock('@/lib/photosphere/factory');
vi.mock('@/lib/assets/assetManager');
vi.mock('@/hooks/useVirtualTour');
vi.mock('@/hooks/useMarkers');
vi.mock('@/hooks/useAssets');
vi.mock('@/lib/supabase', () => ({ supabase: mockSupabase }));

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  }
}));

describe('Tour Creation Integration', () => {
  let cleanup: () => void;
  let user: ReturnType<typeof userEvent.setup>;

  beforeEach(() => {
    cleanup = setupTestEnvironment();
    user = userEvent.setup();
    
    // Mock successful asset upload
    const { assetManager } = require('@/lib/assets/assetManager');
    assetManager.uploadAsset = vi.fn().mockResolvedValue({
      id: 'test-asset-1',
      filename: 'test-panorama.jpg',
      originalName: 'test-panorama.jpg',
      size: 1024 * 1024,
      width: 4096,
      height: 2048,
      aspectRatio: 2,
      format: 'image/jpeg',
      mimeType: 'image/jpeg',
      uploadedAt: new Date().toISOString(),
      optimizedVersions: {
        original: '/uploads/test-panorama.jpg',
        thumbnail: '/uploads/test-panorama_thumb.jpg',
        preview: '/uploads/test-panorama_preview.jpg'
      },
      validation: {
        isValid: true,
        isPanorama: true,
        errors: [],
        warnings: []
      }
    });

    // Mock hooks
    const { useVirtualTour } = require('@/hooks/useVirtualTour');
    const { useMarkers } = require('@/hooks/useMarkers');
    const { useAssets } = require('@/hooks/useAssets');

    useVirtualTour.mockReturnValue({
      tour: null,
      currentNode: null,
      currentNodeId: null,
      isLoading: false,
      error: null,
      loadTour: vi.fn(),
      navigateToNode: vi.fn(),
      attachToPSV: vi.fn(),
      detachFromPSV: vi.fn()
    });

    useMarkers.mockReturnValue({
      markers: [],
      addMarker: vi.fn(),
      removeMarker: vi.fn(),
      setMarkers: vi.fn(),
      attachToPSV: vi.fn(),
      detachFromPSV: vi.fn(),
      createProductMarker: vi.fn(),
      createNavigationMarker: vi.fn(),
      isAttached: false,
      markerCount: 0
    });

    useAssets.mockReturnValue({
      assets: [],
      uploadAssets: vi.fn().mockResolvedValue([]),
      isUploading: false,
      error: null
    });
  });

  afterEach(() => {
    cleanup();
    vi.clearAllMocks();
  });

  describe('Complete Tour Creation Workflow', () => {
    it('should create a tour from start to finish', async () => {
      const onSave = vi.fn();
      const onPublish = vi.fn();

      render(
        <AdvancedTourEditor
          onSave={onSave}
          onPublish={onPublish}
        />
      );

      // Step 1: Upload panoramic images
      const fileInput = screen.getByRole('button', { name: /upload/i });
      expect(fileInput).toBeInTheDocument();

      // Create mock files
      const file1 = createMockFile('living-room.jpg', 'image/jpeg', 2 * 1024 * 1024);
      const file2 = createMockFile('kitchen.jpg', 'image/jpeg', 1.5 * 1024 * 1024);

      // Simulate file upload
      const uploadArea = screen.getByText(/drop 360° images here/i).closest('div');
      expect(uploadArea).toBeInTheDocument();

      // Mock file drop
      fireEvent.drop(uploadArea!, {
        dataTransfer: {
          files: [file1, file2]
        }
      });

      // Wait for upload to complete
      await waitFor(() => {
        expect(screen.getByText(/living-room.jpg/i)).toBeInTheDocument();
        expect(screen.getByText(/kitchen.jpg/i)).toBeInTheDocument();
      });

      // Step 2: Select first scene
      const firstScene = screen.getByText(/living-room.jpg/i);
      await user.click(firstScene);

      // Step 3: Add markers to the scene
      const addMarkerButton = screen.getByRole('button', { name: /add marker/i });
      await user.click(addMarkerButton);

      // Select marker type
      const markerTypeSelect = screen.getByRole('combobox');
      await user.click(markerTypeSelect);
      await user.click(screen.getByText(/product/i));

      // Click on viewer to place marker
      const viewer = screen.getByTestId('psv-container');
      await user.click(viewer);

      // Step 4: Configure the product marker
      await waitFor(() => {
        expect(screen.getByText(/marker properties/i)).toBeInTheDocument();
      });

      const productNameInput = screen.getByLabelText(/product name/i);
      await user.clear(productNameInput);
      await user.type(productNameInput, 'Modern Sofa');

      const productPriceInput = screen.getByLabelText(/price/i);
      await user.clear(productPriceInput);
      await user.type(productPriceInput, '1200');

      // Step 5: Add navigation link to second scene
      await user.click(addMarkerButton);
      await user.click(markerTypeSelect);
      await user.click(screen.getByText(/navigation/i));
      await user.click(viewer);

      // Configure navigation marker
      const targetSceneSelect = screen.getByLabelText(/target scene/i);
      await user.click(targetSceneSelect);
      await user.click(screen.getByText(/kitchen/i));

      // Step 6: Save the tour
      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      await waitFor(() => {
        expect(onSave).toHaveBeenCalledWith(
          expect.objectContaining({
            scenes: expect.arrayContaining([
              expect.objectContaining({
                name: 'living-room',
                markers: expect.arrayContaining([
                  expect.objectContaining({
                    type: 'product',
                    productData: expect.objectContaining({
                      name: 'Modern Sofa',
                      price: 1200
                    })
                  }),
                  expect.objectContaining({
                    type: 'navigation'
                  })
                ])
              })
            ])
          })
        );
      });

      // Step 7: Publish the tour
      const publishButton = screen.getByRole('button', { name: /publish/i });
      await user.click(publishButton);

      await waitFor(() => {
        expect(onPublish).toHaveBeenCalledWith(
          expect.objectContaining({
            status: 'published',
            publishedAt: expect.any(String)
          })
        );
      });
    });

    it('should handle asset upload errors gracefully', async () => {
      // Mock upload failure
      const { assetManager } = require('@/lib/assets/assetManager');
      assetManager.uploadAsset.mockRejectedValue(new Error('Upload failed'));

      render(<AdvancedTourEditor />);

      const uploadArea = screen.getByText(/drop 360° images here/i).closest('div');
      const file = createMockFile('invalid-image.txt', 'text/plain');

      fireEvent.drop(uploadArea!, {
        dataTransfer: {
          files: [file]
        }
      });

      await waitFor(() => {
        expect(screen.getByText(/upload failed/i)).toBeInTheDocument();
      });
    });

    it('should validate panoramic images', async () => {
      const { assetManager } = require('@/lib/assets/assetManager');
      assetManager.uploadAsset.mockResolvedValue({
        id: 'test-asset',
        validation: {
          isValid: true,
          isPanorama: false,
          errors: [],
          warnings: ['Image may not be a proper 360° panorama']
        }
      });

      render(<AdvancedTourEditor />);

      const uploadArea = screen.getByText(/drop 360° images here/i).closest('div');
      const file = createMockFile('regular-image.jpg', 'image/jpeg');

      fireEvent.drop(uploadArea!, {
        dataTransfer: {
          files: [file]
        }
      });

      await waitFor(() => {
        expect(screen.getByText(/may not be a proper 360° panorama/i)).toBeInTheDocument();
      });
    });
  });

  describe('Asset Management Integration', () => {
    it('should upload and manage assets', async () => {
      const { useAssets } = require('@/hooks/useAssets');
      const mockUploadAssets = vi.fn().mockResolvedValue([
        {
          id: 'asset-1',
          originalName: 'panorama.jpg',
          validation: { isValid: true, isPanorama: true, errors: [], warnings: [] }
        }
      ]);

      useAssets.mockReturnValue({
        assets: [],
        uploadAssets: mockUploadAssets,
        isUploading: false,
        error: null
      });

      const onUploadComplete = vi.fn();

      render(
        <AssetUploader
          onUploadComplete={onUploadComplete}
          maxFiles={5}
        />
      );

      const file = createMockFile('panorama.jpg', 'image/jpeg', 2 * 1024 * 1024);
      const uploadArea = screen.getByText(/drag and drop/i).closest('div');

      fireEvent.drop(uploadArea!, {
        dataTransfer: {
          files: [file]
        }
      });

      await waitFor(() => {
        expect(mockUploadAssets).toHaveBeenCalledWith([file]);
      });
    });

    it('should enforce file limits', async () => {
      render(
        <AssetUploader
          maxFiles={2}
        />
      );

      const files = [
        createMockFile('file1.jpg'),
        createMockFile('file2.jpg'),
        createMockFile('file3.jpg') // This should be rejected
      ];

      const uploadArea = screen.getByText(/drag and drop/i).closest('div');

      fireEvent.drop(uploadArea!, {
        dataTransfer: {
          files
        }
      });

      await waitFor(() => {
        expect(screen.getByText(/maximum 2 files allowed/i)).toBeInTheDocument();
      });
    });
  });

  describe('Real-time Collaboration', () => {
    it('should sync changes across multiple editors', async () => {
      const { useVirtualTour } = require('@/hooks/useVirtualTour');
      
      const mockTour = {
        id: 'collaborative-tour',
        nodes: [
          {
            id: 'scene-1',
            name: 'Living Room',
            panorama: '/images/living-room.jpg'
          }
        ]
      };

      useVirtualTour.mockReturnValue({
        tour: mockTour,
        currentNode: mockTour.nodes[0],
        currentNodeId: 'scene-1',
        isLoading: false,
        error: null,
        navigateToNode: vi.fn(),
        attachToPSV: vi.fn(),
        detachFromPSV: vi.fn()
      });

      render(
        <AdvancedTourEditor
          tourId="collaborative-tour"
        />
      );

      await waitFor(() => {
        expect(screen.getByText(/Living Room/i)).toBeInTheDocument();
      });

      // Simulate real-time update
      const updatedTour = {
        ...mockTour,
        nodes: [
          {
            ...mockTour.nodes[0],
            name: 'Updated Living Room'
          }
        ]
      };

      useVirtualTour.mockReturnValue({
        ...useVirtualTour(),
        tour: updatedTour,
        currentNode: updatedTour.nodes[0]
      });

      // Re-render to simulate real-time update
      render(
        <AdvancedTourEditor
          tourId="collaborative-tour"
        />
      );

      await waitFor(() => {
        expect(screen.getByText(/Updated Living Room/i)).toBeInTheDocument();
      });
    });
  });

  describe('Error Recovery', () => {
    it('should recover from PSV initialization errors', async () => {
      const { PSVFactory } = require('@/lib/photosphere/factory');
      
      // First attempt fails, second succeeds
      PSVFactory.createVirtualTour
        .mockRejectedValueOnce(new Error('WebGL context lost'))
        .mockResolvedValueOnce({
          viewer: {},
          plugins: {},
          destroy: vi.fn()
        });

      render(<AdvancedTourEditor />);

      // Should show error initially
      await waitFor(() => {
        expect(screen.getByText(/webgl context lost/i)).toBeInTheDocument();
      });

      // Click retry
      const retryButton = screen.getByRole('button', { name: /retry/i });
      await user.click(retryButton);

      // Should recover
      await waitFor(() => {
        expect(screen.queryByText(/webgl context lost/i)).not.toBeInTheDocument();
      });
    });
  });
});
