/**
 * Clean Tour Embedding Utilities
 * Remove busy controls and create clean tour displays
 */

/**
 * Clean up tour embed URLs to remove busy controls and create minimal displays
 */
export const getCleanEmbedUrl = (originalUrl: string): string => {
  try {
    const url = new URL(originalUrl);
    
    // Panoee tours - aggressive UI hiding and sound disabled
    if (url.hostname.includes('panoee.com')) {
      // Remove all existing parameters and add clean ones
      url.search = '';
      url.searchParams.set('embed', '1');
      url.searchParams.set('ui', '0');
      url.searchParams.set('controls', '0');
      url.searchParams.set('info', '0');
      url.searchParams.set('share', '0');
      url.searchParams.set('logo', '0');
      url.searchParams.set('title', '0');
      url.searchParams.set('autoplay', '0');
      url.searchParams.set('muted', '1');
      url.searchParams.set('sound', '0');
      url.searchParams.set('audio', '0');
      url.searchParams.set('minimal', '1');
      url.searchParams.set('clean', '1');
      url.searchParams.set('allowfullscreen', '0');
      url.searchParams.set('allowautoplay', '0');
      url.searchParams.set('allowmicrophone', '0');
      url.searchParams.set('allowcamera', '0');
      url.searchParams.set('allowgeolocation', '0');
      url.searchParams.set('allowpaymentrequest', '0');
      url.searchParams.set('allowencryptedmedia', '0');
      return url.toString();
    }
    
    // TourMKR tours - aggressive UI hiding and sound disabled
    if (url.hostname.includes('tourmkr.com')) {
      // Clean URL and add minimal parameters
      url.search = '';
      url.searchParams.set('embed', '1');
      url.searchParams.set('ui', '0');
      url.searchParams.set('controls', '0');
      url.searchParams.set('info', '0');
      url.searchParams.set('share', '0');
      url.searchParams.set('logo', '0');
      url.searchParams.set('autoplay', '0');
      url.searchParams.set('muted', '1');
      url.searchParams.set('sound', '0');
      url.searchParams.set('audio', '0');
      url.searchParams.set('minimal', '1');
      url.searchParams.set('allowfullscreen', '0');
      url.searchParams.set('allowautoplay', '0');
      url.searchParams.set('allowmicrophone', '0');
      url.searchParams.set('allowcamera', '0');
      return url.toString();
    }
    
    // MassInteract tours - clean embedding and sound disabled
    if (url.hostname.includes('massinteract.com')) {
      // Add clean parameters for MassInteract
      url.searchParams.set('embed', '1');
      url.searchParams.set('ui', '0');
      url.searchParams.set('controls', '0');
      url.searchParams.set('branding', '0');
      url.searchParams.set('autoplay', '0');
      url.searchParams.set('muted', '1');
      url.searchParams.set('sound', '0');
      url.searchParams.set('audio', '0');
      url.searchParams.set('minimal', '1');
      url.searchParams.set('clean', '1');
      url.searchParams.set('allowfullscreen', '0');
      url.searchParams.set('allowautoplay', '0');
      url.searchParams.set('allowmicrophone', '0');
      url.searchParams.set('allowcamera', '0');
      return url.toString();
    }

    // Kuula tours - clean embedding with minimal UI
    if (url.hostname.includes('kuula.co')) {
      // Kuula has excellent embedding support with clean parameters
      url.searchParams.set('embed', '1');
      url.searchParams.set('ui', '0');
      url.searchParams.set('controls', '0');
      url.searchParams.set('info', '0');
      url.searchParams.set('share', '0');
      url.searchParams.set('logo', '0');
      url.searchParams.set('title', '0');
      url.searchParams.set('autoplay', '0');
      url.searchParams.set('muted', '1');
      url.searchParams.set('sound', '0');
      url.searchParams.set('audio', '0');
      url.searchParams.set('minimal', '1');
      url.searchParams.set('clean', '1');
      url.searchParams.set('branding', '0');
      url.searchParams.set('allowfullscreen', '0');
      url.searchParams.set('allowautoplay', '0');
      url.searchParams.set('allowmicrophone', '0');
      url.searchParams.set('allowcamera', '0');
      return url.toString();
    }
    
    // Generic approach for other platforms
    // Try common parameter names for hiding UI elements and disabling sound
    url.searchParams.set('embed', 'true');
    url.searchParams.set('minimal', 'true');
    url.searchParams.set('ui', 'clean');
    url.searchParams.set('autoplay', 'false');
    url.searchParams.set('muted', 'true');
    url.searchParams.set('sound', 'false');
    url.searchParams.set('audio', 'false');
    
    return url.toString();
    
  } catch (error) {
    // If URL parsing fails, return original
    return originalUrl;
  }
};

/**
 * Get iframe attributes for clean embedding
 */
export const getCleanIframeProps = () => ({
  style: {
    border: 'none',
    outline: 'none',
    background: 'transparent'
  },
  // Removed autoplay and microphone from allow list to prevent sound
  allow: 'vr; xr; accelerometer; gyroscope; fullscreen; camera; geolocation;',
  allowFullScreen: true,
  webkitallowfullscreen: 'true',
  mozallowfullscreen: 'true',
  // Remove referrer to prevent tracking
  referrerPolicy: 'no-referrer' as const,
  // Sandbox for security while allowing necessary features (removed allow-autoplay)
  sandbox: 'allow-scripts allow-same-origin allow-presentation allow-orientation-lock allow-pointer-lock'
});

/**
 * CSS styles for clean tour container
 */
export const getCleanTourStyles = () => ({
  container: {
    position: 'relative' as const,
    width: '100%',
    height: '100%',
    overflow: 'hidden',
    borderRadius: '8px',
    background: '#f8fafc'
  },
  iframe: {
    width: '100%',
    height: '100%',
    border: 'none',
    borderRadius: '8px'
  },
  overlay: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'rgba(255, 255, 255, 0.02)',
    backdropFilter: 'blur(0.5px)',
    WebkitBackdropFilter: 'blur(0.5px)',
    transition: 'all 0.3s ease',
    cursor: 'pointer',
    zIndex: 10
  },
  overlayHover: {
    background: 'rgba(255, 255, 255, 0.01)',
  }
});

/**
 * Platform-specific clean embedding configurations
 */
export const platformConfigs = {
  panoee: {
    name: 'Panoee',
    cleanParams: {
      hideUI: 'true',
      hideControls: 'true',
      hideInfo: 'true',
      hideShare: 'true',
      minimal: 'true'
    }
  },
  tourmkr: {
    name: 'TourMKR',
    cleanParams: {
      ui: 'minimal',
      controls: 'false',
      info: 'false',
      share: 'false'
    }
  },
  massinteract: {
    name: 'MassInteract',
    cleanParams: {
      embed: 'true',
      ui: 'minimal',
      controls: 'basic',
      branding: 'false'
    }
  }
};

/**
 * Detect tour platform from URL
 */
export const detectTourPlatform = (url: string): keyof typeof platformConfigs | 'unknown' => {
  try {
    const urlObj = new URL(url);
    
    if (urlObj.hostname.includes('panoee.com')) return 'panoee';
    if (urlObj.hostname.includes('tourmkr.com')) return 'tourmkr';
    if (urlObj.hostname.includes('massinteract.com')) return 'massinteract';
    
    return 'unknown';
  } catch {
    return 'unknown';
  }
};
