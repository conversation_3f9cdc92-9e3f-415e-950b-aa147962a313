/**
 * WooCommerce Integration Test Page
 * Test your local WordPress setup with real API calls
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  TestTube, 
  CheckCircle, 
  XCircle, 
  Loader2,
  ShoppingCart,
  Package,
  Globe,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';

// Import our services
import { wooCommerceService, type WooCommerceProduct } from '@/services/commerce/WooCommerceService';
import VirtualTourOverlay from '@/components/tour-overlay/VirtualTourOverlay';

const WooCommerceTest: React.FC = () => {
  const [isConnected, setIsConnected] = useState<boolean | null>(null);
  const [isTesting, setIsTesting] = useState(false);
  const [products, setProducts] = useState<WooCommerceProduct[]>([]);
  const [testProductId, setTestProductId] = useState('');
  const [connectionDetails, setConnectionDetails] = useState<any>(null);

  // Test WooCommerce connection
  const testConnection = async () => {
    setIsTesting(true);
    setIsConnected(null);

    try {
      // Step 1: Test WordPress connection using plugin endpoint
      console.log('Testing WordPress connection...');
      const pingEndpoint = import.meta.env.VITE_WP_PING_ENDPOINT || 'http://localhost:10090/wp-json/virtualtour/v1/ping';
      const wpResponse = await fetch(pingEndpoint);
      const wpTest = await wpResponse.json();
      console.log('WordPress ping result:', wpTest);

      // Step 2: Test WooCommerce connection using plugin endpoint
      console.log('Testing WooCommerce connection...');
      const wooEndpoint = import.meta.env.VITE_WOO_TEST_ENDPOINT || 'http://localhost:10090/wp-json/virtualtour/v1/woo-test';
      const wooResponse = await fetch(wooEndpoint);
      const wooTest = await wooResponse.json();
      console.log('WooCommerce test result:', wooTest);

      // Step 3: Get actual products
      console.log('Fetching products...');
      const testProducts = await wooCommerceService.getProducts({ per_page: 5 });
      console.log('Products fetched:', testProducts);

      setProducts(testProducts);
      setIsConnected(true);
      setConnectionDetails({
        baseUrl: 'http://localhost:10090',
        wordpressStatus: wpTest,
        woocommerceStatus: wooTest,
        productsFound: testProducts.length,
        authMethod: import.meta.env.VITE_WOO_AUTH_METHOD || 'consumer-key',
        pluginVersion: wpTest.plugin_version || 'Unknown',
        endpoints: {
          ping: pingEndpoint,
          wooTest: wooEndpoint
        },
        timestamp: new Date().toISOString()
      });

      toast.success(`Connected! Found ${testProducts.length} products`);
    } catch (error) {
      console.error('Connection test failed:', error);
      setIsConnected(false);
      setConnectionDetails({
        error: error instanceof Error ? error.message : 'Unknown error',
        authMethod: import.meta.env.VITE_WOO_AUTH_METHOD || 'consumer-key',
        baseUrl: 'http://localhost:10090',
        timestamp: new Date().toISOString()
      });
      toast.error(`Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsTesting(false);
    }
  };

  // Test specific product fetch
  const testProductFetch = async () => {
    if (!testProductId) {
      toast.error('Please enter a product ID');
      return;
    }

    try {
      const product = await wooCommerceService.getProduct(testProductId);
      toast.success(`Product loaded: ${product.name}`);
      console.log('Product data:', product);
    } catch (error) {
      toast.error('Failed to fetch product');
      console.error('Product fetch error:', error);
    }
  };

  // Sample hotspots for overlay test
  const sampleHotspots = [
    {
      id: 'hotspot-1',
      type: 'product' as const,
      productId: products[0]?.id || 1,
      title: 'Featured Product',
      description: 'Click to view product details',
      position: { x: 30, y: 40 }
    },
    {
      id: 'hotspot-2',
      type: 'product' as const,
      productId: products[1]?.id || 2,
      title: 'Special Offer',
      description: 'Limited time offer',
      position: { x: 70, y: 60 }
    },
    {
      id: 'hotspot-3',
      type: 'info' as const,
      title: 'Tour Information',
      description: 'Learn more about this location',
      position: { x: 50, y: 30 }
    }
  ];

  // Auto-test on mount
  useEffect(() => {
    testConnection();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            WooCommerce Integration Test
          </h1>
          <p className="text-gray-600">
            Testing connection to your local WordPress setup at localhost:10090
          </p>
        </div>

        {/* Connection Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="w-5 h-5" />
              Connection Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                {isConnected === null ? (
                  <AlertCircle className="w-5 h-5 text-yellow-500" />
                ) : isConnected ? (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-500" />
                )}
                
                <span className="font-medium">
                  {isConnected === null ? 'Testing...' : 
                   isConnected ? 'Connected' : 'Disconnected'}
                </span>
                
                {isConnected && connectionDetails && (
                  <Badge variant="secondary">
                    {connectionDetails.productsFound} products found
                  </Badge>
                )}
              </div>

              <Button
                onClick={testConnection}
                disabled={isTesting}
                variant="outline"
              >
                {isTesting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Testing...
                  </>
                ) : (
                  <>
                    <TestTube className="w-4 h-4 mr-2" />
                    Test Connection
                  </>
                )}
              </Button>
            </div>

            {connectionDetails && (
              <div className="bg-gray-50 rounded-lg p-4 text-sm">
                <pre className="text-gray-700">
                  {JSON.stringify(connectionDetails, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Product Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="w-5 h-5" />
              Product Fetch Test
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 mb-4">
              <div className="flex-1">
                <Label htmlFor="product-id">Product ID</Label>
                <Input
                  id="product-id"
                  value={testProductId}
                  onChange={(e) => setTestProductId(e.target.value)}
                  placeholder="Enter product ID to test"
                />
              </div>
              <div className="flex items-end">
                <Button onClick={testProductFetch}>
                  <TestTube className="w-4 h-4 mr-2" />
                  Test Product
                </Button>
              </div>
            </div>

            {products.length > 0 && (
              <div>
                <h4 className="font-medium mb-2">Available Products:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {products.map((product) => (
                    <div
                      key={product.id}
                      className="bg-white rounded-lg p-4 border border-gray-200"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <h5 className="font-medium text-sm truncate">
                          {product.name}
                        </h5>
                        <Badge variant="outline" className="text-xs">
                          ID: {product.id}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">
                        ₦{parseFloat(product.price).toLocaleString()}
                      </p>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setTestProductId(product.id.toString())}
                        className="w-full"
                      >
                        Use This ID
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Virtual Tour Overlay Demo */}
        {isConnected && products.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="w-5 h-5" />
                Virtual Tour Overlay Demo
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                This demo shows how product hotspots work in a virtual tour. 
                Click the hotspots to test the product overlay system.
              </p>
              
              {/* Demo tour container */}
              <div className="relative bg-gradient-to-br from-blue-400 to-purple-600 rounded-xl overflow-hidden">
                {/* Simulated tour background */}
                <div className="aspect-video bg-gradient-to-br from-blue-500 to-purple-700 flex items-center justify-center">
                  <div className="text-center text-white">
                    <h3 className="text-2xl font-bold mb-2">Virtual Tour Demo</h3>
                    <p className="text-blue-100">Click the hotspots to test product overlays</p>
                  </div>
                </div>

                {/* Virtual Tour Overlay */}
                <VirtualTourOverlay
                  tourEngine="custom"
                  hotspots={sampleHotspots}
                  onHotspotClick={(hotspot) => {
                    console.log('Hotspot clicked:', hotspot);
                  }}
                  showCartIndicator={true}
                  enableWhatsAppCheckout={true}
                />
              </div>

              <div className="mt-4 text-sm text-gray-600">
                <p><strong>Hotspot Types:</strong></p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li><span className="text-blue-600">Blue hotspots</span> - Product overlays with WooCommerce data</li>
                  <li><span className="text-green-600">Green hotspots</span> - Information overlays</li>
                  <li><span className="text-purple-600">Purple hotspots</span> - Navigation points</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Configuration Info */}
        <Card>
          <CardHeader>
            <CardTitle>Current Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h4 className="font-medium mb-2">WordPress Setup</h4>
                <ul className="space-y-1 text-gray-600">
                  <li><strong>URL:</strong> http://localhost:10090</li>
                  <li><strong>Consumer Key:</strong> ck_405631cb8df6b2245d0695f814f05e47ca6befbd</li>
                  <li><strong>App Password:</strong> JiQN tXNq ieO1 hRDv 6gjd dtQj</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Integration Features</h4>
                <ul className="space-y-1 text-gray-600">
                  <li>✅ WooCommerce REST API</li>
                  <li>✅ Application Password Auth</li>
                  <li>✅ Product Overlay System</li>
                  <li>✅ WhatsApp Checkout</li>
                  <li>✅ Virtual Tour Hotspots</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default WooCommerceTest;
