
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Eye } from 'lucide-react'
import { Link } from 'react-router-dom'

const Footer = () => {
  return (
    <footer className="bg-background border-t border-border section-gradient-subtle">
      <div className="container py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <img
                src="/lovable-uploads/vrt-logo-all.png"
                alt="VirtualRealTour Logo"
                className="w-8 h-8 object-contain"
              />
              <span className="text-xl font-bold text-foreground">VirtualRealTour</span>
            </div>
            <p className="text-muted-foreground text-sm leading-relaxed">
              Premium 360° virtual tour platform for immersive experiences worldwide.
            </p>
            <Button size="sm" className="hover-lift" asChild>
              <Link to="/showcase" className="flex items-center">
                <Eye className="w-4 h-4 mr-2" />
                View Tours
              </Link>
            </Button>
          </div>

          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Product</h3>
            <ul className="space-y-2 text-sm">
              <li><Link to="/showcase" className="text-muted-foreground hover:text-primary transition-colors link-underline touch-feedback mobile-friendly">Tours</Link></li>
              <li><Link to="/pricing" className="text-muted-foreground hover:text-primary transition-colors link-underline touch-feedback mobile-friendly">Pricing</Link></li>
              <li><Link to="/features" className="text-muted-foreground hover:text-primary transition-colors link-underline touch-feedback mobile-friendly">Features</Link></li>
              <li><Link to="/integrations" className="text-muted-foreground hover:text-primary transition-colors link-underline touch-feedback mobile-friendly">Integrations</Link></li>
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Company</h3>
            <ul className="space-y-2 text-sm">
              <li><Link to="/about" className="text-muted-foreground hover:text-primary transition-colors link-underline touch-feedback mobile-friendly">About</Link></li>
              <li><Link to="/contact" className="text-muted-foreground hover:text-primary transition-colors link-underline touch-feedback mobile-friendly">Contact</Link></li>
              <li><Link to="/careers" className="text-muted-foreground hover:text-primary transition-colors link-underline touch-feedback mobile-friendly">Careers</Link></li>
              <li><Link to="/blog" className="text-muted-foreground hover:text-primary transition-colors link-underline touch-feedback mobile-friendly">Blog</Link></li>
            </ul>
          </div>

          <div className="space-y-4">
            <h3 className="font-semibold text-foreground">Resources</h3>
            <ul className="space-y-2 text-sm">
              <li><Link to="/help" className="text-muted-foreground hover:text-primary transition-colors link-underline touch-feedback mobile-friendly">Help Center</Link></li>
              <li><Link to="/docs" className="text-muted-foreground hover:text-primary transition-colors link-underline touch-feedback mobile-friendly">Documentation</Link></li>
              <li><Link to="/api" className="text-muted-foreground hover:text-primary transition-colors link-underline touch-feedback mobile-friendly">API Reference</Link></li>
              <li><Link to="/status" className="text-muted-foreground hover:text-primary transition-colors link-underline touch-feedback mobile-friendly">Status</Link></li>
            </ul>
          </div>
        </div>

        <Separator className="my-8" />

        <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <div className="text-sm text-muted-foreground">
            © 2025 VirtualRealTour. All rights reserved.
          </div>
          
          <div className="flex flex-wrap justify-center md:justify-end gap-4 md:gap-6 text-sm">
            <Link to="/privacy" className="text-muted-foreground hover:text-primary transition-colors touch-feedback mobile-friendly">Privacy</Link>
            <Link to="/terms" className="text-muted-foreground hover:text-primary transition-colors touch-feedback mobile-friendly">Terms</Link>
            <Link to="/cookies" className="text-muted-foreground hover:text-primary transition-colors touch-feedback mobile-friendly">Cookies</Link>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
