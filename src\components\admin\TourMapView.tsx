import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MapPin, Search, Filter, Eye, Star, ArrowLeft } from 'lucide-react';
import { Tour } from '@/lib/supabase';

interface TourMapViewProps {
  tours: Tour[];
  onBack: () => void;
  onTourSelect: (tour: Tour) => void;
}

const TourMapView = ({ tours, onBack, onTourSelect }: TourMapViewProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [selectedTour, setSelectedTour] = useState<Tour | null>(null);

  // Filter tours that have location data
  const toursWithLocation = tours.filter(tour => 
    tour.location && tour.location.trim() !== ''
  );

  const filteredTours = toursWithLocation.filter(tour => {
    const matchesSearch = tour.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tour.location?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || tour.category === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      draft: 'bg-yellow-500',
      processing: 'bg-blue-500',
      published: 'bg-green-500',
      archived: 'bg-gray-500'
    };
    return colors[status] || 'bg-gray-500';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to List
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Tour Map View</h1>
            <p className="text-muted-foreground">
              {filteredTours.length} tours with location data
            </p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search tours or locations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                <SelectItem value="property">Property</SelectItem>
                <SelectItem value="education">Education</SelectItem>
                <SelectItem value="hospitality">Hospitality</SelectItem>
                <SelectItem value="tourism">Tourism</SelectItem>
                <SelectItem value="culture">Culture</SelectItem>
                <SelectItem value="commercial">Commercial</SelectItem>
                <SelectItem value="healthcare">Healthcare</SelectItem>
                <SelectItem value="government">Government</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Map Container - Placeholder for now */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Interactive Map</CardTitle>
              <p className="text-sm text-muted-foreground">
                Map integration coming soon - Mapbox will be integrated here
              </p>
            </CardHeader>
            <CardContent>
              <div className="h-96 bg-muted rounded-lg flex items-center justify-center">
                <div className="text-center">
                  <MapPin className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">Map View</h3>
                  <p className="text-sm text-muted-foreground">
                    Interactive map with tour locations will be displayed here
                  </p>
                  <div className="mt-4 space-y-2">
                    <p className="text-xs text-muted-foreground">Features to be implemented:</p>
                    <ul className="text-xs text-muted-foreground space-y-1">
                      <li>• Clustered tour markers</li>
                      <li>• Location-based filtering</li>
                      <li>• Tour preview on marker click</li>
                      <li>• Directions and routing</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tour List Sidebar */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Tours ({filteredTours.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {filteredTours.map((tour) => (
                  <div 
                    key={tour.id} 
                    className={`p-3 border rounded-lg cursor-pointer transition-colors hover:bg-muted ${
                      selectedTour?.id === tour.id ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                    onClick={() => {
                      setSelectedTour(tour);
                      onTourSelect(tour);
                    }}
                  >
                    <div className="flex items-start space-x-3">
                      <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center flex-shrink-0">
                        {tour.thumbnail_url ? (
                          <img 
                            src={tour.thumbnail_url} 
                            alt={tour.title}
                            className="w-full h-full object-cover rounded-lg"
                          />
                        ) : (
                          <MapPin className="w-5 h-5 text-muted-foreground" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium text-sm truncate">{tour.title}</h4>
                          {tour.featured && <Star className="w-3 h-3 text-yellow-500 fill-current flex-shrink-0" />}
                        </div>
                        <p className="text-xs text-muted-foreground truncate">{tour.location}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <div className={`w-2 h-2 rounded-full ${getStatusColor(tour.status)}`} />
                          <Badge variant="outline" className="text-xs">{tour.category}</Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
                
                {filteredTours.length === 0 && (
                  <div className="text-center py-8">
                    <MapPin className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">No tours found with location data</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Selected Tour Details */}
          {selectedTour && (
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="text-lg">Tour Details</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <h4 className="font-medium">{selectedTour.title}</h4>
                    <p className="text-sm text-muted-foreground">{selectedTour.description}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Location</p>
                    <p className="text-sm text-muted-foreground">{selectedTour.location}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(selectedTour.status).replace('bg-', 'bg-opacity-20 text-')}>
                      {selectedTour.status}
                    </Badge>
                    <Badge variant="outline">{selectedTour.category}</Badge>
                  </div>
                  <div className="flex items-center gap-2 pt-2">
                    <Button size="sm" className="flex-1">
                      <Eye className="w-4 h-4 mr-2" />
                      View Tour
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default TourMapView;
