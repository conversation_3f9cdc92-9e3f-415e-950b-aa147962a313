/**
 * Admin Demo Tour Management Component
 * Simple interface to set/replace the homepage demo tour
 */

import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  Globe, 
  ExternalLink, 
  Check, 
  X, 
  Eye,
  RefreshCw,
  AlertCircle,
  Star
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

const AdminDemoTour = () => {
  const [selectedTourId, setSelectedTourId] = useState<string>('');
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const queryClient = useQueryClient();

  // Fetch current demo tour
  const { data: currentDemoTour, isLoading: isDemoLoading } = useQuery({
    queryKey: ['demo-tour'],
    queryFn: async () => {
      try {
        // First check if the table exists by trying a simple query
        const { data: tableCheck, error: tableError } = await supabase
          .from('featured_tour_assignments')
          .select('*')
          .eq('section_type', 'demo')
          .limit(1);

        if (tableError) {
          console.error('Table check error:', tableError);
          // If table doesn't exist, return null
          return null;
        }

        // If table exists, try the RPC function
        const { data, error } = await supabase.rpc('get_featured_tours_for_section', {
          section_name: 'demo'
        });

        if (error) {
          console.error('RPC error, falling back to direct query:', error);
          // Fallback to direct query if RPC fails
          const { data: fallbackData, error: fallbackError } = await supabase
            .from('tours')
            .select(`
              id,
              title,
              description,
              thumbnail_url,
              embed_url,
              embed_type,
              category,
              location,
              views,
              featured_tour_assignments!inner(section_type, display_order)
            `)
            .eq('featured_tour_assignments.section_type', 'demo')
            .eq('status', 'published')
            .order('featured_tour_assignments.display_order')
            .limit(1);

          if (fallbackError) {
            console.error('Fallback query error:', fallbackError);
            return null;
          }

          return fallbackData?.[0] ? {
            ...fallbackData[0],
            tour_id: fallbackData[0].id
          } : null;
        }

        return data?.[0] || null;
      } catch (err) {
        console.error('Failed to fetch demo tour:', err);
        return null;
      }
    },
  });

  // Fetch all published tours for selection
  const { data: availableTours = [], isLoading: isToursLoading } = useQuery({
    queryKey: ['available-tours'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tours')
        .select('id, title, description, thumbnail_url, embed_url, embed_type, category, location, views')
        .eq('status', 'published')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching tours:', error);
        return [];
      }

      return data;
    },
  });

  // Set demo tour mutation
  const setDemoTourMutation = useMutation({
    mutationFn: async (tourId: string) => {
      try {
        // First, remove any existing demo tour assignments
        const { error: removeError } = await supabase
          .from('featured_tour_assignments')
          .delete()
          .eq('section_type', 'demo');

        if (removeError) {
          console.error('Remove error:', removeError);
          throw new Error(`Failed to remove current demo tour: ${removeError.message}`);
        }

        // Then add the new demo tour
        const { data: insertData, error: addError } = await supabase
          .from('featured_tour_assignments')
          .insert({
            tour_id: tourId,
            section_type: 'demo',
            display_order: 1,
            is_active: true
          })
          .select();

        if (addError) {
          console.error('Insert error:', addError);
          throw new Error(`Failed to set demo tour: ${addError.message}`);
        }

        console.log('Demo tour set successfully:', insertData);
        return tourId;
      } catch (error) {
        console.error('Mutation error:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('Demo tour updated successfully!');
      queryClient.invalidateQueries({ queryKey: ['demo-tour'] });
      queryClient.invalidateQueries({ queryKey: ['demo-tour-data'] });
      setSelectedTourId('');
    },
    onError: (error) => {
      toast.error(`Failed to update demo tour: ${error.message}`);
    },
  });

  // Remove demo tour mutation
  const removeDemoTourMutation = useMutation({
    mutationFn: async () => {
      try {
        const { error } = await supabase
          .from('featured_tour_assignments')
          .delete()
          .eq('section_type', 'demo');

        if (error) {
          console.error('Remove demo tour error:', error);
          throw new Error(`Failed to remove demo tour: ${error.message}`);
        }

        console.log('Demo tour removed successfully');
      } catch (error) {
        console.error('Remove mutation error:', error);
        throw error;
      }
    },
    onSuccess: () => {
      toast.success('Demo tour removed successfully!');
      queryClient.invalidateQueries({ queryKey: ['demo-tour'] });
      queryClient.invalidateQueries({ queryKey: ['demo-tour-data'] });
    },
    onError: (error) => {
      toast.error(`Failed to remove demo tour: ${error.message}`);
    },
  });

  const handleSetDemoTour = async () => {
    if (!selectedTourId) {
      toast.error('Please select a tour first');
      return;
    }

    // Verify the tour exists first
    const selectedTour = availableTours.find(tour => tour.id === selectedTourId);
    if (!selectedTour) {
      toast.error('Selected tour not found');
      return;
    }

    console.log('Setting demo tour:', selectedTour.title, selectedTourId);
    setDemoTourMutation.mutate(selectedTourId);
  };

  const handleRemoveDemoTour = () => {
    removeDemoTourMutation.mutate();
  };

  const selectedTour = availableTours.find(tour => tour.id === selectedTourId);

  return (
    <div className="space-y-4 md:space-y-6 p-4 md:p-0">
      <div>
        <h2 className="text-xl md:text-2xl font-bold">Demo Tour Management</h2>
        <p className="text-sm md:text-base text-muted-foreground">
          Set the tour that will be displayed in the homepage demo section
        </p>
      </div>

      {/* Current Demo Tour */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Current Demo Tour
          </CardTitle>
          <CardDescription>
            The tour currently displayed on your homepage
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isDemoLoading ? (
            <div className="flex items-center gap-2 text-muted-foreground">
              <RefreshCw className="h-4 w-4 animate-spin" />
              Loading current demo tour...
            </div>
          ) : currentDemoTour ? (
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row items-start gap-4">
                <div className="relative flex-shrink-0 w-full sm:w-auto">
                  <img
                    src={currentDemoTour.thumbnail_url || '/api/placeholder/120/80'}
                    alt={currentDemoTour.title}
                    className="w-full sm:w-24 h-32 sm:h-16 object-cover rounded-lg"
                  />
                  <div className="absolute inset-0 bg-black/40 rounded-lg flex items-center justify-center">
                    <Play className="w-8 h-8 sm:w-6 sm:h-6 text-white" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-base md:text-lg truncate">{currentDemoTour.title}</h3>
                  {currentDemoTour.description && (
                    <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                      {currentDemoTour.description}
                    </p>
                  )}
                  <div className="flex flex-wrap items-center gap-2 mt-2">
                    <Badge variant="secondary" className="text-xs">{currentDemoTour.category}</Badge>
                    {currentDemoTour.embed_type === 'iframe' && (
                      <Badge variant="outline" className="flex items-center gap-1 text-xs">
                        <ExternalLink className="w-3 h-3" />
                        External Tour
                      </Badge>
                    )}
                    <Badge variant="outline" className="flex items-center gap-1 text-xs">
                      <Eye className="w-3 h-3" />
                      {currentDemoTour.views || 0} views
                    </Badge>
                  </div>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open('/', '_blank')}
                  className="w-full sm:w-auto"
                >
                  <Eye className="w-4 h-4 mr-2" />
                  Preview on Homepage
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={handleRemoveDemoTour}
                  disabled={removeDemoTourMutation.isPending}
                  className="w-full sm:w-auto"
                >
                  {removeDemoTourMutation.isPending ? (
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  ) : (
                    <X className="w-4 h-4 mr-2" />
                  )}
                  Remove Demo Tour
                </Button>
              </div>
            </div>
          ) : (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                No demo tour is currently set. Select a tour below to display on your homepage.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Set New Demo Tour */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Set Demo Tour
          </CardTitle>
          <CardDescription>
            Choose a published tour to feature in your homepage demo section
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Tour</label>
            <Select value={selectedTourId} onValueChange={setSelectedTourId}>
              <SelectTrigger>
                <SelectValue placeholder="Choose a tour to set as demo..." />
              </SelectTrigger>
              <SelectContent>
                {isToursLoading ? (
                  <SelectItem value="loading" disabled>
                    Loading tours...
                  </SelectItem>
                ) : availableTours.length === 0 ? (
                  <SelectItem value="no-tours" disabled>
                    No published tours available
                  </SelectItem>
                ) : (
                  availableTours.map((tour) => (
                    <SelectItem key={tour.id} value={tour.id}>
                      <div className="flex items-center gap-2">
                        <span>{tour.title}</span>
                        {tour.embed_type === 'iframe' && (
                          <ExternalLink className="w-3 h-3 text-muted-foreground" />
                        )}
                      </div>
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {selectedTour && (
            <div className="p-3 md:p-4 border rounded-lg bg-muted/50">
              <div className="flex flex-col sm:flex-row items-start gap-4">
                <div className="relative flex-shrink-0 w-full sm:w-auto">
                  <img
                    src={selectedTour.thumbnail_url || '/api/placeholder/120/80'}
                    alt={selectedTour.title}
                    className="w-full sm:w-24 h-32 sm:h-16 object-cover rounded-lg"
                  />
                  <div className="absolute inset-0 bg-black/40 rounded-lg flex items-center justify-center">
                    <Play className="w-8 h-8 sm:w-6 sm:h-6 text-white" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-base truncate">{selectedTour.title}</h4>
                  {selectedTour.description && (
                    <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                      {selectedTour.description}
                    </p>
                  )}
                  <div className="flex flex-wrap items-center gap-2 mt-2">
                    <Badge variant="secondary" className="text-xs">{selectedTour.category}</Badge>
                    {selectedTour.embed_type === 'iframe' && (
                      <Badge variant="outline" className="flex items-center gap-1 text-xs">
                        <ExternalLink className="w-3 h-3" />
                        External Tour
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              onClick={handleSetDemoTour}
              disabled={!selectedTourId || setDemoTourMutation.isPending}
              className="flex-1 w-full sm:w-auto"
            >
              {setDemoTourMutation.isPending ? (
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Check className="w-4 h-4 mr-2" />
              )}
              {currentDemoTour ? 'Replace Demo Tour' : 'Set as Demo Tour'}
            </Button>
            {selectedTourId && (
              <Button
                variant="outline"
                onClick={() => setSelectedTourId('')}
                className="w-full sm:w-auto"
              >
                Clear Selection
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">How it Works</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm text-muted-foreground">
          <p>• The demo tour appears in the "Experience Our Virtual Tours" section on your homepage</p>
          <p>• Visitors can click to launch the tour in full-screen mode</p>
          <p>• You can replace the demo tour anytime by selecting a different tour</p>
          <p>• Only published tours are available for selection</p>
          <p>• External tours (iframe) and uploaded tours are both supported</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminDemoTour;
