# 📦 VirtualRealTour React Bridge Plugin - Installation Package

## 🎯 Quick Installation

### Method 1: WordPress Admin Upload (Recommended)

1. **Create ZIP file** from the `virtualtour-react-bridge` folder
2. **WordPress Admin** → Plugins → Add New → Upload Plugin
3. **Choose file** → Select the ZIP file
4. **Install Now** → Activate Plugin
5. **Settings** → VRT Bridge → Configure

### Method 2: Manual FTP Upload

1. **Upload folder** `virtualtour-react-bridge` to `/wp-content/plugins/`
2. **WordPress Admin** → Plugins → Activate "VirtualRealTour React Bridge"
3. **Settings** → VRT Bridge → Configure

---

## 📁 Plugin Structure

```
virtualtour-react-bridge/
├── virtualtour-react-bridge.php    # Main plugin file
├── readme.txt                      # WordPress plugin readme
├── assets/
│   ├── admin.css                   # Admin interface styles
│   └── admin.js                    # Admin interface JavaScript
└── CREATE_PLUGIN_ZIP.md           # This file
```

---

## 🔧 Creating the ZIP File

### Windows (PowerShell)
```powershell
# Navigate to the wordpress-plugin directory
cd wordpress-plugin

# Create ZIP file
Compress-Archive -Path "virtualtour-react-bridge" -DestinationPath "virtualtour-react-bridge.zip"
```

### macOS/Linux (Terminal)
```bash
# Navigate to the wordpress-plugin directory
cd wordpress-plugin

# Create ZIP file
zip -r virtualtour-react-bridge.zip virtualtour-react-bridge/
```

### Manual ZIP Creation
1. Right-click the `virtualtour-react-bridge` folder
2. Select "Send to" → "Compressed (zipped) folder" (Windows)
3. Or "Compress" (macOS)

---

## ✅ Post-Installation Checklist

### 1. Plugin Activation
- [ ] Plugin appears in WordPress Admin → Plugins
- [ ] Plugin is activated successfully
- [ ] No error messages during activation

### 2. Admin Interface
- [ ] Settings → VRT Bridge menu appears
- [ ] Admin page loads without errors
- [ ] System status shows plugin as active

### 3. API Endpoints
- [ ] `/wp-json/virtualtour/v1/ping` returns success
- [ ] WooCommerce test works (if WooCommerce is active)
- [ ] WPVR test works (if WPVR is active)

### 4. CORS Configuration
- [ ] Add your React app domains to allowed origins
- [ ] Enable CORS in plugin settings
- [ ] Test cross-origin requests from React app

---

## 🧪 Testing the Installation

### Test API Endpoints Manually

```bash
# Health check
curl http://your-wordpress-site.com/wp-json/virtualtour/v1/ping

# Expected response:
{
  "status": "connected",
  "timestamp": "2025-06-30 10:00:00",
  "plugin_version": "1.2.0",
  "wordpress_version": "6.4",
  "woocommerce_active": true,
  "wpvr_active": false
}
```

### Test from React App

```javascript
// Add to your React app for testing
const testWordPressConnection = async () => {
  try {
    const response = await fetch('http://localhost:10090/wp-json/virtualtour/v1/ping');
    const data = await response.json();
    console.log('WordPress connection test:', data);
  } catch (error) {
    console.error('Connection failed:', error);
  }
};
```

---

## 🔧 Configuration for Your Setup

### Environment Variables (React App)
```env
# Add to your .env.local
VITE_WORDPRESS_URL=http://localhost:10090
VITE_WOO_CONSUMER_KEY=ck_405631cb8df6b2245d0695f814f05e47ca6befbd
VITE_WOO_CONSUMER_SECRET=cs_51c2556e9ad57d2ef40c82f6e3bc266e99103f98
VITE_WP_APP_PASSWORD=JiQN tXNq ieO1 hRDv 6gjd dtQj
VITE_WP_USERNAME=iwalk-wp-application-pass
VITE_WOO_AUTH_METHOD=app-password
```

### WordPress Plugin Settings
1. **Go to** Settings → VRT Bridge
2. **Enable CORS** ✅
3. **Add allowed origins:**
   ```
   http://localhost:3000
   http://localhost:3001
   http://localhost:5173
   https://virtualrealtour.ng
   https://staging.virtualrealtour.ng
   https://tour-nigeria-vista.vercel.app
   ```
4. **Save settings**

---

## 🚨 Troubleshooting

### Plugin Won't Activate
- Check PHP version (requires 7.4+)
- Check WordPress version (requires 5.0+)
- Look for PHP errors in WordPress debug log

### API Endpoints Not Working
- Check permalink structure (Settings → Permalinks)
- Flush rewrite rules (deactivate/reactivate plugin)
- Check .htaccess file permissions

### CORS Errors
- Verify allowed origins are correctly formatted
- Include protocol (http:// or https://)
- Check that CORS is enabled in plugin settings

### WooCommerce/WPVR Tests Fail
- Ensure plugins are active and up-to-date
- Check plugin compatibility
- Review WordPress error logs

---

## 📞 Support

- **Documentation:** See `WORDPRESS_SETUP.md` in the main project
- **GitHub Issues:** Report bugs and feature requests
- **WordPress Support:** Use the plugin's support forum

---

## 🔄 Updates

### Manual Updates
1. Download new plugin version
2. Deactivate current plugin
3. Replace plugin files
4. Reactivate plugin

### Automatic Updates (Future)
- Plugin will support WordPress automatic updates
- Update notifications in WordPress admin
- Backup before updating

---

## 📋 Version History

- **v1.2.0** - Production-grade release with full admin interface
- **v1.1.0** - Enhanced WooCommerce and WPVR integration
- **v1.0.0** - Initial release with basic CORS functionality

---

**Ready to install? Create the ZIP file and upload to WordPress!** 🚀
