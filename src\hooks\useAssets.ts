/**
 * Assets Hook
 * React hook for managing asset uploads, validation, and optimization
 */

import { useState, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { assetManager, type AssetMetadata, type UploadOptions, type ValidationResult } from '@/lib/assets/assetManager';

export interface UseAssetsOptions {
  autoUpload?: boolean;
  uploadOptions?: UploadOptions;
  onUploadComplete?: (assets: AssetMetadata[]) => void;
  onUploadError?: (error: string) => void;
  onValidationError?: (errors: string[]) => void;
}

export interface UseAssetsReturn {
  // Asset management
  assets: AssetMetadata[];
  selectedAssets: AssetMetadata[];
  
  // Upload functionality
  uploadAssets: (files: FileList) => Promise<AssetMetadata[]>;
  validateAssets: (files: FileList) => Promise<ValidationResult[]>;
  
  // Asset operations
  getAsset: (assetId: string) => Promise<AssetMetadata | null>;
  deleteAsset: (assetId: string) => Promise<void>;
  refreshAssets: () => Promise<void>;
  
  // Selection management
  selectAsset: (asset: AssetMetadata) => void;
  deselectAsset: (assetId: string) => void;
  selectMultiple: (assets: AssetMetadata[]) => void;
  clearSelection: () => void;
  
  // Filtering and search
  searchAssets: (query: string) => AssetMetadata[];
  filterAssets: (filter: (asset: AssetMetadata) => boolean) => AssetMetadata[];
  
  // State
  isUploading: boolean;
  isLoading: boolean;
  error: string | null;
  uploadProgress: Record<string, number>;
  
  // Statistics
  totalAssets: number;
  totalSize: number;
  panoramaCount: number;
  errorCount: number;
}

export function useAssets(options: UseAssetsOptions = {}): UseAssetsReturn {
  const {
    autoUpload = true,
    uploadOptions = {},
    onUploadComplete,
    onUploadError,
    onValidationError
  } = options;

  // State
  const [assets, setAssets] = useState<AssetMetadata[]>([]);
  const [selectedAssets, setSelectedAssets] = useState<AssetMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});

  // Refs
  const uploadQueueRef = useRef<Map<string, Promise<AssetMetadata>>>(new Map());

  /**
   * Upload multiple assets
   */
  const uploadAssets = useCallback(async (files: FileList): Promise<AssetMetadata[]> => {
    const fileArray = Array.from(files);
    
    if (fileArray.length === 0) {
      return [];
    }

    setIsUploading(true);
    setError(null);
    
    const uploadedAssets: AssetMetadata[] = [];
    const errors: string[] = [];

    try {
      // Validate files first if not auto-uploading
      if (!autoUpload) {
        const validationResults = await validateAssets(files);
        const invalidFiles = validationResults.filter(result => !result.isValid);
        
        if (invalidFiles.length > 0) {
          const allErrors = invalidFiles.flatMap(result => result.errors);
          onValidationError?.(allErrors);
          throw new Error(`Validation failed: ${allErrors.join(', ')}`);
        }
      }

      // Upload files sequentially to avoid overwhelming the server
      for (let i = 0; i < fileArray.length; i++) {
        const file = fileArray[i];
        const fileId = `${file.name}-${Date.now()}-${i}`;
        
        try {
          // Update progress
          setUploadProgress(prev => ({ ...prev, [fileId]: 0 }));
          
          // Create upload promise
          const uploadPromise = assetManager.uploadAsset(file, {
            ...uploadOptions,
            // Add progress callback if supported
          });
          
          uploadQueueRef.current.set(fileId, uploadPromise);
          
          // Simulate progress updates
          const progressInterval = setInterval(() => {
            setUploadProgress(prev => ({
              ...prev,
              [fileId]: Math.min((prev[fileId] || 0) + 10, 90)
            }));
          }, 200);

          const result = await uploadPromise;
          
          clearInterval(progressInterval);
          setUploadProgress(prev => ({ ...prev, [fileId]: 100 }));
          
          uploadedAssets.push(result);
          
          // Add to assets list
          setAssets(prev => [result, ...prev]);
          
          toast.success(`${file.name} uploaded successfully`);
          
        } catch (uploadError) {
          const errorMessage = uploadError instanceof Error ? uploadError.message : 'Upload failed';
          errors.push(`${file.name}: ${errorMessage}`);
          toast.error(`Failed to upload ${file.name}: ${errorMessage}`);
        } finally {
          uploadQueueRef.current.delete(fileId);
          setUploadProgress(prev => {
            const newProgress = { ...prev };
            delete newProgress[fileId];
            return newProgress;
          });
        }
      }

      if (uploadedAssets.length > 0) {
        onUploadComplete?.(uploadedAssets);
      }

      if (errors.length > 0) {
        const errorMessage = `${errors.length} uploads failed`;
        setError(errorMessage);
        onUploadError?.(errorMessage);
      }

      return uploadedAssets;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setError(errorMessage);
      onUploadError?.(errorMessage);
      throw error;
    } finally {
      setIsUploading(false);
    }
  }, [autoUpload, uploadOptions, onUploadComplete, onUploadError, onValidationError]);

  /**
   * Validate multiple assets
   */
  const validateAssets = useCallback(async (files: FileList): Promise<ValidationResult[]> => {
    const fileArray = Array.from(files);
    const results: ValidationResult[] = [];

    for (const file of fileArray) {
      try {
        const result = await assetManager.validateImage(file, true);
        results.push(result);
      } catch (error) {
        results.push({
          isValid: false,
          isPanorama: false,
          errors: [error instanceof Error ? error.message : 'Validation failed'],
          warnings: [],
          metadata: {
            width: 0,
            height: 0,
            aspectRatio: 0,
            fileSize: file.size,
            format: file.type
          }
        });
      }
    }

    return results;
  }, []);

  /**
   * Get asset by ID
   */
  const getAsset = useCallback(async (assetId: string): Promise<AssetMetadata | null> => {
    try {
      setError(null);
      return await assetManager.getAsset(assetId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get asset';
      setError(errorMessage);
      return null;
    }
  }, []);

  /**
   * Delete asset
   */
  const deleteAsset = useCallback(async (assetId: string): Promise<void> => {
    try {
      setError(null);
      await assetManager.deleteAsset(assetId);
      
      // Remove from local state
      setAssets(prev => prev.filter(asset => asset.id !== assetId));
      setSelectedAssets(prev => prev.filter(asset => asset.id !== assetId));
      
      toast.success('Asset deleted successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete asset';
      setError(errorMessage);
      toast.error(errorMessage);
      throw error;
    }
  }, []);

  /**
   * Refresh assets list
   */
  const refreshAssets = useCallback(async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await assetManager.listAssets(1, 100);
      setAssets(result.assets);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh assets';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Select asset
   */
  const selectAsset = useCallback((asset: AssetMetadata) => {
    setSelectedAssets(prev => {
      if (prev.find(a => a.id === asset.id)) {
        return prev; // Already selected
      }
      return [...prev, asset];
    });
  }, []);

  /**
   * Deselect asset
   */
  const deselectAsset = useCallback((assetId: string) => {
    setSelectedAssets(prev => prev.filter(asset => asset.id !== assetId));
  }, []);

  /**
   * Select multiple assets
   */
  const selectMultiple = useCallback((assetsToSelect: AssetMetadata[]) => {
    setSelectedAssets(prev => {
      const existingIds = new Set(prev.map(a => a.id));
      const newAssets = assetsToSelect.filter(a => !existingIds.has(a.id));
      return [...prev, ...newAssets];
    });
  }, []);

  /**
   * Clear selection
   */
  const clearSelection = useCallback(() => {
    setSelectedAssets([]);
  }, []);

  /**
   * Search assets
   */
  const searchAssets = useCallback((query: string): AssetMetadata[] => {
    if (!query.trim()) return assets;
    
    const lowercaseQuery = query.toLowerCase();
    return assets.filter(asset => 
      asset.originalName.toLowerCase().includes(lowercaseQuery) ||
      asset.filename.toLowerCase().includes(lowercaseQuery) ||
      asset.format.toLowerCase().includes(lowercaseQuery)
    );
  }, [assets]);

  /**
   * Filter assets
   */
  const filterAssets = useCallback((filter: (asset: AssetMetadata) => boolean): AssetMetadata[] => {
    return assets.filter(filter);
  }, [assets]);

  // Computed statistics
  const totalAssets = assets.length;
  const totalSize = assets.reduce((sum, asset) => sum + asset.size, 0);
  const panoramaCount = assets.filter(asset => asset.validation.isPanorama).length;
  const errorCount = assets.filter(asset => asset.validation.errors.length > 0).length;

  return {
    // Asset management
    assets,
    selectedAssets,
    
    // Upload functionality
    uploadAssets,
    validateAssets,
    
    // Asset operations
    getAsset,
    deleteAsset,
    refreshAssets,
    
    // Selection management
    selectAsset,
    deselectAsset,
    selectMultiple,
    clearSelection,
    
    // Filtering and search
    searchAssets,
    filterAssets,
    
    // State
    isUploading,
    isLoading,
    error,
    uploadProgress,
    
    // Statistics
    totalAssets,
    totalSize,
    panoramaCount,
    errorCount
  };
}
