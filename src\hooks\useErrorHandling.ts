/**
 * Error Handling Hook
 * React hook for managing PSV errors and recovery
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  psvErrorHandler, 
  PSVErrorCode, 
  type PSVErrorDetails, 
  type ErrorHandlingOptions 
} from '@/lib/photosphere/errorHandling';

export interface UseErrorHandlingOptions extends ErrorHandlingOptions {
  autoRecover?: boolean;
  showToasts?: boolean;
  logErrors?: boolean;
}

export interface UseErrorHandlingReturn {
  // Error state
  error: PSVErrorDetails | null;
  hasError: boolean;
  isRecovering: boolean;
  errorHistory: PSVErrorDetails[];
  
  // Error handling
  handleError: (error: any, code?: PSVErrorCode, context?: Record<string, any>) => Promise<boolean>;
  clearError: () => void;
  clearHistory: () => void;
  
  // Recovery
  retry: () => Promise<boolean>;
  recover: () => Promise<boolean>;
  
  // Statistics
  errorCount: number;
  recoveryCount: number;
  lastErrorTime: string | null;
  
  // Configuration
  updateOptions: (options: Partial<UseErrorHandlingOptions>) => void;
}

export function useErrorHandling(
  options: UseErrorHandlingOptions = {}
): UseErrorHandlingReturn {
  const {
    autoRecover = true,
    showToasts = true,
    logErrors = true,
    enableRetry = true,
    maxRetries = 3,
    retryDelay = 1000,
    enableFallback = true,
    enableReporting = true,
    onError,
    onRecovery
  } = options;

  // State
  const [error, setError] = useState<PSVErrorDetails | null>(null);
  const [isRecovering, setIsRecovering] = useState(false);
  const [errorHistory, setErrorHistory] = useState<PSVErrorDetails[]>([]);
  const [recoveryCount, setRecoveryCount] = useState(0);

  // Refs
  const optionsRef = useRef(options);
  const retryCallbackRef = useRef<(() => Promise<boolean>) | null>(null);
  const recoveryCallbackRef = useRef<(() => Promise<boolean>) | null>(null);

  // Update options ref when options change
  useEffect(() => {
    optionsRef.current = options;
  }, [options]);

  /**
   * Handle error with recovery strategies
   */
  const handleError = useCallback(async (
    errorInput: any,
    code: PSVErrorCode = PSVErrorCode.UNKNOWN_ERROR,
    context?: Record<string, any>
  ): Promise<boolean> => {
    try {
      // Use PSV error handler to process the error
      const recovered = await psvErrorHandler.handleError(errorInput, code, {
        ...context,
        hookContext: 'useErrorHandling'
      });

      // Get the processed error details
      const errorHistory = psvErrorHandler.getErrorHistory();
      const latestError = errorHistory[errorHistory.length - 1];

      if (latestError) {
        setError(latestError);
        setErrorHistory(prev => [...prev, latestError]);

        // Show toast notification if enabled
        if (showToasts) {
          if (recovered) {
            toast.success('Error recovered automatically');
          } else if (latestError.retryable) {
            toast.error(latestError.message, {
              description: 'Click to retry',
              action: {
                label: 'Retry',
                onClick: () => retry()
              }
            });
          } else {
            toast.error(latestError.message);
          }
        }

        // Log error if enabled
        if (logErrors) {
          console.error('PSV Error handled:', latestError);
        }

        // Call custom error handler
        if (onError) {
          onError(latestError);
        }

        // Attempt auto-recovery if enabled
        if (autoRecover && !recovered && latestError.recoverable) {
          setIsRecovering(true);
          const autoRecovered = await recover();
          setIsRecovering(false);
          
          if (autoRecovered) {
            setRecoveryCount(prev => prev + 1);
            if (onRecovery) {
              onRecovery('auto-recovery');
            }
          }
          
          return autoRecovered;
        }
      }

      return recovered;
    } catch (handlingError) {
      console.error('Error in error handling:', handlingError);
      return false;
    }
  }, [autoRecover, showToasts, logErrors, onError, onRecovery]);

  /**
   * Clear current error
   */
  const clearError = useCallback(() => {
    setError(null);
    setIsRecovering(false);
  }, []);

  /**
   * Clear error history
   */
  const clearHistory = useCallback(() => {
    setErrorHistory([]);
    psvErrorHandler.clearErrorHistory();
  }, []);

  /**
   * Retry the last failed operation
   */
  const retry = useCallback(async (): Promise<boolean> => {
    if (!error || !error.retryable) {
      return false;
    }

    setIsRecovering(true);

    try {
      // Call custom retry callback if available
      if (retryCallbackRef.current) {
        const success = await retryCallbackRef.current();
        if (success) {
          clearError();
          setRecoveryCount(prev => prev + 1);
          if (onRecovery) {
            onRecovery('manual-retry');
          }
          toast.success('Retry successful');
          return true;
        }
      }

      // Default retry behavior - just clear the error and hope for the best
      clearError();
      toast.info('Retrying...');
      return true;
    } catch (retryError) {
      console.error('Retry failed:', retryError);
      toast.error('Retry failed');
      return false;
    } finally {
      setIsRecovering(false);
    }
  }, [error, clearError, onRecovery]);

  /**
   * Attempt recovery
   */
  const recover = useCallback(async (): Promise<boolean> => {
    if (!error || !error.recoverable) {
      return false;
    }

    setIsRecovering(true);

    try {
      // Call custom recovery callback if available
      if (recoveryCallbackRef.current) {
        const success = await recoveryCallbackRef.current();
        if (success) {
          clearError();
          setRecoveryCount(prev => prev + 1);
          if (onRecovery) {
            onRecovery('manual-recovery');
          }
          toast.success('Recovery successful');
          return true;
        }
      }

      // Default recovery strategies based on error code
      switch (error.code) {
        case PSVErrorCode.WEBGL_CONTEXT_LOST:
          // Refresh the page as a last resort
          window.location.reload();
          return true;

        case PSVErrorCode.MEMORY_LIMIT_EXCEEDED:
          // Clear caches and try again
          if (window.gc) {
            window.gc();
          }
          clearError();
          toast.success('Memory cleared, trying again');
          return true;

        case PSVErrorCode.NETWORK_ERROR:
          // Wait a bit and clear error
          await new Promise(resolve => setTimeout(resolve, 2000));
          clearError();
          toast.success('Network issue resolved');
          return true;

        default:
          clearError();
          return true;
      }
    } catch (recoveryError) {
      console.error('Recovery failed:', recoveryError);
      toast.error('Recovery failed');
      return false;
    } finally {
      setIsRecovering(false);
    }
  }, [error, clearError, onRecovery]);

  /**
   * Update options
   */
  const updateOptions = useCallback((newOptions: Partial<UseErrorHandlingOptions>) => {
    optionsRef.current = { ...optionsRef.current, ...newOptions };
  }, []);

  /**
   * Set retry callback
   */
  const setRetryCallback = useCallback((callback: () => Promise<boolean>) => {
    retryCallbackRef.current = callback;
  }, []);

  /**
   * Set recovery callback
   */
  const setRecoveryCallback = useCallback((callback: () => Promise<boolean>) => {
    recoveryCallbackRef.current = callback;
  }, []);

  // Computed values
  const hasError = error !== null;
  const errorCount = errorHistory.length;
  const lastErrorTime = error?.timestamp || null;

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  return {
    // Error state
    error,
    hasError,
    isRecovering,
    errorHistory,
    
    // Error handling
    handleError,
    clearError,
    clearHistory,
    
    // Recovery
    retry,
    recover,
    
    // Statistics
    errorCount,
    recoveryCount,
    lastErrorTime,
    
    // Configuration
    updateOptions,
    
    // Internal methods (not exported in interface but available)
    setRetryCallback: setRetryCallback as any,
    setRecoveryCallback: setRecoveryCallback as any
  };
}
