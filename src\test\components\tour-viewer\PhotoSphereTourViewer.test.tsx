/**
 * PhotoSphereTourViewer Component Tests
 * Unit tests for the main PSV React component
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PhotoSphereTourViewer } from '@/components/tour-viewer/PhotoSphereTourViewer';
import { 
  setupTestEnvironment, 
  createTestScenes, 
  createMockPSVInstance,
  mockSupabase
} from '@/test/utils/psvTestUtils';

// Mock PSV Factory
vi.mock('@/lib/photosphere/factory', () => ({
  PSVFactory: {
    create: vi.fn(),
    createSimple: vi.fn(),
    createVirtualTour: vi.fn(),
    createVirtualTourFromId: vi.fn()
  }
}));

// Mock hooks
vi.mock('@/hooks/useVirtualTour', () => ({
  useVirtualTour: vi.fn(() => ({
    tour: null,
    currentNode: null,
    currentNodeId: null,
    isLoading: false,
    isLoadingNode: false,
    error: null,
    loadTour: vi.fn(),
    navigateToNode: vi.fn(),
    refreshTour: vi.fn(),
    attachToPSV: vi.fn(),
    detachFromPSV: vi.fn()
  }))
}));

vi.mock('@/hooks/useMarkers', () => ({
  useMarkers: vi.fn(() => ({
    markers: [],
    addMarker: vi.fn(),
    removeMarker: vi.fn(),
    setMarkers: vi.fn(),
    attachToPSV: vi.fn(),
    detachFromPSV: vi.fn(),
    isAttached: false,
    markerCount: 0
  }))
}));

// Mock Supabase
vi.mock('@/lib/supabase', () => ({
  supabase: mockSupabase
}));

// Mock toast
vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn()
  }
}));

describe('PhotoSphereTourViewer', () => {
  let cleanup: () => void;
  let mockPSVInstance: any;

  beforeEach(() => {
    cleanup = setupTestEnvironment();
    mockPSVInstance = createMockPSVInstance();
    
    // Mock PSV Factory methods
    const { PSVFactory } = require('@/lib/photosphere/factory');
    PSVFactory.create.mockResolvedValue(mockPSVInstance);
    PSVFactory.createSimple.mockResolvedValue(mockPSVInstance);
    PSVFactory.createVirtualTour.mockResolvedValue(mockPSVInstance);
    PSVFactory.createVirtualTourFromId.mockResolvedValue(mockPSVInstance);
  });

  afterEach(() => {
    cleanup();
    vi.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('should render loading state initially', () => {
      const scenes = createTestScenes(1);
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
          enableVirtualTour={false}
        />
      );

      expect(screen.getByText(/Loading Virtual Tour/i)).toBeInTheDocument();
    });

    it('should render viewer container', async () => {
      const scenes = createTestScenes(1);
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
          enableVirtualTour={false}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('psv-container')).toBeInTheDocument();
      });
    });

    it('should render error state when no scenes provided', () => {
      render(
        <PhotoSphereTourViewer
          scenes={[]}
          enableVirtualTour={false}
        />
      );

      expect(screen.getByText(/No scenes available/i)).toBeInTheDocument();
    });
  });

  describe('Single Scene Mode', () => {
    it('should create simple PSV for single scene', async () => {
      const scenes = createTestScenes(1);
      const { PSVFactory } = require('@/lib/photosphere/factory');
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
          enableVirtualTour={false}
        />
      );

      await waitFor(() => {
        expect(PSVFactory.createSimple).toHaveBeenCalledWith(
          expect.any(HTMLElement),
          scenes[0].panorama,
          expect.objectContaining({
            enableAutorotate: false,
            enableSettings: true
          })
        );
      });
    });

    it('should handle autorotate option', async () => {
      const scenes = createTestScenes(1);
      const { PSVFactory } = require('@/lib/photosphere/factory');
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
          enableVirtualTour={false}
          autoRotate={true}
        />
      );

      await waitFor(() => {
        expect(PSVFactory.createSimple).toHaveBeenCalledWith(
          expect.any(HTMLElement),
          scenes[0].panorama,
          expect.objectContaining({
            enableAutorotate: true
          })
        );
      });
    });
  });

  describe('Virtual Tour Mode', () => {
    it('should create virtual tour for multiple scenes', async () => {
      const scenes = createTestScenes(3);
      const { PSVFactory } = require('@/lib/photosphere/factory');
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
          enableVirtualTour={true}
        />
      );

      await waitFor(() => {
        expect(PSVFactory.createVirtualTour).toHaveBeenCalledWith(
          expect.any(HTMLElement),
          scenes,
          expect.objectContaining({
            enableAutorotate: false,
            enableGallery: true,
            enableSettings: true
          })
        );
      });
    });

    it('should create virtual tour from database ID', async () => {
      const { PSVFactory } = require('@/lib/photosphere/factory');
      
      render(
        <PhotoSphereTourViewer
          tourId="test-tour-id"
          enableVirtualTour={true}
        />
      );

      await waitFor(() => {
        expect(PSVFactory.createVirtualTourFromId).toHaveBeenCalledWith(
          expect.any(HTMLElement),
          "test-tour-id",
          expect.any(Object)
        );
      });
    });
  });

  describe('Event Handling', () => {
    it('should call onReady when PSV is initialized', async () => {
      const onReady = vi.fn();
      const scenes = createTestScenes(1);
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
          onReady={onReady}
        />
      );

      await waitFor(() => {
        expect(onReady).toHaveBeenCalledWith(mockPSVInstance);
      });
    });

    it('should call onSceneChange when scene changes', async () => {
      const onSceneChange = vi.fn();
      const scenes = createTestScenes(2);
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
          onSceneChange={onSceneChange}
        />
      );

      // Simulate scene change
      await waitFor(() => {
        const { PSVFactory } = require('@/lib/photosphere/factory');
        const lastCall = PSVFactory.createVirtualTour.mock.calls[0];
        const options = lastCall[2];
        options.onSceneChange('scene-2');
      });

      expect(onSceneChange).toHaveBeenCalledWith('scene-2');
    });

    it('should call onMarkerClick when marker is clicked', async () => {
      const onMarkerClick = vi.fn();
      const scenes = createTestScenes(1);
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
          onMarkerClick={onMarkerClick}
        />
      );

      // Simulate marker click
      await waitFor(() => {
        const { PSVFactory } = require('@/lib/photosphere/factory');
        const lastCall = PSVFactory.createSimple.mock.calls[0];
        const options = lastCall[2];
        if (options.onMarkerClick) {
          options.onMarkerClick('marker-1', { test: 'data' });
        }
      });

      expect(onMarkerClick).toHaveBeenCalledWith('marker-1', { test: 'data' });
    });

    it('should call onError when error occurs', async () => {
      const onError = vi.fn();
      const scenes = createTestScenes(1);
      const { PSVFactory } = require('@/lib/photosphere/factory');
      
      // Mock PSV creation failure
      PSVFactory.createSimple.mockRejectedValue(new Error('PSV creation failed'));
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
          onError={onError}
        />
      );

      await waitFor(() => {
        expect(onError).toHaveBeenCalledWith(expect.any(Error));
      });
    });
  });

  describe('Controls', () => {
    it('should show controls when showControls is true', async () => {
      const scenes = createTestScenes(1);
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
          showControls={true}
        />
      );

      await waitFor(() => {
        expect(screen.getByTestId('psv-controls')).toBeInTheDocument();
      });
    });

    it('should hide controls when showControls is false', async () => {
      const scenes = createTestScenes(1);
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
          showControls={false}
        />
      );

      await waitFor(() => {
        expect(screen.queryByTestId('psv-controls')).not.toBeInTheDocument();
      });
    });
  });

  describe('Error States', () => {
    it('should display error message when PSV creation fails', async () => {
      const scenes = createTestScenes(1);
      const { PSVFactory } = require('@/lib/photosphere/factory');
      
      PSVFactory.createSimple.mockRejectedValue(new Error('WebGL not supported'));
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
        />
      );

      await waitFor(() => {
        expect(screen.getByText(/Failed to Load Tour/i)).toBeInTheDocument();
        expect(screen.getByText(/WebGL not supported/i)).toBeInTheDocument();
      });
    });

    it('should show retry button on error', async () => {
      const scenes = createTestScenes(1);
      const { PSVFactory } = require('@/lib/photosphere/factory');
      
      PSVFactory.createSimple.mockRejectedValue(new Error('Network error'));
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
        />
      );

      await waitFor(() => {
        expect(screen.getByText(/Retry/i)).toBeInTheDocument();
      });
    });

    it('should retry PSV creation when retry button is clicked', async () => {
      const scenes = createTestScenes(1);
      const { PSVFactory } = require('@/lib/photosphere/factory');
      
      // First call fails, second succeeds
      PSVFactory.createSimple
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce(mockPSVInstance);
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
        />
      );

      await waitFor(() => {
        expect(screen.getByText(/Retry/i)).toBeInTheDocument();
      });

      fireEvent.click(screen.getByText(/Retry/i));

      await waitFor(() => {
        expect(PSVFactory.createSimple).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Cleanup', () => {
    it('should cleanup PSV instance on unmount', async () => {
      const scenes = createTestScenes(1);
      
      const { unmount } = render(
        <PhotoSphereTourViewer
          scenes={scenes}
        />
      );

      await waitFor(() => {
        expect(mockPSVInstance.destroy).not.toHaveBeenCalled();
      });

      unmount();

      expect(mockPSVInstance.destroy).toHaveBeenCalled();
    });

    it('should detach hooks on unmount', async () => {
      const scenes = createTestScenes(1);
      const { useVirtualTour } = require('@/hooks/useVirtualTour');
      const { useMarkers } = require('@/hooks/useMarkers');
      
      const mockDetachVirtualTour = vi.fn();
      const mockDetachMarkers = vi.fn();
      
      useVirtualTour.mockReturnValue({
        detachFromPSV: mockDetachVirtualTour,
        tour: null,
        currentNode: null,
        currentNodeId: null,
        isLoading: false,
        error: null
      });
      
      useMarkers.mockReturnValue({
        detachFromPSV: mockDetachMarkers,
        markers: [],
        isAttached: false,
        markerCount: 0
      });
      
      const { unmount } = render(
        <PhotoSphereTourViewer
          tourId="test-tour"
          scenes={scenes}
        />
      );

      unmount();

      expect(mockDetachVirtualTour).toHaveBeenCalled();
      expect(mockDetachMarkers).toHaveBeenCalled();
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', async () => {
      const scenes = createTestScenes(1);
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
        />
      );

      await waitFor(() => {
        const container = screen.getByTestId('psv-container');
        expect(container).toHaveAttribute('role', 'img');
        expect(container).toHaveAttribute('aria-label');
      });
    });

    it('should support keyboard navigation', async () => {
      const scenes = createTestScenes(1);
      
      render(
        <PhotoSphereTourViewer
          scenes={scenes}
        />
      );

      await waitFor(() => {
        const container = screen.getByTestId('psv-container');
        expect(container).toHaveAttribute('tabindex', '0');
      });
    });
  });
});
