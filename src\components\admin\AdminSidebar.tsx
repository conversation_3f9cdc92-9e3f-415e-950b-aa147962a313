import { NavLink, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  Users,
  MapPin,
  Star,
  FileText,
  Settings,
  BarChart3,
  Shield,
  Globe,
  Image,
  UserCog,
  User,
  Activity,
  Sparkles,
  Edit,
  Download,
  DollarSign,
  Store,
  Eye
} from 'lucide-react';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
  useSidebar,
} from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/hooks/useAuth';

// Streamlined tour-centric navigation structure
const adminNavGroups = [
  {
    label: 'Dashboard',
    items: [
      { title: 'Dashboard', url: '/admin', icon: LayoutDashboard, description: 'Tour-focused overview & metrics' },
    ]
  },
  {
    label: 'Pages & Content',
    items: [
      { title: 'Pages Management', url: '/admin/pages', icon: FileText, description: 'Homepage, showcase, about & content sections' },
    ]
  },
  {
    label: 'Tours (Central Hub)',
    items: [
      { title: 'All Tours', url: '/admin/tours', icon: MapPin, description: 'Create, edit, import & manage all tours' },
    ]
  },
  {
    label: 'Overlays & Hotspots',
    items: [
      { title: 'Overlay Studio', url: '/admin/overlays', icon: Sparkles, description: 'Create & customize hotspots, icons & animations' },
    ]
  },
  {
    label: 'Vendors & Users',
    items: [
      { title: 'Vendor Management', url: '/admin/vendors', icon: Users, description: 'Vendor accounts, tours & products' },
      { title: 'User Management', url: '/admin/users', icon: UserCog, description: 'Platform user management' },
    ]
  },
  {
    label: 'Commerce',
    items: [
      { title: 'Orders & Commerce', url: '/admin/commerce', icon: DollarSign, description: 'Orders, transactions & vendor commerce' },
    ]
  },
  {
    label: 'Integrations',
    items: [
      { title: 'Integrations & API', url: '/admin/integrations', icon: Globe, description: 'WPVR, WooCommerce, CloudPano & API settings' },
    ]
  },
  {
    label: 'Tools',
    items: [
      { title: 'Tools & Diagnostics', url: '/admin/tools', icon: Activity, description: 'System health, logs & mobile preview' },
    ]
  }
];

export function AdminSidebar() {
  const { state } = useSidebar();
  const location = useLocation();
  const { user, signOut } = useAuth();
  const currentPath = location.pathname;

  const isActive = (path: string) => {
    if (path === '/admin') {
      return currentPath === '/admin';
    }
    return currentPath.startsWith(path);
  };

  const getNavClassName = (path: string) => {
    return isActive(path)
      ? "bg-primary/10 text-primary border-r-2 border-primary font-medium"
      : "hover:bg-muted text-muted-foreground hover:text-foreground";
  };

  return (
    <Sidebar className="border-r bg-background/95 backdrop-blur-sm supports-[backdrop-filter]:bg-background/60">
      <SidebarHeader className="border-b bg-background/95 px-3 sm:px-4 md:px-6 py-3 md:py-4">
        <div className="flex items-center gap-3 sm:gap-4">
          <div className="flex items-center justify-center rounded-xl overflow-hidden bg-primary/10 h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12 flex-shrink-0">
            <img
              src="/lovable-uploads/vrt-logo-all.png"
              alt="VirtualRealTour Logo"
              className="object-cover h-5 w-5 sm:h-6 sm:w-6 md:h-8 md:w-8"
            />
          </div>
          {state === 'expanded' && (
            <div className="flex-1 min-w-0">
              <h2 className="font-semibold text-foreground text-sm sm:text-base md:text-lg truncate">VirtualRealTour</h2>
              <p className="text-muted-foreground text-xs sm:text-sm truncate">Tour-First Admin</p>
            </div>
          )}
        </div>
      </SidebarHeader>

      <SidebarContent className="px-1 sm:px-2 py-3 sm:py-4 bg-background/95">
        {adminNavGroups.map((group) => (
          <SidebarGroup key={group.label}>
            <SidebarGroupLabel className="px-3 sm:px-4 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              {group.label}
            </SidebarGroupLabel>
            <SidebarGroupContent className="mt-2">
              <SidebarMenu>
                {group.items.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild className="h-auto p-0">
                      <NavLink
                        to={item.url}
                        className={`flex items-center gap-2 sm:gap-3 rounded-md px-2 sm:px-3 py-3 mx-1 sm:mx-2 mb-1 transition-all duration-200 min-h-[44px] touch-target ${getNavClassName(item.url)}`}
                      >
                        <item.icon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                        {state === 'expanded' && (
                          <div className="flex-1 min-w-0">
                            <div className="text-sm font-medium leading-5">{item.title}</div>
                            <div className={`text-xs leading-4 mt-0.5 ${isActive(item.url) ? 'text-primary' : 'text-muted-foreground'}`}>
                              {item.description}
                            </div>
                          </div>
                        )}
                      </NavLink>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}

        <SidebarGroup className="mt-6">
          <SidebarGroupLabel className="px-4 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            Quick Actions
          </SidebarGroupLabel>
          <SidebarGroupContent className="mt-2">
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild className="h-auto p-0">
                  <NavLink to="/dashboard" className="flex items-center gap-3 rounded-md px-3 py-2 mx-2 mb-1 transition-all duration-200 hover:bg-muted text-muted-foreground hover:text-foreground min-h-[2.25rem]">
                    <User className="h-4 w-4 flex-shrink-0" />
                    {state === 'expanded' && <span className="text-sm font-medium">User Dashboard</span>}
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild className="h-auto p-0">
                  <NavLink to="/vendor-dashboard" className="flex items-center gap-3 rounded-md px-3 py-2 mx-2 mb-1 transition-all duration-200 hover:bg-muted text-muted-foreground hover:text-foreground min-h-[2.25rem]">
                    <Store className="h-4 w-4 flex-shrink-0" />
                    {state === 'expanded' && <span className="text-sm font-medium">Vendor Dashboard</span>}
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild className="h-auto p-0">
                  <NavLink to="/" className="flex items-center gap-3 rounded-md px-3 py-2 mx-2 mb-1 transition-all duration-200 hover:bg-muted text-muted-foreground hover:text-foreground min-h-[2.25rem]">
                    <Globe className="h-4 w-4 flex-shrink-0" />
                    {state === 'expanded' && <span className="text-sm font-medium">View Website</span>}
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t px-4 py-5 bg-background/95">
        {state === 'expanded' && (
          <div className="space-y-3">
            <div className="flex items-center gap-3 px-2 py-2 rounded-lg bg-muted">
              <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                <UserCog className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-foreground truncate">{user?.email}</p>
                <p className="text-xs text-muted-foreground">Administrator</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={signOut}
              className="w-full justify-start"
            >
              Sign Out
            </Button>
          </div>
        )}
      </SidebarFooter>
    </Sidebar>
  );
}
