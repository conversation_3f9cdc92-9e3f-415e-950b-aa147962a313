/**
 * Slug Generation Utilities
 * Convert tour titles to URL-friendly slugs while maintaining database ID mapping
 */

/**
 * Generate URL-friendly slug from tour title
 */
export const generateSlug = (title: string): string => {
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

/**
 * Generate unique slug by appending number if needed
 */
export const generateUniqueSlug = (title: string, existingSlugs: string[] = []): string => {
  const baseSlug = generateSlug(title);
  let slug = baseSlug;
  let counter = 1;
  
  while (existingSlugs.includes(slug)) {
    slug = `${baseSlug}-${counter}`;
    counter++;
  }
  
  return slug;
};

/**
 * Find tour by slug in database
 * This replaces ID extraction since we're using clean slugs
 */
export const findTourBySlug = async (slug: string) => {
  // This will be used in components to find tours by slug
  return slug;
};

/**
 * Create tour URL with clean slug only (no ID exposed)
 * Format: "/tour/tour-name-slug"
 */
export const createTourUrl = (title: string, id?: string): string => {
  const slug = generateSlug(title);
  return `/tour/${slug}`;
};

/**
 * Create tour slug for database storage (clean slug only)
 * Format: "tour-name-slug"
 */
export const createTourSlug = (title: string): string => {
  return generateSlug(title);
};

/**
 * Parse tour slug to get readable name and ID
 */
export const parseTourSlug = (slugWithId: string): { name: string; id: string | null } => {
  const id = extractIdFromSlug(slugWithId);
  
  if (!id) {
    return { name: slugWithId, id: null };
  }
  
  // Remove the ID part to get the readable name
  const nameSlug = slugWithId.replace(`-${id}`, '');
  const readableName = nameSlug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
  
  return { name: readableName, id };
};

/**
 * Validate slug format
 */
export const isValidSlug = (slug: string): boolean => {
  return /^[a-z0-9]+(?:-[a-z0-9]+)*$/.test(slug);
};
