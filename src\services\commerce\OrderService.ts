/**
 * Order Service
 * Enhanced order management with status tracking, notifications, and analytics
 * Handles complete order lifecycle from creation to delivery
 */

import { supabase } from '@/lib/supabase';
import { whatsappService } from './WhatsAppService';
import type { Order, OrderItem } from '@/components/commerce/OrderSummary';
import type { CustomerInfo } from '@/components/commerce/ShoppingCart';

export interface OrderFilters {
  status?: string;
  vendorId?: string;
  customerId?: string;
  customerPhone?: string;
  dateFrom?: string;
  dateTo?: string;
  minAmount?: number;
  maxAmount?: number;
  search?: string;
}

export interface OrderUpdate {
  status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
  tracking_number?: string;
  notes?: string;
  estimated_delivery?: string;
}

export interface OrderStats {
  total_orders: number;
  pending_orders: number;
  processing_orders: number;
  completed_orders: number;
  cancelled_orders: number;
  total_revenue: number;
  average_order_value: number;
  orders_today: number;
  orders_this_week: number;
  orders_this_month: number;
}

export interface ShippingOption {
  id: string;
  name: string;
  cost: number;
  estimated_days: number;
}

export interface PromoCode {
  code: string;
  discount_type: 'percentage' | 'fixed';
  discount_value: number;
  minimum_amount?: number;
  valid_until?: string;
  usage_limit?: number;
  used_count?: number;
}

export class OrderService {
  /**
   * Create a new order with enhanced features
   */
  async createOrder(
    customerInfo: CustomerInfo,
    items: any[],
    shippingOption: string,
    promoCode?: string,
    tourContext?: any
  ): Promise<Order> {
    try {
      // Calculate totals
      const subtotal = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
      let shippingCost = 0;
      let discountAmount = 0;

      // Apply shipping cost
      const shipping = await this.getShippingCost(shippingOption, items);
      shippingCost = shipping.cost;

      // Apply promo code
      if (promoCode) {
        const promo = await this.validatePromoCode(promoCode, subtotal);
        if (promo) {
          discountAmount = this.calculateDiscount(promo, subtotal);
        }
      }

      const totalAmount = subtotal + shippingCost - discountAmount;

      // Generate order number
      const { data: orderNumber } = await supabase.rpc('generate_order_number');

      // Create order
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          order_number: orderNumber,
          customer_phone: customerInfo.phone,
          customer_email: customerInfo.email,
          customer_name: customerInfo.name,
          customer_address: customerInfo.address,
          total_amount: totalAmount,
          payment_method: 'whatsapp',
          tour_context: tourContext,
          shipping_option: shippingOption,
          shipping_cost: shippingCost,
          discount_amount: discountAmount,
          promo_code: promoCode,
          vendor_orders: this.groupItemsByVendor(items)
        })
        .select()
        .single();

      if (orderError) throw orderError;

      // Create order items
      for (const item of items) {
        const { error: itemError } = await supabase
          .from('order_items')
          .insert({
            order_id: order.id,
            product_id: item.product.id,
            vendor_id: item.product.vendor.id,
            quantity: item.quantity,
            price: item.product.price,
            vendor_commission: item.product.price * item.quantity * 0.10 // 10% commission
          });

        if (itemError) throw itemError;
      }

      // Update promo code usage
      if (promoCode) {
        await this.updatePromoCodeUsage(promoCode);
      }

      // Get complete order with items
      const completeOrder = await this.getOrder(order.id);
      if (!completeOrder) throw new Error('Failed to retrieve created order');

      // Send notifications
      await this.sendOrderNotifications(completeOrder);

      return completeOrder;
    } catch (error) {
      console.error('Error creating order:', error);
      throw new Error('Failed to create order');
    }
  }

  /**
   * Get order by ID with complete details
   */
  async getOrder(orderId: string): Promise<Order | null> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          items:order_items(
            *,
            product:products(*),
            vendor:vendors(id, name, whatsapp_number)
          )
        `)
        .eq('id', orderId)
        .single();

      if (error) {
        if (error.code === 'PGRST116') return null;
        throw error;
      }

      return this.mapSupabaseOrderToOrder(data);
    } catch (error) {
      console.error('Error fetching order:', error);
      throw new Error('Failed to fetch order');
    }
  }

  /**
   * Get orders with advanced filtering
   */
  async getOrders(filters: OrderFilters = {}, limit = 50, offset = 0): Promise<Order[]> {
    try {
      let query = supabase
        .from('orders')
        .select(`
          *,
          items:order_items(
            *,
            product:products(*),
            vendor:vendors(id, name, whatsapp_number)
          )
        `)
        .order('created_at', { ascending: false })
        .range(offset, offset + limit - 1);

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }

      if (filters.customerPhone) {
        query = query.eq('customer_phone', filters.customerPhone);
      }

      if (filters.dateFrom) {
        query = query.gte('created_at', filters.dateFrom);
      }

      if (filters.dateTo) {
        query = query.lte('created_at', filters.dateTo);
      }

      if (filters.minAmount) {
        query = query.gte('total_amount', filters.minAmount);
      }

      if (filters.maxAmount) {
        query = query.lte('total_amount', filters.maxAmount);
      }

      if (filters.search) {
        query = query.or(`order_number.ilike.%${filters.search}%,customer_name.ilike.%${filters.search}%`);
      }

      const { data, error } = await query;

      if (error) throw error;

      let orders = data.map(order => this.mapSupabaseOrderToOrder(order));

      // Filter by vendor if specified
      if (filters.vendorId) {
        orders = orders.filter(order => 
          order.items.some(item => item.vendor.id === filters.vendorId)
        );
      }

      return orders;
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw new Error('Failed to fetch orders');
    }
  }

  /**
   * Update order status with notifications
   */
  async updateOrderStatus(orderId: string, updates: OrderUpdate): Promise<Order> {
    try {
      const { data, error } = await supabase
        .from('orders')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', orderId)
        .select(`
          *,
          items:order_items(
            *,
            product:products(*),
            vendor:vendors(id, name, whatsapp_number)
          )
        `)
        .single();

      if (error) throw error;

      const order = this.mapSupabaseOrderToOrder(data);

      // Send status update notification
      if (updates.status) {
        await this.sendStatusUpdateNotification(order, updates.status);
      }

      return order;
    } catch (error) {
      console.error('Error updating order:', error);
      throw new Error('Failed to update order');
    }
  }

  /**
   * Get order statistics
   */
  async getOrderStats(vendorId?: string, dateFrom?: string, dateTo?: string): Promise<OrderStats> {
    try {
      let query = supabase.from('orders').select('*');

      if (vendorId) {
        // This would need a more complex query to filter by vendor
        // For now, we'll get all orders and filter in memory
      }

      if (dateFrom) {
        query = query.gte('created_at', dateFrom);
      }

      if (dateTo) {
        query = query.lte('created_at', dateTo);
      }

      const { data: orders, error } = await query;

      if (error) throw error;

      // Calculate statistics
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

      const stats: OrderStats = {
        total_orders: orders?.length || 0,
        pending_orders: orders?.filter(o => o.status === 'pending').length || 0,
        processing_orders: orders?.filter(o => ['confirmed', 'processing'].includes(o.status)).length || 0,
        completed_orders: orders?.filter(o => ['shipped', 'delivered'].includes(o.status)).length || 0,
        cancelled_orders: orders?.filter(o => ['cancelled', 'refunded'].includes(o.status)).length || 0,
        total_revenue: orders?.reduce((sum, order) => sum + order.total_amount, 0) || 0,
        average_order_value: orders?.length ? (orders.reduce((sum, order) => sum + order.total_amount, 0) / orders.length) : 0,
        orders_today: orders?.filter(o => new Date(o.created_at) >= today).length || 0,
        orders_this_week: orders?.filter(o => new Date(o.created_at) >= weekAgo).length || 0,
        orders_this_month: orders?.filter(o => new Date(o.created_at) >= monthAgo).length || 0
      };

      return stats;
    } catch (error) {
      console.error('Error fetching order stats:', error);
      throw new Error('Failed to fetch order statistics');
    }
  }

  /**
   * Get shipping cost for option
   */
  async getShippingCost(optionId: string, items: any[]): Promise<ShippingOption> {
    const shippingOptions: ShippingOption[] = [
      { id: 'standard', name: 'Standard Delivery', cost: 2000, estimated_days: 5 },
      { id: 'express', name: 'Express Delivery', cost: 5000, estimated_days: 2 },
      { id: 'pickup', name: 'Store Pickup', cost: 0, estimated_days: 1 }
    ];

    const option = shippingOptions.find(opt => opt.id === optionId);
    if (!option) throw new Error('Invalid shipping option');

    return option;
  }

  /**
   * Validate promo code
   */
  async validatePromoCode(code: string, orderAmount: number): Promise<PromoCode | null> {
    try {
      // In a real implementation, this would query the database
      const promoCodes: PromoCode[] = [
        {
          code: 'WELCOME10',
          discount_type: 'percentage',
          discount_value: 10,
          minimum_amount: 10000
        },
        {
          code: 'SAVE5000',
          discount_type: 'fixed',
          discount_value: 5000,
          minimum_amount: 20000
        }
      ];

      const promo = promoCodes.find(p => p.code.toLowerCase() === code.toLowerCase());
      
      if (!promo) return null;
      
      if (promo.minimum_amount && orderAmount < promo.minimum_amount) {
        throw new Error(`Minimum order amount is ₦${promo.minimum_amount.toLocaleString()}`);
      }

      return promo;
    } catch (error) {
      console.error('Error validating promo code:', error);
      throw error;
    }
  }

  /**
   * Calculate discount amount
   */
  private calculateDiscount(promo: PromoCode, amount: number): number {
    if (promo.discount_type === 'percentage') {
      return (amount * promo.discount_value) / 100;
    } else {
      return promo.discount_value;
    }
  }

  /**
   * Update promo code usage
   */
  private async updatePromoCodeUsage(code: string): Promise<void> {
    try {
      // In a real implementation, this would update the database
      console.log(`Promo code ${code} used`);
    } catch (error) {
      console.error('Error updating promo code usage:', error);
    }
  }

  /**
   * Send order notifications
   */
  private async sendOrderNotifications(order: Order): Promise<void> {
    try {
      // Send customer confirmation
      await whatsappService.sendOrderConfirmation(order, order.items);

      // Group items by vendor and send notifications
      const vendorGroups = this.groupItemsByVendorMap(order.items);

      for (const [vendorId, items] of vendorGroups) {
        const vendor = items[0].vendor;
        if (vendor.whatsapp_number) {
          await whatsappService.sendOrderToVendor(order, items, vendor);
        }
      }
    } catch (error) {
      console.error('Error sending order notifications:', error);
    }
  }

  /**
   * Send status update notification
   */
  private async sendStatusUpdateNotification(order: Order, newStatus: string): Promise<void> {
    try {
      if (order.customer_phone) {
        const statusMessages: Record<string, string> = {
          confirmed: 'Your order has been confirmed and is being prepared.',
          processing: 'Your order is being processed and will be shipped soon.',
          shipped: 'Great news! Your order has been shipped and is on its way.',
          delivered: 'Your order has been delivered. Thank you for shopping with us!'
        };

        const message = statusMessages[newStatus];
        if (message) {
          // TODO: Send WhatsApp status update
          console.log(`Status update sent for order ${order.order_number}: ${message}`);
        }
      }
    } catch (error) {
      console.error('Error sending status update:', error);
    }
  }

  /**
   * Group items by vendor (for order processing)
   */
  private groupItemsByVendor(items: any[]): Record<string, any> {
    return items.reduce((groups, item) => {
      const vendorId = item.product.vendor.id;
      if (!groups[vendorId]) {
        groups[vendorId] = {
          vendor: item.product.vendor,
          items: [],
          total: 0
        };
      }
      groups[vendorId].items.push(item);
      groups[vendorId].total += item.product.price * item.quantity;
      return groups;
    }, {} as Record<string, any>);
  }

  /**
   * Group items by vendor (for notifications)
   */
  private groupItemsByVendorMap(items: OrderItem[]): Map<string, OrderItem[]> {
    return items.reduce((groups, item) => {
      const vendorId = item.vendor.id;
      if (!groups.has(vendorId)) {
        groups.set(vendorId, []);
      }
      groups.get(vendorId)!.push(item);
      return groups;
    }, new Map<string, OrderItem[]>());
  }

  /**
   * Map Supabase order data to Order interface
   */
  private mapSupabaseOrderToOrder(data: any): Order {
    return {
      id: data.id,
      order_number: data.order_number,
      customer_name: data.customer_name,
      customer_phone: data.customer_phone,
      customer_email: data.customer_email,
      customer_address: data.customer_address,
      total_amount: data.total_amount,
      status: data.status,
      payment_method: data.payment_method,
      payment_status: data.payment_status,
      tour_context: data.tour_context,
      created_at: data.created_at,
      updated_at: data.updated_at,
      items: data.items.map((item: any) => ({
        id: item.id,
        product: {
          id: item.product.id,
          title: item.product.title,
          price: item.product.price,
          images: item.product.images || []
        },
        vendor: {
          id: item.vendor.id,
          name: item.vendor.name,
          whatsapp_number: item.vendor.whatsapp_number
        },
        quantity: item.quantity,
        price: item.price
      }))
    };
  }
}

export const orderService = new OrderService();
