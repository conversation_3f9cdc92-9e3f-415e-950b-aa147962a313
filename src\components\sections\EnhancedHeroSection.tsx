
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Play, ArrowRight } from 'lucide-react'
import { Link } from 'react-router-dom'
import { useScrollAnimation, getAnimationClasses } from '@/hooks/useScrollAnimation'

const EnhancedHeroSection = () => {
  const { elementRef: badgeRef, isVisible: badgeVisible } = useScrollAnimation({ threshold: 0.1, rootMargin: '0px 0px 200px 0px' });
  const { elementRef: titleRef, isVisible: titleVisible } = useScrollAnimation({ threshold: 0.1, rootMargin: '0px 0px 150px 0px' });
  const { elementRef: subtitleRef, isVisible: subtitleVisible } = useScrollAnimation({ threshold: 0.1, rootMargin: '0px 0px 100px 0px' });
  const { elementRef: ctaRef, isVisible: ctaVisible } = useScrollAnimation({ threshold: 0.1, rootMargin: '0px 0px 50px 0px' });

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden pt-16 lg:pt-40">
      {/* Background Image with Overlay - Fast Loading */}
      <div className="absolute inset-0 z-0">
        <img
          src="https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2053&q=80"
          alt="Modern interior space for virtual tours"
          className="w-full h-full object-cover object-center"
          loading="eager"
          decoding="sync"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-black/60 via-black/40 to-black/60"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="max-w-5xl mx-auto text-center space-y-5 lg:space-y-12">
          {/* Enhanced Trust Badge */}
          <div className="flex justify-center pt-4 lg:pt-6 mb-5 lg:mb-12">
            <Badge
              ref={badgeRef}
              variant="secondary"
              className={`inline-flex items-center rounded-lg glass-card text-white px-6 py-3 text-sm font-medium shadow-enhanced-lg hover-scale safe-fade-in ${badgeVisible ? 'animate' : ''}`}
            >
              <span className="mr-2">🇳🇬</span>
              Nigeria's Leading Virtual Tour Platform
            </Badge>
          </div>

          {/* Enhanced Main Headline */}
          <div className="space-y-6">
            <h1
              ref={titleRef}
              className={`text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight tracking-tight drop-shadow-lg safe-fade-in ${titleVisible ? 'animate' : ''}`}
            >
              Create Stunning
              <br />
              <span className="gradient-text drop-shadow-lg">Virtual Tours</span>
            </h1>

            {/* Enhanced Subheadline */}
            <p
              ref={subtitleRef}
              className={`text-lg sm:text-xl md:text-2xl text-white/90 leading-relaxed max-w-4xl mx-auto font-normal drop-shadow-md safe-fade-in ${subtitleVisible ? 'animate' : ''}`}
            >
              Transform any space into an immersive 360° experience that captivates your audience and drives business growth.
            </p>
          </div>
          
          {/* Enhanced CTA Buttons */}
          <div
            ref={ctaRef}
            className={`flex flex-col sm:flex-row gap-4 justify-center items-center safe-fade-in ${ctaVisible ? 'animate' : ''}`}
          >
            <Button
              size="lg"
              className="w-full sm:w-auto text-lg px-8 py-4 bg-primary hover:bg-primary/90 text-primary-foreground rounded-lg font-medium shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 btn-glow button-press touch-feedback mobile-friendly"
              asChild
            >
              <Link to="/contact" className="flex items-center justify-center">
                Start Creating Tours
                <ArrowRight className="w-5 h-5 ml-2 icon-hover" />
              </Link>
            </Button>
            <Button
              size="lg"
              className="w-full sm:w-auto text-lg px-8 py-4 bg-transparent border-2 border-white text-white hover:bg-white hover:text-gray-900 backdrop-blur-sm rounded-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 button-press touch-feedback mobile-friendly"
              asChild
            >
              <Link to="/showcase" className="flex items-center justify-center">
                <Play className="w-5 h-5 mr-2 icon-hover" />
                Watch Demo
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}

export default EnhancedHeroSection
