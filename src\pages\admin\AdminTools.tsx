/**
 * Admin Tools & Diagnostics
 * Consolidates system health, logs, mobile preview, and diagnostic tools
 */

import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Activity, 
  Smartphone, 
  FileText, 
  Settings,
  CheckCircle,
  AlertTriangle,
  XCircle,
  RefreshCw,
  Download,
  Search,
  Filter,
  Monitor,
  Database,
  Server,
  Wifi,
  HardDrive,
  Cpu,
  MemoryStick
} from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';

const AdminTools = () => {
  const [activeTab, setActiveTab] = useState('diagnostics');
  const [logFilter, setLogFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock system health data
  const systemHealth = {
    overall: 'healthy',
    uptime: '99.9%',
    responseTime: '245ms',
    lastCheck: '2 minutes ago',
    services: [
      { name: 'Database', status: 'healthy', responseTime: '12ms' },
      { name: 'File Storage', status: 'healthy', responseTime: '45ms' },
      { name: 'API Server', status: 'healthy', responseTime: '89ms' },
      { name: 'CDN', status: 'warning', responseTime: '156ms' },
      { name: 'Email Service', status: 'healthy', responseTime: '234ms' }
    ],
    metrics: {
      cpu: 45,
      memory: 67,
      disk: 23,
      network: 12
    }
  };

  // Mock error logs
  const errorLogs = [
    {
      id: 1,
      level: 'error',
      message: 'Failed to load tour thumbnail',
      timestamp: '2024-01-15 14:30:25',
      source: 'TourService',
      count: 3
    },
    {
      id: 2,
      level: 'warning',
      message: 'Slow database query detected',
      timestamp: '2024-01-15 14:25:10',
      source: 'DatabaseService',
      count: 1
    },
    {
      id: 3,
      level: 'info',
      message: 'User login successful',
      timestamp: '2024-01-15 14:20:45',
      source: 'AuthService',
      count: 15
    },
    {
      id: 4,
      level: 'error',
      message: 'CloudPano API timeout',
      timestamp: '2024-01-15 14:15:30',
      source: 'CloudPanoService',
      count: 2
    }
  ];

  const getStatusBadge = (status: string) => {
    const variants = {
      'healthy': { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      'warning': { variant: 'secondary' as const, icon: AlertTriangle, color: 'text-yellow-600' },
      'error': { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' }
    };
    
    const config = variants[status as keyof typeof variants] || variants.error;
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`w-3 h-3 ${config.color}`} />
        {status}
      </Badge>
    );
  };

  const getLogLevelBadge = (level: string) => {
    const variants = {
      'error': { variant: 'destructive' as const, color: 'text-red-600' },
      'warning': { variant: 'secondary' as const, color: 'text-yellow-600' },
      'info': { variant: 'outline' as const, color: 'text-blue-600' }
    };
    
    const config = variants[level as keyof typeof variants] || variants.info;
    
    return (
      <Badge variant={config.variant}>
        {level.toUpperCase()}
      </Badge>
    );
  };

  const filteredLogs = errorLogs.filter(log => {
    const matchesSearch = log.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.source.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = logFilter === 'all' || log.level === logFilter;
    
    return matchesSearch && matchesFilter;
  });

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Tools & Diagnostics</h1>
            <p className="text-muted-foreground">
              System health monitoring, error logs, and diagnostic tools
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh All
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Export Logs
            </Button>
          </div>
        </div>

        {/* System Health Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">System Status</p>
                  <p className="text-2xl font-bold">Healthy</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Uptime</p>
                  <p className="text-2xl font-bold">{systemHealth.uptime}</p>
                </div>
                <Activity className="w-8 h-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Response Time</p>
                  <p className="text-2xl font-bold">{systemHealth.responseTime}</p>
                </div>
                <Server className="w-8 h-8 text-purple-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Last Check</p>
                  <p className="text-2xl font-bold">{systemHealth.lastCheck}</p>
                </div>
                <RefreshCw className="w-8 h-8 text-orange-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tools Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
            <TabsTrigger value="diagnostics" className="flex items-center gap-2">
              <Activity className="w-4 h-4" />
              <span className="hidden sm:inline">Diagnostics</span>
            </TabsTrigger>
            <TabsTrigger value="logs" className="flex items-center gap-2">
              <FileText className="w-4 h-4" />
              <span className="hidden sm:inline">Error Logs</span>
            </TabsTrigger>
            <TabsTrigger value="mobile" className="flex items-center gap-2">
              <Smartphone className="w-4 h-4" />
              <span className="hidden sm:inline">Mobile Preview</span>
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center gap-2">
              <Settings className="w-4 h-4" />
              <span className="hidden sm:inline">System Settings</span>
            </TabsTrigger>
          </TabsList>

          {/* System Diagnostics */}
          <TabsContent value="diagnostics" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Service Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Server className="w-5 h-5" />
                    Service Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {systemHealth.services.map((service, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full ${
                            service.status === 'healthy' ? 'bg-green-500' : 
                            service.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                          }`}></div>
                          <span className="font-medium">{service.name}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-muted-foreground">{service.responseTime}</span>
                          {getStatusBadge(service.status)}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* System Metrics */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Monitor className="w-5 h-5" />
                    System Metrics
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Cpu className="w-4 h-4 text-blue-600" />
                        <span className="text-sm font-medium">CPU Usage</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-blue-600 transition-all"
                            style={{ width: `${systemHealth.metrics.cpu}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">{systemHealth.metrics.cpu}%</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <MemoryStick className="w-4 h-4 text-green-600" />
                        <span className="text-sm font-medium">Memory Usage</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-green-600 transition-all"
                            style={{ width: `${systemHealth.metrics.memory}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">{systemHealth.metrics.memory}%</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <HardDrive className="w-4 h-4 text-purple-600" />
                        <span className="text-sm font-medium">Disk Usage</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-purple-600 transition-all"
                            style={{ width: `${systemHealth.metrics.disk}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">{systemHealth.metrics.disk}%</span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Wifi className="w-4 h-4 text-orange-600" />
                        <span className="text-sm font-medium">Network I/O</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-20 h-2 bg-muted rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-orange-600 transition-all"
                            style={{ width: `${systemHealth.metrics.network}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">{systemHealth.metrics.network}%</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Diagnostic Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button variant="outline" className="h-20 flex-col">
                    <Database className="w-6 h-6 mb-2" />
                    Test Database
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <Wifi className="w-6 h-6 mb-2" />
                    Check Connectivity
                  </Button>
                  <Button variant="outline" className="h-20 flex-col">
                    <RefreshCw className="w-6 h-6 mb-2" />
                    Clear Cache
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Error Logs */}
          <TabsContent value="logs" className="space-y-6">
            {/* Log Filters */}
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                      <Input
                        placeholder="Search logs..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <Select value={logFilter} onValueChange={setLogFilter}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Filter by level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Levels</SelectItem>
                      <SelectItem value="error">Error</SelectItem>
                      <SelectItem value="warning">Warning</SelectItem>
                      <SelectItem value="info">Info</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Logs List */}
            <Card>
              <CardHeader>
                <CardTitle>System Logs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredLogs.map((log) => (
                    <div key={log.id} className="flex items-start justify-between p-4 border rounded-lg">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-2">
                          {getLogLevelBadge(log.level)}
                          <span className="text-sm text-muted-foreground">{log.source}</span>
                          {log.count > 1 && (
                            <Badge variant="outline" className="text-xs">
                              {log.count}x
                            </Badge>
                          )}
                        </div>
                        <p className="font-medium mb-1">{log.message}</p>
                        <p className="text-sm text-muted-foreground">{log.timestamp}</p>
                      </div>
                      <Button size="sm" variant="outline">
                        <FileText className="w-4 h-4 mr-1" />
                        Details
                      </Button>
                    </div>
                  ))}
                </div>

                {filteredLogs.length === 0 && (
                  <div className="text-center py-8">
                    <FileText className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-semibold mb-2">No logs found</h3>
                    <p className="text-muted-foreground">
                      {searchTerm || logFilter !== 'all'
                        ? 'Try adjusting your filters'
                        : 'System logs will appear here'
                      }
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Mobile Preview */}
          <TabsContent value="mobile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Smartphone className="w-5 h-5" />
                  Mobile Preview Mode
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Test how your tours and pages look on mobile devices.
                </p>
                <div className="text-center py-8">
                  <Smartphone className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">Mobile preview tools coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* System Settings */}
          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  System Configuration
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground mb-4">
                  Advanced system settings and configuration options.
                </p>
                <div className="text-center py-8">
                  <Settings className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">System settings coming soon</p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminTools;
