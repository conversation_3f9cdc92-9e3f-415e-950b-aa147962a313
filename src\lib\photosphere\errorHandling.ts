/**
 * Production Error Handling for Photo Sphere Viewer
 * Comprehensive error boundaries, retry mechanisms, and graceful degradation
 */

import { toast } from 'sonner';

export enum PSVErrorCode {
  // Initialization errors
  CONTAINER_NOT_FOUND = 'CONTAINER_NOT_FOUND',
  INVALID_PANORAMA = 'INVALID_PANORAMA',
  WEBGL_NOT_SUPPORTED = 'WEBGL_NOT_SUPPORTED',
  INIT_TIMEOUT = 'INIT_TIMEOUT',
  
  // Runtime errors
  PANORAMA_LOAD_FAILED = 'PANORAMA_LOAD_FAILED',
  PLUGIN_ERROR = 'PLUGIN_ERROR',
  MEMORY_LIMIT_EXCEEDED = 'MEMORY_LIMIT_EXCEEDED',
  WEBGL_CONTEXT_LOST = 'WEBGL_CONTEXT_LOST',
  
  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  CORS_ERROR = 'CORS_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  
  // User errors
  INVALID_INPUT = 'INVALID_INPUT',
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  
  // System errors
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
  BROWSER_NOT_SUPPORTED = 'BROWSER_NOT_SUPPORTED'
}

export interface PSVErrorDetails {
  code: PSVErrorCode;
  message: string;
  originalError?: any;
  context?: Record<string, any>;
  timestamp: string;
  userAgent: string;
  url: string;
  stack?: string;
  recoverable: boolean;
  retryable: boolean;
}

export interface RecoveryStrategy {
  name: string;
  description: string;
  execute: () => Promise<boolean>;
  fallback?: () => Promise<boolean>;
}

export interface ErrorHandlingOptions {
  enableRetry?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  enableFallback?: boolean;
  enableReporting?: boolean;
  onError?: (error: PSVErrorDetails) => void;
  onRecovery?: (strategy: string) => void;
}

/**
 * Production Error Handler for PSV
 */
export class PSVErrorHandler {
  private options: Required<ErrorHandlingOptions>;
  private errorHistory: PSVErrorDetails[] = [];
  private retryAttempts = new Map<string, number>();
  private recoveryStrategies = new Map<PSVErrorCode, RecoveryStrategy[]>();

  constructor(options: ErrorHandlingOptions = {}) {
    this.options = {
      enableRetry: true,
      maxRetries: 3,
      retryDelay: 1000,
      enableFallback: true,
      enableReporting: true,
      onError: () => {},
      onRecovery: () => {},
      ...options
    };

    this.setupRecoveryStrategies();
    this.setupGlobalErrorHandlers();
  }

  /**
   * Handle PSV errors with recovery strategies
   */
  async handleError(
    error: any,
    code: PSVErrorCode = PSVErrorCode.UNKNOWN_ERROR,
    context?: Record<string, any>
  ): Promise<boolean> {
    const errorDetails = this.createErrorDetails(error, code, context);
    
    // Log error
    this.logError(errorDetails);
    
    // Add to history
    this.errorHistory.push(errorDetails);
    
    // Notify error callback
    this.options.onError(errorDetails);
    
    // Show user notification
    this.showUserNotification(errorDetails);
    
    // Attempt recovery if possible
    if (errorDetails.recoverable) {
      const recovered = await this.attemptRecovery(errorDetails);
      if (recovered) {
        return true;
      }
    }
    
    // Attempt retry if enabled and retryable
    if (this.options.enableRetry && errorDetails.retryable) {
      const retried = await this.attemptRetry(errorDetails);
      if (retried) {
        return true;
      }
    }
    
    // Fallback strategies
    if (this.options.enableFallback) {
      return await this.attemptFallback(errorDetails);
    }
    
    return false;
  }

  /**
   * Create detailed error information
   */
  private createErrorDetails(
    error: any,
    code: PSVErrorCode,
    context?: Record<string, any>
  ): PSVErrorDetails {
    const isRecoverable = this.isRecoverable(code);
    const isRetryable = this.isRetryable(code);
    
    return {
      code,
      message: error?.message || 'Unknown error occurred',
      originalError: error,
      context: {
        ...context,
        viewport: {
          width: window.innerWidth,
          height: window.innerHeight
        },
        memory: this.getMemoryInfo(),
        webgl: this.getWebGLInfo()
      },
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      stack: error?.stack,
      recoverable: isRecoverable,
      retryable: isRetryable
    };
  }

  /**
   * Setup recovery strategies for different error types
   */
  private setupRecoveryStrategies(): void {
    // WebGL context lost recovery
    this.recoveryStrategies.set(PSVErrorCode.WEBGL_CONTEXT_LOST, [
      {
        name: 'refresh-viewer',
        description: 'Refresh the viewer instance',
        execute: async () => {
          // This would be implemented by the calling code
          return false;
        }
      },
      {
        name: 'reload-page',
        description: 'Reload the entire page',
        execute: async () => {
          window.location.reload();
          return true;
        }
      }
    ]);

    // Panorama load failed recovery
    this.recoveryStrategies.set(PSVErrorCode.PANORAMA_LOAD_FAILED, [
      {
        name: 'retry-load',
        description: 'Retry loading the panorama',
        execute: async () => {
          // Implemented by calling code
          return false;
        }
      },
      {
        name: 'load-fallback',
        description: 'Load a fallback panorama',
        execute: async () => {
          // Load default/fallback panorama
          return false;
        }
      }
    ]);

    // Memory limit exceeded recovery
    this.recoveryStrategies.set(PSVErrorCode.MEMORY_LIMIT_EXCEEDED, [
      {
        name: 'clear-cache',
        description: 'Clear viewer cache',
        execute: async () => {
          // Clear PSV cache
          return false;
        }
      },
      {
        name: 'reduce-quality',
        description: 'Reduce image quality',
        execute: async () => {
          // Switch to lower quality images
          return false;
        }
      }
    ]);

    // Network error recovery
    this.recoveryStrategies.set(PSVErrorCode.NETWORK_ERROR, [
      {
        name: 'retry-request',
        description: 'Retry the network request',
        execute: async () => {
          // Retry with exponential backoff
          return false;
        }
      },
      {
        name: 'use-cached',
        description: 'Use cached version if available',
        execute: async () => {
          // Try to use cached data
          return false;
        }
      }
    ]);
  }

  /**
   * Attempt recovery using available strategies
   */
  private async attemptRecovery(errorDetails: PSVErrorDetails): Promise<boolean> {
    const strategies = this.recoveryStrategies.get(errorDetails.code);
    if (!strategies) return false;

    for (const strategy of strategies) {
      try {
        console.log(`Attempting recovery strategy: ${strategy.name}`);
        const success = await strategy.execute();
        
        if (success) {
          console.log(`Recovery successful: ${strategy.name}`);
          this.options.onRecovery(strategy.name);
          toast.success('Issue resolved automatically');
          return true;
        }
      } catch (recoveryError) {
        console.warn(`Recovery strategy failed: ${strategy.name}`, recoveryError);
      }
    }

    return false;
  }

  /**
   * Attempt retry with exponential backoff
   */
  private async attemptRetry(errorDetails: PSVErrorDetails): Promise<boolean> {
    const retryKey = `${errorDetails.code}-${errorDetails.context?.operation || 'default'}`;
    const currentAttempts = this.retryAttempts.get(retryKey) || 0;
    
    if (currentAttempts >= this.options.maxRetries) {
      console.log(`Max retries exceeded for ${retryKey}`);
      return false;
    }

    const delay = this.options.retryDelay * Math.pow(2, currentAttempts);
    console.log(`Retrying in ${delay}ms (attempt ${currentAttempts + 1}/${this.options.maxRetries})`);
    
    await new Promise(resolve => setTimeout(resolve, delay));
    
    this.retryAttempts.set(retryKey, currentAttempts + 1);
    
    // The actual retry would be handled by the calling code
    return false;
  }

  /**
   * Attempt fallback strategies
   */
  private async attemptFallback(errorDetails: PSVErrorDetails): Promise<boolean> {
    switch (errorDetails.code) {
      case PSVErrorCode.WEBGL_NOT_SUPPORTED:
        return this.fallbackToStaticImage();
      
      case PSVErrorCode.PANORAMA_LOAD_FAILED:
        return this.fallbackToPlaceholder();
      
      case PSVErrorCode.PLUGIN_ERROR:
        return this.fallbackToBasicViewer();
      
      default:
        return this.fallbackToErrorMessage();
    }
  }

  /**
   * Fallback to static image viewer
   */
  private async fallbackToStaticImage(): Promise<boolean> {
    console.log('Falling back to static image viewer');
    toast.info('3D viewer not supported. Showing static image.');
    // Implementation would be handled by calling code
    return false;
  }

  /**
   * Fallback to placeholder image
   */
  private async fallbackToPlaceholder(): Promise<boolean> {
    console.log('Falling back to placeholder image');
    toast.info('Loading fallback image...');
    // Implementation would be handled by calling code
    return false;
  }

  /**
   * Fallback to basic viewer without plugins
   */
  private async fallbackToBasicViewer(): Promise<boolean> {
    console.log('Falling back to basic viewer');
    toast.info('Loading simplified viewer...');
    // Implementation would be handled by calling code
    return false;
  }

  /**
   * Fallback to error message
   */
  private async fallbackToErrorMessage(): Promise<boolean> {
    console.log('Showing error message');
    // Implementation would be handled by calling code
    return true;
  }

  /**
   * Setup global error handlers
   */
  private setupGlobalErrorHandlers(): void {
    // Unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      if (this.isPSVRelatedError(event.reason)) {
        this.handleError(event.reason, PSVErrorCode.UNKNOWN_ERROR, {
          type: 'unhandledrejection'
        });
      }
    });

    // Global errors
    window.addEventListener('error', (event) => {
      if (this.isPSVRelatedError(event.error)) {
        this.handleError(event.error, PSVErrorCode.UNKNOWN_ERROR, {
          type: 'error',
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        });
      }
    });
  }

  /**
   * Check if error is PSV-related
   */
  private isPSVRelatedError(error: any): boolean {
    if (!error) return false;
    
    const errorString = error.toString().toLowerCase();
    const stack = error.stack?.toLowerCase() || '';
    
    return errorString.includes('photosphere') ||
           errorString.includes('psv') ||
           stack.includes('photosphere') ||
           stack.includes('psv');
  }

  /**
   * Determine if error is recoverable
   */
  private isRecoverable(code: PSVErrorCode): boolean {
    const recoverableCodes = [
      PSVErrorCode.WEBGL_CONTEXT_LOST,
      PSVErrorCode.MEMORY_LIMIT_EXCEEDED,
      PSVErrorCode.PLUGIN_ERROR,
      PSVErrorCode.NETWORK_ERROR
    ];
    
    return recoverableCodes.includes(code);
  }

  /**
   * Determine if error is retryable
   */
  private isRetryable(code: PSVErrorCode): boolean {
    const retryableCodes = [
      PSVErrorCode.PANORAMA_LOAD_FAILED,
      PSVErrorCode.NETWORK_ERROR,
      PSVErrorCode.TIMEOUT_ERROR,
      PSVErrorCode.INIT_TIMEOUT
    ];
    
    return retryableCodes.includes(code);
  }

  /**
   * Log error for debugging
   */
  private logError(errorDetails: PSVErrorDetails): void {
    console.group(`🔴 PSV Error: ${errorDetails.code}`);
    console.error('Message:', errorDetails.message);
    console.error('Context:', errorDetails.context);
    console.error('Original Error:', errorDetails.originalError);
    if (errorDetails.stack) {
      console.error('Stack:', errorDetails.stack);
    }
    console.groupEnd();
  }

  /**
   * Show user-friendly notification
   */
  private showUserNotification(errorDetails: PSVErrorDetails): void {
    const userMessages = {
      [PSVErrorCode.WEBGL_NOT_SUPPORTED]: 'Your browser doesn\'t support 3D graphics. Please update your browser.',
      [PSVErrorCode.PANORAMA_LOAD_FAILED]: 'Failed to load the 360° image. Please check your connection.',
      [PSVErrorCode.NETWORK_ERROR]: 'Network connection issue. Please check your internet connection.',
      [PSVErrorCode.MEMORY_LIMIT_EXCEEDED]: 'The tour is too large for your device. Try closing other tabs.',
      [PSVErrorCode.BROWSER_NOT_SUPPORTED]: 'Your browser is not supported. Please use a modern browser.',
      [PSVErrorCode.WEBGL_CONTEXT_LOST]: 'Graphics context lost. Attempting to recover...'
    };

    const message = userMessages[errorDetails.code] || 'An unexpected error occurred. Please try refreshing the page.';
    
    if (errorDetails.recoverable || errorDetails.retryable) {
      toast.error(message, {
        description: 'Attempting to recover automatically...'
      });
    } else {
      toast.error(message);
    }
  }

  /**
   * Get memory information
   */
  private getMemoryInfo(): any {
    if ('memory' in performance) {
      return {
        usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
        totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
        jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit
      };
    }
    return null;
  }

  /**
   * Get WebGL information
   */
  private getWebGLInfo(): any {
    try {
      const canvas = document.createElement('canvas');
      const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      if (!gl) return { supported: false };
      
      return {
        supported: true,
        vendor: gl.getParameter(gl.VENDOR),
        renderer: gl.getParameter(gl.RENDERER),
        version: gl.getParameter(gl.VERSION),
        maxTextureSize: gl.getParameter(gl.MAX_TEXTURE_SIZE)
      };
    } catch (error) {
      return { supported: false, error: error.message };
    }
  }

  /**
   * Get error history
   */
  getErrorHistory(): PSVErrorDetails[] {
    return [...this.errorHistory];
  }

  /**
   * Clear error history
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
    this.retryAttempts.clear();
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    totalErrors: number;
    errorsByCode: Record<string, number>;
    recentErrors: PSVErrorDetails[];
  } {
    const errorsByCode: Record<string, number> = {};
    
    this.errorHistory.forEach(error => {
      errorsByCode[error.code] = (errorsByCode[error.code] || 0) + 1;
    });
    
    const recentErrors = this.errorHistory
      .filter(error => {
        const errorTime = new Date(error.timestamp).getTime();
        const now = Date.now();
        return (now - errorTime) < 24 * 60 * 60 * 1000; // Last 24 hours
      })
      .slice(-10); // Last 10 errors
    
    return {
      totalErrors: this.errorHistory.length,
      errorsByCode,
      recentErrors
    };
  }
}

// Export singleton instance
export const psvErrorHandler = new PSVErrorHandler();
