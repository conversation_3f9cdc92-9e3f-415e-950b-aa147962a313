/**
 * WhatsApp Service
 * Handles WhatsApp Business API integration for order processing
 * Extends existing VRT WhatsApp functionality for e-commerce
 */

import type { CartItem, CustomerInfo } from '@/components/commerce/ShoppingCart';

export interface Order {
  id: string;
  order_number: string;
  customer_name: string;
  customer_phone: string;
  customer_email?: string;
  customer_address?: string;
  total_amount: number;
  status: string;
  tour_context?: {
    tourId: string;
    tourTitle: string;
    sceneId: string;
  };
  created_at: string;
}

export interface OrderItem {
  id: string;
  product: {
    id: string;
    title: string;
    price: number;
    images: string[];
  };
  quantity: number;
  price: number;
}

export interface Vendor {
  id: string;
  name: string;
  whatsapp_number?: string;
  commission_rate: number;
}

export class WhatsAppService {
  private readonly baseUrl = 'https://graph.facebook.com/v17.0';
  private readonly phoneNumberId: string;
  private readonly accessToken: string;

  constructor() {
    this.phoneNumberId = import.meta.env.VITE_WHATSAPP_BUSINESS_PHONE_ID || '';
    this.accessToken = import.meta.env.VITE_WHATSAPP_ACCESS_TOKEN || '';
  }

  /**
   * Send order confirmation to customer
   */
  async sendOrderConfirmation(
    order: Order, 
    items: CartItem[]
  ): Promise<boolean> {
    try {
      if (!this.isConfigured()) {
        console.warn('WhatsApp not configured, skipping order confirmation');
        return false;
      }

      const message = this.createCustomerOrderMessage(order, items);
      
      const response = await fetch(`${this.baseUrl}/${this.phoneNumberId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messaging_product: 'whatsapp',
          to: order.customer_phone,
          type: 'text',
          text: { body: message }
        })
      });

      const result = await response.json();
      
      if (!response.ok) {
        console.error('WhatsApp API error:', result);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error sending WhatsApp confirmation:', error);
      return false;
    }
  }

  /**
   * Send order notification to vendor
   */
  async sendOrderToVendor(
    order: Order, 
    vendorItems: CartItem[], 
    vendor: Vendor
  ): Promise<boolean> {
    try {
      if (!this.isConfigured() || !vendor.whatsapp_number) {
        console.warn('WhatsApp not configured or vendor has no WhatsApp number');
        return false;
      }

      const message = this.createVendorOrderMessage(order, vendorItems, vendor);
      
      const response = await fetch(`${this.baseUrl}/${this.phoneNumberId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messaging_product: 'whatsapp',
          to: vendor.whatsapp_number,
          type: 'text',
          text: { body: message }
        })
      });

      const result = await response.json();
      
      if (!response.ok) {
        console.error('WhatsApp API error:', result);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error sending WhatsApp to vendor:', error);
      return false;
    }
  }

  /**
   * Send product inquiry to vendor
   */
  async sendProductInquiry(
    customerPhone: string,
    customerName: string,
    product: { id: string; title: string; price: number },
    vendor: Vendor,
    tourContext?: { tourTitle: string; sceneId: string }
  ): Promise<boolean> {
    try {
      if (!this.isConfigured() || !vendor.whatsapp_number) {
        return false;
      }

      const message = this.createProductInquiryMessage(
        customerPhone, 
        customerName, 
        product, 
        tourContext
      );
      
      const response = await fetch(`${this.baseUrl}/${this.phoneNumberId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          messaging_product: 'whatsapp',
          to: vendor.whatsapp_number,
          type: 'text',
          text: { body: message }
        })
      });

      return response.ok;
    } catch (error) {
      console.error('Error sending product inquiry:', error);
      return false;
    }
  }

  /**
   * Handle incoming WhatsApp webhooks (for order confirmations)
   */
  async handleWebhook(webhookData: any): Promise<void> {
    try {
      const message = webhookData.entry?.[0]?.changes?.[0]?.value?.messages?.[0];
      
      if (message && message.text) {
        const text = message.text.body.toUpperCase();
        const from = message.from;
        
        if (text.includes('ACCEPT') || text.includes('CONFIRM')) {
          const orderNumber = this.extractOrderNumber(text);
          if (orderNumber) {
            await this.updateOrderStatus(orderNumber, from, 'confirmed');
          }
        } else if (text.includes('REJECT') || text.includes('CANCEL')) {
          const orderNumber = this.extractOrderNumber(text);
          if (orderNumber) {
            await this.updateOrderStatus(orderNumber, from, 'cancelled');
          }
        }
      }
    } catch (error) {
      console.error('Error handling WhatsApp webhook:', error);
    }
  }

  /**
   * Create customer order confirmation message
   */
  private createCustomerOrderMessage(order: Order, items: CartItem[]): string {
    const itemList = items.map(item => 
      `• ${item.product.title} (${item.quantity}x) - ₦${(item.product.price * item.quantity).toLocaleString()}`
    ).join('\n');

    const tourInfo = order.tour_context ? 
      `\n🎯 *Discovered in Tour:* ${order.tour_context.tourTitle}` : '';

    return `
🎉 *Order Confirmed!* - ${order.order_number}

Thank you ${order.customer_name}! Your order has been placed successfully.

📦 *Your Items:*
${itemList}

💰 *Total:* ₦${order.total_amount.toLocaleString()}
📱 *Payment:* WhatsApp Checkout${tourInfo}

📞 *Next Steps:*
• Vendors will contact you directly
• Payment details will be shared
• Delivery will be arranged

Questions? Reply to this message!

---
*VirtualRealTour E-commerce*
🌐 Discover. Shop. Experience.
    `.trim();
  }

  /**
   * Create vendor order notification message
   */
  private createVendorOrderMessage(
    order: Order, 
    items: CartItem[], 
    vendor: Vendor
  ): string {
    const itemList = items.map(item => 
      `• ${item.product.title} (${item.quantity}x) - ₦${(item.product.price * item.quantity).toLocaleString()}`
    ).join('\n');

    const vendorTotal = items.reduce(
      (sum, item) => sum + (item.product.price * item.quantity), 
      0
    );

    const commission = vendorTotal * vendor.commission_rate;
    const vendorEarnings = vendorTotal - commission;

    const tourInfo = order.tour_context ? 
      `\n🎯 *Found in Tour:* ${order.tour_context.tourTitle} - Scene ${order.tour_context.sceneId}` : '';

    return `
🛒 *New Order!* - ${order.order_number}

👤 *Customer Details:*
Name: ${order.customer_name}
Phone: ${order.customer_phone}
${order.customer_email ? `Email: ${order.customer_email}` : ''}
${order.customer_address ? `Address: ${order.customer_address}` : ''}

📦 *Your Items:*
${itemList}

💰 *Financial Summary:*
• Subtotal: ₦${vendorTotal.toLocaleString()}
• Platform Fee (${(vendor.commission_rate * 100)}%): ₦${commission.toLocaleString()}
• *Your Earnings: ₦${vendorEarnings.toLocaleString()}*${tourInfo}

📞 *Action Required:*
Reply "CONFIRM ${order.order_number}" to accept
Reply "REJECT ${order.order_number}" to decline

Contact customer directly: ${order.customer_phone}

---
*VirtualRealTour Vendor Portal*
    `.trim();
  }

  /**
   * Create product inquiry message
   */
  private createProductInquiryMessage(
    customerPhone: string,
    customerName: string,
    product: { id: string; title: string; price: number },
    tourContext?: { tourTitle: string; sceneId: string }
  ): string {
    const tourInfo = tourContext ? 
      `\n🎯 *Found in Tour:* ${tourContext.tourTitle}` : '';

    return `
💬 *Product Inquiry*

👤 *Customer:*
Name: ${customerName}
Phone: ${customerPhone}

🛍️ *Product Interest:*
${product.title}
Price: ₦${product.price.toLocaleString()}${tourInfo}

The customer is interested in this product. Please contact them directly to discuss details, availability, and arrange purchase.

---
*VirtualRealTour Platform*
    `.trim();
  }

  /**
   * Extract order number from WhatsApp message
   */
  private extractOrderNumber(text: string): string | null {
    const match = text.match(/VRT-\d{8}-\d{4}/);
    return match ? match[0] : null;
  }

  /**
   * Update order status based on vendor response
   */
  private async updateOrderStatus(
    orderNumber: string, 
    vendorPhone: string, 
    status: string
  ): Promise<void> {
    try {
      // This would integrate with your Supabase backend
      // For now, we'll log the status change
      console.log(`Order ${orderNumber} ${status} by vendor ${vendorPhone}`);
      
      // TODO: Update order status in database
      // TODO: Notify customer of status change
    } catch (error) {
      console.error('Error updating order status:', error);
    }
  }

  /**
   * Check if WhatsApp is properly configured
   */
  private isConfigured(): boolean {
    return !!(this.phoneNumberId && this.accessToken);
  }

  /**
   * Generate WhatsApp deep link for direct messaging
   */
  generateWhatsAppLink(
    phoneNumber: string, 
    message: string
  ): string {
    const encodedMessage = encodeURIComponent(message);
    const cleanPhone = phoneNumber.replace(/[^\d]/g, '');
    return `https://wa.me/${cleanPhone}?text=${encodedMessage}`;
  }

  /**
   * Create quick contact message for vendor
   */
  createVendorContactMessage(
    productTitle: string,
    customerName: string,
    tourContext?: { tourTitle: string }
  ): string {
    const tourInfo = tourContext ? ` (found in ${tourContext.tourTitle})` : '';
    
    return `Hi! I'm interested in "${productTitle}"${tourInfo}. My name is ${customerName}. Can you provide more details?`;
  }
}

export const whatsappService = new WhatsAppService();
