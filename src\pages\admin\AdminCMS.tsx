/**
 * Admin Content Management System
 * Complete CRUD system for managing all application content
 */

import { useState, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  Upload,
  Save,
  RefreshCw,
  Edit,
  Trash2,
  Plus,
  Image as ImageIcon,
  Palette,
  Globe,
  Settings
} from 'lucide-react';
import AdminLayout from '@/components/admin/AdminLayout';
import { toast } from 'sonner';

interface ContentItem {
  id: string;
  type: 'text' | 'image' | 'background' | 'color' | 'setting';
  key: string;
  value: string;
  description: string;
  category: string;
  updated_at: string;
}

const AdminCMS = () => {
  const [isSaving, setIsSaving] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Content management state
  const [content, setContent] = useState<ContentItem[]>([
    // Hero Section Content
    {
      id: '1',
      type: 'text',
      key: 'hero_title',
      value: 'Create Stunning Virtual Tours',
      description: 'Main hero section title',
      category: 'hero',
      updated_at: new Date().toISOString()
    },
    {
      id: '2',
      type: 'text',
      key: 'hero_subtitle',
      value: 'Transform any space into an immersive 360° experience that captivates your audience and drives business growth.',
      description: 'Hero section subtitle',
      category: 'hero',
      updated_at: new Date().toISOString()
    },
    {
      id: '3',
      type: 'image',
      key: 'hero_background',
      value: 'https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2053&q=80',
      description: 'Hero section background image',
      category: 'hero',
      updated_at: new Date().toISOString()
    },
    // Site Settings
    {
      id: '4',
      type: 'text',
      key: 'site_name',
      value: 'VirtualRealTour',
      description: 'Website name',
      category: 'site',
      updated_at: new Date().toISOString()
    },
    {
      id: '5',
      type: 'text',
      key: 'site_description',
      value: 'Professional 360° Virtual Tours in Nigeria',
      description: 'Site meta description',
      category: 'site',
      updated_at: new Date().toISOString()
    },
    {
      id: '6',
      type: 'color',
      key: 'primary_color',
      value: '#3b82f6',
      description: 'Primary brand color',
      category: 'theme',
      updated_at: new Date().toISOString()
    },
    // About Section Content
    {
      id: '7',
      type: 'text',
      key: 'about_title',
      value: 'About VirtualRealTour',
      description: 'About section title',
      category: 'about',
      updated_at: new Date().toISOString()
    },
    {
      id: '8',
      type: 'text',
      key: 'about_description',
      value: 'We are Nigeria\'s leading virtual tour platform, transforming how businesses showcase their spaces.',
      description: 'About section description',
      category: 'about',
      updated_at: new Date().toISOString()
    },
    {
      id: '9',
      type: 'image',
      key: 'about_image',
      value: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80',
      description: 'About section image',
      category: 'about',
      updated_at: new Date().toISOString()
    },
    // Services Section Content
    {
      id: '10',
      type: 'text',
      key: 'services_title',
      value: 'Our Services',
      description: 'Services section title',
      category: 'services',
      updated_at: new Date().toISOString()
    },
    {
      id: '11',
      type: 'text',
      key: 'services_subtitle',
      value: 'Comprehensive virtual tour solutions for every business need',
      description: 'Services section subtitle',
      category: 'services',
      updated_at: new Date().toISOString()
    },
    // Contact Section Content
    {
      id: '12',
      type: 'text',
      key: 'contact_title',
      value: 'Get In Touch',
      description: 'Contact section title',
      category: 'contact',
      updated_at: new Date().toISOString()
    },
    {
      id: '13',
      type: 'text',
      key: 'contact_subtitle',
      value: 'Ready to transform your space? Let\'s create something amazing together.',
      description: 'Contact section subtitle',
      category: 'contact',
      updated_at: new Date().toISOString()
    },
    // Footer Content
    {
      id: '14',
      type: 'text',
      key: 'footer_description',
      value: 'Nigeria\'s premier virtual tour platform. Creating immersive experiences that drive business growth.',
      description: 'Footer description text',
      category: 'footer',
      updated_at: new Date().toISOString()
    },
    {
      id: '15',
      type: 'text',
      key: 'footer_copyright',
      value: '© 2024 VirtualRealTour. All rights reserved.',
      description: 'Footer copyright text',
      category: 'footer',
      updated_at: new Date().toISOString()
    }
  ]);

  const [editingItem, setEditingItem] = useState<ContentItem | null>(null);
  const [newItem, setNewItem] = useState<Partial<ContentItem>>({
    type: 'text',
    category: 'general'
  });

  // Group content by category
  const contentByCategory = content.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {} as Record<string, ContentItem[]>);

  const handleSaveItem = async (item: ContentItem) => {
    setIsSaving(true);
    try {
      // Update content in state
      setContent(prev => prev.map(c => c.id === item.id ? { ...item, updated_at: new Date().toISOString() } : c));
      
      // Save to localStorage for persistence
      localStorage.setItem('vrt_cms_content', JSON.stringify(content));
      
      toast.success('Content updated successfully');
      setEditingItem(null);
    } catch (error) {
      toast.error('Failed to save content');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteItem = async (id: string) => {
    try {
      setContent(prev => prev.filter(c => c.id !== id));
      localStorage.setItem('vrt_cms_content', JSON.stringify(content.filter(c => c.id !== id)));
      toast.success('Content deleted successfully');
    } catch (error) {
      toast.error('Failed to delete content');
    }
  };

  const handleAddItem = async () => {
    if (!newItem.key || !newItem.value) {
      toast.error('Please fill in all required fields');
      return;
    }

    const item: ContentItem = {
      id: Date.now().toString(),
      type: newItem.type || 'text',
      key: newItem.key,
      value: newItem.value,
      description: newItem.description || '',
      category: newItem.category || 'general',
      updated_at: new Date().toISOString()
    };

    setContent(prev => [...prev, item]);
    localStorage.setItem('vrt_cms_content', JSON.stringify([...content, item]));
    
    setNewItem({ type: 'text', category: 'general' });
    toast.success('Content added successfully');
  };

  const handleImageUpload = async (file: File, itemId: string) => {
    try {
      // In a real app, upload to cloud storage
      // For now, create a local URL
      const imageUrl = URL.createObjectURL(file);

      setContent(prev => prev.map(c =>
        c.id === itemId
          ? { ...c, value: imageUrl, updated_at: new Date().toISOString() }
          : c
      ));

      toast.success('Image uploaded successfully');
    } catch (error) {
      toast.error('Failed to upload image');
    }
  };

  const renderContentEditor = (item: ContentItem) => {
    if (editingItem?.id === item.id) {
      return (
        <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
          <div className="space-y-2">
            <Label htmlFor={`edit-${item.id}`}>Value</Label>
            {item.type === 'text' ? (
              <Textarea
                id={`edit-${item.id}`}
                value={editingItem.value}
                onChange={(e) => setEditingItem({ ...editingItem, value: e.target.value })}
                rows={3}
              />
            ) : item.type === 'color' ? (
              <Input
                id={`edit-${item.id}`}
                type="color"
                value={editingItem.value}
                onChange={(e) => setEditingItem({ ...editingItem, value: e.target.value })}
              />
            ) : (
              <Input
                id={`edit-${item.id}`}
                value={editingItem.value}
                onChange={(e) => setEditingItem({ ...editingItem, value: e.target.value })}
              />
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor={`desc-${item.id}`}>Description</Label>
            <Input
              id={`desc-${item.id}`}
              value={editingItem.description}
              onChange={(e) => setEditingItem({ ...editingItem, description: e.target.value })}
            />
          </div>
          
          <div className="flex gap-2">
            <Button
              onClick={() => handleSaveItem(editingItem)}
              disabled={isSaving}
              size="sm"
            >
              {isSaving ? <RefreshCw className="w-4 h-4 animate-spin" /> : <Save className="w-4 h-4" />}
              Save
            </Button>
            <Button
              onClick={() => setEditingItem(null)}
              variant="outline"
              size="sm"
            >
              Cancel
            </Button>
          </div>
        </div>
      );
    }

    return (
      <div className="flex items-center justify-between p-4 border rounded-lg">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <Badge variant="outline" className="text-xs">
              {item.type}
            </Badge>
            <code className="text-xs bg-muted px-2 py-1 rounded">{item.key}</code>
          </div>
          <p className="text-sm text-muted-foreground mb-2">{item.description}</p>
          
          {item.type === 'image' ? (
            <div className="flex items-center gap-2">
              <img src={item.value} alt={item.key} className="w-16 h-16 object-cover rounded" />
              <span className="text-sm truncate">{item.value}</span>
            </div>
          ) : item.type === 'color' ? (
            <div className="flex items-center gap-2">
              <div
                className="w-6 h-6 rounded border color-swatch"
                data-color={item.value}
                title={`Color: ${item.value}`}
              />
              <span className="text-sm">{item.value}</span>
            </div>
          ) : (
            <p className="text-sm line-clamp-2">{item.value}</p>
          )}
        </div>
        
        <div className="flex items-center gap-2 ml-4">
          {item.type === 'image' && (
            <Button
              onClick={() => fileInputRef.current?.click()}
              variant="outline"
              size="sm"
            >
              <Upload className="w-4 h-4" />
            </Button>
          )}
          <Button
            onClick={() => setEditingItem(item)}
            variant="outline"
            size="sm"
          >
            <Edit className="w-4 h-4" />
          </Button>
          <Button
            onClick={() => handleDeleteItem(item.id)}
            variant="outline"
            size="sm"
            className="text-red-600 hover:text-red-700"
          >
            <Trash2 className="w-4 h-4" />
          </Button>
        </div>
        
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          className="hidden"
          title="Upload image file"
          aria-label="Upload image file"
          onChange={(e) => {
            const file = e.target.files?.[0];
            if (file) handleImageUpload(file, item.id);
          }}
        />
      </div>
    );
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold">Content Management</h1>
            <p className="text-muted-foreground">Manage all website content, images, and settings</p>
          </div>
        </div>

        <Tabs defaultValue="content" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="content">Content</TabsTrigger>
            <TabsTrigger value="images">Images</TabsTrigger>
            <TabsTrigger value="theme">Theme</TabsTrigger>
            <TabsTrigger value="add">Add New</TabsTrigger>
          </TabsList>

          <TabsContent value="content" className="space-y-6">
            {Object.entries(contentByCategory).map(([category, items]) => (
              <Card key={category}>
                <CardHeader>
                  <CardTitle className="capitalize flex items-center gap-2">
                    {category === 'hero' && <Globe className="w-5 h-5" />}
                    {category === 'site' && <Settings className="w-5 h-5" />}
                    {category === 'theme' && <Palette className="w-5 h-5" />}
                    {category} Content
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {items.filter(item => item.type === 'text').map(item => (
                    <div key={item.id}>
                      {renderContentEditor(item)}
                    </div>
                  ))}
                </CardContent>
              </Card>
            ))}
          </TabsContent>

          <TabsContent value="images" className="space-y-6">
            {Object.entries(contentByCategory).map(([category, items]) => {
              const imageItems = items.filter(item => item.type === 'image');
              if (imageItems.length === 0) return null;
              
              return (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle className="capitalize flex items-center gap-2">
                      <ImageIcon className="w-5 h-5" />
                      {category} Images
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {imageItems.map(item => (
                      <div key={item.id}>
                        {renderContentEditor(item)}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>

          <TabsContent value="theme" className="space-y-6">
            {Object.entries(contentByCategory).map(([category, items]) => {
              const themeItems = items.filter(item => item.type === 'color');
              if (themeItems.length === 0) return null;
              
              return (
                <Card key={category}>
                  <CardHeader>
                    <CardTitle className="capitalize flex items-center gap-2">
                      <Palette className="w-5 h-5" />
                      {category} Colors
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {themeItems.map(item => (
                      <div key={item.id}>
                        {renderContentEditor(item)}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              );
            })}
          </TabsContent>

          <TabsContent value="add" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Plus className="w-5 h-5" />
                  Add New Content
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="newKey">Key</Label>
                    <Input
                      id="newKey"
                      value={newItem.key || ''}
                      onChange={(e) => setNewItem({ ...newItem, key: e.target.value })}
                      placeholder="e.g., hero_title"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="newCategory">Category</Label>
                    <Input
                      id="newCategory"
                      value={newItem.category || ''}
                      onChange={(e) => setNewItem({ ...newItem, category: e.target.value })}
                      placeholder="e.g., hero, site, theme"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="newValue">Value</Label>
                  <Textarea
                    id="newValue"
                    value={newItem.value || ''}
                    onChange={(e) => setNewItem({ ...newItem, value: e.target.value })}
                    placeholder="Content value..."
                    rows={3}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="newDescription">Description</Label>
                  <Input
                    id="newDescription"
                    value={newItem.description || ''}
                    onChange={(e) => setNewItem({ ...newItem, description: e.target.value })}
                    placeholder="Description of this content..."
                  />
                </div>
                
                <Button onClick={handleAddItem} className="w-full">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Content
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  );
};

export default AdminCMS;
