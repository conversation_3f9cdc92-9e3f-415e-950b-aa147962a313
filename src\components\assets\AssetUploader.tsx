/**
 * Asset Uploader Component
 * Drag-and-drop uploader for 360° images with validation and progress tracking
 */

import React, { useState, useCallback, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  Image, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  X,
  FileImage,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';

import { assetManager, type AssetMetadata, type UploadOptions } from '@/lib/assets/assetManager';

export interface AssetUploaderProps {
  onUploadComplete?: (assets: AssetMetadata[]) => void;
  onUploadError?: (error: string) => void;
  uploadOptions?: UploadOptions;
  maxFiles?: number;
  acceptedFormats?: string[];
  className?: string;
}

interface UploadItem {
  id: string;
  file: File;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  result?: AssetMetadata;
  error?: string;
  warnings?: string[];
}

const AssetUploader: React.FC<AssetUploaderProps> = ({
  onUploadComplete,
  onUploadError,
  uploadOptions = {},
  maxFiles = 10,
  acceptedFormats = ['image/jpeg', 'image/png', 'image/webp'],
  className = ''
}) => {
  const [uploadItems, setUploadItems] = useState<UploadItem[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);


  // Process uploads
  const processUploads = useCallback(async (items: UploadItem[]) => {
    setIsUploading(true);
    const completedAssets: AssetMetadata[] = [];
    try {
      for (const item of items) {
        try {
          // Update status to uploading
          setUploadItems(prev => prev.map(i => 
            i.id === item.id ? { ...i, status: 'uploading', progress: 10 } : i
          ));

          // Simulate progress during upload
          const progressInterval = setInterval(() => {
            setUploadItems(prev => prev.map(i => 
              i.id === item.id && i.progress < 90 
                ? { ...i, progress: i.progress + 10 } 
                : i
            ));
          }, 200);

          // Upload the asset
          const result = await assetManager.uploadAsset(item.file, uploadOptions);
          
          clearInterval(progressInterval);

          // Update with result
          setUploadItems(prev => prev.map(i => 
            i.id === item.id 
              ? { 
                  ...i, 
                  status: 'completed', 
                  progress: 100, 
                  result,
                  warnings: result.validation.warnings
                } 
              : i
          ));

          completedAssets.push(result);
          toast.success(`${item.file.name} uploaded successfully`);

        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Upload failed';
          
          setUploadItems(prev => prev.map(i => 
            i.id === item.id 
              ? { ...i, status: 'error', error: errorMessage } 
              : i
          ));

          toast.error(`${item.file.name}: ${errorMessage}`);
          onUploadError?.(errorMessage);
        }
      }

      if (completedAssets.length > 0) {
        onUploadComplete?.(completedAssets);
      }

    } finally {
      setIsUploading(false);
    }
  }, [uploadOptions, onUploadComplete, onUploadError]);

  // Handle file selection
  const handleFiles = useCallback(async (files: FileList) => {
    const fileArray = Array.from(files);
    // Validate file count
    if (uploadItems.length + fileArray.length > maxFiles) {
      toast.error(`Maximum ${maxFiles} files allowed`);
      return;
    }
    // Filter and validate files
    const validFiles = fileArray.filter(file => {
      if (!acceptedFormats.includes(file.type)) {
        toast.error(`${file.name}: Unsupported format`);
        return false;
      }
      return true;
    });
    if (validFiles.length === 0) return;
    // Create upload items
    const newItems: UploadItem[] = validFiles.map(file => ({
      id: `${file.name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      file,
      status: 'pending',
      progress: 0
    }));
    setUploadItems(prev => [...prev, ...newItems]);
    // Start uploading
    await processUploads(newItems);
  }, [uploadItems.length, maxFiles, acceptedFormats, processUploads]);


  // Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFiles(files);
    }
  }, [handleFiles]);

  // Remove upload item
  const removeItem = useCallback((itemId: string) => {
    setUploadItems(prev => prev.filter(item => item.id !== itemId));
  }, []);

  // Clear all items
  const clearAll = useCallback(() => {
    setUploadItems([]);
  }, []);

  // Retry failed upload
  const retryUpload = useCallback(async (item: UploadItem) => {
    await processUploads([{ ...item, status: 'pending', progress: 0, error: undefined }]);
  }, [processUploads]);

  const completedCount = uploadItems.filter(item => item.status === 'completed').length;
  const errorCount = uploadItems.filter(item => item.status === 'error').length;
  const totalWarnings = uploadItems.reduce((count, item) => count + (item.warnings?.length || 0), 0);

  return (
    <div className={`asset-uploader space-y-4 ${className}`}>
      {/* Upload Area */}
      <Card
        className={`border-2 border-dashed transition-colors cursor-pointer ${
          isDragOver 
            ? 'border-primary bg-primary/5' 
            : 'border-muted-foreground/25 hover:border-primary/50'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
        role="region"
        aria-label="Upload 360 degree images"
        tabIndex={0}
        onKeyDown={e => {
          if (e.key === 'Enter' || e.key === ' ') fileInputRef.current?.click();
        }}
      >
        <CardContent className="p-8 text-center">
          <div className="flex flex-col items-center gap-4">
            <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center">
              <Upload className="w-8 h-8 text-muted-foreground" aria-hidden="true" />
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2" id="asset-uploader-title">Upload 360° Images</h3>
              <p className="text-muted-foreground mb-4" id="asset-uploader-desc">
                Drag and drop your panoramic images here, or click to browse
              </p>
              <div className="flex flex-wrap gap-2 justify-center text-xs text-muted-foreground">
                <Badge variant="outline">JPEG</Badge>
                <Badge variant="outline">PNG</Badge>
                <Badge variant="outline">WebP</Badge>
                <Badge variant="outline">Max {maxFiles} files</Badge>
                <Badge variant="outline">2:1 aspect ratio recommended</Badge>
              </div>
            </div>
            <Button
              variant="outline"
              disabled={isUploading}
              aria-label="Choose files to upload"
              title="Choose files to upload"
              onClick={() => fileInputRef.current?.click()}
            >
              <FileImage className="w-4 h-4 mr-2" aria-hidden="true" />
              Choose Files
            </Button>
          </div>
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptedFormats.join(',')}
        className="hidden"
        onChange={(e) => e.target.files && handleFiles(e.target.files)}
        aria-label="Select files to upload"
        title="Select files to upload"
        placeholder="Select files to upload"
      />

      {/* Upload Progress */}
      {uploadItems.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">
                Upload Progress ({completedCount}/{uploadItems.length})
              </CardTitle>
              
              <div className="flex items-center gap-2">
                {completedCount > 0 && (
                  <Badge variant="default" className="bg-green-500">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    {completedCount} completed
                  </Badge>
                )}
                
                {errorCount > 0 && (
                  <Badge variant="destructive">
                    <XCircle className="w-3 h-3 mr-1" />
                    {errorCount} failed
                  </Badge>
                )}
                
                {totalWarnings > 0 && (
                  <Badge variant="secondary">
                    <AlertTriangle className="w-3 h-3 mr-1" />
                    {totalWarnings} warnings
                  </Badge>
                )}
                
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAll}
                  disabled={isUploading}
                >
                  Clear All
                </Button>
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-3">
            {uploadItems.map((item) => (
              <div key={item.id} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    <div className="flex-shrink-0">
                      {item.status === 'completed' && (
                        <CheckCircle className="w-5 h-5 text-green-500" />
                      )}
                      {item.status === 'error' && (
                        <XCircle className="w-5 h-5 text-red-500" />
                      )}
                      {(item.status === 'uploading' || item.status === 'processing') && (
                        <Loader2 className="w-5 h-5 animate-spin text-blue-500" />
                      )}
                      {item.status === 'pending' && (
                        <Image className="w-5 h-5 text-muted-foreground" />
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{item.file.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {(item.file.size / 1024 / 1024).toFixed(1)} MB
                        {item.result && ` • ${item.result.width}x${item.result.height}`}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    {item.status === 'error' && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => retryUpload(item)}
                      >
                        Retry
                      </Button>
                    )}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeItem(item.id)}
                      disabled={item.status === 'uploading'}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                {(item.status === 'uploading' || item.status === 'processing') && (
                  <Progress value={item.progress} className="h-2" />
                )}
                
                {item.error && (
                  <Alert variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertDescription>{item.error}</AlertDescription>
                  </Alert>
                )}
                
                {item.warnings && item.warnings.length > 0 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      {item.warnings.join(', ')}
                    </AlertDescription>
                  </Alert>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AssetUploader;
