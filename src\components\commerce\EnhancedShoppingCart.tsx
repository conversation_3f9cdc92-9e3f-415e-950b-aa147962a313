/**
 * EnhancedShoppingCart Component
 * Advanced shopping cart with multi-vendor support, shipping, and promotions
 * Mobile-first responsive design with WhatsApp checkout
 */

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Sheet, 
  SheetContent, 
  SheetDescription, 
  SheetHeader, 
  SheetTitle, 
  SheetTrigger 
} from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  ShoppingCart as CartIcon, 
  Plus, 
  Minus, 
  Trash2, 
  MessageCircle,
  Package,
  MapPin,
  Phone,
  Mail,
  User,
  Truck,
  Gift,
  Percent,
  CreditCard,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import type { CartItem, CustomerInfo } from './ShoppingCart';

interface EnhancedCartItem extends CartItem {
  shipping_cost?: number;
  estimated_delivery?: string;
}

interface ShippingOption {
  id: string;
  name: string;
  description: string;
  cost: number;
  estimated_days: number;
  icon: any;
}

interface PromoCode {
  code: string;
  discount_type: 'percentage' | 'fixed';
  discount_value: number;
  minimum_amount?: number;
  description: string;
}

interface EnhancedShoppingCartProps {
  items: EnhancedCartItem[];
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdateQuantity: (itemId: string, quantity: number) => void;
  onRemoveItem: (itemId: string) => void;
  onCheckout: (customerInfo: CustomerInfo, shippingOption: string, promoCode?: string) => void;
  className?: string;
}

const EnhancedShoppingCart = ({
  items,
  isOpen,
  onOpenChange,
  onUpdateQuantity,
  onRemoveItem,
  onCheckout,
  className
}: EnhancedShoppingCartProps) => {
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    phone: '',
    email: '',
    address: ''
  });
  const [selectedShipping, setSelectedShipping] = useState<string>('standard');
  const [promoCode, setPromoCode] = useState<string>('');
  const [appliedPromo, setAppliedPromo] = useState<PromoCode | null>(null);
  const [isCheckingOut, setIsCheckingOut] = useState(false);
  const [currentStep, setCurrentStep] = useState<'cart' | 'shipping' | 'payment'>('cart');

  // Shipping options
  const shippingOptions: ShippingOption[] = [
    {
      id: 'standard',
      name: 'Standard Delivery',
      description: '3-5 business days',
      cost: 2000,
      estimated_days: 5,
      icon: Package
    },
    {
      id: 'express',
      name: 'Express Delivery',
      description: '1-2 business days',
      cost: 5000,
      estimated_days: 2,
      icon: Truck
    },
    {
      id: 'pickup',
      name: 'Store Pickup',
      description: 'Pick up from vendor location',
      cost: 0,
      estimated_days: 1,
      icon: MapPin
    }
  ];

  // Sample promo codes
  const promoCodes: PromoCode[] = [
    {
      code: 'WELCOME10',
      discount_type: 'percentage',
      discount_value: 10,
      minimum_amount: 10000,
      description: '10% off for new customers'
    },
    {
      code: 'SAVE5000',
      discount_type: 'fixed',
      discount_value: 5000,
      minimum_amount: 20000,
      description: '₦5,000 off orders over ₦20,000'
    }
  ];

  // Calculate totals
  const subtotal = items.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  const selectedShippingOption = shippingOptions.find(option => option.id === selectedShipping);
  const shippingCost = selectedShippingOption?.cost || 0;
  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);

  // Calculate discount
  let discountAmount = 0;
  if (appliedPromo) {
    if (appliedPromo.discount_type === 'percentage') {
      discountAmount = (subtotal * appliedPromo.discount_value) / 100;
    } else {
      discountAmount = appliedPromo.discount_value;
    }
  }

  const total = subtotal + shippingCost - discountAmount;

  // Group items by vendor
  const vendorGroups = items.reduce((groups, item) => {
    const vendorId = item.product.vendor.id;
    if (!groups[vendorId]) {
      groups[vendorId] = {
        vendor: item.product.vendor,
        items: []
      };
    }
    groups[vendorId].items.push(item);
    return groups;
  }, {} as Record<string, { vendor: CartItem['product']['vendor'], items: EnhancedCartItem[] }>);

  const handleQuantityChange = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) {
      onRemoveItem(itemId);
    } else {
      onUpdateQuantity(itemId, newQuantity);
    }
  };

  const handleApplyPromoCode = () => {
    const promo = promoCodes.find(p => p.code.toLowerCase() === promoCode.toLowerCase());
    
    if (!promo) {
      toast.error('Invalid promo code');
      return;
    }

    if (promo.minimum_amount && subtotal < promo.minimum_amount) {
      toast.error(`Minimum order amount is ₦${promo.minimum_amount.toLocaleString()}`);
      return;
    }

    setAppliedPromo(promo);
    toast.success(`Promo code applied! ${promo.description}`);
  };

  const handleRemovePromoCode = () => {
    setAppliedPromo(null);
    setPromoCode('');
    toast.success('Promo code removed');
  };

  const handleCheckout = async () => {
    if (!customerInfo.name || !customerInfo.phone) {
      toast.error('Please fill in your name and phone number');
      return;
    }

    if (items.length === 0) {
      toast.error('Your cart is empty');
      return;
    }

    setIsCheckingOut(true);
    try {
      await onCheckout(customerInfo, selectedShipping, appliedPromo?.code);
      toast.success('Order placed successfully! You will receive WhatsApp confirmation.');
      onOpenChange(false);
      setCurrentStep('cart');
    } catch (error) {
      toast.error('Failed to place order. Please try again.');
    } finally {
      setIsCheckingOut(false);
    }
  };

  const CartItemComponent = ({ item }: { item: EnhancedCartItem }) => (
    <motion.div
      layout
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      className="flex gap-3 p-3 border rounded-lg"
    >
      {/* Product Image */}
      <div className="w-16 h-16 bg-gray-100 rounded-md overflow-hidden flex-shrink-0">
        {item.product.images[0] ? (
          <img
            src={item.product.images[0]}
            alt={item.product.title}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <Package className="w-6 h-6 text-gray-400" />
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-sm line-clamp-2 mb-1">
          {item.product.title}
        </h4>
        <div className="flex items-center gap-1 text-xs text-muted-foreground mb-2">
          <MapPin className="w-3 h-3" />
          <span>{item.product.vendor.name}</span>
        </div>
        {item.tourContext && (
          <div className="text-xs text-blue-600 mb-2">
            From: {item.tourContext.tourTitle}
          </div>
        )}
        <div className="flex items-center justify-between">
          <span className="font-semibold text-sm">
            ₦{(item.product.price * item.quantity).toLocaleString()}
          </span>
          <div className="flex items-center gap-2">
            <Button
              size="sm"
              variant="outline"
              className="w-6 h-6 p-0"
              onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
            >
              <Minus className="w-3 h-3" />
            </Button>
            <span className="text-sm font-medium w-8 text-center">
              {item.quantity}
            </span>
            <Button
              size="sm"
              variant="outline"
              className="w-6 h-6 p-0"
              onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
            >
              <Plus className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="w-6 h-6 p-0 text-red-500 hover:text-red-700"
              onClick={() => onRemoveItem(item.id)}
            >
              <Trash2 className="w-3 h-3" />
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  );

  const CartStep = () => (
    <div className="space-y-4">
      {items.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 text-center">
          <CartIcon className="w-12 h-12 text-gray-400 mb-4" />
          <h3 className="font-medium text-lg mb-2">Your cart is empty</h3>
          <p className="text-muted-foreground text-sm">
            Add some products to get started
          </p>
        </div>
      ) : (
        <>
          {/* Cart Items */}
          <AnimatePresence>
            {items.map((item) => (
              <CartItemComponent key={item.id} item={item} />
            ))}
          </AnimatePresence>

          <Separator />

          {/* Promo Code */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Gift className="w-4 h-4" />
                Promo Code
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              {appliedPromo ? (
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                  <div>
                    <div className="font-medium text-sm text-green-800">{appliedPromo.code}</div>
                    <div className="text-xs text-green-600">{appliedPromo.description}</div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleRemovePromoCode}
                    className="text-green-700 hover:text-green-800"
                  >
                    Remove
                  </Button>
                </div>
              ) : (
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter promo code"
                    value={promoCode}
                    onChange={(e) => setPromoCode(e.target.value)}
                    className="flex-1"
                  />
                  <Button
                    variant="outline"
                    onClick={handleApplyPromoCode}
                    disabled={!promoCode}
                  >
                    Apply
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Order Summary */}
          <Card>
            <CardContent className="p-4 space-y-2">
              <div className="flex justify-between text-sm">
                <span>Subtotal ({itemCount} items)</span>
                <span>₦{subtotal.toLocaleString()}</span>
              </div>
              {appliedPromo && (
                <div className="flex justify-between text-sm text-green-600">
                  <span>Discount ({appliedPromo.code})</span>
                  <span>-₦{discountAmount.toLocaleString()}</span>
                </div>
              )}
              <div className="flex justify-between text-sm">
                <span>Shipping</span>
                <span>{shippingCost === 0 ? 'Free' : `₦${shippingCost.toLocaleString()}`}</span>
              </div>
              <Separator />
              <div className="flex justify-between font-semibold">
                <span>Total</span>
                <span>₦{total.toLocaleString()}</span>
              </div>
            </CardContent>
          </Card>

          <Button
            className="w-full"
            onClick={() => setCurrentStep('shipping')}
          >
            Continue to Shipping
          </Button>
        </>
      )}
    </div>
  );

  const ShippingStep = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCurrentStep('cart')}
        >
          ← Back
        </Button>
        <h3 className="font-semibold">Shipping Options</h3>
      </div>

      <div className="space-y-3">
        {shippingOptions.map((option) => {
          const Icon = option.icon;
          return (
            <Card
              key={option.id}
              className={cn(
                "cursor-pointer transition-all duration-200",
                selectedShipping === option.id 
                  ? "ring-2 ring-blue-500 bg-blue-50" 
                  : "hover:shadow-md"
              )}
              onClick={() => setSelectedShipping(option.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <div className={cn(
                    "w-10 h-10 rounded-lg flex items-center justify-center",
                    selectedShipping === option.id 
                      ? "bg-blue-100 text-blue-600" 
                      : "bg-gray-100 text-gray-600"
                  )}>
                    <Icon className="w-5 h-5" />
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">{option.name}</div>
                    <div className="text-sm text-muted-foreground">{option.description}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">
                      {option.cost === 0 ? 'Free' : `₦${option.cost.toLocaleString()}`}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {option.estimated_days} day{option.estimated_days !== 1 ? 's' : ''}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <Button
        className="w-full"
        onClick={() => setCurrentStep('payment')}
      >
        Continue to Payment
      </Button>
    </div>
  );

  const PaymentStep = () => (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setCurrentStep('shipping')}
        >
          ← Back
        </Button>
        <h3 className="font-semibold">Customer Information</h3>
      </div>

      <div className="space-y-3">
        <div>
          <Label htmlFor="customer-name" className="text-xs">
            Full Name *
          </Label>
          <Input
            id="customer-name"
            placeholder="Enter your full name"
            value={customerInfo.name}
            onChange={(e) => setCustomerInfo(prev => ({ ...prev, name: e.target.value }))}
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="customer-phone" className="text-xs">
            Phone Number *
          </Label>
          <Input
            id="customer-phone"
            placeholder="+234 xxx xxx xxxx"
            value={customerInfo.phone}
            onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="customer-email" className="text-xs">
            Email (Optional)
          </Label>
          <Input
            id="customer-email"
            type="email"
            placeholder="<EMAIL>"
            value={customerInfo.email}
            onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
            className="mt-1"
          />
        </div>

        <div>
          <Label htmlFor="customer-address" className="text-xs">
            Delivery Address {selectedShipping !== 'pickup' && '*'}
          </Label>
          <Textarea
            id="customer-address"
            placeholder="Enter delivery address"
            value={customerInfo.address}
            onChange={(e) => setCustomerInfo(prev => ({ ...prev, address: e.target.value }))}
            className="mt-1"
            rows={3}
          />
        </div>
      </div>

      {/* Final Order Summary */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Order Summary</CardTitle>
        </CardHeader>
        <CardContent className="pt-0 space-y-2">
          <div className="flex justify-between text-sm">
            <span>Items ({itemCount})</span>
            <span>₦{subtotal.toLocaleString()}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Shipping ({selectedShippingOption?.name})</span>
            <span>{shippingCost === 0 ? 'Free' : `₦${shippingCost.toLocaleString()}`}</span>
          </div>
          {appliedPromo && (
            <div className="flex justify-between text-sm text-green-600">
              <span>Discount</span>
              <span>-₦{discountAmount.toLocaleString()}</span>
            </div>
          )}
          <Separator />
          <div className="flex justify-between font-semibold">
            <span>Total</span>
            <span>₦{total.toLocaleString()}</span>
          </div>
        </CardContent>
      </Card>

      <div className="bg-blue-50 p-4 rounded-lg">
        <div className="flex items-start gap-3">
          <MessageCircle className="w-5 h-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-900 mb-1">WhatsApp Checkout</h4>
            <p className="text-sm text-blue-700">
              Your order will be sent to vendors via WhatsApp. You'll receive confirmation and payment details directly.
            </p>
          </div>
        </div>
      </div>

      <Button
        className="w-full"
        onClick={handleCheckout}
        disabled={isCheckingOut || !customerInfo.name || !customerInfo.phone}
      >
        {isCheckingOut ? 'Processing...' : 'Place Order via WhatsApp'}
      </Button>
    </div>
  );

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetTrigger asChild>
        <Button variant="outline" size="sm" className={cn("relative", className)}>
          <CartIcon className="w-4 h-4" />
          {itemCount > 0 && (
            <Badge 
              variant="destructive" 
              className="absolute -top-2 -right-2 w-5 h-5 p-0 flex items-center justify-center text-xs"
            >
              {itemCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>

      <SheetContent className="w-full sm:max-w-lg flex flex-col">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <CartIcon className="w-5 h-5" />
            Shopping Cart
            {currentStep !== 'cart' && (
              <Badge variant="outline" className="ml-auto">
                {currentStep === 'shipping' ? 'Shipping' : 'Checkout'}
              </Badge>
            )}
          </SheetTitle>
          <SheetDescription>
            {currentStep === 'cart' && `${itemCount} items in your cart`}
            {currentStep === 'shipping' && 'Choose your delivery option'}
            {currentStep === 'payment' && 'Complete your order'}
          </SheetDescription>
        </SheetHeader>

        <div className="flex-1 overflow-y-auto py-4">
          <AnimatePresence mode="wait">
            {currentStep === 'cart' && <CartStep key="cart" />}
            {currentStep === 'shipping' && <ShippingStep key="shipping" />}
            {currentStep === 'payment' && <PaymentStep key="payment" />}
          </AnimatePresence>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default EnhancedShoppingCart;
