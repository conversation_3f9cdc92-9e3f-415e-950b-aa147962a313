/**
 * Photo Sphere Tour Viewer
 * Production-ready 360° virtual tour system with PSV v5
 * Complete enterprise features with hotspots and e-commerce
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Play,
  Pause,
  RotateCcw,
  Maximize,
  ShoppingCart,
  Info,
  Navigation,
  ExternalLink,
  Volume2,
  VolumeX,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';

// Import our PSV factory and types
import { PSVFactory, type PSVInstance } from '@/lib/photosphere/factory';
import type { TourScene, TourMarker } from '@/lib/photosphere/types';
import { useVirtualTour } from '@/hooks/useVirtualTour';

// Legacy interface for backward compatibility
interface TourHotspot {
  id: string;
  type: 'navigation' | 'product' | 'info' | 'link' | 'media';
  position: {
    yaw: number;
    pitch: number;
  };
  title: string;
  content: string;
  targetSceneId?: string;
  productData?: {
    id: string;
    name: string;
    price: number;
    image: string;
    description: string;
    whatsappMessage?: string;
  };
  linkUrl?: string;
  mediaUrl?: string;
  style?: {
    color: string;
    size: 'small' | 'medium' | 'large';
    animation: 'pulse' | 'bounce' | 'none';
  };
}

interface PhotoSphereTourViewerProps {
  scenes?: TourScene[];
  tourId?: string; // For loading from database
  initialSceneId?: string;
  autoRotate?: boolean;
  showControls?: boolean;
  enableVirtualTour?: boolean;
  enableGallery?: boolean;
  enableSettings?: boolean;
  enableRealTimeSync?: boolean;
  onHotspotClick?: (hotspot: TourHotspot) => void;
  onSceneChange?: (sceneId: string) => void;
  onMarkerClick?: (markerId: string, markerData: any) => void;
  onReady?: () => void;
  onError?: (error: any) => void;
  className?: string;
}

const PhotoSphereTourViewer = ({
  scenes: propScenes,
  tourId,
  initialSceneId,
  autoRotate = false,
  showControls = true,
  enableVirtualTour = true,
  enableGallery = true,
  enableSettings = true,
  enableRealTimeSync = false,
  onHotspotClick,
  onSceneChange,
  onMarkerClick,
  onReady,
  onError,
  className = ''
}: PhotoSphereTourViewerProps) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const psvInstanceRef = useRef<PSVInstance | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isAutoRotating, setIsAutoRotating] = useState(autoRotate);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use virtual tour hook if tourId is provided
  const virtualTour = useVirtualTour({
    tourId: tourId || '',
    autoLoad: !!tourId,
    enableRealTimeSync,
    onNodeChange: (nodeId, node) => {
      onSceneChange?.(nodeId);
    },
    onError: (err) => {
      setError(err.message);
      onError?.(err);
    }
  });

  // Use scenes from virtual tour or props
  const scenes = virtualTour.tour?.nodes || propScenes || [];
  const currentSceneId = virtualTour.currentNodeId || initialSceneId || scenes[0]?.id;
  const currentScene = scenes.find(scene => scene.id === currentSceneId);

  // Initialize Photo Sphere Viewer with production-ready implementation
  useEffect(() => {
    if (!containerRef.current || !scenes.length) return;

    const initViewer = async () => {
      const startTime = performance.now();

      try {
        setIsLoading(true);
        setError(null);

        // Cleanup previous instance
        if (psvInstanceRef.current) {
          psvInstanceRef.current.destroy();
          psvInstanceRef.current = null;
        }

        // Ensure container has proper dimensions before PSV initialization
        const container = containerRef.current!;

        // Set explicit dimensions if not already set
        if (!container.style.height) {
          container.style.height = '100%';
        }
        if (!container.style.width) {
          container.style.width = '100%';
        }

        // Force layout calculation
        container.offsetHeight;

        // Validate container dimensions
        if (container.offsetWidth === 0 || container.offsetHeight === 0) {
          throw new Error('Container must have non-zero dimensions for PSV initialization');
        }

        // Validate current scene
        if (!currentScene?.panorama) {
          throw new Error('No panorama image available for current scene');
        }

        // Convert legacy hotspots to PSV markers
        const convertHotspotsToMarkers = (hotspots: TourHotspot[]): TourMarker[] => {
          return hotspots.map(hotspot => ({
            id: hotspot.id,
            type: hotspot.type,
            position: {
              yaw: `${hotspot.position.yaw}deg`,
              pitch: `${hotspot.position.pitch}deg`
            },
            title: hotspot.title,
            content: hotspot.content,
            targetSceneId: hotspot.targetSceneId,
            productData: hotspot.productData,
            linkUrl: hotspot.linkUrl,
            mediaUrl: hotspot.mediaUrl,
            style: hotspot.style,
            onClick: (marker, viewer) => {
              handleMarkerClick(hotspot);
            }
          }));
        };

        // Create PSV instance
        const instance = tourId
          ? await PSVFactory.createVirtualTourFromId(containerRef.current, tourId, {
              enableAutorotate: autoRotate,
              enableGallery,
              enableSettings,
              onReady: () => {
                const loadTime = performance.now() - startTime;
                console.log(`🚀 PSV initialized in ${loadTime.toFixed(2)}ms`);
                setIsInitialized(true);
                setIsLoading(false);
                onReady?.();
                toast.success('Virtual tour loaded successfully!');
              },
              onError: (err) => {
                console.error('PSV Error:', err);
                setError(err.message || 'Failed to load virtual tour');
                setIsLoading(false);
                onError?.(err);
                toast.error('Failed to load virtual tour');
              },
              onSceneChange: (sceneId) => {
                virtualTour.navigateToNode(sceneId);
                onSceneChange?.(sceneId);
              },
              onMarkerClick: (markerId, markerData) => {
                onMarkerClick?.(markerId, markerData);
              }
            })
          : scenes.length > 1 && enableVirtualTour
          ? await PSVFactory.createVirtualTour(containerRef.current, scenes, {
              enableAutorotate: autoRotate,
              enableGallery,
              enableSettings,
              onReady: () => {
                const loadTime = performance.now() - startTime;
                console.log(`🚀 Virtual tour initialized in ${loadTime.toFixed(2)}ms`);
                setIsInitialized(true);
                setIsLoading(false);
                onReady?.();
                toast.success('Virtual tour loaded successfully!');
              },
              onError: (err) => {
                console.error('PSV Error:', err);
                setError(err.message || 'Failed to load virtual tour');
                setIsLoading(false);
                onError?.(err);
                toast.error('Failed to load virtual tour');
              },
              onSceneChange: (sceneId) => {
                onSceneChange?.(sceneId);
              },
              onMarkerClick: (markerId, markerData) => {
                onMarkerClick?.(markerId, markerData);
              }
            })
          : await PSVFactory.createSimple(containerRef.current, currentScene!.panorama, {
              enableAutorotate: autoRotate,
              enableSettings,
              onReady: () => {
                const loadTime = performance.now() - startTime;
                console.log(`🚀 360° viewer initialized in ${loadTime.toFixed(2)}ms`);
                setIsInitialized(true);
                setIsLoading(false);
                onReady?.();
                toast.success('360° viewer loaded successfully!');
              },
              onError: (err) => {
                console.error('PSV Error:', err);
                setError(err.message || 'Failed to load 360° viewer');
                setIsLoading(false);
                onError?.(err);
                toast.error('Failed to load 360° viewer');
              }
            });

        psvInstanceRef.current = instance;

        // Attach virtual tour hook to PSV instance
        if (tourId) {
          virtualTour.attachToPSV(instance);
        }

        // Add markers for current scene if not using virtual tour
        if (!enableVirtualTour && currentScene && instance.markerService) {
          const markers = convertHotspotsToMarkers(currentScene.hotspots || []);
          instance.markerService.addMarkers(markers);
        }

      } catch (error) {
        console.error('Failed to initialize Photo Sphere Viewer:', error);
        setError(error instanceof Error ? error.message : 'Unknown error occurred');
        setIsLoading(false);
        onError?.(error);
        toast.error('Failed to initialize virtual tour');
      }
    };

    initViewer();

    return () => {
      // Cleanup virtual tour integration
      if (tourId) {
        virtualTour.detachFromPSV();
      }

      // Cleanup PSV instance
      if (psvInstanceRef.current) {
        psvInstanceRef.current.destroy();
        psvInstanceRef.current = null;
      }
    };
  }, [scenes, tourId, enableVirtualTour, autoRotate, enableGallery, enableSettings, virtualTour]);

  // Helper function to get marker icons
  const getMarkerIcon = (type: string): string => {
    const iconMap = {
      navigation: '🧭',
      product: '🛍️',
      info: 'ℹ️',
      link: '🔗',
      media: '📷'
    };
    return iconMap[type as keyof typeof iconMap] || '📍';
  };

  // Handle marker clicks
  const handleMarkerClick = useCallback((hotspot: TourHotspot) => {
    onHotspotClick?.(hotspot);

    // Handle different hotspot types
    switch (hotspot.type) {
      case 'navigation':
        if (hotspot.targetSceneId) {
          changeScene(hotspot.targetSceneId);
        }
        break;
      case 'product':
        if (hotspot.productData) {
          handleProductClick(hotspot.productData);
        }
        break;
      case 'link':
        if (hotspot.linkUrl) {
          window.open(hotspot.linkUrl, '_blank');
        }
        break;
      case 'info':
        toast.info(hotspot.content || hotspot.title);
        break;
      case 'media':
        if (hotspot.mediaUrl) {
          handleMediaClick(hotspot.mediaUrl);
        }
        break;
    }
  }, [onHotspotClick]);

  const changeScene = useCallback(async (sceneId: string) => {
    if (sceneId !== currentSceneId) {
      if (tourId) {
        // Use virtual tour service for navigation
        await virtualTour.navigateToNode(sceneId);
      } else {
        // Direct scene change for prop-based tours
        onSceneChange?.(sceneId);
        toast.success(`Moved to ${scenes.find(s => s.id === sceneId)?.name || 'new scene'}`);
      }
    }
  }, [currentSceneId, scenes, tourId, virtualTour, onSceneChange]);

  const handleProductClick = (product: any) => {
    // E-commerce integration - WhatsApp
    const message = product.whatsappMessage || 
      `Hi! I'm interested in ${product.name} (${product.price}) from your virtual tour.`;
    
    const whatsappUrl = `https://wa.me/2349077776066?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
    
    toast.success(`Opening WhatsApp for ${product.name}`);
  };

  const handleMediaClick = (mediaUrl: string) => {
    // Handle media playback
    window.open(mediaUrl, '_blank');
  };

  const toggleAutoRotate = useCallback(() => {
    if (psvInstanceRef.current?.plugins.autorotate) {
      try {
        if (isAutoRotating) {
          psvInstanceRef.current.plugins.autorotate.stop();
        } else {
          psvInstanceRef.current.plugins.autorotate.start();
        }
        setIsAutoRotating(!isAutoRotating);
        toast.success(isAutoRotating ? 'Auto-rotation stopped' : 'Auto-rotation started');
      } catch (error) {
        console.error('Autorotate plugin error:', error);
        toast.error('Failed to toggle auto-rotation');
      }
    }
  }, [isAutoRotating]);

  const resetView = useCallback(() => {
    if (psvInstanceRef.current?.viewer && currentScene) {
      try {
        psvInstanceRef.current.viewer.animate({
          yaw: 0,
          pitch: 0,
          zoom: 50
        });
        toast.success('View reset to default position');
      } catch (error) {
        console.error('Failed to reset view:', error);
        toast.error('Failed to reset view');
      }
    }
  }, [currentScene]);

  const toggleFullscreen = useCallback(() => {
    if (psvInstanceRef.current?.viewer) {
      try {
        if (isFullscreen) {
          psvInstanceRef.current.viewer.exitFullscreen();
        } else {
          psvInstanceRef.current.viewer.enterFullscreen();
        }
        setIsFullscreen(!isFullscreen);
      } catch (error) {
        console.error('Fullscreen toggle error:', error);
        toast.error('Failed to toggle fullscreen');
      }
    }
  }, [isFullscreen]);

  // Error state
  if (error || virtualTour.error) {
    const errorMessage = error || virtualTour.error;
    return (
      <div className="flex items-center justify-center h-96 bg-muted rounded-lg">
        <div className="text-center">
          <div className="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <ExternalLink className="w-8 h-8 text-destructive" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">Failed to Load Tour</h3>
          <p className="text-muted-foreground mb-4">{errorMessage}</p>
          <div className="flex gap-2 justify-center">
            <Button
              onClick={() => {
                setError(null);
                if (tourId) {
                  virtualTour.refreshTour();
                } else {
                  window.location.reload();
                }
              }}
              variant="outline"
              size="sm"
            >
              Retry
            </Button>
            {tourId && (
              <Button
                onClick={() => virtualTour.loadTour()}
                variant="outline"
                size="sm"
              >
                Reload Tour
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // No scenes available
  if (!scenes.length) {
    return (
      <div className="flex items-center justify-center h-96 bg-muted rounded-lg">
        <div className="text-center">
          <div className="w-16 h-16 bg-muted-foreground/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Navigation className="w-8 h-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">No Tour Available</h3>
          <p className="text-muted-foreground">No tour scenes have been configured yet.</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`virtual-tour-container relative ${className}`}>
      {/* Loading Overlay */}
      {(isLoading || virtualTour.isLoading || virtualTour.isLoadingNode) && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-50 rounded-lg">
          <div className="text-center text-white">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              {virtualTour.isLoadingNode ? 'Changing Scene' : 'Loading Virtual Tour'}
            </h3>
            <p className="text-sm opacity-90">
              {virtualTour.isLoadingNode
                ? 'Loading new scene...'
                : tourId
                ? 'Loading tour from database...'
                : scenes.length > 1
                ? 'Initializing 360° tour experience...'
                : 'Loading 360° panorama...'
              }
            </p>
          </div>
        </div>
      )}

      {/* Main Viewer */}
      <div 
        ref={containerRef} 
        className="w-full h-full min-h-[400px] rounded-lg overflow-hidden bg-black"
      />

      {/* Scene Navigation */}
      {scenes.length > 1 && (
        <div className="absolute bottom-4 left-4 right-4 flex gap-2 overflow-x-auto pb-2">
          {scenes.map((scene) => (
            <button
              key={scene.id}
              type="button"
              className={`glass-button flex-shrink-0 px-3 py-2 text-sm flex items-center gap-2 ${
                scene.id === currentSceneId
                  ? 'bg-blue-600/30 border-blue-400'
                  : 'hover:bg-white/20'
              }`}
              onClick={() => changeScene(scene.id)}
            >
              {scene.thumbnail && (
                <img
                  src={scene.thumbnail}
                  alt={scene.name}
                  className="w-6 h-6 rounded object-cover"
                />
              )}
              <span className="hidden sm:inline">{scene.name}</span>
              <span className="sm:hidden">{scene.name.slice(0, 8)}...</span>
            </button>
          ))}
        </div>
      )}

      {/* Control Buttons */}
      {showControls && isInitialized && (
        <div className="absolute top-4 right-4 flex flex-col gap-2">
          <button
            type="button"
            className="glass-button p-2 bg-black/50 border-white/20 hover:bg-black/70 touch-target"
            onClick={toggleAutoRotate}
            title={isAutoRotating ? 'Stop auto-rotation' : 'Start auto-rotation'}
          >
            {isAutoRotating ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </button>
          <button
            type="button"
            className="glass-button p-2 bg-black/50 border-white/20 hover:bg-black/70 touch-target"
            onClick={resetView}
            title="Reset view"
          >
            <RotateCcw className="w-4 h-4" />
          </button>
          <button
            type="button"
            className="glass-button p-2 bg-black/50 border-white/20 hover:bg-black/70 touch-target"
            onClick={toggleFullscreen}
            title="Toggle fullscreen"
          >
            <Maximize className="w-4 h-4" />
          </button>
        </div>
      )}

      {/* Tour Info */}
      {currentScene && (
        <div className="absolute top-4 left-4 glass-card max-w-xs sm:max-w-sm">
          <h3 className="font-semibold text-white">{currentScene.name}</h3>
          {currentScene.description && (
            <p className="text-sm text-white/90 mt-1 line-clamp-2">{currentScene.description}</p>
          )}
          <div className="flex items-center gap-2 mt-2 flex-wrap">
            <span className="glass-button px-2 py-1 text-xs">
              {currentScene.markers?.length || currentScene.hotspots?.length || 0} hotspots
            </span>
            {isAutoRotating && (
              <span className="glass-button px-2 py-1 text-xs bg-blue-600/20">
                Auto-rotating
              </span>
            )}
            {scenes.length > 1 && (
              <span className="glass-button px-2 py-1 text-xs">
                {scenes.length} scenes
              </span>
            )}
          </div>
        </div>
      )}


    </div>
  );
};

export default PhotoSphereTourViewer;
