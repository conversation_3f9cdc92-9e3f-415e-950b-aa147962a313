/**
 * CommonNinja Widget Integration Service
 * Comprehensive service for all CommonNinja widgets used in VirtualRealTour
 */

export interface CommonNinjaWidget {
  id: string;
  type: string;
  title: string;
  description?: string;
  embedUrl: string;
  editUrl?: string;
  settings: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface ImageHotspotConfig {
  title: string;
  description?: string;
  imageUrl: string;
  hotspots: Array<{
    x: number;
    y: number;
    label: string;
    content: string;
    type: 'info' | 'product' | 'link' | 'whatsapp';
    action?: {
      url?: string;
      phone?: string;
      message?: string;
      productId?: string;
    };
  }>;
  settings?: {
    showLabels?: boolean;
    hotspotStyle?: string;
    animation?: string;
  };
}

export interface CatalogConfig {
  title: string;
  description?: string;
  products: Array<{
    id: string;
    name: string;
    description: string;
    price: number;
    imageUrl: string;
    category?: string;
    vendor?: string;
  }>;
  settings?: {
    layout?: 'grid' | 'list' | 'masonry';
    columns?: number;
    showPrices?: boolean;
    enableFilters?: boolean;
    enableSearch?: boolean;
  };
}

export interface BookingConfig {
  title: string;
  description?: string;
  businessInfo: {
    name: string;
    phone: string;
    email: string;
    address?: string;
  };
  services: Array<{
    id: string;
    name: string;
    description: string;
    duration: number;
    price: number;
  }>;
  settings?: {
    timeSlots?: string[];
    workingDays?: string[];
    advanceBookingDays?: number;
  };
}

export interface CarouselConfig {
  title: string;
  description?: string;
  items: Array<{
    id: string;
    title: string;
    description?: string;
    imageUrl: string;
    linkUrl?: string;
  }>;
  settings?: {
    autoplay?: boolean;
    showDots?: boolean;
    showArrows?: boolean;
    slidesToShow?: number;
    infinite?: boolean;
  };
}

export class CommonNinjaWidgetService {
  private apiKey: string;
  private baseUrl: string;

  public static readonly WIDGET_TYPES = {
    VIRTUAL_TOUR: 'virtual_tour',
    IMAGE_HOTSPOT: 'image_hotspot',
    CATALOG: 'catalog',
    IMAGE_CAROUSEL: 'image_carousel',
    VIDEO_CAROUSEL: 'video_carousel',
    IMAGE_GRID_SLIDER: 'image_grid_slider',
    BUSINESS_LISTINGS: 'business_listings',
    MAPS: 'maps',
    BOOKING: 'booking',
    INTERACTIVE_VIDEO: 'interactive_video',
    BEFORE_AFTER: 'before_after',
    STOP_MOTION_PLAYER: 'stop_motion_player',
    IMAGE_SLIDER: 'image_slider',
    SCROLL_PROGRESS: 'scroll_progress'
  } as const;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || import.meta.env.VITE_COMMONNINJA_API_KEY || '';
    this.baseUrl = 'https://api.commoninja.com/platform/api/v1';
  }

  get isConfigured(): boolean {
    return !!this.apiKey;
  }

  private async makeRequest<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<T> {
    if (!this.isConfigured) {
      throw new Error('CommonNinja API key is not configured');
    }

    const url = `${this.baseUrl}${endpoint}`;
    const response = await fetch(url, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Unknown error' }));
      throw new Error(error.message || `HTTP ${response.status}`);
    }

    return response.json();
  }

  /**
   * Create Image Hotspot Widget for Product Placement
   */
  async createImageHotspot(config: ImageHotspotConfig): Promise<CommonNinjaWidget> {
    const widgetConfig = {
      widget_type: CommonNinjaWidgetService.WIDGET_TYPES.IMAGE_HOTSPOT,
      title: config.title,
      description: config.description,
      settings: {
        image_url: config.imageUrl,
        hotspots: config.hotspots,
        show_labels: config.settings?.showLabels !== false,
        hotspot_style: config.settings?.hotspotStyle || 'default',
        animation: config.settings?.animation || 'pulse',
      },
    };

    const response = await this.makeRequest<any>('/widgets', {
      method: 'POST',
      body: JSON.stringify(widgetConfig),
    });

    return this.mapResponseToWidget(response);
  }

  /**
   * Create Product Catalog Widget
   */
  async createCatalog(config: CatalogConfig): Promise<CommonNinjaWidget> {
    const widgetConfig = {
      widget_type: CommonNinjaWidgetService.WIDGET_TYPES.CATALOG,
      title: config.title,
      description: config.description,
      settings: {
        products: config.products,
        layout: config.settings?.layout || 'grid',
        columns: config.settings?.columns || 3,
        show_prices: config.settings?.showPrices !== false,
        enable_filters: config.settings?.enableFilters !== false,
        enable_search: config.settings?.enableSearch !== false,
      },
    };

    const response = await this.makeRequest<any>('/widgets', {
      method: 'POST',
      body: JSON.stringify(widgetConfig),
    });

    return this.mapResponseToWidget(response);
  }

  /**
   * Create Booking Widget for Service Appointments
   */
  async createBooking(config: BookingConfig): Promise<CommonNinjaWidget> {
    const widgetConfig = {
      widget_type: CommonNinjaWidgetService.WIDGET_TYPES.BOOKING,
      title: config.title,
      description: config.description,
      settings: {
        business_info: config.businessInfo,
        services: config.services,
        time_slots: config.settings?.timeSlots || ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00'],
        working_days: config.settings?.workingDays || ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
        advance_booking_days: config.settings?.advanceBookingDays || 30,
      },
    };

    const response = await this.makeRequest<any>('/widgets', {
      method: 'POST',
      body: JSON.stringify(widgetConfig),
    });

    return this.mapResponseToWidget(response);
  }

  /**
   * Create Image Carousel Widget
   */
  async createImageCarousel(config: CarouselConfig): Promise<CommonNinjaWidget> {
    const widgetConfig = {
      widget_type: CommonNinjaWidgetService.WIDGET_TYPES.IMAGE_CAROUSEL,
      title: config.title,
      description: config.description,
      settings: {
        items: config.items,
        autoplay: config.settings?.autoplay || false,
        show_dots: config.settings?.showDots !== false,
        show_arrows: config.settings?.showArrows !== false,
        slides_to_show: config.settings?.slidesToShow || 1,
        infinite: config.settings?.infinite !== false,
      },
    };

    const response = await this.makeRequest<any>('/widgets', {
      method: 'POST',
      body: JSON.stringify(widgetConfig),
    });

    return this.mapResponseToWidget(response);
  }

  /**
   * Update Widget Configuration
   */
  async updateWidget(widgetId: string, updates: Partial<any>): Promise<CommonNinjaWidget> {
    const response = await this.makeRequest<any>(`/widgets/${widgetId}`, {
      method: 'PATCH',
      body: JSON.stringify(updates),
    });

    return this.mapResponseToWidget(response);
  }

  /**
   * Delete Widget
   */
  async deleteWidget(widgetId: string): Promise<void> {
    await this.makeRequest(`/widgets/${widgetId}`, {
      method: 'DELETE',
    });
  }

  /**
   * Get Widget by ID
   */
  async getWidget(widgetId: string): Promise<CommonNinjaWidget> {
    const response = await this.makeRequest<any>(`/widgets/${widgetId}`);
    return this.mapResponseToWidget(response);
  }

  /**
   * List All Widgets
   */
  async listWidgets(type?: string): Promise<CommonNinjaWidget[]> {
    const endpoint = type ? `/widgets?type=${type}` : '/widgets';
    const response = await this.makeRequest<any>(endpoint);
    return response.widgets.map((widget: any) => this.mapResponseToWidget(widget));
  }

  private mapResponseToWidget(response: any): CommonNinjaWidget {
    return {
      id: response.id,
      type: response.widget_type,
      title: response.title,
      description: response.description,
      embedUrl: response.embed_url,
      editUrl: response.edit_url,
      settings: response.settings,
      createdAt: new Date(response.created_at),
      updatedAt: new Date(response.updated_at),
    };
  }
}
