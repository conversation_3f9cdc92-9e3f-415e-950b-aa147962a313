/**
 * Performance Monitoring Utility
 * Advanced performance tracking for VirtualRealTour e-commerce platform
 * Optimized for Nigerian mobile networks and e-commerce metrics
 */

export interface PerformanceMetrics {
  // Core Web Vitals
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  fcp?: number; // First Contentful Paint
  ttfb?: number; // Time to First Byte
  
  // Custom E-commerce Metrics
  timeToInteractive?: number;
  cartLoadTime?: number;
  productSearchTime?: number;
  checkoutFlowTime?: number;
  whatsappResponseTime?: number;
  
  // Network Metrics
  connectionType?: string;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
  
  // User Experience Metrics
  pageLoadTime?: number;
  domContentLoaded?: number;
  resourceLoadTime?: number;
  
  // Mobile-specific Metrics
  deviceMemory?: number;
  hardwareConcurrency?: number;
  
  // Business Metrics
  conversionFunnelStep?: string;
  userEngagementTime?: number;
  bounceRate?: number;
}

export interface PerformanceReport {
  timestamp: string;
  url: string;
  userAgent: string;
  metrics: PerformanceMetrics;
  recommendations: string[];
  score: number;
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {};
  private observers: PerformanceObserver[] = [];
  private startTime: number = performance.now();
  private isMonitoring: boolean = false;

  constructor() {
    this.initializeMonitoring();
  }

  /**
   * Initialize performance monitoring
   */
  private initializeMonitoring(): void {
    if (typeof window === 'undefined') return;

    this.isMonitoring = true;
    this.collectNetworkInfo();
    this.collectDeviceInfo();
    this.setupPerformanceObservers();
    this.trackPageLoad();
    this.trackUserInteractions();
  }

  /**
   * Collect network information
   */
  private collectNetworkInfo(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.metrics.connectionType = connection.type;
      this.metrics.effectiveType = connection.effectiveType;
      this.metrics.downlink = connection.downlink;
      this.metrics.rtt = connection.rtt;
    }
  }

  /**
   * Collect device information
   */
  private collectDeviceInfo(): void {
    if ('deviceMemory' in navigator) {
      this.metrics.deviceMemory = (navigator as any).deviceMemory;
    }
    
    if ('hardwareConcurrency' in navigator) {
      this.metrics.hardwareConcurrency = navigator.hardwareConcurrency;
    }
  }

  /**
   * Setup performance observers for Core Web Vitals
   */
  private setupPerformanceObservers(): void {
    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          this.metrics.lcp = lastEntry.startTime;
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.push(lcpObserver);
      } catch (e) {
        console.warn('LCP observer not supported');
      }

      // First Input Delay (FID)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.metrics.fid = entry.processingStart - entry.startTime;
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.push(fidObserver);
      } catch (e) {
        console.warn('FID observer not supported');
      }

      // Cumulative Layout Shift (CLS)
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          this.metrics.cls = clsValue;
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        this.observers.push(clsObserver);
      } catch (e) {
        console.warn('CLS observer not supported');
      }

      // Navigation timing
      try {
        const navigationObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            this.metrics.fcp = entry.firstContentfulPaint;
            this.metrics.ttfb = entry.responseStart - entry.requestStart;
            this.metrics.domContentLoaded = entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart;
          });
        });
        navigationObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navigationObserver);
      } catch (e) {
        console.warn('Navigation observer not supported');
      }
    }
  }

  /**
   * Track page load performance
   */
  private trackPageLoad(): void {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        
        this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart;
        this.metrics.timeToInteractive = this.calculateTTI();
        
        // Track resource loading
        const resources = performance.getEntriesByType('resource');
        this.metrics.resourceLoadTime = resources.reduce((total, resource) => {
          return total + (resource.responseEnd - resource.startTime);
        }, 0);
      }, 0);
    });
  }

  /**
   * Calculate Time to Interactive (TTI)
   */
  private calculateTTI(): number {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    return navigation.domInteractive - navigation.fetchStart;
  }

  /**
   * Track user interactions
   */
  private trackUserInteractions(): void {
    let engagementStartTime = performance.now();
    let isEngaged = true;

    // Track engagement time
    const trackEngagement = () => {
      if (isEngaged) {
        this.metrics.userEngagementTime = performance.now() - engagementStartTime;
      }
    };

    // Reset engagement timer on user activity
    const resetEngagement = () => {
      engagementStartTime = performance.now();
      isEngaged = true;
    };

    // Track when user becomes inactive
    const handleInactivity = () => {
      isEngaged = false;
    };

    // Event listeners for user activity
    ['click', 'scroll', 'keypress', 'mousemove', 'touchstart'].forEach(event => {
      document.addEventListener(event, resetEngagement, { passive: true });
    });

    // Track inactivity
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        handleInactivity();
      } else {
        resetEngagement();
      }
    });

    // Update engagement time periodically
    setInterval(trackEngagement, 1000);
  }

  /**
   * Track e-commerce specific metrics
   */
  public trackEcommerceMetric(metric: keyof PerformanceMetrics, value: number): void {
    this.metrics[metric] = value;
  }

  /**
   * Track cart performance
   */
  public trackCartPerformance(): void {
    const startTime = performance.now();
    
    // Simulate cart loading measurement
    requestAnimationFrame(() => {
      this.metrics.cartLoadTime = performance.now() - startTime;
    });
  }

  /**
   * Track product search performance
   */
  public trackProductSearch(searchTerm: string): void {
    const startTime = performance.now();
    
    // Mark search completion
    const markSearchComplete = () => {
      this.metrics.productSearchTime = performance.now() - startTime;
    };

    // Call this when search results are displayed
    (window as any).markSearchComplete = markSearchComplete;
  }

  /**
   * Track checkout flow performance
   */
  public trackCheckoutFlow(step: string): void {
    if (!this.metrics.checkoutFlowTime) {
      this.metrics.checkoutFlowTime = performance.now() - this.startTime;
    }
    
    this.metrics.conversionFunnelStep = step;
  }

  /**
   * Track WhatsApp integration performance
   */
  public trackWhatsAppResponse(): void {
    const startTime = performance.now();
    
    // Mark WhatsApp response
    const markWhatsAppResponse = () => {
      this.metrics.whatsappResponseTime = performance.now() - startTime;
    };

    (window as any).markWhatsAppResponse = markWhatsAppResponse;
  }

  /**
   * Generate performance score (0-100)
   */
  private calculatePerformanceScore(): number {
    let score = 100;
    
    // LCP scoring (0-25 points)
    if (this.metrics.lcp) {
      if (this.metrics.lcp > 4000) score -= 25;
      else if (this.metrics.lcp > 2500) score -= 15;
      else if (this.metrics.lcp > 1200) score -= 5;
    }
    
    // FID scoring (0-25 points)
    if (this.metrics.fid) {
      if (this.metrics.fid > 300) score -= 25;
      else if (this.metrics.fid > 100) score -= 15;
      else if (this.metrics.fid > 50) score -= 5;
    }
    
    // CLS scoring (0-25 points)
    if (this.metrics.cls) {
      if (this.metrics.cls > 0.25) score -= 25;
      else if (this.metrics.cls > 0.1) score -= 15;
      else if (this.metrics.cls > 0.05) score -= 5;
    }
    
    // Page load time scoring (0-25 points)
    if (this.metrics.pageLoadTime) {
      if (this.metrics.pageLoadTime > 5000) score -= 25;
      else if (this.metrics.pageLoadTime > 3000) score -= 15;
      else if (this.metrics.pageLoadTime > 1500) score -= 5;
    }
    
    return Math.max(0, score);
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    
    if (this.metrics.lcp && this.metrics.lcp > 2500) {
      recommendations.push('Optimize Largest Contentful Paint by compressing images and reducing server response time');
    }
    
    if (this.metrics.fid && this.metrics.fid > 100) {
      recommendations.push('Reduce First Input Delay by optimizing JavaScript execution and using code splitting');
    }
    
    if (this.metrics.cls && this.metrics.cls > 0.1) {
      recommendations.push('Improve Cumulative Layout Shift by setting image dimensions and avoiding dynamic content insertion');
    }
    
    if (this.metrics.pageLoadTime && this.metrics.pageLoadTime > 3000) {
      recommendations.push('Optimize page load time by enabling compression, using CDN, and minimizing resource sizes');
    }
    
    if (this.metrics.cartLoadTime && this.metrics.cartLoadTime > 500) {
      recommendations.push('Optimize shopping cart performance by implementing virtual scrolling and lazy loading');
    }
    
    if (this.metrics.effectiveType === '2g' || this.metrics.effectiveType === 'slow-2g') {
      recommendations.push('Optimize for slow networks by implementing progressive loading and reducing bundle size');
    }
    
    return recommendations;
  }

  /**
   * Generate comprehensive performance report
   */
  public generateReport(): PerformanceReport {
    return {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics: { ...this.metrics },
      recommendations: this.generateRecommendations(),
      score: this.calculatePerformanceScore()
    };
  }

  /**
   * Send performance data to analytics
   */
  public sendToAnalytics(): void {
    const report = this.generateReport();
    
    // Send to analytics service (implement based on your analytics provider)
    if (typeof gtag !== 'undefined') {
      gtag('event', 'performance_metrics', {
        custom_map: {
          lcp: report.metrics.lcp,
          fid: report.metrics.fid,
          cls: report.metrics.cls,
          score: report.score
        }
      });
    }
    
    // Send to custom analytics endpoint
    fetch('/api/analytics/performance', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(report)
    }).catch(error => {
      console.warn('Failed to send performance data:', error);
    });
  }

  /**
   * Cleanup observers
   */
  public cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.isMonitoring = false;
  }
}

// Global performance monitor instance
let performanceMonitor: PerformanceMonitor | null = null;

/**
 * Initialize performance monitoring
 */
export const initPerformanceMonitoring = (): PerformanceMonitor => {
  if (!performanceMonitor && typeof window !== 'undefined') {
    performanceMonitor = new PerformanceMonitor();
    
    // Send performance data when page is about to unload
    window.addEventListener('beforeunload', () => {
      performanceMonitor?.sendToAnalytics();
    });
    
    // Send performance data periodically
    setInterval(() => {
      performanceMonitor?.sendToAnalytics();
    }, 30000); // Every 30 seconds
  }
  
  return performanceMonitor!;
};

/**
 * Get current performance monitor instance
 */
export const getPerformanceMonitor = (): PerformanceMonitor | null => {
  return performanceMonitor;
};

/**
 * Track e-commerce specific events
 */
export const trackEcommerceEvent = (event: string, data?: any): void => {
  const monitor = getPerformanceMonitor();
  if (monitor) {
    switch (event) {
      case 'cart_open':
        monitor.trackCartPerformance();
        break;
      case 'product_search':
        monitor.trackProductSearch(data?.searchTerm || '');
        break;
      case 'checkout_step':
        monitor.trackCheckoutFlow(data?.step || '');
        break;
      case 'whatsapp_click':
        monitor.trackWhatsAppResponse();
        break;
    }
  }
};

// Export for global use in development
if (typeof window !== 'undefined') {
  (window as any).performanceMonitor = {
    init: initPerformanceMonitoring,
    track: trackEcommerceEvent,
    getReport: () => performanceMonitor?.generateReport()
  };
}
