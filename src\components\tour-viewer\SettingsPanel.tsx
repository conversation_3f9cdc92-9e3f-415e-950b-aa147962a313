/**
 * Settings Panel Component
 * Advanced settings panel for Photo Sphere Viewer enterprise features
 */

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Settings, 
  RotateCcw, 
  Navigation, 
  Monitor, 
  Zap, 
  Accessibility, 
  Palette,
  Download,
  BarChart3,
  Save,
  RotateCw
} from 'lucide-react';
import { toast } from 'sonner';

import type { EnterpriseSettings, TourAnalytics } from '@/lib/photosphere/enterpriseFeatures';

export interface SettingsPanelProps {
  settings: EnterpriseSettings;
  analytics?: TourAnalytics;
  onSettingsChange: (settings: Partial<EnterpriseSettings>) => void;
  onExport?: (format: 'json' | 'xml' | 'csv') => void;
  onClose?: () => void;
  className?: string;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({
  settings,
  analytics,
  onSettingsChange,
  onExport,
  onClose,
  className = ''
}) => {
  const [localSettings, setLocalSettings] = useState<EnterpriseSettings>(settings);
  const [hasChanges, setHasChanges] = useState(false);

  // Update local settings and track changes
  const updateSetting = useCallback((path: string, value: any) => {
    setLocalSettings(prev => {
      const newSettings = { ...prev };
      const keys = path.split('.');
      let current: any = newSettings;
      
      for (let i = 0; i < keys.length - 1; i++) {
        current = current[keys[i]];
      }
      
      current[keys[keys.length - 1]] = value;
      setHasChanges(true);
      return newSettings;
    });
  }, []);

  // Apply settings
  const applySettings = useCallback(() => {
    onSettingsChange(localSettings);
    setHasChanges(false);
    toast.success('Settings applied successfully');
  }, [localSettings, onSettingsChange]);

  // Reset settings
  const resetSettings = useCallback(() => {
    setLocalSettings(settings);
    setHasChanges(false);
    toast.info('Settings reset to original values');
  }, [settings]);

  return (
    <div className={`settings-panel bg-background border rounded-lg shadow-lg ${className}`}>
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Settings className="w-5 h-5" />
          <h2 className="text-lg font-semibold">Tour Settings</h2>
          {hasChanges && (
            <Badge variant="secondary">Unsaved Changes</Badge>
          )}
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={resetSettings}
            disabled={!hasChanges}
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
          
          <Button
            size="sm"
            onClick={applySettings}
            disabled={!hasChanges}
          >
            <Save className="w-4 h-4 mr-2" />
            Apply
          </Button>
          
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
            >
              Close
            </Button>
          )}
        </div>
      </div>

      <div className="p-4">
        <Tabs defaultValue="autorotate" className="w-full">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="autorotate" className="flex items-center gap-1">
              <RotateCw className="w-4 h-4" />
              <span className="hidden sm:inline">Auto</span>
            </TabsTrigger>
            <TabsTrigger value="navigation" className="flex items-center gap-1">
              <Navigation className="w-4 h-4" />
              <span className="hidden sm:inline">Nav</span>
            </TabsTrigger>
            <TabsTrigger value="quality" className="flex items-center gap-1">
              <Monitor className="w-4 h-4" />
              <span className="hidden sm:inline">Quality</span>
            </TabsTrigger>
            <TabsTrigger value="performance" className="flex items-center gap-1">
              <Zap className="w-4 h-4" />
              <span className="hidden sm:inline">Perf</span>
            </TabsTrigger>
            <TabsTrigger value="accessibility" className="flex items-center gap-1">
              <Accessibility className="w-4 h-4" />
              <span className="hidden sm:inline">A11y</span>
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-1">
              <BarChart3 className="w-4 h-4" />
              <span className="hidden sm:inline">Stats</span>
            </TabsTrigger>
          </TabsList>

          {/* Auto-rotate Settings */}
          <TabsContent value="autorotate" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <RotateCw className="w-4 h-4" />
                  Auto-rotation Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="autorotate-enabled">Enable Auto-rotation</Label>
                  <Switch
                    id="autorotate-enabled"
                    checked={localSettings.autoRotate.enabled}
                    onCheckedChange={(checked) => updateSetting('autoRotate.enabled', checked)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="autorotate-speed">Rotation Speed</Label>
                  <Select
                    value={localSettings.autoRotate.speed}
                    onValueChange={(value) => updateSetting('autoRotate.speed', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="0.5rpm">Slow (0.5 RPM)</SelectItem>
                      <SelectItem value="1rpm">Normal (1 RPM)</SelectItem>
                      <SelectItem value="2rpm">Fast (2 RPM)</SelectItem>
                      <SelectItem value="3rpm">Very Fast (3 RPM)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <Label htmlFor="autorotate-delay">Start Delay (ms)</Label>
                  <Input
                    id="autorotate-delay"
                    type="number"
                    value={localSettings.autoRotate.delay}
                    onChange={(e) => updateSetting('autoRotate.delay', parseInt(e.target.value))}
                    min="0"
                    max="10000"
                    step="500"
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="autorotate-idle">Auto-start on Idle</Label>
                  <Switch
                    id="autorotate-idle"
                    checked={localSettings.autoRotate.idle}
                    onCheckedChange={(checked) => updateSetting('autoRotate.idle', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Navigation Settings */}
          <TabsContent value="navigation" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Navigation className="w-4 h-4" />
                  Navigation Controls
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="nav-compass">Show Compass</Label>
                  <Switch
                    id="nav-compass"
                    checked={localSettings.navigation.showCompass}
                    onCheckedChange={(checked) => updateSetting('navigation.showCompass', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="nav-zoom">Show Zoom Controls</Label>
                  <Switch
                    id="nav-zoom"
                    checked={localSettings.navigation.showZoom}
                    onCheckedChange={(checked) => updateSetting('navigation.showZoom', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="nav-fullscreen">Show Fullscreen</Label>
                  <Switch
                    id="nav-fullscreen"
                    checked={localSettings.navigation.showFullscreen}
                    onCheckedChange={(checked) => updateSetting('navigation.showFullscreen', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="nav-download">Show Download</Label>
                  <Switch
                    id="nav-download"
                    checked={localSettings.navigation.showDownload}
                    onCheckedChange={(checked) => updateSetting('navigation.showDownload', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="nav-move">Show Move Controls</Label>
                  <Switch
                    id="nav-move"
                    checked={localSettings.navigation.showMove}
                    onCheckedChange={(checked) => updateSetting('navigation.showMove', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Quality Settings */}
          <TabsContent value="quality" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Monitor className="w-4 h-4" />
                  Quality Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="quality-resolution">Resolution</Label>
                  <Select
                    value={localSettings.quality.resolution}
                    onValueChange={(value) => updateSetting('quality.resolution', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low (Fast)</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="ultra">Ultra (Slow)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="quality-antialiasing">Anti-aliasing</Label>
                  <Switch
                    id="quality-antialiasing"
                    checked={localSettings.quality.antialiasing}
                    onCheckedChange={(checked) => updateSetting('quality.antialiasing', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="quality-filtering">Texture Filtering</Label>
                  <Switch
                    id="quality-filtering"
                    checked={localSettings.quality.textureFiltering}
                    onCheckedChange={(checked) => updateSetting('quality.textureFiltering', checked)}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Performance Settings */}
          <TabsContent value="performance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Zap className="w-4 h-4" />
                  Performance Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="perf-cache">Enable Caching</Label>
                  <Switch
                    id="perf-cache"
                    checked={localSettings.performance.enableCache}
                    onCheckedChange={(checked) => updateSetting('performance.enableCache', checked)}
                  />
                </div>
                
                <div>
                  <Label htmlFor="perf-preload">Preload Distance</Label>
                  <Input
                    id="perf-preload"
                    type="number"
                    value={localSettings.performance.preloadDistance}
                    onChange={(e) => updateSetting('performance.preloadDistance', parseInt(e.target.value))}
                    min="0"
                    max="10"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Number of connected scenes to preload
                  </p>
                </div>
                
                <div>
                  <Label htmlFor="perf-cache-size">Max Cache Size (MB)</Label>
                  <Input
                    id="perf-cache-size"
                    type="number"
                    value={localSettings.performance.maxCacheSize}
                    onChange={(e) => updateSetting('performance.maxCacheSize', parseInt(e.target.value))}
                    min="10"
                    max="500"
                    step="10"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Accessibility Settings */}
          <TabsContent value="accessibility" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Accessibility className="w-4 h-4" />
                  Accessibility Settings
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label htmlFor="a11y-keyboard">Keyboard Navigation</Label>
                  <Switch
                    id="a11y-keyboard"
                    checked={localSettings.accessibility.keyboardNavigation}
                    onCheckedChange={(checked) => updateSetting('accessibility.keyboardNavigation', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="a11y-screen-reader">Screen Reader Support</Label>
                  <Switch
                    id="a11y-screen-reader"
                    checked={localSettings.accessibility.screenReaderSupport}
                    onCheckedChange={(checked) => updateSetting('accessibility.screenReaderSupport', checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="a11y-contrast">High Contrast Mode</Label>
                  <Switch
                    id="a11y-contrast"
                    checked={localSettings.accessibility.highContrast}
                    onCheckedChange={(checked) => updateSetting('accessibility.highContrast', checked)}
                  />
                </div>
                
                <div className="text-xs text-muted-foreground p-3 bg-muted rounded">
                  <strong>Keyboard Shortcuts:</strong><br />
                  Arrow keys: Navigate • +/-: Zoom • F: Fullscreen • Space: Auto-rotate
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analytics */}
          <TabsContent value="analytics" className="space-y-4">
            {analytics ? (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm flex items-center gap-2">
                    <BarChart3 className="w-4 h-4" />
                    Tour Analytics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-muted rounded">
                      <div className="text-2xl font-bold">{analytics.viewCount}</div>
                      <div className="text-xs text-muted-foreground">Total Views</div>
                    </div>
                    <div className="text-center p-3 bg-muted rounded">
                      <div className="text-2xl font-bold">{Math.round(analytics.averageViewTime)}s</div>
                      <div className="text-xs text-muted-foreground">Avg. View Time</div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">User Interactions</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Zoom Actions:</span>
                        <span>{analytics.userInteractions.zoom}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Rotate Actions:</span>
                        <span>{analytics.userInteractions.rotate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Fullscreen Toggles:</span>
                        <span>{analytics.userInteractions.fullscreen}</span>
                      </div>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Device Stats</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Desktop:</span>
                        <span>{analytics.deviceStats.desktop}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Mobile:</span>
                        <span>{analytics.deviceStats.mobile}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Tablet:</span>
                        <span>{analytics.deviceStats.tablet}</span>
                      </div>
                    </div>
                  </div>
                  
                  {onExport && (
                    <>
                      <Separator />
                      <div>
                        <h4 className="text-sm font-medium mb-2">Export Data</h4>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onExport('json')}
                          >
                            <Download className="w-4 h-4 mr-2" />
                            JSON
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onExport('csv')}
                          >
                            <Download className="w-4 h-4 mr-2" />
                            CSV
                          </Button>
                        </div>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <BarChart3 className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No Analytics Data</h3>
                  <p className="text-muted-foreground">
                    Analytics data will appear here once the tour starts collecting user interactions.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default SettingsPanel;
