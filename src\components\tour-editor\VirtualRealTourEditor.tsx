/**
 * VirtualRealTour Editor
 * Complete tour creation system with glass/fluid design
 */

import { useState, useRef, useCallback, useEffect } from 'react';
import { 
  Upload, 
  Plus, 
  Save, 
  Eye, 
  Share2, 
  MapPin,
  Settings,
  Camera,
  Trash2
} from 'lucide-react';
import { toast } from 'sonner';
import PhotoSphereTourViewer from '../tour-viewer/PhotoSphereTourViewer';
import { tourDataSyncService } from '@/services/data-sync/TourDataSyncService';
import { convertToWebP, generatePanoramaThumbnail } from '@/lib/performance/imageOptimization';

// Types
interface TourScene {
  id: string;
  name: string;
  panorama: string;
  thumbnail?: string;
  initialView: {
    yaw: number;
    pitch: number;
    zoom: number;
  };
  hotspots: TourHotspot[];
  audio?: string;
  description?: string;
}

interface TourHotspot {
  id: string;
  type: 'navigation' | 'product' | 'info' | 'link' | 'media';
  position: {
    yaw: number;
    pitch: number;
  };
  title: string;
  content: string;
  targetSceneId?: string;
  productData?: {
    id: string;
    name: string;
    price: number;
    image: string;
    description: string;
    whatsappMessage?: string;
  };
  linkUrl?: string;
  mediaUrl?: string;
  style?: {
    color: string;
    size: 'small' | 'medium' | 'large';
    animation: 'pulse' | 'bounce' | 'none';
  };
}

interface VirtualRealTourEditorProps {
  tourId?: string;
  tourData: {
    title: string;
    description: string;
    category: string;
    location: string;
  };
  onSave?: (tourData: any) => void;
  onPublish?: (tourData: any) => void;
  onClose?: () => void;
}

const VirtualRealTourEditor = ({
  tourId,
  tourData,
  onSave,
  onPublish,
  onClose
}: VirtualRealTourEditorProps) => {
  const [scenes, setScenes] = useState<TourScene[]>([]);
  const [currentSceneIndex, setCurrentSceneIndex] = useState(0);
  const [isAddingHotspot, setIsAddingHotspot] = useState(false);
  const [hotspotType, setHotspotType] = useState<TourHotspot['type']>('info');
  const [selectedHotspot, setSelectedHotspot] = useState<TourHotspot | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const currentScene = scenes[currentSceneIndex];

  // Auto-save functionality with data sync
  useEffect(() => {
    if (scenes.length > 0 && tourId) {
      const autoSaveTimer = setTimeout(async () => {
        try {
          await tourDataSyncService.updateTour(tourId, {
            custom_scenes: scenes,
            tour_platform: 'custom',
            updated_at: new Date().toISOString()
          });

          const tourData = {
            scenes,
            embedUrl: `https://virtualrealtour.ng/embed/tour/${tourId}`,
            totalScenes: scenes.length,
            totalHotspots: scenes.reduce((total, scene) => total + scene.hotspots.length, 0),
            lastAutoSave: new Date().toISOString(),
            syncStatus: 'synced'
          };

          onSave?.(tourData);
          toast.success('Auto-saved and synced', { duration: 2000 });
        } catch (error) {
          console.error('Auto-save error:', error);
          toast.error('Auto-save failed', { duration: 2000 });
        }
      }, 5000);

      return () => clearTimeout(autoSaveTimer);
    }
  }, [scenes, tourId, onSave]);

  const processFiles = async (files: FileList) => {
    const fileArray = Array.from(files);
    
    for (let index = 0; index < fileArray.length; index++) {
      const file = fileArray[index];
      
      if (!file.type.startsWith('image/')) {
        toast.error(`${file.name} is not a valid image file`);
        continue;
      }

      try {
        toast.loading(`Processing ${file.name}...`, { id: `processing-${index}` });

        // Optimize image to WebP format
        const optimizedImage = await convertToWebP(file, {
          quality: 0.85,
          maxWidth: 4096,
          maxHeight: 2048
        });

        // Generate thumbnail
        const thumbnail = await generatePanoramaThumbnail(file, 120, 60);

        const newScene: TourScene = {
          id: `scene-${Date.now()}-${index}`,
          name: `Scene ${scenes.length + index + 1}`,
          panorama: optimizedImage.url,
          thumbnail,
          initialView: { yaw: 0, pitch: 0, zoom: 50 },
          hotspots: [],
          description: `360° view of ${file.name.replace(/\.[^/.]+$/, '')}`
        };

        setScenes(prev => [...prev, newScene]);
        
        toast.success(
          `Scene "${newScene.name}" added! Optimized from ${(file.size / 1024 / 1024).toFixed(1)}MB to ${(optimizedImage.size / 1024 / 1024).toFixed(1)}MB`,
          { id: `processing-${index}` }
        );

      } catch (error) {
        console.error('Error processing file:', error);
        toast.error(`Failed to process ${file.name}`, { id: `processing-${index}` });
      }
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;
    await processFiles(files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    if (files) {
      await processFiles(files);
    }
  };

  const handleViewerClick = useCallback((position: { yaw: number; pitch: number }) => {
    if (!isAddingHotspot || !currentScene) return;

    const newHotspot: TourHotspot = {
      id: `hotspot-${Date.now()}`,
      type: hotspotType,
      position,
      title: `${hotspotType.charAt(0).toUpperCase() + hotspotType.slice(1)} Hotspot`,
      content: 'Click to edit this hotspot',
      style: {
        color: '#3b82f6',
        size: 'medium',
        animation: 'pulse'
      }
    };

    setScenes(prev => prev.map((scene, index) => 
      index === currentSceneIndex 
        ? { ...scene, hotspots: [...scene.hotspots, newHotspot] }
        : scene
    ));

    setIsAddingHotspot(false);
    setSelectedHotspot(newHotspot);
    toast.success('Hotspot added! Configure it in the properties panel.');
  }, [isAddingHotspot, currentScene, hotspotType, currentSceneIndex]);

  const removeHotspot = (hotspotId: string) => {
    setScenes(prev => prev.map((scene, index) => 
      index === currentSceneIndex 
        ? { ...scene, hotspots: scene.hotspots.filter(h => h.id !== hotspotId) }
        : scene
    ));
    setSelectedHotspot(null);
    toast.success('Hotspot removed');
  };

  const updateHotspot = (hotspotId: string, updates: Partial<TourHotspot>) => {
    setScenes(prev => prev.map((scene, index) => 
      index === currentSceneIndex 
        ? { 
            ...scene, 
            hotspots: scene.hotspots.map(h => 
              h.id === hotspotId ? { ...h, ...updates } : h
            ) 
          }
        : scene
    ));
  };

  const handleSave = async () => {
    try {
      if (tourId) {
        await tourDataSyncService.updateTour(tourId, {
          custom_scenes: scenes,
          tour_platform: 'custom',
          updated_at: new Date().toISOString()
        });
      }

      const tourData = {
        scenes,
        embedUrl: `https://virtualrealtour.ng/embed/tour/${tourId}`,
        totalScenes: scenes.length,
        totalHotspots: scenes.reduce((total, scene) => total + scene.hotspots.length, 0),
        syncStatus: 'synced'
      };

      onSave?.(tourData);
      toast.success('Tour saved and synced successfully!');
    } catch (error) {
      console.error('Save error:', error);
      toast.error('Failed to save tour');
    }
  };

  const handlePublish = async () => {
    if (scenes.length === 0) {
      toast.error('Please add at least one scene before publishing');
      return;
    }

    try {
      if (tourId) {
        await tourDataSyncService.updateTour(tourId, {
          custom_scenes: scenes,
          status: 'published',
          tour_platform: 'custom',
          updated_at: new Date().toISOString()
        });
      }

      const tourData = {
        scenes,
        embedUrl: `https://virtualrealtour.ng/embed/tour/${tourId}`,
        totalScenes: scenes.length,
        totalHotspots: scenes.reduce((total, scene) => total + scene.hotspots.length, 0),
        publishedAt: new Date().toISOString(),
        syncStatus: 'synced'
      };

      onPublish?.(tourData);
      toast.success('Tour published and synced successfully!');
    } catch (error) {
      console.error('Publish error:', error);
      toast.error('Failed to publish tour');
    }
  };

  const togglePreview = () => {
    setIsPreviewMode(!isPreviewMode);
    setIsAddingHotspot(false);
    setSelectedHotspot(null);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Editor Header */}
      <div className="glass-navigation mb-4 rounded-lg">
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-semibold flex items-center gap-2 glass-label">
                <Camera className="w-5 h-5" />
                {tourData.title}
              </h1>
              <p className="text-sm text-muted-foreground">{tourData.location}</p>
            </div>
            <div className="flex items-center gap-2">
              <button type="button" className="glass-button px-3 py-2 text-sm" onClick={togglePreview}>
                <Eye className="w-4 h-4 mr-2" />
                {isPreviewMode ? 'Edit Mode' : 'Preview'}
              </button>
              <button type="button" className="glass-button px-3 py-2 text-sm" onClick={handleSave}>
                <Save className="w-4 h-4 mr-2" />
                Save
              </button>
              <button type="button" className="glass-button px-3 py-2 text-sm bg-blue-600/20 border-blue-400" onClick={handlePublish}>
                <Share2 className="w-4 h-4 mr-2" />
                Publish
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="flex-1 tour-editor-grid grid grid-cols-1 lg:grid-cols-4 gap-4">
        {/* Left Panel - Scene Management */}
        <div className="lg:col-span-1 tour-editor-sidebar glass-panel">
          <div className="p-4 border-b border-white/10">
            <h3 className="text-base font-semibold flex items-center gap-2 glass-label">
              <MapPin className="w-4 h-4" />
              Scenes ({scenes.length})
            </h3>
          </div>
          <div className="p-4 space-y-4">
            {/* Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-4 text-center transition-colors cursor-pointer ${
                isDragOver ? 'border-blue-500 glass-bg-secondary' : 'border-white/20 glass-bg-tertiary hover:border-blue-400'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <Upload className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm font-medium mb-1">Upload 360° Images</p>
              <p className="text-xs text-muted-foreground">
                Drag & drop or click to browse
              </p>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              multiple
              onChange={handleImageUpload}
              className="hidden"
              aria-label="Upload 360 degree panoramic images"
            />

            {/* Scene List */}
            <div className="space-y-2">
              {scenes.map((scene, index) => (
                <div
                  key={scene.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    index === currentSceneIndex ? 'border-primary bg-primary/5' : 'hover:bg-muted/50'
                  }`}
                  onClick={() => setCurrentSceneIndex(index)}
                >
                  <div className="flex items-center gap-2">
                    {scene.thumbnail && (
                      <img 
                        src={scene.thumbnail} 
                        alt={scene.name}
                        className="w-12 h-6 rounded object-cover"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{scene.name}</p>
                      <p className="text-xs text-muted-foreground">
                        {scene.hotspots.length} hotspots
                      </p>
                    </div>
                  </div>
                </div>
              ))}
              
              {scenes.length === 0 && (
                <div className="text-center py-8 text-muted-foreground">
                  <Camera className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-sm">Upload 360° images to start</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Main Editor Area */}
        <div className="lg:col-span-2 tour-editor-main glass-panel">
          <div className="p-4 border-b border-white/10">
            <div className="flex items-center justify-between">
              <h3 className="text-base font-semibold glass-label">
                {currentScene ? currentScene.name : 'Select a scene to edit'}
              </h3>
              {currentScene && !isPreviewMode && (
                <div className="flex items-center gap-2">
                  <label htmlFor="hotspot-type" className="text-sm glass-label">Add:</label>
                  <select
                    id="hotspot-type"
                    value={hotspotType}
                    onChange={(e) => setHotspotType(e.target.value as TourHotspot['type'])}
                    className="glass-select text-sm px-2 py-1"
                    title="Select hotspot type"
                  >
                    <option value="info">📍 Info Point</option>
                    <option value="product">🛍️ Product</option>
                    <option value="navigation">🧭 Navigation</option>
                    <option value="link">🔗 External Link</option>
                    <option value="media">📷 Media</option>
                  </select>
                  <button
                    type="button"
                    className={`glass-button px-3 py-2 text-sm ${
                      isAddingHotspot ? 'bg-blue-600/30 border-blue-400' : ''
                    }`}
                    onClick={() => setIsAddingHotspot(!isAddingHotspot)}
                  >
                    <Plus className="w-4 h-4 mr-2" />
                    {isAddingHotspot ? 'Click on tour' : 'Add Hotspot'}
                  </button>
                </div>
              )}
            </div>
          </div>
          <div className="p-0">
            {currentScene ? (
              <div className="h-96 relative">
                <PhotoSphereTourViewer
                  scenes={[currentScene]}
                  showControls={!isAddingHotspot}
                  onHotspotClick={(hotspot) => setSelectedHotspot(hotspot)}
                  className="h-full"
                />
                {isAddingHotspot && (
                  <div className="absolute bottom-4 left-4 right-4 glass-card border border-blue-400/50">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                      <p className="text-sm font-medium">
                        Click anywhere on the 360° image to place a {hotspotType} hotspot
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="h-96 flex items-center justify-center text-muted-foreground">
                <div className="text-center max-w-md">
                  <Camera className="w-16 h-16 mx-auto mb-6 text-blue-500" />
                  <h3 className="text-xl font-semibold text-foreground mb-3">Start Creating Your Virtual Tour</h3>
                  <p className="text-sm mb-6">Upload 360° panoramic images to begin building your immersive virtual tour experience with interactive hotspots and e-commerce integration.</p>
                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    className="glass-button px-4 py-2 bg-blue-600/20 border-blue-400 hover:bg-blue-600/30"
                  >
                    <Upload className="w-4 h-4 mr-2" />
                    Upload 360° Images
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - Properties */}
        <div className="lg:col-span-1 tour-editor-properties glass-panel">
          <div className="p-4 border-b border-white/10">
            <h3 className="text-base font-semibold flex items-center gap-2 glass-label">
              <Settings className="w-4 h-4" />
              Properties
            </h3>
          </div>
          <div className="p-4">
            {selectedHotspot ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium glass-label">Hotspot Settings</h4>
                  <button
                    type="button"
                    className="glass-button px-2 py-1 text-sm bg-red-600/20 border-red-400 hover:bg-red-600/30"
                    onClick={() => removeHotspot(selectedHotspot.id)}
                    title="Delete hotspot"
                    aria-label="Delete hotspot"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <label className="glass-label">Title</label>
                    <input
                      className="glass-input w-full"
                      value={selectedHotspot.title}
                      onChange={(e) => updateHotspot(selectedHotspot.id, { title: e.target.value })}
                      placeholder="Enter hotspot title"
                    />
                  </div>
                  
                  <div>
                    <label className="glass-label">Content</label>
                    <textarea
                      className="glass-textarea w-full"
                      value={selectedHotspot.content}
                      onChange={(e) => updateHotspot(selectedHotspot.id, { content: e.target.value })}
                      rows={3}
                      placeholder="Enter hotspot description"
                    />
                  </div>
                </div>
              </div>
            ) : currentScene ? (
              <div className="space-y-4">
                <h4 className="font-medium glass-label">🎬 Scene Settings</h4>
                
                <div className="space-y-3">
                  <div>
                    <label className="glass-label">Scene Name</label>
                    <input
                      className="glass-input w-full"
                      value={currentScene.name}
                      onChange={(e) => {
                        setScenes(prev => prev.map((scene, index) => 
                          index === currentSceneIndex ? { ...scene, name: e.target.value } : scene
                        ));
                      }}
                      placeholder="Enter scene name"
                    />
                  </div>
                  
                  <div>
                    <label className="glass-label">Description</label>
                    <textarea
                      className="glass-textarea w-full"
                      value={currentScene.description || ''}
                      onChange={(e) => {
                        setScenes(prev => prev.map((scene, index) => 
                          index === currentSceneIndex ? { ...scene, description: e.target.value } : scene
                        ));
                      }}
                      rows={3}
                      placeholder="Describe this scene..."
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Settings className="w-8 h-8 mx-auto mb-2" />
                <p className="text-sm">Select a scene or hotspot to edit properties</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default VirtualRealTourEditor;
