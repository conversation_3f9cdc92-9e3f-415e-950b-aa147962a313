
import { useState } from 'react';
import { <PERSON><PERSON>, Di<PERSON><PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Upload,
  FileImage,
  Link2,
  Video,
  ChevronLeft,
  ChevronRight,
  Check,
  X
} from 'lucide-react';
import UploadStepOne from './upload/UploadStepOne';
import UploadStepTwo from './upload/UploadStepTwo';
import UploadStepThree from './upload/UploadStepThree';
import UploadStepFour from './upload/UploadStepFour';
import CommonNinjaTourEditor from './tour-editor/CommonNinjaTourEditor';
import { useUploadForm } from '@/hooks/useUploadForm';

interface UploadModalProps {
  isOpen?: boolean;
  open?: boolean;
  onClose?: () => void;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: () => void;
  initialData?: {
    title?: string;
    description?: string;
    category?: string;
    location?: string;
    businessInfo?: any;
    tourPlatform?: 'commonninja' | 'custom';
  };
}

const UploadModal = ({
  isOpen,
  open,
  onClose,
  onOpenChange,
  onSuccess,
  initialData
}: UploadModalProps) => {
  const modalOpen = isOpen ?? open ?? false;
  const handleOpenChange = onOpenChange ?? ((open: boolean) => !open && onClose?.());
  const [uploadMethod, setUploadMethod] = useState<'images' | 'embed' | 'video'>('images');
  const {
    currentStep,
    setCurrentStep,
    formData,
    setFormData,
    isSubmitting,
    handleNext,
    handlePrev,
    handleSubmit,
    resetForm,
    canProceed
  } = useUploadForm(onSuccess, () => handleOpenChange(false), initialData);

  const handleClose = () => {
    handleOpenChange(false);
    resetForm();
    setUploadMethod('images');
  };

  const handleMethodChange = (method: 'images' | 'embed' | 'video') => {
    setUploadMethod(method);
    setCurrentStep(1);
    // Reset form data when changing methods
    resetForm();
  };

  const uploadMethods = [
    {
      id: 'images' as const,
      title: '360° Images',
      description: 'Upload panoramic images to create your virtual tour',
      icon: FileImage,
      recommended: true
    },
    {
      id: 'embed' as const,
      title: 'Embed External Tour',
      description: 'Embed tours from Matterport, Kuula, or other platforms',
      icon: Link2
    },
    {
      id: 'video' as const,
      title: '360° Video',
      description: 'Upload 360-degree video content',
      icon: Video,
      comingSoon: true
    }
  ];

  const steps = [
    { number: 1, title: 'Basic Info', description: 'Tour details' },
    { number: 2, title: 'Method', description: 'Creation type' },
    { number: 3, title: 'Content', description: 'Upload files' },
    { number: 4, title: 'Review', description: 'Verify details' },
    { number: 5, title: 'Editor', description: 'Build tour' },
    { number: 6, title: 'Publish', description: 'Create tour' }
  ];

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return 'Basic Information';
      case 2: return 'Creation Method';
      case 3: return 'Upload Content';
      case 4: return 'Review Details';
      case 5: return 'Tour Editor';
      case 6: return 'Create Tour';
      default: return 'Upload Tour';
    }
  };

  return (
    <Dialog open={modalOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Create New Virtual Tour</DialogTitle>
          <p className="text-sm text-muted-foreground">
            Step {currentStep} of 6 - {getStepTitle()}
          </p>

          {/* Progress Bar */}
          <div className="mt-4">
            <Progress value={(currentStep / 6) * 100} className="h-2" />
            <div className="flex justify-between mt-2">
              {steps.map((step) => (
                <div
                  key={step.number}
                  className={`flex flex-col items-center text-xs ${
                    currentStep >= step.number
                      ? 'text-primary'
                      : 'text-muted-foreground'
                  }`}
                >
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mb-1 ${
                    currentStep > step.number
                      ? 'bg-primary text-primary-foreground'
                      : currentStep === step.number
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted text-muted-foreground'
                  }`}>
                    {currentStep > step.number ? (
                      <Check className="h-3 w-3" />
                    ) : (
                      step.number
                    )}
                  </div>
                  <span className="hidden sm:block">{step.title}</span>
                </div>
              ))}
            </div>
          </div>
        </DialogHeader>

        {/* Upload Method Selection - Only show on step 1 */}
        {currentStep === 1 && (
          <div className="flex-shrink-0 py-4 border-b">
            <h3 className="text-sm font-medium mb-3">Choose Upload Method</h3>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
              {uploadMethods.map((method) => (
                <Card
                  key={method.id}
                  className={`cursor-pointer transition-all duration-200 ${
                    uploadMethod === method.id
                      ? 'border-primary bg-primary/5 shadow-sm'
                      : 'hover:border-primary/50 hover:shadow-sm'
                  } ${method.comingSoon ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={() => !method.comingSoon && handleMethodChange(method.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex flex-col items-center text-center space-y-2">
                      <div className={`p-2 rounded-lg ${
                        uploadMethod === method.id ? 'bg-primary text-primary-foreground' : 'bg-muted'
                      }`}>
                        <method.icon className="h-5 w-5" />
                      </div>
                      <div>
                        <h4 className="font-medium text-sm">{method.title}</h4>
                        <p className="text-xs text-muted-foreground mt-1">
                          {method.description}
                        </p>
                        {method.recommended && (
                          <Badge variant="secondary" className="mt-2 text-xs">
                            Recommended
                          </Badge>
                        )}
                        {method.comingSoon && (
                          <Badge variant="outline" className="mt-2 text-xs">
                            Coming Soon
                          </Badge>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Main Content Area */}
        <div className="space-y-6">
          {currentStep === 1 && (
            <UploadStepOne
              formData={formData}
              setFormData={setFormData}
            />
          )}
          {currentStep === 2 && (
            <UploadStepTwo
              formData={formData}
              setFormData={setFormData}
            />
          )}
          {currentStep === 3 && (
            <UploadStepThree
              formData={formData}
            />
          )}
          {currentStep === 4 && (
            <UploadStepFour
              formData={formData}
              setFormData={setFormData}
              uploadMethod={uploadMethod}
            />
          )}
          {currentStep === 5 && uploadMethod === 'images' && formData.files.length > 0 && (
            <CommonNinjaTourEditor
              tourData={{
                title: formData.title,
                description: formData.description,
                category: formData.category,
                location: formData.location,
                images: formData.files
              }}
              onSave={(data) => {
                console.log('Tour saved:', data);
                // Update formData with saved tour data
              }}
              onPublish={(data) => {
                console.log('Tour published:', data);
                // Handle tour publishing
                handleSubmit();
              }}
            />
          )}

        </div>

        {/* Footer Navigation */}
        <div className="flex justify-end gap-3 pt-4">
          {currentStep > 1 && (
            <Button
              type="button"
              variant="outline"
              onClick={handlePrev}
              disabled={isSubmitting}
            >
              <ChevronLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          )}
          {currentStep === 1 && (
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
          )}
          {currentStep < 6 && (
            <Button
              onClick={handleNext}
              disabled={!canProceed() || isSubmitting}
            >
              {currentStep === 4 && uploadMethod === 'images' ? 'Open Editor' : 'Next'}
              <ChevronRight className="h-4 w-4 ml-2" />
            </Button>
          )}
          {currentStep === 6 && (
            <Button
              onClick={handleSubmit}
              disabled={!canProceed() || isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent mr-2" />
                  Creating...
                </>
              ) : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Create Tour
                </>
              )}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UploadModal;
