# 🌍 Virtual Real Tour (VRT)

<div align="center">

![Virtual Real Tour Logo](https://via.placeholder.com/200x80/3B82F6/FFFFFF?text=VRT)

**Lightning-fast, secure virtual tour platform for businesses across Nigeria and beyond**

[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-20232A?style=for-the-badge&logo=react&logoColor=61DAFB)](https://reactjs.org/)
[![Vite](https://img.shields.io/badge/Vite-646CFF?style=for-the-badge&logo=vite&logoColor=white)](https://vitejs.dev/)
[![Supabase](https://img.shields.io/badge/Supabase-3ECF8E?style=for-the-badge&logo=supabase&logoColor=white)](https://supabase.com/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)](https://tailwindcss.com/)

[🚀 Live Demo](https://tour-nigeria-vista.vercel.app) • [📖 Documentation](#documentation) • [🐛 Report Bug](https://github.com/iwalk-jo/tour-nigeria-vista/issues) • [✨ Request Feature](https://github.com/iwalk-jo/tour-nigeria-vista/issues)

</div>

---

## 🎯 **Overview**

Virtual Real Tour (VRT) is a cutting-edge platform that enables businesses to create, manage, and showcase immersive 360° virtual tours. Built with modern web technologies, VRT delivers lightning-fast performance, enterprise-grade security, and seamless user experiences across all devices.

### ✨ **Key Features**

🏢 **Business-Focused**
- Multi-category support (Real Estate, Hotels, Restaurants, Offices, Schools, etc.)
- Business profile management with contact integration
- Professional tour showcase with custom branding

🚀 **Lightning-Fast Performance**
- Auto-loading tours with smart intersection observers
- Optimized bundle splitting and lazy loading
- Mobile-first responsive design
- Progressive Web App (PWA) capabilities

🔒 **Enterprise Security**
- Secure tour embedding with no source URL exposure
- Sound popup prevention and clean iframe sandboxing
- Row-level security with Supabase
- HTTPS-enforced deployment

🎨 **Modern UI/UX**
- Glass morphism effects and smooth animations
- Light/dark theme support
- Accessibility-compliant (WCAG 2.1 AA)
- Industry-standard shadcn/ui components

📱 **Mobile-Optimized**
- Touch-friendly interactions
- Responsive breakpoints for all devices
- Optimized loading for mobile networks
- Native app-like experience

---

## 🚀 **Quick Start**

### **Prerequisites**

- **Node.js** 18+ or **Bun** 1.0+
- **Git** for version control
- **Supabase** account ([Sign up free](https://supabase.com))

### **Installation**

1. **Clone the repository**
```bash
git clone https://github.com/iwalk-jo/tour-nigeria-vista.git
cd tour-nigeria-vista
```

2. **Install dependencies** (Recommended: Bun for faster installs)
```bash
# Using Bun (Recommended)
bun install

# Or using npm
npm install
```

3. **Environment Setup**
```bash
# Copy environment template
cp .env.example .env.local

# Edit with your Supabase credentials
nano .env.local
```

4. **Start development server**
```bash
# Using Bun
bun run dev

# Or using npm
npm run dev
```

5. **Open your browser**
```
http://localhost:3000
```

---

## ⚙️ **Environment Configuration**

Create a `.env.local` file in the root directory:

```env
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Optional: Analytics & Monitoring
VITE_ANALYTICS_ID=your_analytics_id
VITE_SENTRY_DSN=your_sentry_dsn

# Development
VITE_APP_ENV=development
```

### **Getting Supabase Credentials**

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to **Settings** → **API**
3. Copy your **Project URL** and **anon/public key**
4. Run the database migrations (see [Database Setup](#database-setup))

---

## 🏗️ **Tech Stack**

### **Frontend**
- **React 18** - Modern UI library with hooks
- **TypeScript** - Type-safe JavaScript
- **Vite** - Lightning-fast build tool
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - High-quality component library
- **Framer Motion** - Smooth animations
- **React Query** - Server state management

### **Backend & Database**
- **Supabase** - Backend-as-a-Service
- **PostgreSQL** - Robust relational database
- **Row Level Security** - Database-level security
- **Real-time subscriptions** - Live data updates

### **Deployment & DevOps**
- **Vercel** - Edge deployment platform
- **GitHub Actions** - CI/CD pipeline
- **ESLint & Prettier** - Code quality tools
- **Bun** - Fast package manager and runtime

---

## 📁 **Project Structure**

```
tour-nigeria-vista/
├── src/
│   ├── components/          # Reusable UI components
│   │   ├── ui/             # shadcn/ui components
│   │   ├── admin/          # Admin dashboard components
│   │   ├── sections/       # Page sections
│   │   └── upload/         # Tour upload components
│   ├── pages/              # Route components
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Utilities and configurations
│   ├── utils/              # Helper functions
│   ├── config/             # App configuration
│   └── styles/             # Global styles
├── supabase/
│   ├── migrations/         # Database migrations
│   └── functions/          # Edge functions
├── public/                 # Static assets
└── docs/                   # Documentation
```

---

## 🛠️ **Available Scripts**

```bash
# Development
bun run dev              # Start development server
bun run build            # Build for production
bun run preview          # Preview production build
bun run clean            # Clean build artifacts

# Code Quality
bun run lint             # Run ESLint
bun run lint:fix         # Fix ESLint issues
bun run type-check       # TypeScript type checking

# Database
bun run db:generate      # Generate database types
bun run db:push          # Push schema changes
bun run db:reset         # Reset database

# Deployment
bun run deploy           # Deploy to Vercel
```

---

## 🗄️ **Database Setup**

1. **Create Supabase Project**
   - Visit [supabase.com](https://supabase.com)
   - Create new project
   - Note your project URL and anon key

2. **Run Migrations**
```bash
# Install Supabase CLI
npm install -g @supabase/cli

# Login to Supabase
supabase login

# Link your project
supabase link --project-ref your-project-ref

# Run migrations
supabase db push
```

3. **Set up Authentication**
   - Enable Email/Password auth in Supabase dashboard
   - Configure email templates
   - Set up OAuth providers (optional)

---

## 🚀 **Deployment**

### **Vercel Deployment (Recommended)**

1. **Connect Repository**
   - Import project to Vercel
   - Connect GitHub repository

2. **Environment Variables**
   - Add Supabase URL and anon key
   - Configure production environment

3. **Deploy**
```bash
# Using Vercel CLI
npm i -g vercel
vercel --prod

# Or push to main branch for auto-deployment
git push origin main
```

### **Manual Deployment**

```bash
# Build the project
bun run build

# Deploy dist/ folder to your hosting provider
```

---

## 📱 **PWA Features**

VRT includes Progressive Web App capabilities:

- **Offline Support** - Basic functionality without internet
- **App Installation** - Install as native app on mobile/desktop
- **Push Notifications** - Tour updates and notifications
- **Background Sync** - Sync data when connection restored

---

## 🤝 **Contributing**

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### **Development Workflow**

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### **Code Standards**

- Follow TypeScript best practices
- Use ESLint and Prettier configurations
- Write meaningful commit messages
- Add tests for new features
- Update documentation

---

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🙏 **Acknowledgments**

- [shadcn/ui](https://ui.shadcn.com/) for beautiful components
- [Supabase](https://supabase.com/) for backend infrastructure
- [Vercel](https://vercel.com/) for deployment platform
- [Tailwind CSS](https://tailwindcss.com/) for styling system

---

<div align="center">

**Built with ❤️ by [Iwalk Tech](https://iwalktech.com)**

[Website](https://tour-nigeria-vista.vercel.app) • [GitHub](https://github.com/iwalk-jo/tour-nigeria-vista) • [Issues](https://github.com/iwalk-jo/tour-nigeria-vista/issues)

</div>
