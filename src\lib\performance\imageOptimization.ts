/**
 * Image Optimization Utilities
 * Performance-focused image handling for VirtualRealTour
 */

export interface ImageOptimizationOptions {
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
  maxWidth?: number;
  maxHeight?: number;
  progressive?: boolean;
}

export interface OptimizedImage {
  url: string;
  width: number;
  height: number;
  format: string;
  size: number;
}

/**
 * Convert image to WebP format with quality optimization
 */
export async function convertToWebP(
  file: File, 
  options: ImageOptimizationOptions = {}
): Promise<OptimizedImage> {
  const {
    quality = 0.8,
    maxWidth = 2048,
    maxHeight = 1024
  } = options;

  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate optimal dimensions
      let { width, height } = img;
      
      if (width > maxWidth) {
        height = (height * maxWidth) / width;
        width = maxWidth;
      }
      
      if (height > maxHeight) {
        width = (width * maxHeight) / height;
        height = maxHeight;
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and optimize
      ctx!.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (!blob) {
            reject(new Error('Failed to convert image'));
            return;
          }

          const url = URL.createObjectURL(blob);
          resolve({
            url,
            width,
            height,
            format: 'webp',
            size: blob.size
          });
        },
        'image/webp',
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Generate thumbnail for 360° panorama
 */
export async function generatePanoramaThumbnail(
  file: File,
  width: number = 120,
  height: number = 60
): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      canvas.width = width;
      canvas.height = height;

      // Draw center portion of panorama for thumbnail
      const sourceX = (img.width - (img.width * 0.3)) / 2;
      const sourceWidth = img.width * 0.3;
      
      ctx!.drawImage(
        img,
        sourceX, 0, sourceWidth, img.height,
        0, 0, width, height
      );

      resolve(canvas.toDataURL('image/jpeg', 0.7));
    };

    img.onerror = () => reject(new Error('Failed to generate thumbnail'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Lazy loading with intersection observer
 */
export class LazyImageLoader {
  private observer: IntersectionObserver;
  private images = new Set<HTMLImageElement>();

  constructor(options: IntersectionObserverInit = {}) {
    this.observer = new IntersectionObserver(
      this.handleIntersection.bind(this),
      {
        rootMargin: '50px',
        threshold: 0.1,
        ...options
      }
    );
  }

  observe(img: HTMLImageElement) {
    this.images.add(img);
    this.observer.observe(img);
  }

  unobserve(img: HTMLImageElement) {
    this.images.delete(img);
    this.observer.unobserve(img);
  }

  private handleIntersection(entries: IntersectionObserverEntry[]) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target as HTMLImageElement;
        this.loadImage(img);
        this.observer.unobserve(img);
        this.images.delete(img);
      }
    });
  }

  private loadImage(img: HTMLImageElement) {
    const src = img.dataset.src;
    if (!src) return;

    // Create new image to preload
    const newImg = new Image();
    
    newImg.onload = () => {
      img.src = src;
      img.classList.add('loaded');
      img.removeAttribute('data-src');
    };

    newImg.onerror = () => {
      img.classList.add('error');
    };

    newImg.src = src;
  }

  destroy() {
    this.observer.disconnect();
    this.images.clear();
  }
}

/**
 * Progressive image component with blur-up effect
 */
export function createProgressiveImage(
  lowQualitySrc: string,
  highQualitySrc: string,
  alt: string = ''
): HTMLElement {
  const container = document.createElement('div');
  container.className = 'progressive-image';

  const placeholder = document.createElement('img');
  placeholder.src = lowQualitySrc;
  placeholder.alt = alt;
  placeholder.style.filter = 'blur(5px)';
  placeholder.style.transition = 'filter 0.3s ease';

  const mainImage = document.createElement('img');
  mainImage.style.position = 'absolute';
  mainImage.style.top = '0';
  mainImage.style.left = '0';
  mainImage.style.opacity = '0';
  mainImage.style.transition = 'opacity 0.3s ease';

  mainImage.onload = () => {
    mainImage.style.opacity = '1';
    placeholder.style.filter = 'blur(0)';
  };

  mainImage.src = highQualitySrc;
  mainImage.alt = alt;

  container.appendChild(placeholder);
  container.appendChild(mainImage);

  return container;
}

/**
 * Memory management for large images
 */
export class ImageMemoryManager {
  private static cache = new Map<string, string>();
  private static maxCacheSize = 50; // Maximum cached images

  static cacheImage(key: string, url: string) {
    if (this.cache.size >= this.maxCacheSize) {
      // Remove oldest entry
      const firstKey = this.cache.keys().next().value;
      const oldUrl = this.cache.get(firstKey);
      if (oldUrl) {
        URL.revokeObjectURL(oldUrl);
      }
      this.cache.delete(firstKey);
    }
    
    this.cache.set(key, url);
  }

  static getCachedImage(key: string): string | undefined {
    return this.cache.get(key);
  }

  static clearCache() {
    this.cache.forEach(url => URL.revokeObjectURL(url));
    this.cache.clear();
  }

  static revokeImage(key: string) {
    const url = this.cache.get(key);
    if (url) {
      URL.revokeObjectURL(url);
      this.cache.delete(key);
    }
  }
}

// Global lazy loader instance
export const globalLazyLoader = new LazyImageLoader();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  globalLazyLoader.destroy();
  ImageMemoryManager.clearCache();
});
