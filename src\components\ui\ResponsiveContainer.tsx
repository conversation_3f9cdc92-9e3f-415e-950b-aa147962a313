/**
 * ResponsiveContainer Component
 * Provides mobile-first responsive containers and layouts
 * Prevents overflow and ensures proper mobile experience
 */

import { ReactNode } from 'react';
import { useResponsiveDesign } from '@/hooks/useResponsiveDesign';
import { cn } from '@/lib/utils';

interface ResponsiveContainerProps {
  children: ReactNode;
  className?: string;
  preventOverflow?: boolean;
  mobileFullWidth?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg';
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

export const ResponsiveContainer = ({
  children,
  className = '',
  preventOverflow = true,
  mobileFullWidth = true,
  padding = 'md',
  maxWidth = 'full'
}: ResponsiveContainerProps) => {
  const { isMobile, getContainerClasses } = useResponsiveDesign();

  const paddingClasses = {
    none: '',
    sm: isMobile ? 'p-2' : 'p-4',
    md: isMobile ? 'p-4' : 'p-6',
    lg: isMobile ? 'p-6' : 'p-8'
  };

  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  };

  return (
    <div
      className={cn(
        'w-full',
        preventOverflow && getContainerClasses(),
        mobileFullWidth && isMobile ? 'w-full' : maxWidthClasses[maxWidth],
        paddingClasses[padding],
        className
      )}
    >
      {children}
    </div>
  );
};

interface ResponsiveGridProps {
  children: ReactNode;
  className?: string;
  cols?: {
    mobile: number;
    tablet?: number;
    desktop?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
}

export const ResponsiveGrid = ({
  children,
  className = '',
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md'
}: ResponsiveGridProps) => {
  const { getGridColumns } = useResponsiveDesign();

  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6'
  };

  return (
    <div
      className={cn(
        'grid',
        getGridColumns(cols.mobile, cols.tablet, cols.desktop),
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  );
};

interface ResponsiveStackProps {
  children: ReactNode;
  className?: string;
  direction?: 'vertical' | 'horizontal' | 'responsive';
  spacing?: 'sm' | 'md' | 'lg';
  align?: 'start' | 'center' | 'end' | 'stretch';
  justify?: 'start' | 'center' | 'end' | 'between' | 'around';
}

export const ResponsiveStack = ({
  children,
  className = '',
  direction = 'responsive',
  spacing = 'md',
  align = 'start',
  justify = 'start'
}: ResponsiveStackProps) => {
  const { isMobile } = useResponsiveDesign();

  const spacingClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6'
  };

  const alignClasses = {
    start: 'items-start',
    center: 'items-center',
    end: 'items-end',
    stretch: 'items-stretch'
  };

  const justifyClasses = {
    start: 'justify-start',
    center: 'justify-center',
    end: 'justify-end',
    between: 'justify-between',
    around: 'justify-around'
  };

  const getDirectionClass = () => {
    if (direction === 'vertical') return 'flex-col';
    if (direction === 'horizontal') return 'flex-row';
    return isMobile ? 'flex-col' : 'flex-row'; // responsive
  };

  return (
    <div
      className={cn(
        'flex',
        getDirectionClass(),
        spacingClasses[spacing],
        alignClasses[align],
        justifyClasses[justify],
        className
      )}
    >
      {children}
    </div>
  );
};

interface ResponsiveTextProps {
  children: ReactNode;
  className?: string;
  size?: {
    mobile: string;
    tablet?: string;
    desktop?: string;
  };
  weight?: 'normal' | 'medium' | 'semibold' | 'bold';
  color?: 'default' | 'muted' | 'primary' | 'destructive';
  preventOverflow?: boolean;
}

export const ResponsiveText = ({
  children,
  className = '',
  size = { mobile: 'text-sm', tablet: 'text-base', desktop: 'text-base' },
  weight = 'normal',
  color = 'default',
  preventOverflow = true
}: ResponsiveTextProps) => {
  const { getFontSize, getTextClasses } = useResponsiveDesign();

  const weightClasses = {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold'
  };

  const colorClasses = {
    default: 'text-foreground',
    muted: 'text-muted-foreground',
    primary: 'text-primary',
    destructive: 'text-destructive'
  };

  return (
    <span
      className={cn(
        getFontSize(size.mobile, size.tablet, size.desktop),
        weightClasses[weight],
        colorClasses[color],
        preventOverflow && getTextClasses(),
        className
      )}
    >
      {children}
    </span>
  );
};

interface ResponsiveButtonGroupProps {
  children: ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical' | 'responsive';
  size?: 'sm' | 'default' | 'lg';
  fullWidth?: boolean;
}

export const ResponsiveButtonGroup = ({
  children,
  className = '',
  orientation = 'responsive',
  size = 'default',
  fullWidth = false
}: ResponsiveButtonGroupProps) => {
  const { isMobile, getTouchTargetSize } = useResponsiveDesign();

  const getOrientation = () => {
    if (orientation === 'horizontal') return 'flex-row';
    if (orientation === 'vertical') return 'flex-col';
    return isMobile ? 'flex-col' : 'flex-row'; // responsive
  };

  return (
    <div
      className={cn(
        'flex gap-2',
        getOrientation(),
        fullWidth && 'w-full',
        isMobile && getTouchTargetSize(),
        className
      )}
    >
      {children}
    </div>
  );
};

// Utility component for mobile-safe images
interface ResponsiveImageProps {
  src: string;
  alt: string;
  className?: string;
  aspectRatio?: 'square' | 'video' | 'wide' | 'auto';
  objectFit?: 'cover' | 'contain' | 'fill';
}

export const ResponsiveImage = ({
  src,
  alt,
  className = '',
  aspectRatio = 'auto',
  objectFit = 'cover'
}: ResponsiveImageProps) => {
  const { getImageClasses } = useResponsiveDesign();

  const aspectRatioClasses = {
    square: 'aspect-square',
    video: 'aspect-video',
    wide: 'aspect-[21/9]',
    auto: ''
  };

  const objectFitClasses = {
    cover: 'object-cover',
    contain: 'object-contain',
    fill: 'object-fill'
  };

  return (
    <img
      src={src}
      alt={alt}
      className={cn(
        getImageClasses(),
        aspectRatioClasses[aspectRatio],
        objectFitClasses[objectFit],
        className
      )}
      loading="lazy"
    />
  );
};
