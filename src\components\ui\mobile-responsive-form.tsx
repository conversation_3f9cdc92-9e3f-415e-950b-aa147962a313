/**
 * Mobile-Responsive Form Components
 * Optimized for mobile input and touch interactions
 */

import { forwardRef } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Mobile-optimized form container
interface MobileFormProps extends React.FormHTMLAttributes<HTMLFormElement> {
  children: React.ReactNode;
  spacing?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
}

const MobileForm = forwardRef<HTMLFormElement, MobileFormProps>(
  ({ className, children, spacing = 'md', fullWidth = true, ...props }, ref) => {
    const spacingClasses = {
      sm: "space-y-3",
      md: "space-y-4 sm:space-y-6",
      lg: "space-y-6 sm:space-y-8"
    };

    return (
      <form
        ref={ref}
        className={cn(
          spacingClasses[spacing],
          fullWidth && "w-full",
          // Mobile-optimized padding
          "p-4 sm:p-6",
          className
        )}
        {...props}
      >
        {children}
      </form>
    );
  }
);

MobileForm.displayName = "MobileForm";

// Mobile-optimized form field group
interface MobileFormFieldProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  required?: boolean;
  error?: string;
  stackOnMobile?: boolean;
}

const MobileFormField = forwardRef<HTMLDivElement, MobileFormFieldProps>(
  ({ className, children, required = false, error, stackOnMobile = true, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "space-y-2",
        stackOnMobile && "flex flex-col sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4",
        className
      )}
      {...props}
    >
      {children}
      {error && (
        <motion.p
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-sm text-destructive mt-1"
        >
          {error}
        </motion.p>
      )}
    </div>
  )
);

MobileFormField.displayName = "MobileFormField";

// Mobile-optimized label
interface MobileLabelProps extends React.LabelHTMLAttributes<HTMLLabelElement> {
  children: React.ReactNode;
  required?: boolean;
  minWidth?: boolean;
}

const MobileLabel = forwardRef<HTMLLabelElement, MobileLabelProps>(
  ({ className, children, required = false, minWidth = false, ...props }, ref) => (
    <Label
      ref={ref}
      className={cn(
        // Mobile-optimized text size
        "text-sm sm:text-base font-medium",
        // Minimum width for form alignment
        minWidth && "sm:min-w-[120px] sm:text-right",
        // Touch-friendly spacing
        "mb-1 sm:mb-0",
        className
      )}
      {...props}
    >
      {children}
      {required && <span className="text-destructive ml-1">*</span>}
    </Label>
  )
);

MobileLabel.displayName = "MobileLabel";

// Mobile-optimized input
interface MobileInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  error?: boolean;
  fullWidth?: boolean;
}

const MobileInput = forwardRef<HTMLInputElement, MobileInputProps>(
  ({ className, error = false, fullWidth = true, ...props }, ref) => (
    <Input
      ref={ref}
      className={cn(
        // Mobile-optimized sizing
        "h-12 sm:h-10 text-base sm:text-sm",
        // Full width on mobile
        fullWidth && "w-full",
        // Error styling
        error && "border-destructive focus-visible:ring-destructive",
        // Touch-friendly padding
        "px-4 py-3 sm:px-3 sm:py-2",
        className
      )}
      {...props}
    />
  )
);

MobileInput.displayName = "MobileInput";

// Mobile-optimized textarea
interface MobileTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  error?: boolean;
  fullWidth?: boolean;
  autoResize?: boolean;
}

const MobileTextarea = forwardRef<HTMLTextAreaElement, MobileTextareaProps>(
  ({ className, error = false, fullWidth = true, autoResize = false, ...props }, ref) => (
    <Textarea
      ref={ref}
      className={cn(
        // Mobile-optimized sizing
        "min-h-[100px] sm:min-h-[80px] text-base sm:text-sm",
        // Full width on mobile
        fullWidth && "w-full",
        // Error styling
        error && "border-destructive focus-visible:ring-destructive",
        // Touch-friendly padding
        "px-4 py-3 sm:px-3 sm:py-2",
        // Auto-resize
        autoResize && "resize-none",
        className
      )}
      {...props}
    />
  )
);

MobileTextarea.displayName = "MobileTextarea";

// Mobile-optimized select
interface MobileSelectProps {
  children: React.ReactNode;
  placeholder?: string;
  value?: string;
  onValueChange?: (value: string) => void;
  error?: boolean;
  fullWidth?: boolean;
  className?: string;
}

const MobileSelect = forwardRef<HTMLButtonElement, MobileSelectProps>(
  ({ children, placeholder, value, onValueChange, error = false, fullWidth = true, className }, ref) => (
    <Select value={value} onValueChange={onValueChange}>
      <SelectTrigger
        ref={ref}
        className={cn(
          // Mobile-optimized sizing
          "h-12 sm:h-10 text-base sm:text-sm",
          // Full width on mobile
          fullWidth && "w-full",
          // Error styling
          error && "border-destructive focus-visible:ring-destructive",
          // Touch-friendly padding
          "px-4 py-3 sm:px-3 sm:py-2",
          className
        )}
      >
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent className="max-h-[200px] sm:max-h-[300px]">
        {children}
      </SelectContent>
    </Select>
  )
);

MobileSelect.displayName = "MobileSelect";

// Mobile-optimized button group
interface MobileButtonGroupProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  orientation?: 'horizontal' | 'vertical';
  stackOnMobile?: boolean;
  fullWidthOnMobile?: boolean;
  spacing?: 'sm' | 'md' | 'lg';
}

const MobileButtonGroup = forwardRef<HTMLDivElement, MobileButtonGroupProps>(
  ({ 
    className, 
    children, 
    orientation = 'horizontal',
    stackOnMobile = true,
    fullWidthOnMobile = true,
    spacing = 'md',
    ...props 
  }, ref) => {
    const spacingClasses = {
      sm: "gap-2",
      md: "gap-3 sm:gap-4",
      lg: "gap-4 sm:gap-6"
    };

    return (
      <div
        ref={ref}
        className={cn(
          "flex",
          // Orientation and mobile stacking
          orientation === 'horizontal' && !stackOnMobile && "flex-row",
          orientation === 'vertical' && "flex-col",
          stackOnMobile && "flex-col sm:flex-row",
          // Spacing
          spacingClasses[spacing],
          // Full width buttons on mobile
          fullWidthOnMobile && "[&>button]:w-full sm:[&>button]:w-auto",
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

MobileButtonGroup.displayName = "MobileButtonGroup";

// Mobile-optimized submit button
interface MobileSubmitButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  loading?: boolean;
  fullWidth?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const MobileSubmitButton = forwardRef<HTMLButtonElement, MobileSubmitButtonProps>(
  ({ className, children, loading = false, fullWidth = true, size = 'md', ...props }, ref) => {
    const sizeClasses = {
      sm: "h-10 px-4 text-sm",
      md: "h-12 sm:h-10 px-6 text-base sm:text-sm",
      lg: "h-14 sm:h-12 px-8 text-lg sm:text-base"
    };

    return (
      <Button
        ref={ref}
        type="submit"
        disabled={loading}
        className={cn(
          sizeClasses[size],
          fullWidth && "w-full sm:w-auto",
          // Loading state
          loading && "opacity-70 cursor-not-allowed",
          // Touch-friendly styling
          "font-medium rounded-lg",
          // Enhanced touch feedback
          "active:scale-[0.98] transition-transform duration-150",
          className
        )}
        {...props}
      >
        {loading ? (
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            <span>Processing...</span>
          </div>
        ) : (
          children
        )}
      </Button>
    );
  }
);

MobileSubmitButton.displayName = "MobileSubmitButton";

// Form section for grouping related fields
interface MobileFormSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string;
  description?: string;
  children: React.ReactNode;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

const MobileFormSection = forwardRef<HTMLDivElement, MobileFormSectionProps>(
  ({ 
    className, 
    title, 
    description, 
    children, 
    collapsible = false, 
    defaultExpanded = true,
    ...props 
  }, ref) => (
    <div
      ref={ref}
      className={cn(
        "space-y-4",
        // Mobile-optimized padding and borders
        "p-4 sm:p-6 border rounded-lg",
        className
      )}
      {...props}
    >
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-lg sm:text-xl font-semibold">{title}</h3>
          )}
          {description && (
            <p className="text-sm sm:text-base text-muted-foreground">{description}</p>
          )}
        </div>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  )
);

MobileFormSection.displayName = "MobileFormSection";

export {
  MobileForm,
  MobileFormField,
  MobileLabel,
  MobileInput,
  MobileTextarea,
  MobileSelect,
  MobileButtonGroup,
  MobileSubmitButton,
  MobileFormSection
};
