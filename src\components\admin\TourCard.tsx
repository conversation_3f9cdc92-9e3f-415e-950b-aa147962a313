/**
 * Admin Tour Card Component
 * Display tour information with management actions
 * Supports both grid and list view modes
 */

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Eye, 
  Edit, 
  MoreHorizontal, 
  MapPin, 
  Calendar, 
  ShoppingCart,
  Users,
  BarChart3,
  ExternalLink,
  Copy,
  Trash2,
  Settings
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { createTourUrl } from '@/lib/slugUtils';

interface Tour {
  id: string;
  title: string;
  description: string;
  category: string;
  location: string;
  status: 'draft' | 'published';
  thumbnail_url?: string;
  business_name?: string;
  created_at: string;
  updated_at: string;
  views_count: number;
  commerce_enabled: boolean;
  vendor_id?: string;
  slug?: string;
}

interface TourCardProps {
  tour: Tour;
  viewMode: 'grid' | 'list';
  onUpdate?: () => void;
}

const TourCard = ({ tour, viewMode, onUpdate }: TourCardProps) => {
  const [isLoading, setIsLoading] = useState(false);

  const tourUrl = createTourUrl(tour.slug || tour.id);
  const fullTourUrl = `${window.location.origin}${tourUrl}`;

  const handleCopyUrl = () => {
    navigator.clipboard.writeText(fullTourUrl);
    toast.success('Tour URL copied to clipboard');
  };

  const handleViewTour = () => {
    window.open(tourUrl, '_blank');
  };

  const handleEditTour = () => {
    // TODO: Open edit modal
    toast.info('Edit functionality coming soon');
  };

  const handleDeleteTour = async () => {
    if (!confirm('Are you sure you want to delete this tour?')) return;
    
    setIsLoading(true);
    try {
      // TODO: Implement delete functionality
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Tour deleted successfully');
      onUpdate?.();
    } catch (error) {
      toast.error('Failed to delete tour');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleStatus = async () => {
    setIsLoading(true);
    try {
      // TODO: Implement status toggle
      await new Promise(resolve => setTimeout(resolve, 1000));
      const newStatus = tour.status === 'published' ? 'draft' : 'published';
      toast.success(`Tour ${newStatus === 'published' ? 'published' : 'unpublished'} successfully`);
      onUpdate?.();
    } catch (error) {
      toast.error('Failed to update tour status');
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (viewMode === 'list') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4 flex-1">
                {/* Thumbnail */}
                <div className="w-16 h-16 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
                  {tour.thumbnail_url ? (
                    <img 
                      src={tour.thumbnail_url} 
                      alt={tour.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <MapPin className="w-6 h-6 text-muted-foreground" />
                  )}
                </div>

                {/* Tour Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold truncate">{tour.title}</h3>
                    <Badge variant={tour.status === 'published' ? 'default' : 'secondary'}>
                      {tour.status}
                    </Badge>
                    {tour.commerce_enabled && (
                      <Badge variant="outline">
                        <ShoppingCart className="w-3 h-3 mr-1" />
                        Commerce
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground truncate mb-1">
                    {tour.business_name || tour.description}
                  </p>
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      {tour.location}
                    </span>
                    <span className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      {formatDate(tour.created_at)}
                    </span>
                    <span className="flex items-center gap-1">
                      <Eye className="w-3 h-3" />
                      {tour.views_count || 0} views
                    </span>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center gap-2">
                <Button size="sm" variant="outline" onClick={handleViewTour}>
                  <Eye className="w-4 h-4 mr-1" />
                  View
                </Button>
                <Button size="sm" variant="outline" onClick={handleEditTour}>
                  <Edit className="w-4 h-4 mr-1" />
                  Edit
                </Button>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="sm" variant="ghost">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleCopyUrl}>
                      <Copy className="w-4 h-4 mr-2" />
                      Copy URL
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleToggleStatus}>
                      <Settings className="w-4 h-4 mr-2" />
                      {tour.status === 'published' ? 'Unpublish' : 'Publish'}
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={handleDeleteTour}
                      className="text-destructive"
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  // Grid view
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Card className="hover:shadow-lg transition-all duration-300 group">
        {/* Thumbnail */}
        <div className="relative aspect-video bg-muted overflow-hidden">
          {tour.thumbnail_url ? (
            <img 
              src={tour.thumbnail_url} 
              alt={tour.title}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <MapPin className="w-12 h-12 text-muted-foreground" />
            </div>
          )}
          
          {/* Status Badge */}
          <div className="absolute top-3 left-3">
            <Badge variant={tour.status === 'published' ? 'default' : 'secondary'}>
              {tour.status}
            </Badge>
          </div>

          {/* Commerce Badge */}
          {tour.commerce_enabled && (
            <div className="absolute top-3 right-3">
              <Badge variant="outline" className="bg-white/90">
                <ShoppingCart className="w-3 h-3 mr-1" />
                Commerce
              </Badge>
            </div>
          )}

          {/* Quick Actions Overlay */}
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-2">
            <Button size="sm" variant="secondary" onClick={handleViewTour}>
              <Eye className="w-4 h-4 mr-1" />
              View
            </Button>
            <Button size="sm" variant="secondary" onClick={handleEditTour}>
              <Edit className="w-4 h-4 mr-1" />
              Edit
            </Button>
          </div>
        </div>

        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg truncate">{tour.title}</CardTitle>
              <CardDescription className="line-clamp-2">
                {tour.business_name || tour.description}
              </CardDescription>
            </div>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" variant="ghost">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={handleViewTour}>
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Open Tour
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleCopyUrl}>
                  <Copy className="w-4 h-4 mr-2" />
                  Copy URL
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleToggleStatus}>
                  <Settings className="w-4 h-4 mr-2" />
                  {tour.status === 'published' ? 'Unpublish' : 'Publish'}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem 
                  onClick={handleDeleteTour}
                  className="text-destructive"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>

        <CardContent className="pt-0">
          {/* Tour Details */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <MapPin className="w-4 h-4" />
              <span className="truncate">{tour.location}</span>
            </div>
            
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="w-4 h-4" />
              <span>Created {formatDate(tour.created_at)}</span>
            </div>

            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-1 text-muted-foreground">
                <Eye className="w-4 h-4" />
                <span>{tour.views_count || 0} views</span>
              </div>
              
              {tour.vendor_id && (
                <div className="flex items-center gap-1 text-muted-foreground">
                  <Users className="w-4 h-4" />
                  <span>Vendor</span>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 mt-4">
            <Button 
              size="sm" 
              variant="outline" 
              className="flex-1"
              onClick={handleEditTour}
            >
              <Edit className="w-4 h-4 mr-1" />
              Edit
            </Button>
            <Button 
              size="sm" 
              className="flex-1"
              onClick={handleViewTour}
            >
              <Eye className="w-4 h-4 mr-1" />
              View
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default TourCard;
