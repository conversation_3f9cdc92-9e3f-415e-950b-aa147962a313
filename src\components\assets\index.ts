/**
 * Asset Management Components
 * Export all asset-related components and utilities
 */

// Main components
export { default as AssetUploader } from './AssetUploader';
export { default as AssetBrowser } from './AssetBrowser';

// Types
export type { AssetUploaderProps } from './AssetUploader';
export type { AssetBrowserProps } from './AssetBrowser';

// Asset manager and types
export { assetManager } from '@/lib/assets/assetManager';
export type { 
  AssetMetadata, 
  UploadOptions, 
  ValidationResult 
} from '@/lib/assets/assetManager';

// Hook
export { useAssets } from '@/hooks/useAssets';
export type { UseAssetsOptions, UseAssetsReturn } from '@/hooks/useAssets';
