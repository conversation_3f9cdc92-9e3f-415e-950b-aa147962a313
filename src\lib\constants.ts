// Tour categories
export const categories = [
  { value: 'property', label: 'Property' },
  { value: 'education', label: 'Education' },
  { value: 'hospitality', label: 'Hospitality' },
  { value: 'tourism', label: 'Tourism' },
  { value: 'culture', label: 'Culture' },
  { value: 'commercial', label: 'Commercial' },
  { value: 'healthcare', label: 'Healthcare' },
  { value: 'government', label: 'Government' }
];

// FAQ data
export const faqData = [
  {
    question: "How do I create my first virtual tour?",
    answer: "Sign up for an account, upload your 360° images or videos, add interactive hotspots, and publish your tour. Our step-by-step guide makes it easy."
  },
  {
    question: "What file formats do you support?",
    answer: "We support JPEG, PNG, and WebP for images, and MP4, WebM, and QuickTime for videos. Maximum file size is 100MB per file."
  },
  {
    question: "Can I use this on mobile devices?",
    answer: "Yes! Our platform is fully responsive and optimized for mobile devices. Your virtual tours work seamlessly on phones and tablets."
  },
  {
    question: "Do you offer enterprise solutions?",
    answer: "Yes, we provide custom enterprise solutions with advanced features, dedicated support, and custom integrations. Contact our sales team."
  }
];
