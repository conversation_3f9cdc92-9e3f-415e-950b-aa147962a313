
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, Users, Award, Globe, Camera, Smartphone, Headset, Link as LinkIcon } from 'lucide-react';
import { Link } from 'react-router-dom';

const About = () => {
  const stats = [
    { icon: Eye, label: "Virtual Tours Created", value: "12,800+", color: "text-blue-600" },
    { icon: Users, label: "Active Users", value: "8,400+", color: "text-theme-primary" },
    { icon: Globe, label: "Countries Reached", value: "85+", color: "text-purple-600" },
    { icon: Award, label: "Years of Experience", value: "5+", color: "text-orange-600" }
  ];

  const features = [
    {
      icon: Camera,
      title: "360° Photography & Video",
      description: "Support for both panoramic images and immersive 360° videos with crystal clear quality."
    },
    {
      icon: Smartphone,
      title: "Mobile Optimized",
      description: "Seamless experience across all devices with responsive design and touch controls."
    },
    {
      icon: Headset,
      title: "VR Ready",
      description: "Compatible with VR headsets for the ultimate immersive experience."
    },
    {
      icon: LinkIcon,
      title: "Interactive Hotspots",
      description: "Add clickable hotspots for navigation, information, WhatsApp contact, and external links."
    }
  ];

  const team = [
    {
      name: "Adebayo Johnson",
      role: "Founder & CEO",
      description: "Former Google engineer with 8+ years in VR/AR technology and Nigerian real estate.",
      image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
    },
    {
      name: "Funmi Adeoye",
      role: "CTO",
      description: "Tech lead specializing in 3D graphics and immersive web technologies.",
      image: "https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
    },
    {
      name: "Chidi Okafor",
      role: "Lead Designer",
      description: "UI/UX expert focused on creating intuitive virtual tour experiences.",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      {/* Enhanced Hero Section with Background */}
      <section className="relative pt-32 lg:pt-40 pb-20 overflow-hidden">
        <div className="absolute inset-0 z-0">
          <img
            src="https://images.unsplash.com/photo-1497366811353-6870744d04b2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2069&q=80"
            alt="Modern business office space showcasing our company story"
            className="w-full h-full object-cover object-center"
          />
          <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-black/70"></div>
        </div>

        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center rounded-full bg-white/10 backdrop-blur-sm border border-white/20 px-4 py-2 text-sm font-medium text-white mb-6">
              🇳🇬 Made in Nigeria
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 drop-shadow-lg">
              About VirtualRealTour
            </h1>
            <p className="text-xl md:text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed drop-shadow-md">
              We're revolutionizing how Nigerians showcase and explore spaces through
              cutting-edge 360° virtual tour technology. From Lagos penthouses to Abuja offices,
              we bring immersive experiences to your fingertips.
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 sm:px-6 py-8">

        {/* Enhanced Stats Section */}
        <section className="relative mb-20 overflow-hidden rounded-3xl">
          <div className="absolute inset-0 z-0">
            <img
              src="https://images.unsplash.com/photo-1555529902-5261145633bf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
              alt="Shopping mall and retail achievements showcasing our impact"
              className="w-full h-full object-cover object-center"
            />
            <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-black/50 to-black/70"></div>
          </div>

          <div className="relative z-10 p-8 lg:p-12">
            <div className="text-center mb-12">
              <div className="inline-flex items-center rounded-full bg-white/10 backdrop-blur-sm border border-white/20 px-4 py-2 text-sm font-medium text-white mb-6">
                📊 Our Impact
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4 drop-shadow-lg">
                Trusted by Thousands
              </h2>
            </div>

            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.map((stat, index) => (
                <div key={index} className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center border border-white/20 hover:bg-white/20 transition-all duration-300 hover:scale-105">
                  <div className="w-12 h-12 bg-theme-primary/20 backdrop-blur-sm rounded-lg flex items-center justify-center mx-auto mb-4">
                    <stat.icon className="w-6 h-6 text-theme-primary" />
                  </div>
                  <div className="text-2xl lg:text-3xl font-bold text-white mb-2 drop-shadow-md">{stat.value}</div>
                  <div className="text-sm text-white/80">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Mission Section */}
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-3xl p-8 md:p-12 mb-16">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">Our Mission</h2>
            <p className="text-lg md:text-xl text-gray-700 leading-relaxed mb-8">
              To democratize immersive technology in Nigeria by providing an affordable, 
              user-friendly platform that enables businesses, educators, and individuals 
              to create professional 360° virtual tours without technical expertise.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-left">
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">🏠 Real Estate</h3>
                <p className="text-gray-600 text-sm">Help property developers showcase homes and offices to remote buyers</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">🎓 Education</h3>
                <p className="text-gray-600 text-sm">Enable schools and universities to offer virtual campus tours</p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">🏨 Hospitality</h3>
                <p className="text-gray-600 text-sm">Allow hotels and venues to attract guests with immersive previews</p>
              </div>
            </div>
          </div>
        </div>

        {/* Technology Features */}
        <div className="mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
            Cutting-Edge Technology
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow group">
                <CardContent className="p-6 text-center">
                  <feature.icon className="w-12 h-12 mx-auto mb-4 text-blue-600 group-hover:scale-110 transition-transform" />
                  <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                  <p className="text-sm text-gray-600">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Team Section (conditionally rendered) */}
        {typeof window !== 'undefined' && localStorage.getItem('showTeamSection') !== 'false' && (
          <div className="mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-center text-gray-900 mb-12">
              Meet Our Team
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {team.map((member, index) => (
                <Card key={index} className="hover:shadow-lg transition-shadow overflow-hidden">
                  <div className="aspect-square overflow-hidden bg-gray-100">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(member.name)}&background=3b82f6&color=fff&size=400`;
                      }}
                    />
                  </div>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-1">{member.name}</h3>
                    <Badge variant="secondary" className="mb-3">{member.role}</Badge>
                    <p className="text-gray-600 text-sm">{member.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Nigerian Focus */}
        <div className="bg-theme-primary-light border border-theme-primary-border rounded-3xl p-8 md:p-12 mb-16">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Built for Nigeria 🇳🇬
            </h2>
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              We understand the unique challenges and opportunities in the Nigerian market. 
              Our platform is optimized for local internet speeds, supports mobile-first usage, 
              and integrates with popular Nigerian communication channels like WhatsApp.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">📱 WhatsApp Integration</h4>
                <p className="text-gray-600 text-sm">Direct contact buttons for instant customer communication</p>
              </div>
              <div>
                <h4 className="font-semibold text-gray-900 mb-2">⚡ Optimized Loading</h4>
                <p className="text-gray-600 text-sm">Fast loading even on slower internet connections</p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Ready to Create Your Virtual Tour?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Join thousands of Nigerian businesses already using VirtualRealTour to showcase their spaces
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild className="text-lg px-8 py-4">
              <Link to="/auth">Get Started Today</Link>
            </Button>
            <Button size="lg" variant="outline" asChild className="text-lg px-8 py-4">
              <Link to="/showcase">View Sample Tours</Link>
            </Button>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default About;
