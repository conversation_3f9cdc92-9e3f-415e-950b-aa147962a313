/**
 * WooCommerce Configuration Component
 * Admin interface for managing WooCommerce + WPVR integration
 */

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { 
  Settings, 
  Save, 
  TestTube, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  ExternalLink,
  Key,
  Globe,
  ShoppingCart,
  MessageCircle
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

// Import our services
import { wooCommerceService } from '@/services/commerce/WooCommerceService';

interface WooCommerceConfigProps {
  className?: string;
}

interface ConfigState {
  wordpressUrl: string;
  consumerKey: string;
  consumerSecret: string;
  jwtSecret: string;
  wpvrEnabled: boolean;
  wpvrApiKey: string;
  defaultWhatsApp: string;
  whatsappEnabled: boolean;
}

interface ConnectionStatus {
  wordpress: 'connected' | 'disconnected' | 'testing';
  woocommerce: 'connected' | 'disconnected' | 'testing';
  wpvr: 'connected' | 'disconnected' | 'testing';
}

const WooCommerceConfig: React.FC<WooCommerceConfigProps> = ({ className = '' }) => {
  const [config, setConfig] = useState<ConfigState>({
    wordpressUrl: import.meta.env.VITE_WORDPRESS_URL || '',
    consumerKey: import.meta.env.VITE_WOO_CONSUMER_KEY || '',
    consumerSecret: import.meta.env.VITE_WOO_CONSUMER_SECRET || '',
    jwtSecret: import.meta.env.VITE_JWT_SECRET || '',
    wpvrEnabled: import.meta.env.VITE_WPVR_ENABLED === 'true',
    wpvrApiKey: import.meta.env.VITE_WPVR_API_KEY || '',
    defaultWhatsApp: import.meta.env.VITE_DEFAULT_WHATSAPP_NUMBER || '',
    whatsappEnabled: import.meta.env.VITE_WHATSAPP_ENABLED === 'true'
  });

  const [status, setStatus] = useState<ConnectionStatus>({
    wordpress: 'disconnected',
    woocommerce: 'disconnected',
    wpvr: 'disconnected'
  });

  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Test WordPress connection
  const testWordPressConnection = async () => {
    setStatus(prev => ({ ...prev, wordpress: 'testing' }));
    
    try {
      const response = await fetch(`${config.wordpressUrl}/wp-json/wp/v2/posts?per_page=1`);
      if (response.ok) {
        setStatus(prev => ({ ...prev, wordpress: 'connected' }));
        toast.success('WordPress connection successful');
      } else {
        throw new Error('WordPress API not accessible');
      }
    } catch (error) {
      setStatus(prev => ({ ...prev, wordpress: 'disconnected' }));
      toast.error('WordPress connection failed');
    }
  };

  // Test WooCommerce connection
  const testWooCommerceConnection = async () => {
    setStatus(prev => ({ ...prev, woocommerce: 'testing' }));
    
    try {
      await wooCommerceService.getProducts({ per_page: 1 });
      setStatus(prev => ({ ...prev, woocommerce: 'connected' }));
      toast.success('WooCommerce connection successful');
    } catch (error) {
      setStatus(prev => ({ ...prev, woocommerce: 'disconnected' }));
      toast.error('WooCommerce connection failed');
    }
  };

  // Test WPVR connection
  const testWPVRConnection = async () => {
    setStatus(prev => ({ ...prev, wpvr: 'testing' }));
    
    try {
      // Test WPVR API endpoint
      const response = await fetch(`${config.wordpressUrl}/wp-json/wpvr/v1/tours?per_page=1`);
      if (response.ok) {
        setStatus(prev => ({ ...prev, wpvr: 'connected' }));
        toast.success('WPVR connection successful');
      } else {
        throw new Error('WPVR API not accessible');
      }
    } catch (error) {
      setStatus(prev => ({ ...prev, wpvr: 'disconnected' }));
      toast.error('WPVR connection failed');
    }
  };

  // Save configuration
  const saveConfiguration = async () => {
    setIsSaving(true);
    
    try {
      // In a real implementation, you would save to your backend/database
      // For now, we'll just simulate saving to localStorage
      localStorage.setItem('woocommerce_config', JSON.stringify(config));
      
      setLastSaved(new Date());
      toast.success('Configuration saved successfully');
      
      // Test all connections after saving
      await Promise.all([
        testWordPressConnection(),
        testWooCommerceConnection(),
        config.wpvrEnabled ? testWPVRConnection() : Promise.resolve()
      ]);
      
    } catch (error) {
      toast.error('Failed to save configuration');
    } finally {
      setIsSaving(false);
    }
  };

  // Load saved configuration on mount
  useEffect(() => {
    const savedConfig = localStorage.getItem('woocommerce_config');
    if (savedConfig) {
      try {
        const parsed = JSON.parse(savedConfig);
        setConfig(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Error loading saved config:', error);
      }
    }
  }, []);

  // Status icon component
  const StatusIcon = ({ status }: { status: 'connected' | 'disconnected' | 'testing' }) => {
    switch (status) {
      case 'connected':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'testing':
        return <AlertCircle className="w-4 h-4 text-yellow-500 animate-pulse" />;
      default:
        return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">WooCommerce Integration</h2>
          <p className="text-gray-600">Configure WordPress, WooCommerce, and WPVR integration</p>
        </div>
        
        {lastSaved && (
          <Badge variant="secondary" className="text-xs">
            Last saved: {lastSaved.toLocaleTimeString()}
          </Badge>
        )}
      </div>

      {/* Connection Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Connection Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 text-gray-600" />
                <span className="font-medium">WordPress</span>
              </div>
              <div className="flex items-center gap-2">
                <StatusIcon status={status.wordpress} />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={testWordPressConnection}
                  disabled={status.wordpress === 'testing'}
                >
                  <TestTube className="w-3 h-3" />
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <ShoppingCart className="w-4 h-4 text-gray-600" />
                <span className="font-medium">WooCommerce</span>
              </div>
              <div className="flex items-center gap-2">
                <StatusIcon status={status.woocommerce} />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={testWooCommerceConnection}
                  disabled={status.woocommerce === 'testing'}
                >
                  <TestTube className="w-3 h-3" />
                </Button>
              </div>
            </div>

            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4 text-gray-600" />
                <span className="font-medium">WPVR</span>
              </div>
              <div className="flex items-center gap-2">
                <StatusIcon status={status.wpvr} />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={testWPVRConnection}
                  disabled={status.wpvr === 'testing' || !config.wpvrEnabled}
                >
                  <TestTube className="w-3 h-3" />
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* WordPress Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5" />
            WordPress Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="wordpress-url">WordPress URL</Label>
            <Input
              id="wordpress-url"
              value={config.wordpressUrl}
              onChange={(e) => setConfig(prev => ({ ...prev, wordpressUrl: e.target.value }))}
              placeholder="https://admin.virtualrealtour.ng"
            />
          </div>

          <div>
            <Label htmlFor="jwt-secret">JWT Secret Key</Label>
            <Input
              id="jwt-secret"
              type="password"
              value={config.jwtSecret}
              onChange={(e) => setConfig(prev => ({ ...prev, jwtSecret: e.target.value }))}
              placeholder="Your JWT secret key"
            />
          </div>
        </CardContent>
      </Card>

      {/* WooCommerce Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="w-5 h-5" />
            WooCommerce Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="consumer-key">Consumer Key</Label>
            <Input
              id="consumer-key"
              type="password"
              value={config.consumerKey}
              onChange={(e) => setConfig(prev => ({ ...prev, consumerKey: e.target.value }))}
              placeholder="ck_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
            />
          </div>

          <div>
            <Label htmlFor="consumer-secret">Consumer Secret</Label>
            <Input
              id="consumer-secret"
              type="password"
              value={config.consumerSecret}
              onChange={(e) => setConfig(prev => ({ ...prev, consumerSecret: e.target.value }))}
              placeholder="cs_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
            />
          </div>
        </CardContent>
      </Card>

      {/* WPVR Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5" />
            WPVR Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="wpvr-enabled"
              checked={config.wpvrEnabled}
              onCheckedChange={(checked) => setConfig(prev => ({ ...prev, wpvrEnabled: checked }))}
            />
            <Label htmlFor="wpvr-enabled">Enable WPVR Integration</Label>
          </div>

          {config.wpvrEnabled && (
            <div>
              <Label htmlFor="wpvr-api-key">WPVR API Key</Label>
              <Input
                id="wpvr-api-key"
                type="password"
                value={config.wpvrApiKey}
                onChange={(e) => setConfig(prev => ({ ...prev, wpvrApiKey: e.target.value }))}
                placeholder="Your WPVR API key"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* WhatsApp Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageCircle className="w-5 h-5" />
            WhatsApp Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch
              id="whatsapp-enabled"
              checked={config.whatsappEnabled}
              onCheckedChange={(checked) => setConfig(prev => ({ ...prev, whatsappEnabled: checked }))}
            />
            <Label htmlFor="whatsapp-enabled">Enable WhatsApp Checkout</Label>
          </div>

          {config.whatsappEnabled && (
            <div>
              <Label htmlFor="default-whatsapp">Default WhatsApp Number</Label>
              <Input
                id="default-whatsapp"
                value={config.defaultWhatsApp}
                onChange={(e) => setConfig(prev => ({ ...prev, defaultWhatsApp: e.target.value }))}
                placeholder="+2348000000000"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button
          onClick={saveConfiguration}
          disabled={isSaving}
          className="min-w-[120px]"
        >
          {isSaving ? (
            <>
              <AlertCircle className="w-4 h-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              Save Configuration
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default WooCommerceConfig;
