import { useState, useEffect } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Save,
  Eye,
  Globe,
  MapPin,
  Building,
  Phone,
  Mail,
  Clock,
  ExternalLink,
  Play,
  Settings,
  Info,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { supabase, Tour } from '@/lib/supabase';
import { toast } from 'sonner';
import EnhancedTourCard from '@/components/EnhancedTourCard';
import { createTourUrl } from '@/lib/slugUtils';

interface TourEditModalProps {
  tour: Tour;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

const TourEditModal = ({ tour, open, onOpenChange, onSuccess }: TourEditModalProps) => {
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState('edit');

  const [formData, setFormData] = useState({
    title: tour.title,
    description: tour.description || '',
    category: tour.category,
    location: tour.location || '',
    business_name: tour.business_name || '',
    business_type: tour.business_type || '',
    contact_phone: tour.contact_phone || '',
    contact_email: tour.contact_email || '',
    website: tour.website || '',
    business_hours: tour.business_hours || '',
    embed_url: tour.embed_url || '',
    embed_type: tour.embed_type || '',
    status: tour.status
  });

  useEffect(() => {
    setFormData({
      title: tour.title,
      description: tour.description || '',
      category: tour.category,
      location: tour.location || '',
      business_name: tour.business_name || '',
      business_type: tour.business_type || '',
      contact_phone: tour.contact_phone || '',
      contact_email: tour.contact_email || '',
      website: tour.website || '',
      business_hours: tour.business_hours || '',
      embed_url: tour.embed_url || '',
      embed_type: tour.embed_type || '',
      status: tour.status
    });
  }, [tour]);

  const updateTourMutation = useMutation({
    mutationFn: async () => {
      const { data, error } = await supabase
        .from('tours')
        .update(formData)
        .eq('id', tour.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-all-tours'] });
      toast.success('Tour updated successfully!');
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(`Failed to update tour: ${error.message}`);
    },
  });

  const publishTourMutation = useMutation({
    mutationFn: async () => {
      const { data, error } = await supabase
        .from('tours')
        .update({ ...formData, status: 'published' })
        .eq('id', tour.id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-all-tours'] });
      toast.success('Tour published successfully! It will now appear on your website.');
      setFormData({ ...formData, status: 'published' });
      onSuccess?.();
    },
    onError: (error) => {
      toast.error(`Failed to publish tour: ${error.message}`);
    },
  });

  const handleSave = () => {
    updateTourMutation.mutate();
  };

  const handlePublish = () => {
    publishTourMutation.mutate();
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      draft: 'bg-yellow-100 text-yellow-800',
      processing: 'bg-blue-100 text-blue-800',
      published: 'bg-green-100 text-green-800',
      archived: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'published':
        return <CheckCircle className="w-4 h-4" />;
      case 'draft':
        return <AlertCircle className="w-4 h-4" />;
      default:
        return <Info className="w-4 h-4" />;
    }
  };

  const getTourPreviewUrl = () => {
    if (formData.embed_url) {
      return formData.embed_url;
    }
    return `/tour/${tour.slug || tour.id}`;
  };

  const getDisplayLocation = () => {
    if (formData.status === 'published') {
      return [
        'Homepage Featured Section (if featured)',
        'Tours Showcase Page',
        'Category Pages',
        'Search Results',
        'Direct Link Access'
      ];
    }
    return ['Not visible to public (Draft mode)'];
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-full max-w-full sm:max-w-2xl md:max-w-4xl lg:max-w-6xl max-h-[90vh] overflow-y-auto p-2 sm:p-4 md:p-6 flex flex-col gap-4">
        <DialogHeader className="modal-header-sticky w-full">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 w-full">
            <div>
              <DialogTitle className="flex flex-col md:flex-row md:items-center gap-3">
                <span>Edit Tour: {tour.title}</span>
                <Badge className={getStatusColor(formData.status)}>
                  {getStatusIcon(formData.status)}
                  {formData.status}
                </Badge>
              </DialogTitle>
              <DialogDescription>
                Edit tour details, preview changes, and manage publishing status
              </DialogDescription>
            </div>
            <div className="flex flex-col md:flex-row items-stretch md:items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => setActiveTab('preview')} className="w-full md:w-auto">
                <Eye className="w-4 h-4 mr-2" />
                Preview
              </Button>
              {formData.embed_url && (
                <Button variant="outline" size="sm" asChild className="w-full md:w-auto">
                  <Link to={tour.slug ? `/tour/${tour.slug}` : createTourUrl(formData.title)}>
                    <ExternalLink className="w-4 h-4 mr-2" />
                    View Tour
                  </Link>
                </Button>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="modal-content-area w-full">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-1 sm:grid-cols-3">
              <TabsTrigger value="edit">
                <Settings className="w-4 h-4 mr-2" />
                Edit Details
              </TabsTrigger>
              <TabsTrigger value="preview">
                <Eye className="w-4 h-4 mr-2" />
                Preview
              </TabsTrigger>
              <TabsTrigger value="publish">
                <Globe className="w-4 h-4 mr-2" />
                Publishing
              </TabsTrigger>
            </TabsList>

          {/* Edit Tab */}
          <TabsContent value="edit" className="space-y-6 w-full">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Tour Title *</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      placeholder="e.g., Luxury Villa in Lekki Phase 1"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Describe what visitors will experience..."
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="category">Category</Label>
                      <Select value={formData.category} onValueChange={(value) => setFormData({ ...formData, category: value as Tour['category'] })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="property">Property</SelectItem>
                          <SelectItem value="education">Education</SelectItem>
                          <SelectItem value="hospitality">Hospitality</SelectItem>
                          <SelectItem value="tourism">Tourism</SelectItem>
                          <SelectItem value="culture">Culture</SelectItem>
                          <SelectItem value="commercial">Commercial</SelectItem>
                          <SelectItem value="healthcare">Healthcare</SelectItem>
                          <SelectItem value="government">Government</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        value={formData.location}
                        onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                        placeholder="e.g., Victoria Island, Lagos"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="embed_url">Tour URL</Label>
                    <Input
                      id="embed_url"
                      value={formData.embed_url}
                      onChange={(e) => setFormData({ ...formData, embed_url: e.target.value })}
                      placeholder="e.g., https://my.matterport.com/show/?m=..."
                      type="url"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="embed_type">Embed Type</Label>
                    <Select 
                      value={formData.embed_type} 
                      onValueChange={(value: 'iframe' | 'link' | 'custom') => setFormData({ ...formData, embed_type: value })}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select embed type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="iframe">Iframe Embed</SelectItem>
                        <SelectItem value="link">Direct Link</SelectItem>
                        <SelectItem value="custom">Custom Integration</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>

              {/* Business Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Business Information</CardTitle>
                  <CardDescription>
                    Optional information for Google Business integration
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="business_name">Business Name</Label>
                    <Input
                      id="business_name"
                      value={formData.business_name}
                      onChange={(e) => setFormData({ ...formData, business_name: e.target.value })}
                      placeholder="e.g., Luxury Hotels Lagos"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="business_type">Business Type</Label>
                    <Input
                      id="business_type"
                      value={formData.business_type}
                      onChange={(e) => setFormData({ ...formData, business_type: e.target.value })}
                      placeholder="e.g., Hotel, Restaurant, Office"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contact_phone">Contact Phone</Label>
                    <Input
                      id="contact_phone"
                      value={formData.contact_phone}
                      onChange={(e) => setFormData({ ...formData, contact_phone: e.target.value })}
                      placeholder="e.g., +234 ************"
                      type="tel"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="contact_email">Contact Email</Label>
                    <Input
                      id="contact_email"
                      value={formData.contact_email}
                      onChange={(e) => setFormData({ ...formData, contact_email: e.target.value })}
                      placeholder="e.g., <EMAIL>"
                      type="email"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="website">Website</Label>
                    <Input
                      id="website"
                      value={formData.website}
                      onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                      placeholder="e.g., https://www.business.com"
                      type="url"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="business_hours">Business Hours</Label>
                    <Input
                      id="business_hours"
                      value={formData.business_hours}
                      onChange={(e) => setFormData({ ...formData, business_hours: e.target.value })}
                      placeholder="e.g., Mon-Fri 9AM-6PM"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Preview Tab */}
          <TabsContent value="preview" className="space-y-6 w-full">
            <div className="w-full">
              <div className="mb-4">
                <h2 className="text-xl font-semibold">Tour Preview</h2>
                <p className="text-muted-foreground text-sm">This is how your tour will appear to visitors</p>
              </div>
              {formData.embed_url ? (
                <div className="space-y-4">
                  {/* Clean, full-width tour preview (no Card) */}
                  <div className="w-full h-[350px] md:h-[450px] rounded-lg overflow-hidden border bg-background flex items-center justify-center">
                    <iframe
                      src={formData.embed_url}
                      title={formData.title}
                      className="w-full h-full border-0"
                      allowFullScreen
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm w-full">
                    <div>
                      <h4 className="font-semibold mb-2">Tour Information</h4>
                      <p><strong>Title:</strong> {formData.title}</p>
                      <p><strong>Category:</strong> {formData.category}</p>
                      <p><strong>Location:</strong> {formData.location}</p>
                      {formData.description && <p><strong>Description:</strong> {formData.description}</p>}
                    </div>
                    {(formData.business_name || formData.contact_phone || formData.contact_email) && (
                      <div>
                        <h4 className="font-semibold mb-2">Business Information</h4>
                        {formData.business_name && <p><strong>Business:</strong> {formData.business_name}</p>}
                        {formData.contact_phone && <p><strong>Phone:</strong> {formData.contact_phone}</p>}
                        {formData.contact_email && <p><strong>Email:</strong> {formData.contact_email}</p>}
                        {formData.website && (
                          <p>
                            <strong>Website:</strong>
                            <a href={formData.website} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline ml-1">
                              {formData.website}
                            </a>
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <AlertCircle className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">No Tour Content</h3>
                  <p className="text-muted-foreground">
                    Add a tour URL in the Edit tab to preview your tour
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Publishing Tab */}
          <TabsContent value="publish" className="space-y-6 w-full">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
              <Card>
                <CardHeader>
                  <CardTitle>Publishing Status</CardTitle>
                  <CardDescription>
                    Control when and where your tour appears
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Badge className={getStatusColor(formData.status)}>
                      {getStatusIcon(formData.status)}
                      {formData.status}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                      Current status
                    </span>
                  </div>

                  <div className="space-y-2">
                    <Label>Change Status</Label>
                    <Select 
                      value={formData.status} 
                      onValueChange={(value: 'draft' | 'published' | 'archived') => setFormData({ ...formData, status: value })}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">Draft (Not visible to public)</SelectItem>
                        <SelectItem value="published">Published (Live on website)</SelectItem>
                        <SelectItem value="archived">Archived (Hidden from public)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {formData.status === 'published' && (
                    <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center gap-2 text-green-800">
                        <CheckCircle className="w-4 h-4" />
                        <span className="font-medium">Tour is Live!</span>
                      </div>
                      <p className="text-sm text-green-700 mt-1">
                        Your tour is visible to the public and can be accessed via direct link.
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Where Your Tour Appears</CardTitle>
                  <CardDescription>
                    Locations where published tours are displayed
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {getDisplayLocation().map((location, index) => (
                      <div key={index} className="flex items-center gap-3">
                        <div className={`w-2 h-2 rounded-full ${formData.status === 'published' ? 'bg-green-500' : 'bg-gray-300'}`} />
                        <span className="text-sm">{location}</span>
                      </div>
                    ))}
                  </div>

                  {formData.status === 'published' && (
                    <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                      <p className="text-sm text-blue-800">
                        <strong>Direct Link:</strong> Your tour can be accessed at:
                      </p>
                      <code className="text-xs bg-white px-2 py-1 rounded mt-1 block">
                        {window.location.origin}{tour.slug ? `/tour/${tour.slug}` : createTourUrl(formData.title)}
                      </code>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
        </div>

        {/* Action Buttons */}
        <div className="modal-footer-sticky flex flex-col md:flex-row md:justify-between gap-4 pt-4 border-t w-full">
          <Button variant="outline" onClick={() => onOpenChange(false)} className="w-full md:w-auto">
            Close
          </Button>

          <div className="modal-buttons flex flex-col md:flex-row gap-3 w-full md:w-auto">
            <Button
              variant="outline"
              onClick={handleSave}
              disabled={updateTourMutation.isPending}
              className="w-full md:w-auto"
            >
              <Save className="w-4 h-4 mr-2" />
              {updateTourMutation.isPending ? 'Saving...' : 'Save Changes'}
            </Button>

            {formData.status !== 'published' && (
              <Button
                onClick={handlePublish}
                disabled={publishTourMutation.isPending || !formData.embed_url}
                className="w-full md:w-auto"
              >
                <Globe className="w-4 h-4 mr-2" />
                {publishTourMutation.isPending ? 'Publishing...' : 'Publish Tour'}
              </Button>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TourEditModal;
