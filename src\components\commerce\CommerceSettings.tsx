/**
 * CommerceSettings Component
 * Comprehensive platform configuration and settings management
 * Mobile-first responsive design for admin configuration
 */

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { 
  Settings, 
  DollarSign, 
  MessageCircle, 
  Package, 
  Users,
  Shield,
  Bell,
  Globe,
  Truck,
  CreditCard,
  Mail,
  Phone,
  Save,
  RefreshCw,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface PlatformSettings {
  general: {
    platform_name: string;
    platform_description: string;
    support_email: string;
    support_phone: string;
    business_address: string;
    currency: string;
    timezone: string;
    language: string;
  };
  commerce: {
    default_commission_rate: number;
    minimum_order_amount: number;
    maximum_order_amount: number;
    auto_approve_vendors: boolean;
    require_vendor_verification: boolean;
    enable_product_reviews: boolean;
    enable_vendor_ratings: boolean;
  };
  whatsapp: {
    business_phone_id: string;
    access_token: string;
    webhook_url: string;
    verify_token: string;
    enable_automation: boolean;
    auto_respond_enabled: boolean;
    business_hours_start: string;
    business_hours_end: string;
  };
  shipping: {
    default_shipping_cost: number;
    free_shipping_threshold: number;
    enable_express_shipping: boolean;
    express_shipping_cost: number;
    enable_store_pickup: boolean;
    estimated_delivery_days: number;
  };
  notifications: {
    email_notifications: boolean;
    sms_notifications: boolean;
    whatsapp_notifications: boolean;
    order_notifications: boolean;
    vendor_notifications: boolean;
    customer_notifications: boolean;
  };
  security: {
    require_phone_verification: boolean;
    enable_two_factor_auth: boolean;
    session_timeout_minutes: number;
    max_login_attempts: number;
    enable_rate_limiting: boolean;
  };
}

interface CommerceSettingsProps {
  className?: string;
}

const CommerceSettings = ({ className }: CommerceSettingsProps) => {
  const [settings, setSettings] = useState<PlatformSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      
      // Simulate API call - replace with actual service
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSettings({
        general: {
          platform_name: 'VirtualRealTour',
          platform_description: 'Immersive 360° virtual tours with integrated e-commerce',
          support_email: '<EMAIL>',
          support_phone: '+234-XXX-XXX-XXXX',
          business_address: 'Lagos, Nigeria',
          currency: 'NGN',
          timezone: 'Africa/Lagos',
          language: 'en'
        },
        commerce: {
          default_commission_rate: 0.10,
          minimum_order_amount: 1000,
          maximum_order_amount: 5000000,
          auto_approve_vendors: false,
          require_vendor_verification: true,
          enable_product_reviews: true,
          enable_vendor_ratings: true
        },
        whatsapp: {
          business_phone_id: process.env.VITE_WHATSAPP_BUSINESS_PHONE_ID || '',
          access_token: process.env.VITE_WHATSAPP_ACCESS_TOKEN || '',
          webhook_url: 'https://virtualrealtour.com/api/whatsapp/webhook',
          verify_token: 'vrt_webhook_verify_token',
          enable_automation: true,
          auto_respond_enabled: true,
          business_hours_start: '09:00',
          business_hours_end: '18:00'
        },
        shipping: {
          default_shipping_cost: 2000,
          free_shipping_threshold: 50000,
          enable_express_shipping: true,
          express_shipping_cost: 5000,
          enable_store_pickup: true,
          estimated_delivery_days: 5
        },
        notifications: {
          email_notifications: true,
          sms_notifications: false,
          whatsapp_notifications: true,
          order_notifications: true,
          vendor_notifications: true,
          customer_notifications: true
        },
        security: {
          require_phone_verification: true,
          enable_two_factor_auth: false,
          session_timeout_minutes: 60,
          max_login_attempts: 5,
          enable_rate_limiting: true
        }
      });
    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error('Failed to load settings');
    } finally {
      setIsLoading(false);
    }
  };

  const updateSetting = (section: keyof PlatformSettings, key: string, value: any) => {
    if (!settings) return;
    
    setSettings(prev => ({
      ...prev!,
      [section]: {
        ...prev![section],
        [key]: value
      }
    }));
    setHasChanges(true);
  };

  const saveSettings = async () => {
    if (!settings) return;
    
    try {
      setIsSaving(true);
      
      // Simulate API call - replace with actual service
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      toast.success('Settings saved successfully');
      setHasChanges(false);
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  };

  const resetSettings = async () => {
    await loadSettings();
    setHasChanges(false);
    toast.success('Settings reset to defaults');
  };

  const SettingCard = ({ 
    title, 
    description, 
    children 
  }: { 
    title: string; 
    description: string; 
    children: React.ReactNode; 
  }) => (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {children}
      </CardContent>
    </Card>
  );

  const SettingRow = ({ 
    label, 
    description, 
    children 
  }: { 
    label: string; 
    description?: string; 
    children: React.ReactNode; 
  }) => (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
      <div className="flex-1">
        <Label className="text-sm font-medium">{label}</Label>
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
      </div>
      <div className="sm:w-64">
        {children}
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-10 bg-gray-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (!settings) return null;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold">Platform Settings</h1>
          <p className="text-muted-foreground">Configure your e-commerce platform</p>
        </div>
        <div className="flex items-center gap-2">
          {hasChanges && (
            <Badge variant="outline" className="text-orange-600 border-orange-600">
              <AlertCircle className="w-3 h-3 mr-1" />
              Unsaved Changes
            </Badge>
          )}
          <Button variant="outline" onClick={resetSettings} disabled={isSaving}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Reset
          </Button>
          <Button onClick={saveSettings} disabled={!hasChanges || isSaving}>
            {isSaving ? (
              <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <Save className="w-4 h-4 mr-2" />
            )}
            Save Changes
          </Button>
        </div>
      </div>

      {/* Settings Tabs */}
      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 lg:grid-cols-6">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="commerce">Commerce</TabsTrigger>
          <TabsTrigger value="whatsapp">WhatsApp</TabsTrigger>
          <TabsTrigger value="shipping">Shipping</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
        </TabsList>

        {/* General Settings */}
        <TabsContent value="general" className="space-y-6">
          <SettingCard
            title="Platform Information"
            description="Basic platform details and contact information"
          >
            <SettingRow label="Platform Name">
              <Input
                value={settings.general.platform_name}
                onChange={(e) => updateSetting('general', 'platform_name', e.target.value)}
              />
            </SettingRow>
            <SettingRow label="Description">
              <Textarea
                value={settings.general.platform_description}
                onChange={(e) => updateSetting('general', 'platform_description', e.target.value)}
                rows={2}
              />
            </SettingRow>
            <SettingRow label="Support Email">
              <Input
                type="email"
                value={settings.general.support_email}
                onChange={(e) => updateSetting('general', 'support_email', e.target.value)}
              />
            </SettingRow>
            <SettingRow label="Support Phone">
              <Input
                value={settings.general.support_phone}
                onChange={(e) => updateSetting('general', 'support_phone', e.target.value)}
              />
            </SettingRow>
            <SettingRow label="Business Address">
              <Textarea
                value={settings.general.business_address}
                onChange={(e) => updateSetting('general', 'business_address', e.target.value)}
                rows={2}
              />
            </SettingRow>
          </SettingCard>

          <SettingCard
            title="Localization"
            description="Currency, timezone, and language settings"
          >
            <SettingRow label="Currency">
              <Select
                value={settings.general.currency}
                onValueChange={(value) => updateSetting('general', 'currency', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="NGN">Nigerian Naira (₦)</SelectItem>
                  <SelectItem value="USD">US Dollar ($)</SelectItem>
                  <SelectItem value="EUR">Euro (€)</SelectItem>
                </SelectContent>
              </Select>
            </SettingRow>
            <SettingRow label="Timezone">
              <Select
                value={settings.general.timezone}
                onValueChange={(value) => updateSetting('general', 'timezone', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Africa/Lagos">Africa/Lagos (WAT)</SelectItem>
                  <SelectItem value="UTC">UTC</SelectItem>
                  <SelectItem value="America/New_York">America/New_York (EST)</SelectItem>
                </SelectContent>
              </Select>
            </SettingRow>
            <SettingRow label="Language">
              <Select
                value={settings.general.language}
                onValueChange={(value) => updateSetting('general', 'language', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="yo">Yoruba</SelectItem>
                  <SelectItem value="ig">Igbo</SelectItem>
                  <SelectItem value="ha">Hausa</SelectItem>
                </SelectContent>
              </Select>
            </SettingRow>
          </SettingCard>
        </TabsContent>

        {/* Commerce Settings */}
        <TabsContent value="commerce" className="space-y-6">
          <SettingCard
            title="Commission & Pricing"
            description="Default commission rates and order limits"
          >
            <SettingRow 
              label="Default Commission Rate" 
              description="Default commission rate for new vendors"
            >
              <div className="flex items-center gap-2">
                <Input
                  type="number"
                  min="0"
                  max="100"
                  step="0.1"
                  value={settings.commerce.default_commission_rate * 100}
                  onChange={(e) => updateSetting('commerce', 'default_commission_rate', Number(e.target.value) / 100)}
                />
                <span className="text-sm text-muted-foreground">%</span>
              </div>
            </SettingRow>
            <SettingRow label="Minimum Order Amount">
              <div className="flex items-center gap-2">
                <span className="text-sm">₦</span>
                <Input
                  type="number"
                  min="0"
                  value={settings.commerce.minimum_order_amount}
                  onChange={(e) => updateSetting('commerce', 'minimum_order_amount', Number(e.target.value))}
                />
              </div>
            </SettingRow>
            <SettingRow label="Maximum Order Amount">
              <div className="flex items-center gap-2">
                <span className="text-sm">₦</span>
                <Input
                  type="number"
                  min="0"
                  value={settings.commerce.maximum_order_amount}
                  onChange={(e) => updateSetting('commerce', 'maximum_order_amount', Number(e.target.value))}
                />
              </div>
            </SettingRow>
          </SettingCard>

          <SettingCard
            title="Vendor Management"
            description="Vendor approval and verification settings"
          >
            <SettingRow 
              label="Auto-approve Vendors" 
              description="Automatically approve new vendor applications"
            >
              <Switch
                checked={settings.commerce.auto_approve_vendors}
                onCheckedChange={(checked) => updateSetting('commerce', 'auto_approve_vendors', checked)}
              />
            </SettingRow>
            <SettingRow 
              label="Require Vendor Verification" 
              description="Require document verification for vendors"
            >
              <Switch
                checked={settings.commerce.require_vendor_verification}
                onCheckedChange={(checked) => updateSetting('commerce', 'require_vendor_verification', checked)}
              />
            </SettingRow>
            <SettingRow 
              label="Enable Product Reviews" 
              description="Allow customers to review products"
            >
              <Switch
                checked={settings.commerce.enable_product_reviews}
                onCheckedChange={(checked) => updateSetting('commerce', 'enable_product_reviews', checked)}
              />
            </SettingRow>
            <SettingRow 
              label="Enable Vendor Ratings" 
              description="Allow customers to rate vendors"
            >
              <Switch
                checked={settings.commerce.enable_vendor_ratings}
                onCheckedChange={(checked) => updateSetting('commerce', 'enable_vendor_ratings', checked)}
              />
            </SettingRow>
          </SettingCard>
        </TabsContent>

        {/* WhatsApp Settings */}
        <TabsContent value="whatsapp" className="space-y-6">
          <SettingCard
            title="WhatsApp Business API"
            description="Configure WhatsApp Business API integration"
          >
            <SettingRow label="Business Phone Number ID">
              <Input
                value={settings.whatsapp.business_phone_id}
                onChange={(e) => updateSetting('whatsapp', 'business_phone_id', e.target.value)}
                placeholder="Enter phone number ID"
              />
            </SettingRow>
            <SettingRow label="Access Token">
              <Input
                type="password"
                value={settings.whatsapp.access_token}
                onChange={(e) => updateSetting('whatsapp', 'access_token', e.target.value)}
                placeholder="Enter access token"
              />
            </SettingRow>
            <SettingRow label="Webhook URL">
              <Input
                value={settings.whatsapp.webhook_url}
                onChange={(e) => updateSetting('whatsapp', 'webhook_url', e.target.value)}
                placeholder="https://your-domain.com/api/whatsapp/webhook"
              />
            </SettingRow>
            <SettingRow label="Verify Token">
              <Input
                value={settings.whatsapp.verify_token}
                onChange={(e) => updateSetting('whatsapp', 'verify_token', e.target.value)}
                placeholder="Enter verify token"
              />
            </SettingRow>
          </SettingCard>

          <SettingCard
            title="Automation Settings"
            description="Configure automated messaging and responses"
          >
            <SettingRow 
              label="Enable Automation" 
              description="Enable automated WhatsApp messaging"
            >
              <Switch
                checked={settings.whatsapp.enable_automation}
                onCheckedChange={(checked) => updateSetting('whatsapp', 'enable_automation', checked)}
              />
            </SettingRow>
            <SettingRow 
              label="Auto-respond" 
              description="Automatically respond to common queries"
            >
              <Switch
                checked={settings.whatsapp.auto_respond_enabled}
                onCheckedChange={(checked) => updateSetting('whatsapp', 'auto_respond_enabled', checked)}
              />
            </SettingRow>
            <SettingRow label="Business Hours Start">
              <Input
                type="time"
                value={settings.whatsapp.business_hours_start}
                onChange={(e) => updateSetting('whatsapp', 'business_hours_start', e.target.value)}
              />
            </SettingRow>
            <SettingRow label="Business Hours End">
              <Input
                type="time"
                value={settings.whatsapp.business_hours_end}
                onChange={(e) => updateSetting('whatsapp', 'business_hours_end', e.target.value)}
              />
            </SettingRow>
          </SettingCard>
        </TabsContent>

        {/* Shipping Settings */}
        <TabsContent value="shipping" className="space-y-6">
          <SettingCard
            title="Shipping Configuration"
            description="Configure shipping options and costs"
          >
            <SettingRow label="Default Shipping Cost">
              <div className="flex items-center gap-2">
                <span className="text-sm">₦</span>
                <Input
                  type="number"
                  min="0"
                  value={settings.shipping.default_shipping_cost}
                  onChange={(e) => updateSetting('shipping', 'default_shipping_cost', Number(e.target.value))}
                />
              </div>
            </SettingRow>
            <SettingRow 
              label="Free Shipping Threshold" 
              description="Minimum order amount for free shipping"
            >
              <div className="flex items-center gap-2">
                <span className="text-sm">₦</span>
                <Input
                  type="number"
                  min="0"
                  value={settings.shipping.free_shipping_threshold}
                  onChange={(e) => updateSetting('shipping', 'free_shipping_threshold', Number(e.target.value))}
                />
              </div>
            </SettingRow>
            <SettingRow 
              label="Enable Express Shipping" 
              description="Offer express shipping option"
            >
              <Switch
                checked={settings.shipping.enable_express_shipping}
                onCheckedChange={(checked) => updateSetting('shipping', 'enable_express_shipping', checked)}
              />
            </SettingRow>
            <SettingRow label="Express Shipping Cost">
              <div className="flex items-center gap-2">
                <span className="text-sm">₦</span>
                <Input
                  type="number"
                  min="0"
                  value={settings.shipping.express_shipping_cost}
                  onChange={(e) => updateSetting('shipping', 'express_shipping_cost', Number(e.target.value))}
                  disabled={!settings.shipping.enable_express_shipping}
                />
              </div>
            </SettingRow>
            <SettingRow 
              label="Enable Store Pickup" 
              description="Allow customers to pick up orders"
            >
              <Switch
                checked={settings.shipping.enable_store_pickup}
                onCheckedChange={(checked) => updateSetting('shipping', 'enable_store_pickup', checked)}
              />
            </SettingRow>
            <SettingRow 
              label="Estimated Delivery Days" 
              description="Default delivery time estimate"
            >
              <div className="flex items-center gap-2">
                <Input
                  type="number"
                  min="1"
                  max="30"
                  value={settings.shipping.estimated_delivery_days}
                  onChange={(e) => updateSetting('shipping', 'estimated_delivery_days', Number(e.target.value))}
                />
                <span className="text-sm text-muted-foreground">days</span>
              </div>
            </SettingRow>
          </SettingCard>
        </TabsContent>

        {/* Notifications Settings */}
        <TabsContent value="notifications" className="space-y-6">
          <SettingCard
            title="Notification Channels"
            description="Configure notification delivery methods"
          >
            <SettingRow 
              label="Email Notifications" 
              description="Send notifications via email"
            >
              <Switch
                checked={settings.notifications.email_notifications}
                onCheckedChange={(checked) => updateSetting('notifications', 'email_notifications', checked)}
              />
            </SettingRow>
            <SettingRow 
              label="SMS Notifications" 
              description="Send notifications via SMS"
            >
              <Switch
                checked={settings.notifications.sms_notifications}
                onCheckedChange={(checked) => updateSetting('notifications', 'sms_notifications', checked)}
              />
            </SettingRow>
            <SettingRow 
              label="WhatsApp Notifications" 
              description="Send notifications via WhatsApp"
            >
              <Switch
                checked={settings.notifications.whatsapp_notifications}
                onCheckedChange={(checked) => updateSetting('notifications', 'whatsapp_notifications', checked)}
              />
            </SettingRow>
          </SettingCard>

          <SettingCard
            title="Notification Types"
            description="Configure which events trigger notifications"
          >
            <SettingRow 
              label="Order Notifications" 
              description="Notify about order status changes"
            >
              <Switch
                checked={settings.notifications.order_notifications}
                onCheckedChange={(checked) => updateSetting('notifications', 'order_notifications', checked)}
              />
            </SettingRow>
            <SettingRow 
              label="Vendor Notifications" 
              description="Notify about vendor activities"
            >
              <Switch
                checked={settings.notifications.vendor_notifications}
                onCheckedChange={(checked) => updateSetting('notifications', 'vendor_notifications', checked)}
              />
            </SettingRow>
            <SettingRow 
              label="Customer Notifications" 
              description="Notify about customer activities"
            >
              <Switch
                checked={settings.notifications.customer_notifications}
                onCheckedChange={(checked) => updateSetting('notifications', 'customer_notifications', checked)}
              />
            </SettingRow>
          </SettingCard>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-6">
          <SettingCard
            title="Authentication & Security"
            description="Configure security and authentication settings"
          >
            <SettingRow 
              label="Require Phone Verification" 
              description="Require phone number verification for new accounts"
            >
              <Switch
                checked={settings.security.require_phone_verification}
                onCheckedChange={(checked) => updateSetting('security', 'require_phone_verification', checked)}
              />
            </SettingRow>
            <SettingRow 
              label="Two-Factor Authentication" 
              description="Enable 2FA for admin accounts"
            >
              <Switch
                checked={settings.security.enable_two_factor_auth}
                onCheckedChange={(checked) => updateSetting('security', 'enable_two_factor_auth', checked)}
              />
            </SettingRow>
            <SettingRow 
              label="Session Timeout" 
              description="Automatic logout after inactivity"
            >
              <div className="flex items-center gap-2">
                <Input
                  type="number"
                  min="5"
                  max="480"
                  value={settings.security.session_timeout_minutes}
                  onChange={(e) => updateSetting('security', 'session_timeout_minutes', Number(e.target.value))}
                />
                <span className="text-sm text-muted-foreground">minutes</span>
              </div>
            </SettingRow>
            <SettingRow 
              label="Max Login Attempts" 
              description="Maximum failed login attempts before lockout"
            >
              <Input
                type="number"
                min="3"
                max="10"
                value={settings.security.max_login_attempts}
                onChange={(e) => updateSetting('security', 'max_login_attempts', Number(e.target.value))}
              />
            </SettingRow>
            <SettingRow 
              label="Rate Limiting" 
              description="Enable API rate limiting"
            >
              <Switch
                checked={settings.security.enable_rate_limiting}
                onCheckedChange={(checked) => updateSetting('security', 'enable_rate_limiting', checked)}
              />
            </SettingRow>
          </SettingCard>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default CommerceSettings;
