/**
 * Vendor Dashboard Sidebar
 * Consistent navigation structure matching admin panel for vendors
 */

import { NavLink, useLocation } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarHeader,
  SidebarFooter,
  useSidebar
} from '@/components/ui/sidebar';
import {
  LayoutDashboard,
  MapPin,
  Package,
  ShoppingCart,
  BarChart3,
  User,
  Settings,
  Globe,
  LogOut,
  Building2,
  Eye,
  Plus,
  DollarSign,
  Users,
  Star,
  MessageSquare,
  HelpCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

// Vendor-specific navigation structure
const vendorNavGroups = [
  {
    label: 'Dashboard',
    items: [
      { title: 'Overview', url: '/vendor-dashboard', icon: LayoutDashboard, description: 'Your business overview & stats' },
    ]
  },
  {
    label: 'Tours & Content',
    items: [
      { title: 'My Tours', url: '/vendor-dashboard/tours', icon: MapPin, description: 'Manage your virtual tours' },
      { title: 'Create Tour', url: '/vendor-dashboard/create', icon: Plus, description: 'Create a new tour' },
    ]
  },
  {
    label: 'Store Management',
    items: [
      { title: 'Products', url: '/vendor-dashboard/products', icon: Package, description: 'Manage your product catalog' },
      { title: 'Orders', url: '/vendor-dashboard/orders', icon: ShoppingCart, description: 'View and manage orders' },
      { title: 'Inventory', url: '/vendor-dashboard/inventory', icon: Package, description: 'Stock management' },
    ]
  },
  {
    label: 'Business',
    items: [
      { title: 'Analytics', url: '/vendor-dashboard/analytics', icon: BarChart3, description: 'Sales and tour performance' },
      { title: 'Revenue', url: '/vendor-dashboard/revenue', icon: DollarSign, description: 'Earnings and payouts' },
      { title: 'Customers', url: '/vendor-dashboard/customers', icon: Users, description: 'Customer management' },
      { title: 'Reviews', url: '/vendor-dashboard/reviews', icon: Star, description: 'Customer reviews & ratings' },
    ]
  },
  {
    label: 'Account',
    items: [
      { title: 'Profile', url: '/vendor-dashboard/profile', icon: User, description: 'Business profile settings' },
      { title: 'Settings', url: '/vendor-dashboard/settings', icon: Settings, description: 'Account preferences' },
    ]
  }
];

const VendorSidebar = () => {
  const location = useLocation();
  const { state } = useSidebar();
  const { signOut, profile } = useAuth();

  const isActiveRoute = (url: string) => {
    if (url === '/vendor-dashboard') {
      return location.pathname === '/vendor-dashboard';
    }
    return location.pathname.startsWith(url);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <Sidebar className="border-r bg-background">
      <SidebarHeader className="border-b p-4">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <Building2 className="w-4 h-4 text-primary-foreground" />
          </div>
          {state === 'expanded' && (
            <div className="flex flex-col">
              <span className="font-semibold text-sm">VirtualRealTour</span>
              <span className="text-xs text-muted-foreground">Vendor Dashboard</span>
            </div>
          )}
        </div>
      </SidebarHeader>

      <SidebarContent className="flex-1 overflow-y-auto">
        {vendorNavGroups.map((group) => (
          <SidebarGroup key={group.label}>
            <SidebarGroupLabel className="px-4 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              {group.label}
            </SidebarGroupLabel>
            <SidebarGroupContent className="mt-2">
              <SidebarMenu>
                {group.items.map((item) => {
                  const Icon = item.icon;
                  const isActive = isActiveRoute(item.url);
                  
                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild className="h-auto p-0">
                        <NavLink
                          to={item.url}
                          className={`
                            flex items-center gap-3 rounded-md px-3 py-2 mx-2 mb-1 transition-all duration-200
                            min-h-[2.25rem] touch-target
                            ${isActive 
                              ? 'bg-primary text-primary-foreground shadow-sm' 
                              : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                            }
                          `}
                        >
                          <Icon className="h-4 w-4 flex-shrink-0" />
                          {state === 'expanded' && (
                            <div className="flex flex-col min-w-0 flex-1">
                              <span className="text-sm font-medium truncate">{item.title}</span>
                              {item.description && (
                                <span className="text-xs opacity-75 truncate">{item.description}</span>
                              )}
                            </div>
                          )}
                        </NavLink>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}

        {/* Quick Actions */}
        <SidebarGroup className="mt-6">
          <SidebarGroupLabel className="px-4 py-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            Quick Actions
          </SidebarGroupLabel>
          <SidebarGroupContent className="mt-2">
            <SidebarMenu>
              <SidebarMenuItem>
                <SidebarMenuButton asChild className="h-auto p-0">
                  <NavLink to="/" className="flex items-center gap-3 rounded-md px-3 py-2 mx-2 mb-1 transition-all duration-200 hover:bg-muted text-muted-foreground hover:text-foreground min-h-[2.25rem]">
                    <Globe className="h-4 w-4 flex-shrink-0" />
                    {state === 'expanded' && <span className="text-sm font-medium">View Website</span>}
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild className="h-auto p-0">
                  <NavLink to="/showcase" className="flex items-center gap-3 rounded-md px-3 py-2 mx-2 mb-1 transition-all duration-200 hover:bg-muted text-muted-foreground hover:text-foreground min-h-[2.25rem]">
                    <Eye className="h-4 w-4 flex-shrink-0" />
                    {state === 'expanded' && <span className="text-sm font-medium">Tour Showcase</span>}
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild className="h-auto p-0">
                  <NavLink to="/vendor-dashboard/support" className="flex items-center gap-3 rounded-md px-3 py-2 mx-2 mb-1 transition-all duration-200 hover:bg-muted text-muted-foreground hover:text-foreground min-h-[2.25rem]">
                    <MessageSquare className="h-4 w-4 flex-shrink-0" />
                    {state === 'expanded' && <span className="text-sm font-medium">Support</span>}
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
              <SidebarMenuItem>
                <SidebarMenuButton asChild className="h-auto p-0">
                  <NavLink to="/help" className="flex items-center gap-3 rounded-md px-3 py-2 mx-2 mb-1 transition-all duration-200 hover:bg-muted text-muted-foreground hover:text-foreground min-h-[2.25rem]">
                    <HelpCircle className="h-4 w-4 flex-shrink-0" />
                    {state === 'expanded' && <span className="text-sm font-medium">Help Center</span>}
                  </NavLink>
                </SidebarMenuButton>
              </SidebarMenuItem>
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="border-t p-4">
        {/* Vendor Profile Section */}
        <div className="flex items-center gap-3 mb-3">
          <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
            <Building2 className="w-4 h-4" />
          </div>
          {state === 'expanded' && (
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">
                {profile?.full_name || 'Vendor'}
              </p>
              <p className="text-xs text-muted-foreground truncate">
                {profile?.email || '<EMAIL>'}
              </p>
              <Badge variant="secondary" className="text-xs mt-1">
                Vendor Account
              </Badge>
            </div>
          )}
        </div>

        {/* Sign Out Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSignOut}
          className={`
            w-full justify-start gap-3 text-muted-foreground hover:text-foreground
            ${state === 'collapsed' ? 'px-2' : 'px-3'}
          `}
        >
          <LogOut className="h-4 w-4 flex-shrink-0" />
          {state === 'expanded' && <span className="text-sm">Sign Out</span>}
        </Button>
      </SidebarFooter>
    </Sidebar>
  );
};

export default VendorSidebar;
