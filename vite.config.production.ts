/**
 * Vite Production Configuration
 * Optimized for world-class performance and Nigerian market deployment
 */

import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { visualizer } from 'rollup-plugin-visualizer';
import { compression } from 'vite-plugin-compression';

export default defineConfig({
  plugins: [
    react({
      // Enable React Fast Refresh for development
      fastRefresh: true,
      // Optimize JSX for production
      jsxImportSource: '@emotion/react',
      babel: {
        plugins: [
          // Remove console.log in production
          ['transform-remove-console', { exclude: ['error', 'warn'] }]
        ]
      }
    }),
    
    // Bundle analyzer for optimization insights
    visualizer({
      filename: 'dist/stats.html',
      open: false,
      gzipSize: true,
      brotliSize: true
    }),
    
    // Gzip compression for better performance
    compression({
      algorithm: 'gzip',
      ext: '.gz'
    }),
    
    // Brotli compression for modern browsers
    compression({
      algorithm: 'brotliCompress',
      ext: '.br'
    })
  ],

  // Path resolution
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@/components': resolve(__dirname, './src/components'),
      '@/pages': resolve(__dirname, './src/pages'),
      '@/hooks': resolve(__dirname, './src/hooks'),
      '@/services': resolve(__dirname, './src/services'),
      '@/utils': resolve(__dirname, './src/utils'),
      '@/lib': resolve(__dirname, './src/lib'),
      '@/types': resolve(__dirname, './src/types')
    }
  },

  // Build optimization
  build: {
    // Target modern browsers for better performance
    target: ['es2020', 'chrome80', 'firefox78', 'safari14', 'edge88'],
    
    // Output directory
    outDir: 'dist',
    
    // Generate source maps for debugging
    sourcemap: true,
    
    // Minification
    minify: 'terser',
    terserOptions: {
      compress: {
        // Remove console.log in production
        drop_console: true,
        drop_debugger: true,
        // Remove unused code
        dead_code: true,
        // Optimize comparisons
        comparisons: true,
        // Optimize conditionals
        conditionals: true,
        // Optimize loops
        loops: true,
        // Remove unused variables
        unused: true
      },
      mangle: {
        // Mangle variable names for smaller bundle
        toplevel: true,
        safari10: true
      },
      format: {
        // Remove comments
        comments: false
      }
    },
    
    // Rollup options for advanced optimization
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      },
      
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Vendor chunks
          'vendor-react': ['react', 'react-dom', 'react-router-dom'],
          'vendor-ui': ['framer-motion', 'lucide-react'],
          'vendor-utils': ['clsx', 'tailwind-merge'],
          
          // Commerce chunks
          'commerce-core': [
            './src/components/commerce/ShoppingCart.tsx',
            './src/components/commerce/ProductCard.tsx',
            './src/hooks/useShoppingCart.ts'
          ],
          'commerce-vendor': [
            './src/components/commerce/VendorDashboard.tsx',
            './src/pages/VendorDashboard.tsx',
            './src/pages/VendorProducts.tsx'
          ],
          'commerce-admin': [
            './src/components/commerce/AdminCommerceHub.tsx',
            './src/pages/admin/AdminCommerce.tsx'
          ],
          
          // WhatsApp integration
          'whatsapp': [
            './src/components/commerce/WhatsAppChatWidget.tsx',
            './src/services/commerce/EnhancedWhatsAppService.ts'
          ]
        },
        
        // Optimize chunk file names
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId.split('/').pop()?.replace('.tsx', '').replace('.ts', '')
            : 'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        },
        
        // Optimize asset file names
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || [];
          const ext = info[info.length - 1];
          
          if (/\.(png|jpe?g|svg|gif|tiff|bmp|ico)$/i.test(assetInfo.name || '')) {
            return `images/[name]-[hash][extname]`;
          }
          if (/\.(woff2?|eot|ttf|otf)$/i.test(assetInfo.name || '')) {
            return `fonts/[name]-[hash][extname]`;
          }
          if (ext === 'css') {
            return `css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        }
      },
      
      // External dependencies (if using CDN)
      external: [
        // Uncomment if using CDN for these libraries
        // 'react',
        // 'react-dom'
      ]
    },
    
    // Chunk size warnings
    chunkSizeWarningLimit: 1000,
    
    // Asset inlining threshold
    assetsInlineLimit: 4096
  },

  // Server configuration for production preview
  preview: {
    port: 3000,
    host: true,
    strictPort: true,
    cors: true,
    headers: {
      // Security headers
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
      
      // Performance headers
      'Cache-Control': 'public, max-age=31536000, immutable'
    }
  },

  // Optimization for dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'framer-motion',
      'lucide-react',
      'clsx',
      'tailwind-merge'
    ],
    exclude: [
      // Exclude large dependencies that should be loaded separately
    ]
  },

  // Environment variables
  define: {
    __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
    __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    __PRODUCTION__: JSON.stringify(true)
  },

  // CSS optimization
  css: {
    devSourcemap: false,
    preprocessorOptions: {
      scss: {
        additionalData: `@import "@/styles/variables.scss";`
      }
    },
    postcss: {
      plugins: [
        // Autoprefixer for browser compatibility
        require('autoprefixer'),
        // PurgeCSS for removing unused styles
        require('@fullhuman/postcss-purgecss')({
          content: [
            './src/**/*.{js,jsx,ts,tsx}',
            './index.html'
          ],
          defaultExtractor: content => content.match(/[\w-/:]+(?<!:)/g) || [],
          safelist: [
            // Preserve dynamic classes
            /^(bg-|text-|border-|hover:|focus:|active:)/,
            // Preserve Radix UI classes
            /^(radix-|data-)/,
            // Preserve animation classes
            /^(animate-|transition-)/
          ]
        }),
        // CSS nano for minification
        require('cssnano')({
          preset: ['default', {
            discardComments: { removeAll: true },
            normalizeWhitespace: true,
            colormin: true,
            convertValues: true,
            discardDuplicates: true,
            discardEmpty: true,
            mergeRules: true,
            minifyFontValues: true,
            minifyParams: true,
            minifySelectors: true,
            reduceIdents: true,
            reduceTransforms: true,
            svgo: true,
            uniqueSelectors: true
          }]
        })
      ]
    }
  },

  // Experimental features
  experimental: {
    // Enable build optimizations
    renderBuiltUrl: (filename, { hostType }) => {
      if (hostType === 'js') {
        return { js: `/${filename}` };
      } else {
        return { relative: true };
      }
    }
  }
});
