/**
 * Global Performance Configuration
 * Lightning-fast loading for entire application
 */

export const PERFORMANCE_CONFIG = {
  // Lazy loading thresholds
  INTERSECTION_THRESHOLD: 0.1,
  INTERSECTION_ROOT_MARGIN: '100px',
  
  // Debounce/throttle timings
  HOVER_DEBOUNCE: 50,
  SCROLL_THROTTLE: 16, // 60fps
  SEARCH_DEBOUNCE: 300,
  RESIZE_DEBOUNCE: 100,
  
  // Cache settings
  QUERY_STALE_TIME: 5 * 60 * 1000, // 5 minutes
  QUERY_CACHE_TIME: 10 * 60 * 1000, // 10 minutes
  IMAGE_CACHE_TIME: 30 * 60 * 1000, // 30 minutes
  
  // Preloading limits
  PRELOAD_TOURS_COUNT: 6,
  PRELOAD_IMAGES_COUNT: 10,
  PREFETCH_PAGES: ['/', '/showcase', '/about'],
  
  // Animation settings
  FAST_ANIMATION: 150,
  NORMAL_ANIMATION: 300,
  SLOW_ANIMATION: 500,
  
  // Loading priorities
  CRITICAL_RESOURCES: ['navigation', 'hero', 'featured-tours'],
  DEFERRED_RESOURCES: ['footer', 'analytics', 'social-widgets'],
  
  // Bundle optimization
  CHUNK_SIZE_LIMIT: 244 * 1024, // 244KB
  ASSET_SIZE_LIMIT: 512 * 1024, // 512KB
  
  // Image optimization
  IMAGE_QUALITY: 85,
  IMAGE_FORMATS: ['webp', 'avif', 'jpg'],
  RESPONSIVE_BREAKPOINTS: [320, 640, 768, 1024, 1280, 1536],
  
  // Tour loading strategy
  TOUR_LOADING_STRATEGY: {
    homepage: 'eager', // Load immediately
    showcase: 'lazy', // Load on scroll
    dashboard: 'lazy', // Load on scroll
    admin: 'eager', // Load immediately for editing
    preview: 'eager' // Load immediately in modals
  },
  
  // Memory management
  MAX_CACHED_TOURS: 50,
  CLEANUP_INTERVAL: 5 * 60 * 1000, // 5 minutes
  MEMORY_THRESHOLD: 100 * 1024 * 1024, // 100MB
  
  // Network optimization
  CONCURRENT_REQUESTS: 6,
  REQUEST_TIMEOUT: 10000, // 10 seconds
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 second
  
  // Progressive loading
  PROGRESSIVE_LOADING: {
    enabled: true,
    chunkSize: 10, // Load 10 items at a time
    loadMoreThreshold: 3 // Load more when 3 items from end
  }
} as const;

export const DEVICE_PERFORMANCE_PROFILES = {
  high: {
    preloadCount: 10,
    animationDuration: PERFORMANCE_CONFIG.NORMAL_ANIMATION,
    enableParallax: true,
    enableBlur: true,
    imageQuality: 90
  },
  medium: {
    preloadCount: 6,
    animationDuration: PERFORMANCE_CONFIG.FAST_ANIMATION,
    enableParallax: false,
    enableBlur: true,
    imageQuality: 85
  },
  low: {
    preloadCount: 3,
    animationDuration: PERFORMANCE_CONFIG.FAST_ANIMATION,
    enableParallax: false,
    enableBlur: false,
    imageQuality: 75
  }
} as const;

/**
 * Detect device performance profile
 */
export const getDevicePerformanceProfile = () => {
  // Check for performance hints
  const connection = (navigator as any).connection;
  const memory = (performance as any).memory;
  
  // Low-end device indicators
  if (connection?.effectiveType === '2g' || connection?.effectiveType === 'slow-2g') {
    return DEVICE_PERFORMANCE_PROFILES.low;
  }
  
  if (memory?.usedJSHeapSize && memory.totalJSHeapSize) {
    const memoryRatio = memory.usedJSHeapSize / memory.totalJSHeapSize;
    if (memoryRatio > 0.8) {
      return DEVICE_PERFORMANCE_PROFILES.low;
    }
  }
  
  // Check CPU cores (rough performance indicator)
  const cores = navigator.hardwareConcurrency || 4;
  if (cores <= 2) {
    return DEVICE_PERFORMANCE_PROFILES.medium;
  }
  
  // Default to high performance
  return DEVICE_PERFORMANCE_PROFILES.high;
};

/**
 * Performance monitoring utilities
 */
export const PERFORMANCE_MARKS = {
  APP_START: 'app-start',
  APP_READY: 'app-ready',
  ROUTE_START: 'route-start',
  ROUTE_END: 'route-end',
  TOUR_LOAD_START: 'tour-load-start',
  TOUR_LOAD_END: 'tour-load-end',
  MODAL_OPEN: 'modal-open',
  MODAL_READY: 'modal-ready'
} as const;
