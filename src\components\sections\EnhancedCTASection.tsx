
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { ArrowRight, Phone, Mail, MessageCircle } from 'lucide-react'
import { Link } from 'react-router-dom'

const EnhancedCTASection = () => {
  const contactMethods = [
    {
      icon: Phone,
      title: "Call Us",
      description: "Speak with our experts",
      action: "+234 ************",
      link: "tel:+2349077776066"
    },
    {
      icon: Mail,
      title: "Email Us",
      description: "Get detailed information",
      action: "Get Quote",
      link: "/contact"
    },
    {
      icon: MessageCircle,
      title: "WhatsApp",
      description: "Quick consultation",
      action: "Chat Now",
      link: "https://wa.me/2349077776066?text=Hi,%20I'm%20interested%20in%20virtual%20tour%20services"
    }
  ]

  return (
    <section className="py-16 lg:py-20">
      <div className="container px-4 sm:px-6 lg:px-8">
        <div className="bg-gray-50 rounded-2xl p-8 lg:p-12 border border-gray-200">
          {/* Clean Main CTA */}
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
              Ready to Transform
              <br />
              <span className="text-blue-600">Your Space?</span>
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 mb-8 lg:mb-12 leading-relaxed max-w-4xl mx-auto">
              Join hundreds of businesses using professional virtual tours to showcase their spaces 
              and attract more customers.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12 lg:mb-16">
              <Button 
                size="lg" 
                className="w-full sm:w-auto text-lg px-10 py-4 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium" 
                asChild
              >
                <Link to="/contact" className="flex items-center justify-center">
                  Get Free Consultation
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
              <Button 
                size="lg" 
                variant="outline" 
                className="w-full sm:w-auto text-lg px-10 py-4 border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white rounded-lg font-medium" 
                asChild
              >
                <Link to="/showcase" className="flex items-center justify-center">
                  View Sample Tours
                </Link>
              </Button>
            </div>
          </div>

          {/* Clean Contact Methods */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-5xl mx-auto">
            {contactMethods.map((method, index) => (
              <div key={method.title} className="bg-white rounded-lg p-6 border border-gray-200 text-center hover:shadow-md transition-shadow duration-300">
                <div className="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <method.icon className="w-8 h-8 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{method.title}</h3>
                <p className="text-gray-600 mb-4 text-sm">{method.description}</p>
                {method.link.startsWith('http') || method.link.startsWith('tel:') ? (
                  <a 
                    href={method.link}
                    className="inline-flex items-center justify-center text-blue-600 hover:text-blue-700 font-medium transition-colors duration-300"
                    target={method.link.startsWith('http') ? '_blank' : undefined}
                    rel={method.link.startsWith('http') ? 'noopener noreferrer' : undefined}
                  >
                    {method.action}
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </a>
                ) : (
                  <Link 
                    to={method.link}
                    className="inline-flex items-center justify-center text-blue-600 hover:text-blue-700 font-medium transition-colors duration-300"
                  >
                    {method.action}
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </Link>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default EnhancedCTASection
