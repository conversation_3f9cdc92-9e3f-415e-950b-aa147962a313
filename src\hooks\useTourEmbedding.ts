/**
 * useTourEmbedding Hook
 * Manages secure tour embedding and overlay data
 * Enhances existing tour functionality without breaking current system
 */

import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

interface TourEmbedData {
  id: string;
  title: string;
  description?: string;
  embed_url: string;
  secure_url: string;
  overlay_config?: {
    theme: 'glass' | 'dark' | 'light' | 'minimal';
    hotspots: Array<{
      id: string;
      type: 'product' | 'info' | 'video' | 'link' | 'pano' | 'image' | 'text';
      position: { x: number; y: number };
      content: any;
      animation: string;
      style: any;
    }>;
    controls: {
      showNavigation: boolean;
      showFullscreen: boolean;
      showShare: boolean;
      showAudio: boolean;
    };
  };
  branding: {
    showWatermark: boolean;
    customLogo?: string;
    brandColors?: {
      primary: string;
      secondary: string;
    };
  };
}

interface UseTourEmbeddingReturn {
  tourData: TourEmbedData | null;
  isLoading: boolean;
  error: string | null;
  generateSecureUrl: (tourId: string) => string;
  generateEmbedCode: (tourId: string, options?: EmbedOptions) => string;
  updateOverlayConfig: (tourId: string, config: any) => Promise<void>;
}

interface EmbedOptions {
  width?: string;
  height?: string;
  showControls?: boolean;
  allowFullscreen?: boolean;
  theme?: 'glass' | 'dark' | 'light' | 'minimal';
}

export const useTourEmbedding = (tourId?: string): UseTourEmbeddingReturn => {
  const [tourData, setTourData] = useState<TourEmbedData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Generate secure URL that prevents source leakage
  const generateSecureUrl = (id: string): string => {
    return `${window.location.origin}/tours/${id}`;
  };

  // Generate embed code for external sites
  const generateEmbedCode = (id: string, options: EmbedOptions = {}): string => {
    const {
      width = '100%',
      height = '400px',
      showControls = true,
      allowFullscreen = true,
      theme = 'glass'
    } = options;

    const secureUrl = generateSecureUrl(id);
    
    return `<iframe 
  src="${secureUrl}" 
  width="${width}" 
  height="${height}" 
  frameborder="0" 
  ${allowFullscreen ? 'allowfullscreen' : ''}
  title="Virtual Tour"
  style="border-radius: 8px; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);"
></iframe>`;
  };

  // Fetch tour data with overlay configuration
  const fetchTourData = async (id: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const { data: tour, error: tourError } = await supabase
        .from('tours')
        .select(`
          id,
          title,
          description,
          embed_url,
          overlay_config,
          branding_config
        `)
        .eq('id', id)
        .single();

      if (tourError) throw tourError;

      if (tour) {
        const tourEmbedData: TourEmbedData = {
          id: tour.id,
          title: tour.title,
          description: tour.description,
          embed_url: tour.embed_url,
          secure_url: generateSecureUrl(tour.id),
          overlay_config: tour.overlay_config || {
            theme: 'glass',
            hotspots: [],
            controls: {
              showNavigation: true,
              showFullscreen: true,
              showShare: true,
              showAudio: true
            }
          },
          branding: tour.branding_config || {
            showWatermark: true,
            brandColors: {
              primary: '#3b82f6',
              secondary: '#64748b'
            }
          }
        };

        setTourData(tourEmbedData);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch tour data');
    } finally {
      setIsLoading(false);
    }
  };

  // Update overlay configuration
  const updateOverlayConfig = async (id: string, config: any) => {
    try {
      const { error } = await supabase
        .from('tours')
        .update({ overlay_config: config })
        .eq('id', id);

      if (error) throw error;

      // Update local state
      if (tourData) {
        setTourData({
          ...tourData,
          overlay_config: config
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update overlay config');
    }
  };

  // Fetch tour data when tourId changes
  useEffect(() => {
    if (tourId) {
      fetchTourData(tourId);
    }
  }, [tourId]);

  return {
    tourData,
    isLoading,
    error,
    generateSecureUrl,
    generateEmbedCode,
    updateOverlayConfig
  };
};

// Utility function to validate tour URLs
export const validateTourUrl = (url: string): boolean => {
  try {
    const urlObj = new URL(url);
    // Allow only trusted domains
    const trustedDomains = [
      'virtualrealtour.ng',
      'cloudpano.com',
      'commonninja.com',
      '3dvista.com'
    ];
    
    return trustedDomains.some(domain => urlObj.hostname.includes(domain));
  } catch {
    return false;
  }
};

// Utility function to extract tour ID from various URL formats
export const extractTourId = (url: string): string | null => {
  try {
    const urlObj = new URL(url);
    
    // Handle different URL patterns
    if (urlObj.hostname.includes('cloudpano.com')) {
      const match = url.match(/\/tour\/([^\/\?]+)/);
      return match ? match[1] : null;
    }
    
    if (urlObj.hostname.includes('commonninja.com')) {
      const match = url.match(/\/widget\/([^\/\?]+)/);
      return match ? match[1] : null;
    }
    
    if (urlObj.hostname.includes('virtualrealtour.ng')) {
      const match = url.match(/\/tours\/([^\/\?]+)/);
      return match ? match[1] : null;
    }
    
    return null;
  } catch {
    return null;
  }
};
