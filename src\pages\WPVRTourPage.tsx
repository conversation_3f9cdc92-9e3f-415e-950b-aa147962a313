/**
 * WPVR Tour Page
 * Displays WordPress WPVR tours with WooCommerce integration
 * Route: /wpvr-tour/:slug
 */

import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, Share2, Info, ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

// Import our components
import WPVRTourViewer from '@/components/tour-viewer/WPVRTourViewer';
import Navigation from '@/components/Navigation';
import Footer from '@/components/Footer';
import { useShoppingCart } from '@/hooks/useShoppingCart';

interface WPVRTourPageProps {
  className?: string;
}

const WPVRTourPage: React.FC<WPVRTourPageProps> = ({ className = '' }) => {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [tourData, setTourData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const { items, totalItems } = useShoppingCart();

  // WordPress/WPVR configuration
  const wpvrBaseUrl = import.meta.env.VITE_WORDPRESS_URL || 'https://admin.virtualrealtour.ng';

  useEffect(() => {
    if (!slug) {
      setError('Tour slug is required');
      setIsLoading(false);
      return;
    }

    // Simulate loading tour metadata (in real implementation, fetch from WordPress API)
    const loadTourData = async () => {
      try {
        setIsLoading(true);
        
        // In a real implementation, you would fetch tour data from WordPress:
        // const response = await fetch(`${wpvrBaseUrl}/wp-json/wp/v2/wpvr_item?slug=${slug}`);
        // const tourData = await response.json();
        
        // For now, we'll use mock data
        const mockTourData = {
          id: slug,
          title: `Virtual Tour: ${slug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}`,
          description: 'Immersive 360° virtual tour experience with interactive hotspots and product integration.',
          featured_image: 'https://images.unsplash.com/photo-1560518883-ce09059eeffa?w=1200&h=675&fit=crop',
          categories: ['Virtual Tour', 'Interactive'],
          created_date: new Date().toISOString(),
          author: 'VirtualRealTour Admin'
        };

        setTourData(mockTourData);
        setError(null);
      } catch (err) {
        console.error('Error loading tour data:', err);
        setError('Failed to load tour data');
        toast.error('Failed to load tour');
      } finally {
        setIsLoading(false);
      }
    };

    loadTourData();
  }, [slug, wpvrBaseUrl]);

  // Handle back navigation
  const handleBack = () => {
    navigate(-1);
  };

  // Handle share
  const handleShare = async () => {
    const shareData = {
      title: tourData?.title || 'Virtual Tour',
      text: tourData?.description || 'Check out this amazing virtual tour!',
      url: window.location.href
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      await navigator.clipboard.writeText(window.location.href);
      toast.success('Tour link copied to clipboard');
    }
  };

  // Handle external link to WordPress
  const handleViewInWordPress = () => {
    const wpUrl = `${wpvrBaseUrl}/wpvr_item/${slug}`;
    window.open(wpUrl, '_blank');
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="flex items-center justify-center min-h-[80vh]">
          <Card className="max-w-md mx-4">
            <CardContent className="p-6 text-center">
              <h2 className="text-xl font-semibold text-gray-900 mb-2">
                Tour Not Found
              </h2>
              <p className="text-gray-600 mb-4">{error}</p>
              <div className="flex gap-2 justify-center">
                <Button onClick={handleBack} variant="outline">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Go Back
                </Button>
                <Button onClick={() => navigate('/')}>
                  Go Home
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-black ${className}`}>
      {/* Navigation - Hidden in fullscreen mode */}
      <div className="relative z-40">
        <Navigation />
      </div>

      {/* Tour Info Bar - Only show when not in fullscreen */}
      {!isLoading && tourData && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="relative z-30 bg-white/95 backdrop-blur-sm border-b border-gray-200"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleBack}
                  className="text-gray-600 hover:text-gray-900"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back
                </Button>
                
                <div>
                  <h1 className="text-lg font-semibold text-gray-900 truncate max-w-md">
                    {tourData.title}
                  </h1>
                  <div className="flex items-center space-x-2 mt-1">
                    {tourData.categories?.map((category: string, index: number) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {category}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {/* Shopping cart indicator */}
                {totalItems > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate('/cart')}
                    className="relative"
                  >
                    Cart ({totalItems})
                  </Button>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleShare}
                  className="text-gray-600 hover:text-gray-900"
                >
                  <Share2 className="w-4 h-4" />
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleViewInWordPress}
                  className="text-gray-600 hover:text-gray-900"
                >
                  <ExternalLink className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      )}

      {/* WPVR Tour Viewer */}
      <div className="relative">
        {slug && (
          <WPVRTourViewer
            tourSlug={slug}
            wpvrBaseUrl={wpvrBaseUrl}
            onBack={handleBack}
            onShare={handleShare}
            showControls={true}
            autoplay={false}
          />
        )}
      </div>

      {/* Tour Description - Overlay at bottom */}
      {!isLoading && tourData && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="absolute bottom-4 left-4 right-4 z-30 pointer-events-none"
        >
          <Card className="bg-white/90 backdrop-blur-sm border-white/20 shadow-lg pointer-events-auto">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <p className="text-sm text-gray-600 leading-relaxed">
                    {tourData.description}
                  </p>
                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                    <span>By {tourData.author}</span>
                    <span>•</span>
                    <span>{new Date(tourData.created_date).toLocaleDateString()}</span>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  className="ml-4 text-gray-500 hover:text-gray-700"
                >
                  <Info className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
};

export default WPVRTourPage;
